---
description: Django Backend (DRF) Specific Guidelines
globs: *.py,packages/backend/**
alwaysApply: false
---
# Django Backend (DRF) Specific Guidelines

These guidelines apply specifically to the Django backend, located in `packages/backend`.

## API Design

- **RESTful Principles:** Design the API following RESTful principles, often using Django REST Framework (DRF).
- **FHIR Compatibility:** Try to stay close to FHIR standards where applicable, but this is not a strict requirement. (This was in the original, keeping if relevant)
- **Versioning:** Use API versioning (e.g., `/api/v1/`). Configuration for this might be in `packages/backend/api/settings/` or main Django settings.
- **Endpoints (Views/ViewSets):** Endpoints are typically Django Views or DRF ViewSets, organized into modules within `packages/backend/api/[app_name]/` (e.g., `packages/backend/api/patient/views.py`).
- **Serializers:** Define request and response data shapes using DRF Serializers, often located in `[app_name]/serializers.py`.
- **HTTP Methods:** Use appropriate HTTP methods for each operation, handled by View/ViewSet methods.
- **Authentication & Permissions**: Handled by Django's authentication system and DRF permission classes, configured in settings and applied to views/viewsets.

## Database

- **Database System:** The project uses PostgreSQL as its primary database.
- **ORM:** Django ORM is used for database interaction.
- **Migrations:** Database schema changes are managed via Django migrations (`./cli makemigrations`, `./cli migrate`). Migration files are located in `[app_name]/migrations/`.
- **Repository Pattern:** Data access logic may be abstracted using a repository pattern, potentially found in `packages/backend/impl/django/repositories/`.
- **Models:** Django models define the database schema and are typically located in `[app_name]/models.py`.

## Services

- **Business Logic:** Business logic can be implemented in service classes, potentially located in `packages/backend/impl/services/`.

## Configuration

- **Settings:** Django settings manage configuration, primarily in `packages/backend/babylone/settings/`. Environment-specific configurations might be handled via environment variables or separate settings files.

## Documentation

- **API Documentation:** DRF can auto-generate API documentation (e.g., using `drf-spectacular` or `drf-yasg`). Check for its setup and access endpoint (commonly `/api/schema/` or `/api/docs/`).

## Testing

- **Test Framework:** Use `pytest` (indicated by `.pytest_cache`, `tox.ini`) for writing and running tests.
- **Test Location:** Tests are typically co-located with Django apps (e.g., `packages/backend/accounts/tests/`) or in a dedicated `tests` directory per app.
- **Fixtures:** Use `pytest` fixtures (`conftest.py` or app-specific `fixtures/`) to manage test dependencies and setup.

## Related Guidelines

- For general Python guidelines, see [Python Guidelines](mdc:python-general.prompt.md)
