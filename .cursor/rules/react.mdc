---
description: React Specific Guidelines
globs: packages/webapp/*,**.tsx,**.css,**.ts
alwaysApply: false
---
# React Specific Guidelines

These guidelines apply specifically to the React frontend in `packages/webapp`.

## Build Tools & Environment

- **Create React App (CRA):** Uses `react-scripts` for development server, building, and other scripts.
- **Package Manager:** npm

## Component Structure

- **Use functional components** with hooks instead of class components.
- **Each component** should ideally be in its own directory with related files if complex, or in a relevant feature/module directory.
- **Use PascalCase** for component names (e.g., `PatientList`).
- **Define TypeScript interfaces** for all component props.
- **Always use React. for react functions** for example : do not use useEffect alone, just React.useEffect.

## Styling

- **MUI Joy UI:** Used as a primary UI component library (from `@mui/joy`).
- **Sass/CSS:** Uses Sass (`.scss`) and regular CSS (`.css`) files for styling.

## Data and State

- **API Interaction:** Use clients in `src/clients` directory (e.g., `ApiClient.tsx`).
- **Authentication:** Using `@axa-fr/react-oidc` library with Keycloak.
- **State Management:** Primarily React Context (see `src/contexts/`).

## Routing

- **React Router:** Used for navigation (see `src/router.tsx`).

## Testing

- **Cypress:** For end-to-end tests (see `packages/webapp/cypress/` and `cypress.config.ts`).

## Related Guidelines

- For general TypeScript guidelines, see [TypeScript Guidelines](mdc:typescript-general.prompt.md)
