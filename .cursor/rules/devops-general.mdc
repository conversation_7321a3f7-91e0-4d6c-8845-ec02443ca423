---
description: General DevOps guidelines
globs: deployment/*,packages/devtools/*
alwaysApply: false
---
# DevOps Guidelines

## General Principles

- **Automation:** Strive to automate build, test, and deployment processes.
- **Containerization:** The project heavily utilizes Docker and Docker Compose for deployment.
- **CI/CD:** GitHub Actions are used for Continuous Integration and Continuous Deployment (see `.github/workflows/`).
- **Secrets Management:** Sensitive information should be managed securely (e.g., GitHub secrets for CI/CD, environment variables for runtime, cloud provider's secret manager if applicable).
- **Environments:** Define clear distinctions and configurations for different environments (e.g., dev, staging, prod).

## Docker & Docker Compose

- **`Dockerfile`:** Defines the application image for the backend.
- **`compose.yml` (and similar like `postgres-compose.yml`, `sso-compose.yml`):** Used to define and run multi-container Docker applications, especially for local development.
- **Workflow:** Use Docker Compose commands (often wrapped in the `cli` script) to build images, start/stop services, run migrations, etc.

## CLI (`./cli` script)

- **Usage:** The `cli` script provides a convenient interface for common development and operational tasks.
- **Environments:** The `cli` supports environment-specific commands or configurations (e.g., via flags like `--env`).

## GitHub Actions (`.github/workflows/`)

- Workflows define automated CI/CD pipelines.
- Common jobs include linting, testing, building Docker images, and deploying to various environments.

## Related Guidelines

- For Python guidelines, see [Python Guidelines](mdc:python-general.prompt.md)
- For Project Overview, see [Project Overview](mdc:project-specific.prompt.md)
