---
description: General TypeScript guidelines
globs: packages/webapp/*,**.tsx,**.css,**.ts
alwaysApply: false
---
# General TypeScript Guidelines

These guidelines apply to all TypeScript code, primarily within the `packages/webapp` directory.

## Style and Formatting

- **Code Style:** Follow the Airbnb JavaScript Style Guide (or project-defined ESLint config).
- **Formatting:** Use Prettier for automatic code formatting (see `.prettierrc`, `package.json` scripts).
- **File Extensions:** Use `.tsx` for files with JSX, and `.ts` for files without JSX.
- **Types:** Prefer interfaces over types for object shapes where appropriate (e.g., for public APIs of components/modules). Use PascalCase for type names.
- **Imports:** Use absolute imports with the `@/` alias if configured (check `tsconfig.json`). Otherwise, use relative imports consistently.
- **Avoid `any`:** Strive for strong typing. Use `unknown` when the type is truly unknown, then narrow it down.

## Project Structure (`packages/webapp/src/`)

- **`components/`:** Reusable UI components.
- **`pages/`:** Top-level route components.
- **`models/`:** TypeScript interfaces and types (e.g., `custom.ts`, `types.ts`, auto-generated types).
- **`clients/`:** API interaction modules (e.g., `ApiClient.tsx`).
- **`hooks/`:** Custom React hooks.
- **`contexts/`:** React Context for shared state management.
- **`utils/`:** Helper functions and utilities (e.g., `utils.ts`).
- **`assets/`:** Static assets like images, icons.
- **`layouts/`:** Layout components for different page structures.

## React Specifics (covered more in react.mdc)

- **Use functional components** with hooks.
- **Define interfaces** for component props using TypeScript.

## Antipatterns & Best Practices

- Avoid class components in new code; prefer functional components.
- Be mindful of prop drilling; use React Context or other state management solutions for deeply shared state.
- Prefer specific types over `any`.
- Ensure `.js` or `.jsx` extensions are not used for TypeScript files.
- Maintain consistency in import paths (absolute vs. relative based on project setup).
