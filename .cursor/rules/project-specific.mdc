---
description:
globs:
alwaysApply: true
---
# Project Overview

This project is a web application with a Python/Django backend and a React frontend.
It utilizes Docker for containerization and development consistency.

## Project Structure

The project follows a monorepo structure with the following main components:

- **`packages/backend`**: Django backend application (provides APIs).
- **`packages/webapp`**: React frontend application (consumes APIs, user interface).
- **`packages/devtools`**: Development tools and utilities (if present).
- **`.github/workflows/`**: CI/CD pipelines using GitHub Actions.
- **Root Directory Files**:
  - `cli`: Main command-line interface for project tasks.
  - `Dockerfile`: For building the backend Docker image.
  - `compose.yml`, `postgres-compose.yml`, `sso-compose.yml`: Docker Compose files for local development and service orchestration.
  - `pyproject.toml`: Python backend dependencies (Poetry).
  - `package.json`: Frontend dependencies (npm/Yarn).

## Key Technologies & Practices

- **Backend**: Python with Django and Django REST Framework (DRF).
- **Frontend**: TypeScript with <PERSON>act, Create React App (react-scripts), and MUI Joy UI / Chakra UI.
- **Database**: PostgreSQL.
- **API Style**: RESTful APIs.
- **Containerization**: Docker and Docker Compose.
- **CI/CD**: GitHub Actions.
- **DevOps CLI**: `./cli` script for managing common tasks.
- **Authentication**: Keycloak for SSO, integrated with Django backend and React frontend.
- **Dependency Management**:
  - Backend: Poetry (`pyproject.toml`, `poetry.lock`).
  - Frontend: npm
