---
description: General Python guidelines
globs: *.py,packages/backend/**
alwaysApply: false
---
# General Python Guidelines

These guidelines apply to all Python code within the repository, primarily in `packages/backend/`.

## Style and Formatting

- **Code Style:** Follow PEP 8.
- **Formatting:** Use `ruff` for automatic formatting (configure in `pyproject.toml`).
- **Type Hinting:** Use type hints for all function arguments and return values.
- **Docstrings:** Write clear docstrings using a consistent format (e.g., Google, reStructuredText).
- **Error Handling:** Use specific exceptions. For API errors within DRF, use DRF's `APIException` or custom subclasses.
- **Logging:** Use the standard `logging` module. Avoid `print()` for server-side logging.
- **Asynchronous:** Use `async` and `await` for asynchronous operations if applicable (Django supports async views and ORM operations).

## Project Structure (Django Focused - `packages/backend/`)

- **Django Apps:** Backend is structured into Django apps (e.g., `accounts`, `patient`, `consultation`).
  - **`models.py`**: Defines Django models (database schema).
  - **`views.py` / `viewsets.py`**: Contains Django views or DRF viewsets for handling requests.
  - **`serializers.py`**: DRF serializers for request/response data handling.
  - **`urls.py`**: URL routing for the app.
  - **`admin.py`**: Django admin configurations.
  - **`forms.py`**: Django forms (if not solely relying on DRF serializers for input).
  - **`tests/` or `tests.py`**: Unit and integration tests for the app.
  - **`migrations/`**: Database migration files.
- **`impl/`**: May contain implementations of specific business logic or repository patterns.
  - **`impl/services/`**: For service layer/business logic if used.
  - **`impl/django/repositories/`**: For repository pattern implementations if used.
- **`babylone/settings/`**: Django project settings.
- **`api/`**: Often a top-level Django app or a collection of apps that expose API endpoints.

## Dependency Management

- **Poetry:** Used for dependency management. Dependencies are defined in `pyproject.toml` and locked in `poetry.lock`.
- Use `poetry add <package>` to add dependencies, `poetry install` to install.

## Antipatterns & Best Practices

- Avoid `print()` for debugging in committed code; use logging or a debugger.
- Avoid bare `except:` clauses; catch specific exceptions.
- Follow Django's best practices for queries (e.g., use `select_related`, `prefetch_related` to optimize DB access).
- Keep views/viewsets lean; delegate business logic to services or model methods.
- Write comprehensive tests for models, views, and services.
