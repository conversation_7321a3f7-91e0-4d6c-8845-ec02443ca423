//

function load_saved_praticiens(csrf, user) {
    url = "/accounts/load_praticiens_lies/"
    var result = "";
    $.ajax({
            type: "POST",
            url: url,
            async: false,
            /* Necessary to return result at the end of the function  https://stackoverflow.com/questions/5316697/jquery-return-data-after-ajax-call-success */
            data: {
                'zkf_user': user,
                //send csrf token
                'csrfmiddlewaretoken': csrf,
            },

            success: function(data) {
                result = data
            },
            error: function(resultat, statut, erreur) {
                console.log(resultat)
                console.log(statut)
                console.log(erreur)
            },
        })
        // console.log(result)
    return result


}

function save_preference_praticiens(csrf, user, praticiens) {
    url = "/accounts/save_praticiens_lies/"
    var result = "";
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        /* Necessary to return result at the end of the function  https://stackoverflow.com/questions/5316697/jquery-return-data-after-ajax-call-success */
        data: {
            'zkf_user': user,
            'praticiens': praticiens,
            //send csrf token
            'csrfmiddlewaretoken': csrf,
        },

        success: function(data) {
            result = data
                // console.log('praticiens saved')
        },
        error: function(resultat, statut, erreur) {
            console.log(resultat)
            console.log(statut)
            console.log(erreur)
        },
    })

    return result
}


function filter_praticiens() {
    var praticiens_list = [];

    $('div#praticiens input[type=checkbox]').each(function() {
        if ($(this).is(":checked")) {
            praticiens_list.push($(this).attr('value'));
        }
    });
    praticiens_list = JSON.stringify(praticiens_list)
        // console.log(praticiens_list)
        // console.log('one more')
    return praticiens_list
}