{% extends "base_generic.html" %}
{% load static %}
{% block style %}

{% load widget_tweaks %}

<style type="text/css">
body {
	background: #eee !important;
}
.wrapper {
	margin-top: 80px;
  margin-bottom: 80px;
}
.form-signin {
  padding: 15px 35px 45px;
  margin: 0 auto;
  background-color: #fff;
  border: 1px solid rgba(0,0,0,0.1);

  .form-signin-heading,
	.checkbox {
	  margin-bottom: 30px;
	}
	.checkbox {
	  font-weight: normal;
	}
	.form-control {
	  position: relative;
	  font-size: 16px;
	  height: auto;
	  padding: 10px;
		@include box-sizing(border-box);
		&:focus {
		  z-index: 2;
		}
	}
	input[type="text"] {
	  margin-bottom: -1px;
	  border-bottom-left-radius: 0;
	  border-bottom-right-radius: 0;
	}
	input[type="password"] {
	  margin-bottom: 20px;
	  border-top-left-radius: 0;
	  border-top-right-radius: 0;
	}
}
@media(max-width: 500px){
	footer{
		height: 130px !important;
		padding-bottom: 10px;
		padding-top: 10px;
	}
}
</style>
{% endblock %}

{% block content %}

<script>
	//https://www.sitepoint.com/get-url-parameters-with-javascript/
	$(document).ready(function(){
		const queryString = window.location.search;
		const urlParams = new URLSearchParams(queryString);

		if (urlParams.has('username')){
			const username = urlParams.get('username')
			document.getElementById("id_username").value = username
			document.getElementById("id_password").focus();
		}
	});
	
</script>
<div class = "container" align="center">
	<div class = "col-md-6 col-md-offset-3">
		<div class="wrapper">
			<form class="form-signin" method = 'POST' action= '' enctype = "multipart/form-data" style="margin-top: -30px; margin-bottom: -18px;">{% csrf_token %}
				<h2 class="form-signin-heading">{{title}}</h2>
				
				<!-- username -->
				{{ form.username.label_tag }}
				{% render_field form.username type="text" class="form-control" placeholder="" autocomplete="username"%}
				{% if form.username.help_text %}
					<small class="form-text text-muted">{{ form.username.help_text }}</small>
				{% endif %}
				
				<!-- login -->
				{{ form.password.label_tag }}
				{% render_field form.password type="password" class="form-control" placeholder="" autocomplete="new-password" %}
				{% if form.password.help_text %}
					<small class="form-text text-muted">{{ form.password.help_text }}</small>
				{% endif %}

				{% comment %} {{form.as_p }}	 {% endcomment %}
				</label><br>
				<button class="btn btn-lg btn-primary btn-block" type="submit">{{title}}</button>
				<br>
				{% comment %} {% if title == "Login" %}<h5>Not Registered? <a href = "/accounts/register">Register Here</a></h5>{% endif %} {% endcomment %}
				
				{% if title == "Register" %}<h5>Already Registered? <a href = "/accounts/login">Login Here</a></h5>{% endif %}

			</form>

		</div>
	</div>
</div>

<script>
	$(document).ready(function(){
		$("#id_username").focus();
	});
</script>

{% endblock content %}
 

 autocomplete=new-password