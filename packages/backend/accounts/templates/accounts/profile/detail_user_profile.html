{% extends "base_generic.html" %}
{% load static %}
{% block style %}

{% load widget_tweaks %}

<style type="text/css">
body {
	background: #eee !important;
}
.wrapper {
	margin-top: 80px;
  margin-bottom: 80px;
}
.form-signin {
  padding: 15px 35px 45px;
  margin: 0 auto;
  background-color: #fff;
  border: 1px solid rgba(0,0,0,0.1);

  .form-signin-heading,
	.checkbox {
	  margin-bottom: 30px;
	}
	.checkbox {
	  font-weight: normal;
	}
	.form-control {
	  position: relative;
	  font-size: 16px;
	  height: auto;
	  padding: 10px;
		@include box-sizing(border-box);
		&:focus {
		  z-index: 2;
		}
	}
	input[type="text"] {
	  margin-bottom: -1px;
	  border-bottom-left-radius: 0;
	  border-bottom-right-radius: 0;
	}
	input[type="password"] {
	  margin-bottom: 20px;
	  border-top-left-radius: 0;
	  border-top-right-radius: 0;
	}
}
@media(max-width: 500px){
	footer{
		height: 130px !important;
		padding-bottom: 10px;
		padding-top: 10px;
	}
}
</style>
{% endblock %}

{% block content %}

<script>
	//https://www.sitepoint.com/get-url-parameters-with-javascript/
	$(document).ready(function(){
		const queryString = window.location.search;
		const urlParams = new URLSearchParams(queryString);

		if (urlParams.has('username')){
			const username = urlParams.get('username')
			document.getElementById("id_username").value = username
			document.getElementById("id_password").focus();
		}
	});
	
</script>

<a href = "{% url 'accounts:edit-profile' user %}" class = "btn btn-primary">Edit</a>
<div class = "container" align="center">
	<div class = "col-md-6 col-md-offset-3">
		<div class="wrapper">
			<form class="form-signin" method = 'POST' action= '' enctype = "multipart/form-data" style="margin-top: -30px; margin-bottom: -18px;">{% csrf_token %}
				<h2 class="form-signin-heading">{{title}}</h2>
				
				{{user.first_name}}<br>
				{{user.last_name}}<br>
				email
				{{user.email}}<br>

				praticiens lies
				{{user.profile.praticiens_lies.all}}<br>
				
				environnement user
				{{user.profile.zkf_environnement_user}}<br>
				environnements lies:
				{{user.profile.zkf_environnement_lie.all}}<br>

				<b>users ayant donné authorisation !</b>:
				{{users_environnement_lies}}<br>

				secretaire
				{{user.profile.secretaire}}<br>
				secretariat
				{{user.profile.secretariat}}<br>
				#which spe the user has access to ? 
				{{user.profile.specialite_profile.all}}<br>
				#specialite which is mainly used by the user !
				{{user.profile.specialite_active}}<br>
				Service
				{{user.profile.service}}<br>
				Sites
				{{user.profile.site}}<br>
				Groupe de facturation
				{{user.profile.groupe_facturation}}<br>
				entete
				{{user.profile.entete}}<br>
				role
				{{user.profile.role}}<br>
				titre
				{{user.profile.titre}}<br>

				conventionnement
				{{user.profile.conventionnement}}<br>
				activite_ccam
				{{user.profile.activite_ccam}}<br>
				
				rpps:
				{{user.profile.rpps}}<br>

				entete_ordonnance
				{{user.profile.entete_ordonnance}}<br>

				derniere connexion
				{{user.profile.last_seen}}<br>


				Preferences:<br>
				Affichage service
				{{user.preferences_user.affichage_service}}<br>
				affichage service statut
				{{user.preferences_user.affichage_service_status}}<br>

				report hebdomadaire
				{{user.preferences_user.report_hebdo}}<br>

				Ordonnance par defautlt (doit être une ordonnance vierge...)
				{{user.preferences_user.ordonnance_default}}<br>

				Impression recto/verso
				{{user.preferences_user.impression_recto_verso}}<br>

			</form>

		</div>
	</div>
</div>

<script>
	$(document).ready(function(){
		$("#id_username").focus();
	});
</script>

{% endblock content %}
 
