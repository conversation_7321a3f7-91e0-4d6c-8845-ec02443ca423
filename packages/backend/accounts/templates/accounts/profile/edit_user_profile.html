{% extends "base_generic.html" %}
{% load static %}
{% block style %}

{% load widget_tweaks %}

<style type="text/css">
body {
	background: #eee !important;
}
.wrapper {
	margin-top: 80px;
  margin-bottom: 80px;
}
.form-signin {
  padding: 15px 35px 45px;
  margin: 0 auto;
  background-color: #fff;
  border: 1px solid rgba(0,0,0,0.1);

  .form-signin-heading,
	.checkbox {
	  margin-bottom: 30px;
	}
	.checkbox {
	  font-weight: normal;
	}
	.form-control {
	  position: relative;
	  font-size: 16px;
	  height: auto;
	  padding: 10px;
		@include box-sizing(border-box);
		&:focus {
		  z-index: 2;
		}
	}
	input[type="text"] {
	  margin-bottom: -1px;
	  border-bottom-left-radius: 0;
	  border-bottom-right-radius: 0;
	}
	input[type="password"] {
	  margin-bottom: 20px;
	  border-top-left-radius: 0;
	  border-top-right-radius: 0;
	}
}
@media(max-width: 500px){
	footer{
		height: 130px !important;
		padding-bottom: 10px;
		padding-top: 10px;
	}
}
</style>
{% endblock %}

{% block content %}

<script>
	//https://www.sitepoint.com/get-url-parameters-with-javascript/
	$(document).ready(function(){
		const queryString = window.location.search;
		const urlParams = new URLSearchParams(queryString);

		if (urlParams.has('username')){
			const username = urlParams.get('username')
			document.getElementById("id_username").value = username
			document.getElementById("id_password").focus();
		}
	});
	
</script>
<div class = "container" align="center">
	<div class = "col-md-6 col-md-offset-3">
		<div class="wrapper">
			<form class="form-signin" method = 'POST' action= '' enctype = "multipart/form-data" style="margin-top: -30px; margin-bottom: -18px;">

				{% csrf_token %}
				{% for hidden_field in form.hidden_fields %}
						{{ hidden_field }}
					{% endfor %}
				<br>
				{{form.errors}}
				
				<h2 class="form-signin-heading">{{title}}</h2>
								
				
				{{form.as_p}}
				
				<h6>Profile</h6>
				{{ profileformset.management_form }}
						{% for form in profileformset %}
							{{form.errors}}
							{{ form.id }}<!-- has to be inserted https://docs.djangoproject.com/en/2.1/topics/forms/modelforms/#using-the-formset-in-the-template-->
							{{form.as_p}}
						{% endfor %}
				
				<h6>Preferences</h6>
				{{ preferencesformset.management_form }}
						{% for form in preferencesformset %}
							{{form.errors}}
							{{ form.id }}<!-- has to be inserted https://docs.djangoproject.com/en/2.1/topics/forms/modelforms/#using-the-formset-in-the-template-->
							{{form.as_p}}
						{% endfor %}

				
		<button type="submit" class="btn btn-success">Valider</button>
		
		<a href="{% url "accounts:detail-profile" user %}">Cancel</a>

			</form>

		</div>
	</div>
</div>

<script>
	$(document).ready(function(){
		$("#id_username").focus();
	});
</script>

{% endblock content %}
 
