from datetime import timedelta
from rest_framework import serializers
from django.utils import timezone
from annuaire_sante.services import AnnuaireSanteClient


class AnnuaireSanteVerificationField(serializers.DateTimeField):
    """
    A custom field that handles RPPS verification with Annuaire Santé.
    It will verify the RPPS only when the last attempt was more than 24 hours ago.
    """

    def __init__(self, **kwargs):
        super().__init__(read_only=True, **kwargs)

    def get_attribute(self, instance):
        profile = getattr(instance, "profile", None)
        if not profile or not profile.rpps:
            return None

        last_verification = profile.annuaire_sante_verification
        last_attempt = profile.annuaire_sante_last_attempt
        now = timezone.now()

        # If we have a recent attempt (success or failure), return current verification status
        if last_attempt and (now - last_attempt) <= timedelta(days=1):
            return last_verification

        # Only verify if enough time has passed since last attempt
        if not last_attempt or (now - last_attempt) > timedelta(days=1):
            client = AnnuaireSanteClient()
            doctor = client.get_doctor_info(str(profile.rpps))

            # Update verification time
            profile.annuaire_sante_verification = now if doctor is not None else None
            profile.annuaire_sante_last_attempt = now
            profile.save(
                update_fields=[
                    "annuaire_sante_verification",
                    "annuaire_sante_last_attempt",
                ]
            )
            return profile.annuaire_sante_verification

        return last_verification
