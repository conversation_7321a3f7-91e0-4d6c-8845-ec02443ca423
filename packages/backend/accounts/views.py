from django.shortcuts import render, redirect
from django.http import HttpResponse
from django.contrib.auth.models import User

from django.contrib.auth import authenticate, login, logout
from .forms import (
    UsersLoginForm,
    UsersRegisterForm,
    UseraccountForm,
    PreferencesFormset,
    ProfileFormset,
)

# from django.contrib.auth.models import User

import json

# from django.utils import simplejson as json

from django.views.generic import (
    CreateView,
    DetailView,
    UpdateView,
    View,
)
from django.urls import reverse
from django.contrib.auth.mixins import LoginRequiredMixin

from audit.views import AuditableMixin
from accounts.models import Site, Lieu
from schedule.models import Vacation


from django.core.serializers.json import DjangoJSONEncoder


# print manager
from impression.views import print_manager_list


from accounts.forms import CreateVacationForm, CreateAbsenceForm


def login_view(request):
    form = UsersLoginForm(request.POST or None)
    if form.is_valid():
        username = form.cleaned_data.get("username")
        password = form.cleaned_data.get("password")
        user = authenticate(username=username, password=password)
        login(request, user)

        # update print manager: generate a list of all docs waiting to be printed, and save it in cache !
        print_manager_list(request)

        return redirect("/patient")
    return render(
        request,
        "accounts/form.html",
        {
            "form": form,
            "title": "Login",
        },
    )


def register_view(request):
    form = UsersRegisterForm(request.POST or None)
    if form.is_valid():
        user = form.save()
        password = form.cleaned_data.get("password")
        user.set_password(password)
        user.save()
        new_user = authenticate(username=user.username, password=password)
        login(request, new_user)
        return redirect("/accounts/login")
    return render(
        request,
        "accounts/form.html",
        {
            "title": "Register",
            "form": form,
        },
    )


def logout_view(request):
    logout(request)
    if request.method == "GET" and "username" in request.GET:
        username = request.GET["username"]
        url = "/accounts/login/?username=" + username
    else:
        url = "/accounts/login/"

    return redirect(url)


class LoadPraticiensLies(LoginRequiredMixin, AuditableMixin, View):
    def post(self, request, *args, **kwargs):
        user = request.POST["zkf_user"]
        user = User.objects.get(id=user)
        praticiens = user.preferences_user.current_praticiens_lies_selectionnes.all()
        praticiens = list(praticiens.values("last_name"))
        praticiens = [x["last_name"] for x in praticiens]
        praticiens = json.dumps(praticiens)

        return HttpResponse(praticiens)


class SavePraticiensLies(LoginRequiredMixin, AuditableMixin, View):
    def post(self, request, *args, **kwargs):
        user = request.POST["zkf_user"]
        user = User.objects.get(id=user)
        praticiens = json.loads(request.POST["praticiens"])

        # clear relationship
        user.preferences_user.current_praticiens_lies_selectionnes.clear()

        # update praticiens lies
        for p in praticiens:
            praticien_lie = User.objects.get(id=p)
            user.preferences_user.current_praticiens_lies_selectionnes.add(
                praticien_lie
            )

        return HttpResponse("updated")


class DetailUserProfile(LoginRequiredMixin, DetailView):
    model = User
    context_object_name = "user"
    template_name = "accounts/profile/detail_user_profile.html"
    slug_url_kwarg = "username"
    slug_field = "username"

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(**kwargs)
        # get users who authorized the sharing of their environnement !
        context["users_environnement_lies"] = User.objects.filter(
            profile__zkf_environnement_lie__in=[
                self.request.user.profile.zkf_environnement_user
            ]
        )
        return context


class EditUserProfile(LoginRequiredMixin, UpdateView):
    model = User
    template_name = "accounts/profile/edit_user_profile.html"
    slug_url_kwarg = "username"
    slug_field = "username"
    form_class = UseraccountForm

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({"user": self.request.user})
        return kwargs

    def get_success_url(self):
        return reverse("accounts:detail-profile", args=[self.get_object()])

    def get_context_data(self, *args, **kwargs):
        data = super().get_context_data(**kwargs)
        if self.request.POST:
            data["profileformset"] = ProfileFormset(
                self.request.POST, instance=self.object
            )
            data["preferencesformset"] = PreferencesFormset(
                self.request.POST, instance=self.object
            )
        else:
            data["profileformset"] = ProfileFormset(instance=self.object)
            data["preferencesformset"] = PreferencesFormset(instance=self.object)
        return data

    def form_valid(self, form):
        context = self.get_context_data()
        profile = context["profileformset"]
        preferences = context["preferencesformset"]

        self.object = form.save()

        if profile.is_valid():
            profile.instance = self.object
            profile.save()

        if preferences.is_valid():
            preferences.instance = self.object
            preferences.save()

        return super(EditUserProfile, self).form_valid(form)


# get lieux of a Site !
class LoadLieu(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        site = Site.objects.get(id=request.POST["site_id"])

        lieux = Lieu.objects.filter(zkf_site=site).values("id", "nom_lieu")
        # https://stackoverflow.com/questions/36588126/uuid-is-not-json-serializable
        # print (json.dumps( list(lieux), cls=DjangoJSONEncoder))
        return HttpResponse(json.dumps(list(lieux), cls=DjangoJSONEncoder))

        # return HttpResponse(lieux)


class DetailVacation(LoginRequiredMixin, DetailView):
    template_name = "dashboard/vacations/detail_vacation.html"
    model = Vacation
    context_object_name = "vacation"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        return context


class CreateVacation(LoginRequiredMixin, CreateView):
    template_name = "dashboard/vacations/create_vacation.html"
    form_class = CreateVacationForm

    def get_success_url(self):
        return reverse("dashboard:vacations")


class EditVacation(LoginRequiredMixin, UpdateView):
    model = Vacation
    template_name = "dashboard/vacations/create_vacation.html"
    form_class = CreateVacationForm
    context_object_name = "vacation"

    def get_success_url(self):
        return reverse("dashboard:vacations")


class CreateAbsence(LoginRequiredMixin, CreateView):
    template_name = "dashboard/vacations/edit_absence.html"
    form_class = CreateAbsenceForm

    def get_success_url(self):
        return reverse("dashboard:absence")


class EditAbsence(LoginRequiredMixin, UpdateView):
    model = Vacation
    template_name = "dashboard/vacations/edit_absence.html"
    form_class = CreateAbsenceForm
    context_object_name = "absence"

    def get_success_url(self):
        return reverse("dashboard:absence")
