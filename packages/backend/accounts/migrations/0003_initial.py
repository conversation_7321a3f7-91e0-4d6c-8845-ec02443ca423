# Generated by Django 5.1.4 on 2025-01-27 15:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0002_initial"),
        ("ccam", "0001_initial"),
        ("comptabilite", "0001_initial"),
        ("document", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="preferences_user",
            name="ordonnance_default",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="document.ordonnancetemplate",
            ),
        ),
        migrations.AddField(
            model_name="preferences_user",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="activite_ccam",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="ccam.r_activite",
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="conventionnement",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="ccam.r_tb_23",
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="entete",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="accounts.entetedocument",
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="groupe_facturation",
            field=models.ManyToManyField(
                blank=True, to="comptabilite.groupefacturation"
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="praticiens_lies",
            field=models.ManyToManyField(
                blank=True,
                related_name="%(app_label)s_%(class)s_praticiens_lies",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="zkf_environnement_lie",
            field=models.ManyToManyField(
                blank=True,
                related_name="%(app_label)s_%(class)s_environnement_lie",
                to="accounts.environnement",
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="zkf_environnement_user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="accounts.environnement",
            ),
        ),
        migrations.AddField(
            model_name="profileadministratif",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="profileadministratif",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="profileadministratif",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="profileadministratif",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="rendezvous",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="rendezvous",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="rendezvous",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="rendezvous",
            name="zkf_user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="secretariat",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="secretariat",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="secretariat",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="secretariat",
            name="zkf_environnement",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="accounts.environnement"
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="secretariat",
            field=models.ManyToManyField(blank=True, to="accounts.secretariat"),
        ),
        migrations.AddField(
            model_name="service",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="service",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="service",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="service",
            name="zkf_environnement",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="accounts.environnement"
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="service",
            field=models.ManyToManyField(to="accounts.service"),
        ),
        migrations.AddField(
            model_name="intervenant",
            name="service",
            field=models.ManyToManyField(blank=True, to="accounts.service"),
        ),
        migrations.AddField(
            model_name="site",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="site",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="site",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="site",
            name="zkf_environnement_site",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="accounts.environnement"
            ),
        ),
        migrations.AddField(
            model_name="service",
            name="site_service",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="accounts.site"
            ),
        ),
        migrations.AddField(
            model_name="secretariat",
            name="zkf_site",
            field=models.ManyToManyField(to="accounts.site"),
        ),
        migrations.AddField(
            model_name="profile",
            name="site",
            field=models.ManyToManyField(to="accounts.site"),
        ),
        migrations.AddField(
            model_name="lieu",
            name="zkf_site",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="accounts.site"
            ),
        ),
        migrations.AddField(
            model_name="intervenant",
            name="site",
            field=models.ManyToManyField(blank=True, to="accounts.site"),
        ),
        migrations.AddField(
            model_name="specialite",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="specialite",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="specialite",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="service",
            name="specialite_service",
            field=models.ManyToManyField(to="accounts.specialite"),
        ),
        migrations.AddField(
            model_name="secretariat",
            name="specialite_secretariat",
            field=models.ManyToManyField(to="accounts.specialite"),
        ),
        migrations.AddField(
            model_name="profile",
            name="specialite_active",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_specialite_active",
                to="accounts.specialite",
            ),
        ),
        migrations.AddField(
            model_name="profile",
            name="specialite_profile",
            field=models.ManyToManyField(to="accounts.specialite"),
        ),
        migrations.AddField(
            model_name="intervenant",
            name="specialite_principale",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="specialite_principale",
                to="accounts.specialite",
            ),
        ),
        migrations.AddField(
            model_name="intervenant",
            name="specialite_secondaire",
            field=models.ManyToManyField(
                blank=True,
                related_name="specialite_secondaire",
                to="accounts.specialite",
            ),
        ),
        migrations.AddField(
            model_name="stationnementpatient",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="stationnementpatient",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_deleted_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="stationnementpatient",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="%(app_label)s_%(class)s_modified_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="stationnementpatient",
            name="zkf_service",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="accounts.service"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="intervenant",
            unique_together={("zkf_environnement", "nom", "prenom")},
        ),
    ]
