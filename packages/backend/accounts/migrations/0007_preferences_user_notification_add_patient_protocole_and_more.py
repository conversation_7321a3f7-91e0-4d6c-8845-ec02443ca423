# Generated by Django 5.1.7 on 2025-04-10 07:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0006_intervenant_date_debut_intervenant_date_fin_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="preferences_user",
            name="notification_add_patient_protocole",
            field=models.BooleanField(
                default=True,
                help_text="Notification lors de l'ajout d'un patient à un protocole",
            ),
        ),
        migrations.AddField(
            model_name="preferences_user",
            name="notification_add_report",
            field=models.BooleanField(
                default=True, help_text="Notification lors de l'ajout d'un compte rendu"
            ),
        ),
        migrations.AddField(
            model_name="preferences_user",
            name="notification_creation_operation",
            field=models.BooleanField(
                default=True,
                help_text="Notification lors de la création d'une opération",
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="preferences_user",
            name="notification_rappel",
            field=models.<PERSON><PERSON>an<PERSON>ield(
                default=True, help_text="Notification lors de l'ajout d'un rappel"
            ),
        ),
        migrations.AddField(
            model_name="preferences_user",
            name="notification_report_attente_validation",
            field=models.BooleanField(
                default=True,
                help_text="Notification lorsqu'un compte rendu est en attente de validation",
            ),
        ),
        migrations.AddField(
            model_name="preferences_user",
            name="notification_report_validation",
            field=models.BooleanField(
                default=True, help_text="Notification lorsqu'un compte rendu est validé"
            ),
        ),
    ]
