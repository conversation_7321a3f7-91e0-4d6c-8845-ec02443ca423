# Generated by Django 5.1.7 on 2025-05-12 09:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "accounts",
            "0007_preferences_user_notification_add_patient_protocole_and_more",
        ),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="preferences_user",
            options={
                "verbose_name": "Préférences utilisateur",
                "verbose_name_plural": "Préférences utilisateurs",
            },
        ),
        migrations.RemoveField(
            model_name="preferences_user",
            name="notification_add_patient_protocole",
        ),
        migrations.RemoveField(
            model_name="preferences_user",
            name="notification_add_report",
        ),
        migrations.RemoveField(
            model_name="preferences_user",
            name="notification_creation_operation",
        ),
        migrations.RemoveField(
            model_name="preferences_user",
            name="notification_rappel",
        ),
        migrations.RemoveField(
            model_name="preferences_user",
            name="notification_report_attente_validation",
        ),
        migrations.RemoveField(
            model_name="preferences_user",
            name="notification_report_validation",
        ),
        migrations.AddField(
            model_name="preferences_user",
            name="preference_notification",
            field=models.JSONField(default=dict),
        ),
    ]
