# Generated by Django 5.1.4 on 2025-01-27 15:43

import django.contrib.auth.models
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Lieu",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("nom_lieu", models.CharField(max_length=200)),
                ("acces_lieu", models.TextField(blank=True)),
                ("description_lieu", models.TextField(blank=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PerformanceMachineLearning",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "nom_classifier",
                    models.CharField(blank=True, max_length=256, null=True),
                ),
                ("score_classifier", models.TextField(blank=True, null=True)),
                ("date_analyse", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Preferences_user",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "affichage_service",
                    models.CharField(
                        choices=[("SPE", "Specialite"), ("USE", "User")],
                        default="SPE",
                        max_length=5,
                    ),
                ),
                (
                    "affichage_service_status",
                    models.CharField(
                        choices=[("STA", "Static"), ("DYN", "Dynamique")],
                        default="DYN",
                        max_length=5,
                    ),
                ),
                ("report_hebdo", models.BooleanField(default=False)),
                ("impression_recto_verso", models.BooleanField(default=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Profile",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                ("secretaire", models.CharField(blank=True, max_length=200)),
                (
                    "role",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("SEC", "Secretaire"),
                            ("MED", "Medecin"),
                            ("INF", "Infirmier"),
                        ],
                        default="MED",
                        max_length=3,
                    ),
                ),
                (
                    "titre",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Mr", "Monsieur"),
                            ("Dr", "Docteur"),
                            ("Mme", "Madame"),
                        ],
                        default="Dr",
                        max_length=90,
                    ),
                ),
                (
                    "modificateurs_default",
                    models.CharField(
                        blank=True,
                        help_text="Modificateurs par défaut pour les actes CCAM: lettres séparées par une virgule. Ex: J,K ",
                        max_length=200,
                        null=True,
                    ),
                ),
                ("rpps", models.BigIntegerField(blank=True, null=True)),
                (
                    "annuaire_sante_verification",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "annuaire_sante_last_attempt",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("entete_ordonnance", models.TextField(blank=True)),
                ("last_seen", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ProfileAdministratif",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="RendezVous",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date_heure_debut", models.DateTimeField()),
                ("date_heure_fin", models.DateTimeField()),
                ("motif", models.CharField(max_length=500)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Secretariat",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("nom_secretariat", models.CharField(max_length=200)),
                ("numero_secretariat", models.CharField(max_length=200)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Service",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("nom_service", models.CharField(max_length=200)),
                ("template_document", models.TextField(blank=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Site",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("nom_site", models.CharField(max_length=200)),
                ("adresse_site", models.CharField(max_length=256)),
                ("zip_code_site", models.IntegerField()),
                ("ville_site", models.CharField(max_length=200)),
                (
                    "telephone_site",
                    models.CharField(blank=True, max_length=30, null=True),
                ),
                ("adeli_site", models.CharField(blank=True, max_length=30, null=True)),
                (
                    "operation_schedule_color",
                    models.CharField(default="#B45F04", max_length=8),
                ),
                (
                    "consultation_schedule_color",
                    models.CharField(default="#669999", max_length=8),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Specialite",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("spe", models.CharField(max_length=200)),
                ("spe_medecin", models.CharField(max_length=200)),
                ("data_app", models.CharField(max_length=200)),
            ],
            options={
                "ordering": ["spe"],
            },
        ),
        migrations.CreateModel(
            name="StationnementPatient",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("nom_stationnement", models.CharField(max_length=200)),
                ("description_stationnement", models.TextField(blank=True, null=True)),
                ("multiple_patient", models.BooleanField(default=False)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="CustomUser",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("auth.user",),
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="Absence",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("date_heure_start", models.DateTimeField()),
                ("date_heure_end", models.DateTimeField()),
                ("motif", models.CharField(max_length=500)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_modified_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "zkf_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Environnement",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("nom_environnement", models.CharField(max_length=256)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_modified_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="EnteteDocument",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("titre", models.CharField(max_length=256)),
                ("texte", models.TextField()),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_modified_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "zkf_environnement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="accounts.environnement",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Intervenant",
            fields=[
                ("created_on", models.DateTimeField(auto_now_add=True)),
                ("modified_on", models.DateTimeField(auto_now=True, null=True)),
                ("deleted", models.BooleanField(default=False)),
                ("deleted_on", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("titre", models.CharField(blank=True, max_length=256, null=True)),
                ("nom", models.CharField(blank=True, max_length=256, null=True)),
                ("prenom", models.CharField(blank=True, max_length=256, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("groupe", models.ManyToManyField(blank=True, to="auth.group")),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="%(app_label)s_%(class)s_modified_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
