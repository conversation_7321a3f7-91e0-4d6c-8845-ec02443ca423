# Generated by Django 5.1.4 on 2025-03-04 20:38

from django.db import migrations, models
from accounts.models import Specialite
import pandas as pd
from django.conf import settings


def get_omop_spe(apps, schema_editor):
    specialites = Specialite.objects.all()
    csv_path = str(settings.BASE_DIR) + "/accounts/scripts/accounts_specialite.csv"
    print(f"Reading CSV from: {csv_path}")  # Add print for debugging path
    try:
        # convert csv to pandas, specifying the correct header row (index 2) and delimiter:
        df = pd.read_csv(csv_path, header=2, delimiter=",")
    except FileNotFoundError:
        print(f"Error: CSV file not found at {csv_path}")
        return  # Stop execution if file not found
    except Exception as e:
        print(f"Error reading CSV: {e}")
        return  # Stop execution on other read errors

    for spe in specialites:
        # spe.omop_code = df[spe.spe]['omop_code']
        # match the specialite with the omop code
        try:
            # Check if 'spe' column exists before accessing
            if "spe" not in df.columns:
                print("Error: 'spe' column not found in DataFrame.")
                break  # Stop processing if 'spe' column is missing

            matching_rows = df.loc[df["spe"] == spe.spe]

            if not matching_rows.empty:
                # Check if 'omop' column exists before accessing
                if "omop" not in df.columns:
                    print("Error: 'omop' column not found in DataFrame.")
                    break  # Stop processing if 'omop' column is missing
                spe.omop_code = matching_rows["omop"].values[0]
                spe.save()
            else:
                print(
                    f"Warning: No matching row found in CSV for specialite: {spe.spe}"
                )

        except IndexError:
            # This specific exception might be less likely now with the check for empty matching_rows
            # but keep it for safety. It could happen if ["omop"].values[0] fails for some reason.
            print(f"Warning: IndexError while processing specialite: {spe.spe}")
        except Exception as e:
            # Catch other potential errors during processing
            print(f"Error processing specialite {spe.spe}: {e}")


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0004_specialite_type_specialite"),
    ]

    operations = [
        migrations.AddField(
            model_name="specialite",
            name="omop_code",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.RunPython(get_omop_spe),
    ]
