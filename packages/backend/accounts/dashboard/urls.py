# from django.conf.urls import url, include
from django.urls import path
from .views import (
    AbsenceUser,
    AssuranceSanteList,
    CcamMenuStat,
    CcamMenuStatIndex,
    CcamSubMenuStat,
    ConsignesSurveillance,
    ConsultationTypeList,
    Correspondants,
    Cros,
    DmisList,
    DocumentsLibres,
    ExtractionFiltre,
    ExtractionResult,
    Geographie,
    HomeDashboard,
    ImportCros,
    Ordonnances,
    ProtocolesList,
    ScorePrediction,
    Secretariat,
    Statistiques,
    StatistiquesFiltre,
    UploadComptesRendus,
    VacationsService,
    VacationsServiceView,
    VacationsUser,
)
from contact.views.correspondant import SearchCorrespondantDashboard

app_name = "dashboard"  # define an app name for redirections in templates

urlpatterns = [
    # index
    path("", HomeDashboard.as_view(), name="home"),
    # Statistiques
    path(
        "statistiques-filtre", StatistiquesFiltre.as_view(), name="statistiques-filtre"
    ),
    path("statistiques", Statistiques.as_view(), name="statistiques"),
    # DMIS
    path(
        "dispositifs-medicaux-list",
        DmisList.as_view(),
        name="dispositifs-medicaux-list",
    ),
    # List correspondants
    path("correspondants", Correspondants.as_view(), name="correspondants"),
    # Search correspondant
    path(
        "query-correspondant",
        SearchCorrespondantDashboard.as_view(),
        name="query-correspondant",
    ),
    # CROS
    # List CROS
    path("cros", Cros.as_view(), name="cros"),
    # import cros type for each specialite
    path("import-cros-type", ImportCros.as_view(), name="import-cros-type"),
    # Ordonnances types
    # List Ordo
    path("ordonnances", Ordonnances.as_view(), name="ordonnances"),
    # Consignes post opératoires types
    path(
        "consignes-surveillance",
        ConsignesSurveillance.as_view(),
        name="consignes-surveillance",
    ),
    # Documents libres
    path("docs-libres", DocumentsLibres.as_view(), name="docs-libres"),
    # Mutuelles
    path(
        "assurance-sante-list/",
        AssuranceSanteList.as_view(),
        name="assurance-sante-list",
    ),
    # Protcoles
    path("protocoles-list/", ProtocolesList.as_view(), name="protocole-list"),
    # Consultations types
    path(
        "consultation-type/",
        ConsultationTypeList.as_view(),
        name="consultation-type-list",
    ),
    # Prediction scores
    path("prediction-score/", ScorePrediction.as_view(), name="prediction-score"),
    # Extraction de codes
    path("extraction", ExtractionResult.as_view(), name="extraction-result"),
    path("extraction-filtre", ExtractionFiltre.as_view(), name="extraction-filtre"),
    # Repartition geographique des patients
    path("geographie", Geographie.as_view(), name="geographie"),
    # Vacations
    path("vacations", VacationsUser.as_view(), name="vacations"),
    path("vacations-service", VacationsServiceView.as_view(), name="vacations-service"),
    path("affichage-vacations", VacationsService.as_view(), name="affichage-vacations"),
    path("absence", AbsenceUser.as_view(), name="absence"),
    # Secretariat
    path("secretariat", Secretariat.as_view(), name="secretariat"),
    path(
        "upload-comptes-rendus",
        UploadComptesRendus.as_view(),
        name="upload-comptes-rendus",
    ),
    # get stat by CCAM arborescence
    # search ccam through menu
    path(
        "ccam_search_menu_stat_index/",
        CcamMenuStatIndex.as_view(),
        name="menu-ccam-stat-index",
    ),
    path("ccam_search_menu_stat/", CcamMenuStat.as_view(), name="menu-ccam-stat"),
    # search through submenu
    path(
        "ccam_search_sub_menu_stat/",
        CcamSubMenuStat.as_view(),
        name="submenu-ccam-stat",
    ),
]
