from django.http import HttpResponse
from django.template.loader import render_to_string

from django.views.generic import (
    TemplateView,
    ListView,
    View,
)
from django.contrib.auth.mixins import LoginRequiredMixin

from django.db.models import Q  # making complex queries
from django.db.models import Count
from codage.ccam.models import R_menu

from django.apps import apps  # import model from string

import datetime
import json


from contact.models import Contact, Specialite
from operation.models import CroType, Protocole, Operation  # MyCustomTag
from document.models.ordonnance import OrdonnanceTemplate
from document.models.libre import DocumentLibreTemplate
from hospitalisation.models import ConsigneSortie
from assurance_sante.models import AssuranceSante
from consultation.models import ConsultationType
from contact.models import <PERSON>resse, Ville
from accounts.models import Site
from django.contrib.auth.models import User
from operation.models import OperationDevisCcam, R_acte
from materiel.models import MedicalDevice
from sort.tag.models import FullTag


from accounts.scripts.user_stats import (
    consultations_stat,
    operations_stat,
    operation_by_consultation_lieu,
)
from accounts.models import PerformanceMachineLearning, Absence

# filter users
from accounts.scripts.filter_user import (
    get_linked_users_by_role,
    get_linked_users_by_site,
)

# user stats
from accounts.scripts.user_report import report_consultations, report_operations
from accounts.dashboard.scripts.vacations import get_week
from schedule.models import Vacation

# utc datetime object


class HomeDashboard(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/index.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # get last week range dates
        # https://stackoverflow.com/questions/********/how-can-i-get-the-previous-week-in-python
        today = datetime.date.today()
        start = today - datetime.timedelta(days=today.weekday(), weeks=1)
        end = start + datetime.timedelta(days=6)
        report_consultation = report_consultations(
            start=start, end=end, user=self.request.user
        )
        # print (report_operations(start = start, end = end, user = self.request.user))
        try:
            context["consultations_stat"] = report_consultation.to_html(escape=False)
            context["operations_stat"] = report_operations(
                start=start, end=end, user=self.request.user
            ).to_html(escape=False)
        except Exception as ex:
            context["operations_stat"] = f"Pas de données: {str(ex)}"
            context["consultations_stat"] = f"Pas de données: {str(ex)}"
            pass

        return context


class Statistiques(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/statistiques/statistiques.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # start = date.today().replace(day=1) + relativedelta(months=-13)
        # end = date.today()
        start = self.request.GET["start"]
        end = self.request.GET["end"]
        cs_stat = consultations_stat(start, end, self.request.user)

        context["consultations_mois"] = json.dumps(cs_stat)
        context["cs_stat"] = {
            "mean": cs_stat["mean"],
            "median": cs_stat["median"],
            "min": cs_stat["min"],
            "max": cs_stat["max"],
        }
        context["operations_mois"] = json.dumps(
            operations_stat(start, end, self.request.user)
        )

        context["operation_by_lieu_consultation"] = json.dumps(
            operation_by_consultation_lieu(start, end, self.request.user)
        )
        context["lieu_consultation"] = json.dumps(
            consultations_stat(start, end, self.request.user)
        )

        return context


class StatistiquesFiltre(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/statistiques/statistiques_filtre.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class Correspondants(LoginRequiredMixin, ListView):
    template_name = "dashboard/correspondants.html"
    context_object_name = "correspondants"
    model = Contact
    queryset = Contact.objects.filter(
        zkf_contactcategorie__contact_categorie="Correspondant"
    )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # get all specialite
        specialite = Specialite.objects.all()
        context["specialite"] = specialite
        return context


class Cros(LoginRequiredMixin, ListView):
    template_name = "dashboard/cros.html"
    context_object_name = "cros"
    model = CroType

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = CroType.objects.filter(zkf_user=self.request.user, deleted=False)
        return queryset


class ImportCros(LoginRequiredMixin, ListView):
    template_name = "dashboard/cros.html"
    context_object_name = "cros"
    model = CroType

    def get_queryset(self):
        # insert logique somewhere....

        # 1/ get user specialite active
        user_spe_principale = self.request.user.profile.specialite_active.spe
        specialite = "_".join(user_spe_principale.split(" ")).lower()

        # 2/ get all cros type of the specialite
        model_cros = apps.get_model(specialite, "crotype")
        cros = model_cros.objects.all()

        # 3/ Copy the cros in the user cros type !
        for c in cros:
            o = CroType(
                zkf_user=self.request.user,
                cro_type_titre=c.titre,
                cro_type=c.corps,
                nom_modele_specialite=specialite,
            )
            o.save()

        queryset = super().get_queryset()
        queryset = CroType.objects.filter(zkf_user=self.request.user)
        return queryset


class Ordonnances(LoginRequiredMixin, ListView):
    template_name = "dashboard/ordonnances.html"
    context_object_name = "ordonnance"
    model = OrdonnanceTemplate

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = OrdonnanceTemplate.objects.filter(zkf_user=self.request.user)
        return queryset


class ConsignesSurveillance(LoginRequiredMixin, ListView):
    template_name = "dashboard/consignes_surveillance.html"
    context_object_name = "consigne"
    model = ConsigneSortie

    def get_queryset(self):
        queryset = super().get_queryset()
        # queryset = ConsigneSortie.objects.filter( zkf_user = self.request.user)
        return queryset


class DocumentsLibres(LoginRequiredMixin, ListView):
    template_name = "dashboard/docs_libres.html"
    context_object_name = "doc"
    model = DocumentLibreTemplate

    def get_queryset(self):
        queryset = super().get_queryset()
        # queryset = DocumentLibre.objects.filter( zkf_user = self.request.user)
        return queryset


class AssuranceSanteList(LoginRequiredMixin, ListView):
    template_name = "dashboard/mutuelles.html"
    context_object_name = "mutuelle"
    model = AssuranceSante

    def get_queryset(self):
        queryset = super().get_queryset()
        # queryset = DocumentLibre.objects.filter( zkf_user = self.request.user)
        return queryset


class ProtocolesList(LoginRequiredMixin, ListView):
    template_name = "dashboard/protocoles.html"
    context_object_name = "protocole"
    model = Protocole

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(
            zkf_environnement_protocole=self.request.user.profile.zkf_environnement_user
        )
        return queryset


class ScorePrediction(LoginRequiredMixin, ListView):
    template_name = "dashboard/prediction_score.html"
    context_object_name = "score"
    model = PerformanceMachineLearning

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(zkf_user=self.request.user)
        print(queryset)
        return queryset


class ConsultationTypeList(LoginRequiredMixin, ListView):
    template_name = "dashboard/consultation_type.html"
    context_object_name = "consultations_type"
    model = ConsultationType

    def get_queryset(self):
        queryset = super().get_queryset()
        queryset = queryset.filter(zkf_user=self.request.user)
        return queryset


class ExtractionResult(LoginRequiredMixin, View):
    template_name = "dashboard/extraction/extraction.html"

    def get(self, request, *args, **kwargs):
        # context = super().get_context_data(**kwargs)
        start = self.request.GET["start"]
        end = self.request.GET["end"]
        tag = json.loads(self.request.GET["tags"])

        code = self.request.GET["codes"]

        code = [x.strip().upper() for x in code.split(",")]
        # print (Operation.objects.filter(tags__in = ['7bc5b270-0b53-4fce-9516-eef677e659dd', 'bccc6395-114d-4be5-9e3a-b0682ca78662']))

        operations = Operation.objects.filter(
            Q(operation_date__gte=start, operation_date__lte=end)
            & ((Q(tags__in=tag)) | (Q(operationdevisccam__acte_ccam__in=code)))
        )
        operations = operations.filter(
            redacteur__profile__zkf_environnement_user=self.request.user.profile.zkf_environnement_user
        )
        context = {}
        context["operations"] = operations.distinct()

        # generate corps_document with variables

        html = render_to_string(self.template_name, context=context, request=request)

        # print (operations, start, end, tag, code)
        return HttpResponse(
            json.dumps(
                {
                    "corps": html,
                    "operations_list": [str(o.id) for o in operations.distinct()],
                }
            )
        )


class ExtractionFiltre(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/extraction/extraction_filtre.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["tags"] = FullTag.objects.all()
        return context


class Geographie(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/geographie.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # get the localisation of all the patients of the current user
        adresses = Adresse.objects.filter(
            zkf_patient__praticien_referent__in=[self.request.user]
        )

        # group by city and calculate the percentage by city !
        # 1/ count the number of patients by city
        total = adresses.count()
        adresses = adresses.values("ville", "code_postal").annotate(
            percent=(Count("ville"))
        )
        adresses = [a for a in adresses]

        for a in adresses:
            # calculate the percentage of patient by city
            a["percent"] = a["percent"] / total

            # convert the city to a gps coordinate
            try:
                city = Ville.objects.get(
                    nom_ville=a["ville"], code_postal=a["code_postal"]
                )
            except Exception as ex:
                print(ex)
                city = None

            if city:
                a["gps"] = city.coordonnees_gps
            else:
                del a
        # sort list of dicts
        adresses.sort(key=lambda x: x["percent"], reverse=True)
        context["adresses"] = json.dumps(adresses)

        return context


"""
Vacations:
For a user
General for all users: secretaires, infirmiers etc...
"""


class VacationsUser(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/vacations/vacations_user.html"

    def get_context_data(self, **kwargs):
        # https://vindevs.com/blog/how-change-id-uuid-django-website-p3/
        # vacations = Vacation.objects.all()
        # for v in vacations:
        #     old_obj_id = v.id
        #
        #     # it creates new object with the same data as a previous one
        #     # but id field is now a uuid string
        #     v.id = uuid.uuid4()
        #     v.save()
        #
        #     # So, if we do not need to store objects with old integer ids we have to delete them.
        #     Vacation.objects.filter(id=old_obj_id).first().delete()
        #
        context = super().get_context_data(**kwargs)
        today = datetime.date.today()
        vacations = Vacation.objects.filter(
            deleted=False, zkf_user=self.request.user
        ).order_by("jour_semaine")
        context["vacations"] = vacations.filter(
            Q(fin_periode=None) | Q(fin_periode__gt=today)
        )
        context["vacations_ancienne"] = vacations.filter(fin_periode__lt=today)
        context["vacations_a_venir"] = vacations.filter(debut_periode__gt=today)

        return context


class VacationsServiceView(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/vacations/vacations_service_view.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["users"] = get_linked_users_by_role(
            self.request.user
        )  # User.objects.filter(profile__zkf_environnement_user = self.request.user.profile.zkf_environnement_user)
        context["sites_users"] = get_linked_users_by_site(
            self.request.user
        )  # Site.objects.filter(zkf_environnement_site = self.request.user.profile.zkf_environnement_user)
        context["sites"] = Site.objects.filter(
            zkf_environnement_site=self.request.user.profile.zkf_environnement_user
        )
        return context


class VacationsService(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/vacations/display_vacations.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["days"] = days = [d for d in get_week(datetime.date.today())]
        schedule = {}

        sites = json.loads(self.request.GET["sites"])
        users = json.loads(self.request.GET["users"])
        today = datetime.date.today()

        # Create a dict: {site : {user : {vacations} } }
        for s in sites:
            s = Site.objects.get(id=s)

            schedule[s] = {}
            # create a dict {user: {day : time }
            user_dict = {}
            for u in users:
                u = User.objects.get(id=u)
                absences = Absence.objects.filter(zkf_user=u)

                vacations = Vacation.objects.filter(
                    deleted=False, zkf_user=u, site=s
                ).order_by("jour_semaine")
                vacations = vacations.filter(
                    Q(fin_periode=None) | Q(fin_periode__gt=today)
                )
                vacations_dict = {1: [], 2: [], 3: [], 4: [], 5: [], 6: [], 7: []}

                for vacation in vacations:
                    # check if vacation is inside absence: datetime range !
                    # https://stackoverflow.com/questions/325933/determine-whether-two-date-ranges-overlap
                    # return true =>
                    # (StartDate1 <= EndDate2) and (StartDate2 <= EndDate1)
                    # Date1: absence, Date2: vacation
                    # combine day of the week and time for vacation:
                    vacation_start = datetime.datetime.combine(
                        days[vacation.jour_semaine - 1], vacation.debut_vacation
                    )
                    vacation_end = datetime.datetime.combine(
                        days[vacation.jour_semaine - 1], vacation.fin_vacation
                    )
                    overlap = False
                    for absence in absences:
                        # https://stackoverflow.com/questions/7065164/how-to-make-a-datetime-object-aware-not-naive
                        utc = datetime.timezone.utc
                        if (
                            absence.date_heure_start.replace(tzinfo=utc)
                            < vacation_end.replace(tzinfo=utc)
                        ) and (
                            vacation_start.replace(tzinfo=utc)
                            < absence.date_heure_end.replace(tzinfo=utc)
                        ):
                            overlap = True
                            motif_absence = absence.motif

                    # save either vacation or absence / update vacations list
                    vacation_absence = [vacation]
                    if overlap is True:
                        vacation_absence = str("Absence " + str(motif_absence))

                    vacations_dict.update(
                        {
                            vacation.jour_semaine: vacations_dict[vacation.jour_semaine]
                            + [vacation_absence]
                        }
                    )
                # filter paires/impaires semaines
                user_dict = vacations_dict
                schedule[s][u] = user_dict

        context["schedule"] = schedule

        return context


class AbsenceUser(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/vacations/absence.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        today = datetime.date.today()
        context["absences"] = Absence.objects.filter(
            zkf_user=self.request.user, date_heure_start__gte=today
        ).order_by("-date_heure_start")
        context["absences_passees"] = Absence.objects.filter(
            zkf_user=self.request.user, date_heure_end__lte=today
        ).order_by("-date_heure_start")

        return context


# Secretariat
class Secretariat(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/secretariat/index.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        return context


class UploadComptesRendus(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/secretariat/upload_compte_rendu.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        return context


"""CCAM statstiques par code"""


# arborsecence of ccam


class CcamMenuStatIndex(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/statistiques/ccam/index.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class CcamMenuStat(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/statistiques/ccam/menu_search_ccam.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        start = self.request.GET["start"]
        end = self.request.GET["end"]
        # Get lit of First level menu
        context["first_level"] = first_level = R_menu.objects.filter(
            cod_pere=1
        ).order_by("rang")
        actes_codes = {}

        # all actes crées by a user
        # exclude YYYY codes
        actes_all = list(
            OperationDevisCcam.objects.filter(
                zkf_operation__redacteur=self.request.user,
                zkf_operation__operation_date__gte=start,
                zkf_operation__operation_date__lte=end,
            )
            .exclude(acte_ccam__istartswith="YYYY")
            .values_list("acte_ccam", flat=True)
        )  # acte_ccam__in = list_actes_menu).count()##.values_list('acte_ccam', flat=True)

        for f in first_level:
            list_actes_menu = []

            def recursive_queryset(code):
                codes_pere = R_menu.objects.filter(cod_pere=code)
                if codes_pere.exists():
                    for f in codes_pere:
                        recursive_queryset(f.cod_menu)
                    # return queryset
                else:
                    list_actes_menu.extend(
                        list(
                            R_acte.objects.filter(menu_cod=code).values_list(
                                "cod_acte", flat=True
                            )
                        )
                    )

            # loop through all submenus to add codes in the list
            recursive_queryset(f.cod_menu)

            # filter actes_all by submenu acte:
            # Using list comprehension
            actes_all_Output = [b for b in actes_all if b in list_actes_menu]
            # print (100*len(actes_all_Output)/len(actes_all))
            pourcentage = 0
            if len(actes_all) != 0:
                pourcentage = round(100 * len(actes_all_Output) / len(actes_all), 2)

            actes_codes[f] = {
                "pourcentage": pourcentage,
                "nombre": len(actes_all_Output),
            }

        context["first_level"] = actes_codes

        return context


class CcamSubMenuStat(LoginRequiredMixin, TemplateView):
    template_name = "dashboard/statistiques/ccam/sub_menu_ccam.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        start = self.request.GET["start"]
        end = self.request.GET["end"]
        # Get lit of First level menu
        cod_menu = self.request.GET["sub_menu"]
        sub_menu = R_menu.objects.filter(cod_pere=cod_menu).order_by("rang")

        actes_codes = {}

        # all actes crées by a user
        # exclude YYYY codes
        actes_all = list(
            OperationDevisCcam.objects.filter(
                zkf_operation__redacteur=self.request.user,
                zkf_operation__operation_date__gte=start,
                zkf_operation__operation_date__lte=end,
            )
            .exclude(acte_ccam__istartswith="YYYY")
            .values_list("acte_ccam", flat=True)
        )  # acte_ccam__in = list_actes_menu).count()##.values_list('acte_ccam', flat=True)

        for sm in sub_menu:
            list_actes_menu = []
            # all actes crées by a user
            # actes_crees = OperationDevisCcam.objects.filter(
            #     zkf_operation__redacteur=self.request.user,
            # ).values_list("acte_ccam", flat=True)

            def recursive_queryset(code):
                codes_pere = R_menu.objects.filter(cod_pere=code)
                if codes_pere.exists():
                    for f in codes_pere:
                        recursive_queryset(f.cod_menu)
                    # return queryset
                else:
                    list_actes_menu.extend(
                        list(
                            R_acte.objects.filter(menu_cod=code).values_list(
                                "cod_acte", flat=True
                            )
                        )
                    )

            # loop through all submenus to add codes in the list
            recursive_queryset(sm.cod_menu)

            # filter actes_all by submenu acte:
            # Using list comprehension

            actes_all_Output = [b for b in actes_all if b in list_actes_menu]
            # print (100*len(actes_all_Output)/len(actes_all))
            pourcentage = 0
            if len(actes_all) != 0:
                pourcentage = round(100 * len(actes_all_Output) / len(actes_all), 2)
            actes_codes[sm] = {
                "pourcentage": pourcentage,
                "nombre": len(actes_all_Output),
            }

        actes_codes_last_menu = {}
        if not sub_menu.exists():
            actes = R_acte.objects.filter(menu_cod=cod_menu)
            # filter actes_all by queryset acte:
            # Using list comprehension
            for a in actes:
                actes_all_Output = [b for b in actes_all if b in [a.cod_acte]]
                pourcentage = 0
                if len(actes_all) != 0:
                    pourcentage = round(100 * len(actes_all_Output) / len(actes_all), 2)
                actes_codes_last_menu[a] = {
                    "pourcentage": pourcentage,
                    "nombre": len(actes_all_Output),
                }

            context["actes"] = actes_codes_last_menu

        context["sub_menu"] = actes_codes

        return context


"""Display all DMIS, edit them inline ! """


class DmisList(LoginRequiredMixin, ListView):
    template_name = "dashboard/dmis/dmis_list.html"
    context_object_name = "dmis"
    model = MedicalDevice

    def get_queryset(self):
        queryset = super().get_queryset()
        # queryset = queryset.filter(zkf_user = self.request.user)
        return queryset
