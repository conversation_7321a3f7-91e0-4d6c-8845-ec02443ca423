import datetime

one_day = datetime.timedelta(days=1)

"""
Function to get all days of the selected week !
"""


# https://stackoverflow.com/questions/2003841/how-can-i-get-the-current-week-using-python
def get_week(date):
    """Return the full week (monday first) of the week containing the given date.

    'date' may be a datetime or date instance (the same type is returned).
    """
    day_idx = (date.weekday()) % 7  # turn sunday into 0, monday into 1, etc.
    date = date - datetime.timedelta(days=day_idx)

    for n in range(7):
        yield date
        date += one_day

    return date

    #
    # print list(get_week(datetime.datetime.now().date()))
    # # [datetime.date(2010, 1, 3), datetime.date(2010, 1, 4),
    # #  datetime.date(2010, 1, 5), datetime.date(2010, 1, 6),
    # #  datetime.date(2010, 1, 7), datetime.date(2010, 1, 8),
    # #  datetime.date(2010, 1, 9)]
    # print [d.isoformat() for d in get_week(datetime.datetime.now().date())]
