{% block content %}

<script>

	function load_sub_menu($this){
		var url = "{% url 'dashboard:submenu-ccam-stat' %}"
		
		$.ajax({
			type: "GET",
			//cache: false,
			url: url,
			data: {'csrfmiddlewaretoken': '{{csrf_token}}', 
					'sub_menu' : $this.attr('id'),
					'start' : $("#date_start").val(),
					'end' : $("#date_end").val(),
			}, 
	//		dataType = 'html',
			success : function(data) {
				$($this.attr('data-div')).html(data);
			},
			error : function(resultat, statut, erreur){
				console.log(resultat)
			},
		});
	}

	$("ul.menu_titles li").click(function(event){
		event.stopPropagation();
		if (event.isPropagationStopped() == true){
			var $this = $(this)
			load_sub_menu($this);
			}
	});





</script>


{% if messages %}
<ul class="messages">
	{% for message in messages %}
	<div class="alert alert-primary alert-dismissible fade show mx-auto mw-75" style="width: 800px" role="alert">
		<li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</li>
<!-- 		<button type="button" class="close" data-dismiss="alert" aria-label="Close"> -->
<!-- 		<span aria-hidden="true">&times;</span> -->
	<div>
	{% endfor %}
</ul>
{% endif %}

<ul class="menu_titles">

	{% for menu, valeurs in first_level.items %}

		<li id = "{{menu.cod_menu}}" class="cod_pere" data-div="#sub_menu_display_id_{{menu.cod_menu}}"  >	
			{{menu.rang}} - {{menu.libelle}} - <b>{{valeurs.nombre}}</b> ({{valeurs.pourcentage}}%)-<br>
		</li>
			<div id ="sub_menu_display_id_{{menu.cod_menu}}"></div>
			
			<br>


	{% endfor %}
</ul>


{% endblock content %}

