
{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}

<script>
    //Load CCAM arborescence
    $(document).ready(function(){

        function load_ccam_arborescence(){
          
            $.ajax({
                type: "GET",
                //cache: false,
                url: "{% url 'dashboard:menu-ccam-stat' %}",
                data: {'csrfmiddlewaretoken': '{{csrf_token}}', 
                        'start' : $("#date_start").val(),
                        'end' : $("#date_end").val(),
                }, 
        //		dataType = 'html',
                success : function(data) {
                    $("#ccam_arborescence").html(data); 
                },
                error : function(resultat, statut, erreur){
                    console.log(resultat)
                },
            });
        }

        load_ccam_arborescence();

        //Set date start end depending on current date
        $("#date_start").val(moment().startOf('year').format('YYYY-MM-DD'))
        $("#date_end").val(moment().format("YYYY-MM-DD"));

        $("#filter_previous_mois").click(function(){
            $("#date_start").val(moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD'));
            $("#date_end").val(moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD'));
        });
        $("#filter_mois").click(function(){
            $("#date_start").val(moment().startOf('month').format('YYYY-MM-DD'));
            $("#date_end").val(moment().endOf('month').format('YYYY-MM-DD'));
        });
        $("#filter_annee").click(function(){
            $("#date_start").val(moment().startOf('year').format('YYYY-MM-DD'));
            $("#date_end").val(moment().endOf('year').format('YYYY-MM-DD'));
        });
        $("#filter_12_mois_glissant").click(function(){
            $("#date_start").val(moment().startOf('month').subtract(12, 'month').format('YYYY-MM-DD'));
            $("#date_end").val(moment().endOf('month').format('YYYY-MM-DD'));
        });

        $("#show").click(function(){
            load_ccam_arborescence();
        });

    });

</script>


<div class="form-row align-items-center">
    
    <div class="form-group col-md">
      <label for="date_start" class="sr-only" >start</label>
      <input type="date" id="date_start" name="date_start" class="form-control" >

    </div>
    <div class="form-group col-md">
      <label for="date_end" class="sr-only" >End</label>
      <input type="date" id="date_end" name="date_end" class="form-control" >
    </div> 
</div>


<button class="btn btn-outline-info" id="filter_mois">Mois</button>
<button class="btn btn-outline-info" id="filter_previous_mois">Mois précédent</button>
<button class="btn btn-outline-info" id="filter_annee">Année</button>
<button class="btn btn-outline-info" id="filter_12_mois_glissant">12 mois glissants</button>
<button id="show" class="btn btn-dark" >   go   </button>



<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-body bg-light">
                <h5>{{ user.username }} - Statistiques CCAM</h5>
                <div id="ccam_arborescence"></div>
                <a href="{% url 'dashboard:home' %}" class="btn btn-primary">Accueil</a>
            </div>
        </div>
    </div>


{% endblock content %}

