
{% load static %}
{% block content %}


<p>
    <h6>Statistiques</h6>
    <ul>
        <li>Nombre de consultations en moyenne par jour de consultation: {{cs_stat.mean}}</li>
        <li>Mediane de consultations par jour de consultation: {{cs_stat.median}}</li>
        <li>Nombre minimum de consultation: {{cs_stat.min}}</li>
        <li>Nombre maximum de consultation: {{cs_stat.max}}</li>
    </ul>
</p>

<div class="chart-container" id = "consultations_count" style="position: relative; height:40vh; width:40vw">
    <canvas id="myChart"></canvas>
</div>
<br><br>

<div class="chart-container" style="position: relative; height:40vh; width:40vw">
    <canvas id="myChart_operations"></canvas>
</div>

<br><br><br>
<div class="chart-container" style="position: relative; ">
    <canvas id="myChart_lieu_consultation_operation"></canvas>
</div>

<br><br><br>
<div class="chart-container" style="position: relative; ">
    <canvas id="myChart_lieu_consultation"></canvas>
</div>


<script>

var data = JSON.parse("{{consultations_mois|escapejs}}")

sum = data.data.reduce((a, b) => a + b, 0)
var ctx = document.getElementById('myChart');

var myChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: data.label,
        datasets: [{
            label: '# de consultations ' + sum,
            data: data.data,
			backgroundColor: 'rgba(75, 192, 192, 0.2)',
            /*backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)',
                'rgba(255, 159, 64, 0.2)'
            ],*/
            /*borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)'
            ],*/
            borderWidth: 1
        }]
    },
    options: {
        scales: {
            yAxes: [{
                ticks: {
                    beginAtZero: true
                }
            }]
        },
        responsive: true,
        maintainAspectRatio: true,
    }
});

   

</script>



<script>
//operations
var data = ""
data = JSON.parse("{{operations_mois|escapejs}}")
sum = data.data.reduce((a, b) => a + b, 0)
var ctx = document.getElementById('myChart_operations');
var myChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: data.label,
        datasets: [{
            label: '# d\'operations ' + sum,
            data: data.data,
			backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderWidth: 1,
            hoverBackgroundColor: "rgba(232,105,90,0.8)",
            hoverBorderColor: "orange",
            scaleStepWidth: 1,
            /*backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)',
                'rgba(255, 159, 64, 0.2)'
            ],*/
            /*borderColor: [
                'rgba(255, 99, 132, 1)',
                {% comment %} 'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)' {% endcomment %}
            ],*/
            borderWidth: 1
        }]
    },
    options: {
        scales: {
            yAxes: [{
                ticks: {
                    beginAtZero: true
                }
            }]
        }
    }
});

</script>



<script>
//operations
var dataOpeCs = ""
dataOpeCs = JSON.parse("{{operation_by_lieu_consultation|escapejs}}")

var ctxOpe = document.getElementById('myChart_lieu_consultation_operation');
var myChart = new Chart(ctxOpe, {
    type: 'bar',
    data: {
        labels: dataOpeCs.label,
        datasets: [{
            label: 'lieus de consultation avant operation' ,
            data: dataOpeCs.data,
			backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderWidth: 1,
            hoverBackgroundColor: "rgba(232,105,90,0.8)",
            hoverBorderColor: "orange",
            scaleStepWidth: 1,
            /*backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)',
                'rgba(255, 159, 64, 0.2)'
            ],
            /*borderColor: [
                'rgba(255, 99, 132, 1)',
                {% comment %} 'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)' {% endcomment %}
            ],*/
            borderWidth: 1
        }]
    },
    options: {
        scales: {
            yAxes: [{
                ticks: {
                    beginAtZero: true
                }
            }]
        }
    }
});

colors = ['red','orange','yellow','green','blue','purple','red']

function addData(chart, color, data) {
    iter = (JSON.parse(data.data))
    
    i =0
    for (let key in iter){
        //console.log(key, iter[key])
        chart.data.datasets.push({
            label: key,
            backgroundColor: colors[i],
            data: iter[key],
        });
        i++;
        chart.update();
    }
}


// inserting the new dataset after 3 seconds
setTimeout(function() {
	addData(myChart, 'grey', dataOpeCs);
}, 30);

</script>


<script>

    //lieu de consultations
    var dataLieu = ""
    dataLieu = JSON.parse("{{lieu_consultation|escapejs}}")

    var ctxLieu = document.getElementById('myChart_lieu_consultation');
    var myChartCs = new Chart(ctxLieu, {
        type: 'bar',
        data: {
            labels: dataLieu.label,
            datasets: [{
                label: 'lieus de consultation' ,
                data: dataLieu.lieu,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderWidth: 1,
                hoverBackgroundColor: "rgba(232,105,90,0.8)",
                hoverBorderColor: "orange",
                scaleStepWidth: 1,
                /*backgroundColor: [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 206, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)',
                    'rgba(255, 159, 64, 0.2)'
                ],
                /*borderColor: [
                    'rgba(255, 99, 132, 1)',
                    {% comment %} 'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)' {% endcomment %}
                ],*/
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
    
    colors = ['red','orange','yellow','green','blue','purple','red']
    
    function addDataCs(chart, color, data) {
        iter = (JSON.parse(data.lieu))
        i =0
        for (let key in iter){
            //console.log(key, iter[key])
            chart.data.datasets.push({
                label: key,
                backgroundColor: colors[i],
                data: iter[key],
            });
            i++;
            chart.update();
        }
    }
    
    
    // inserting the new dataset after 3 seconds
    setTimeout(function() {
        addDataCs(myChartCs, 'grey', dataLieu);
    }, 30);
    
</script>




{% endblock content %}

