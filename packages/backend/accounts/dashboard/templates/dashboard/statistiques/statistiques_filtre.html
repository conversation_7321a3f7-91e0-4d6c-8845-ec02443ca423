{% extends "base_generic.html" %}

{% load static %}
{% block content %}

<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.3/Chart.min.js"></script>
<!-- https://chartjs-plugin-datalabels.netlify.app/guide/getting-started.html#registration -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@0.7.0"></script>

<!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/chartist.js/latest/chartist.min.css">
<script src="https://cdn.jsdelivr.net/chartist.js/latest/chartist.min.js"></script> -->

{% include "dashboard/dashboard_navbar.html" %}

<h4>
	Statistiques d'activité
</h4>

<button class="btn btn-outline-info" id="filter_mois">Mois</button>
<button class="btn btn-outline-info" id="filter_annee">Année</button>
<button class="btn btn-outline-info" id="filter_12_mois_glissant">12 mois glissants</button>


<div class="form-row align-items-center">
    
    <div class="form-group col-md">
      <label for="start" class="sr-only" >start</label>
      <input type = "date" id = "start" class="form-control">
    </div>
    <div class="form-group col-md">
      <label for="end" class="sr-only" >End</label>
      <input type = "date" id = "end" class="form-control">
    </div> 

    <div class="form-group col-md">
        <!-- <label for="inputPassword4">Password</label> -->
        <button id="show" class="btn btn-dark">   go   </button>
    </div>

    <div class="form-group col-md">
        <input type="checkbox" id = "compare">
    </div>
</div>

<br>

<div class="form-row align-items-center" id="compare_dates">
    <div class="form-group col-md">
      <label for="start2" class="sr-only" >start</label>
      <input type = "date" id = "start2" class="form-control">
    </div>
    <div class="form-group col-md">
      <label for="end2" class="sr-only" >End</label>
      <input type = "date" id = "end2" class="form-control">
    </div> 
</div>

<div id="result">

</div>


<script>

    //display another period
    $(document).ready(function(){
        $("#compare_dates").hide();
        $("#compare").prop('checked', false); 
        $("#compare").click(function(){
            if ($("#compare").is(":checked")){
                $("#compare_dates").show();
            }
            else{
                $("#compare_dates").hide();
            }
        })
    })

    //udpate charts depending of dates
    function display_results(){			
        start = $("#start").val()
        end = $("#end").val()
        $.ajax({
            type: "GET",
            cache: false,
            url: "{% url 'dashboard:statistiques' %}", 
            data: {'csrfmiddlewaretoken': '{{csrf_token}}', 'start':start, 'end':end}, 
    //		dataType = 'html',
            success : function(data) {
                $("#result").html(data); 
            },
            error : function(resultat, statut, erreur){
                console.log(resultat)
            },
        });
    };

    
    $("#show").click(function (){
        display_results();
    });

    //load current year by default
    $(document).ready(function(){
        $("#start").val(moment().startOf('year').format('YYYY-MM-DD'))
        $("#end").val(moment().format("YYYY-MM-DD"));
        display_results();
    });
    
    //set dates automatically
    $(document).ready(function () {

        $("#filter_mois").click(function(){
            $("#start").val(moment().startOf('month').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('month').format('YYYY-MM-DD'));
        });
        $("#filter_annee").click(function(){
            $("#start").val(moment().startOf('year').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('year').format('YYYY-MM-DD'));
        });
        $("#filter_12_mois_glissant").click(function(){
            $("#start").val(moment().startOf('month').subtract(12, 'month').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('month').format('YYYY-MM-DD'));
        });
        
    });

</script>






{% endblock content %}

