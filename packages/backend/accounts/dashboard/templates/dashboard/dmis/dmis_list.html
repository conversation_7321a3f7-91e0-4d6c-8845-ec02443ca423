{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}

{% comment %} 
<h4>
	DMI <a href = "{% url 'operation:add-cro-type' %}" class = "btn btn-primary" >+</a>
</h4> {% endcomment %}


<link rel="stylesheet" href="//cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script src = "//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script>
    $( document ).ready(function() {
		$('#DmisTable').DataTable({
			paging: false,
			//scrollY: 400,
			ordering: true,
		});

		

	});
  


function update_code_hpva(id)  {      
	//save note onclick leave
	
	code_hpva = $("#"+id).val();  

	//ajax to save note 
		$.ajax({
		type: "POST",
		data: {
			'dmi_id': id,
			'code_hpva': code_hpva,
			'csrfmiddlewaretoken': '{{ csrf_token }}'
		},
		url: "{% url 'materiel:update-dmi-code-hpva' %}",
		success: function(data) {
			console.log(data)
			alert('code mis à jour  ' + code_hpva); //display result in main div
		},
		error: function(resultat, statut, erreur) {
			console.log(resultat);
		},
	})
}

</script>


{% if dmis %}

<table class="table table-striped" id = "DmisTable">
	<thead>
		<tr>
			<th>Marque</th>
			<th>Identifiant</th>
			<th>libelle commercial</th>
			<th>gtin</th>
			<th>descriptif Ref</th>
			<th>Code HPVA</th>
		</tr>
	</thead>

	<tbody>
		
		{% for d in dmis %}
		<tr>
			<td>
				{{d.marque}} 
			</td>
			<td>
				{{d.dm_identifiant}} 
			</td>
			<td>
				{{d.libelle_commercial}} 
			</td>
			<td>
				{{d.gtin}} 
			</td>
			<td>
				{{d.descriptif_ref}} 
			</td>
			<td>
				<input type="text" value="{{d.code_hpva}}" id="{{d.id}}" class="form-control" onblur="update_code_hpva('{{d.id}}')" />
			</td>

		</tr>
	{% endfor %}

	</tbody>
</table>

<p>
	
		
{% else %}
	Pas de dmis crée
{% endif %}
</p>





{% endblock content %}

