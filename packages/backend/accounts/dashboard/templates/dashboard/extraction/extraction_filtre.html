{% extends "base_generic.html" %}

{% load static %}
{% block content %}

<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.3/Chart.min.js"></script>
<!-- https://chartjs-plugin-datalabels.netlify.app/guide/getting-started.html#registration -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@0.7.0"></script>

<!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/chartist.js/latest/chartist.min.css">
<script src="https://cdn.jsdelivr.net/chartist.js/latest/chartist.min.js"></script> -->

{% include "dashboard/dashboard_navbar.html" %}

<h4>
	Recherche d'activité
</h4>

<button class="btn btn-outline-info" id="filter_mois"><PERSON><PERSON></button>
<button class="btn btn-outline-info" id="filter_previous_mois"><PERSON><PERSON> précédent</button>
<button class="btn btn-outline-info" id="filter_annee">Année</button>
<button class="btn btn-outline-info" id="filter_12_mois_glissant">12 mois glissants</button>


<div class="form-row align-items-center">
    
    <div class="form-group col-md">
      <label for="start" class="sr-only" >start</label>
      <input type = "date" id = "start" class="form-control">
    </div>
    <div class="form-group col-md">
      <label for="end" class="sr-only" >End</label>
      <input type = "date" id = "end" class="form-control">
    </div> 
</div>

<div class = "content">
    <div class="row">
        <div class="col">
            <input class ="form-control" type = "text" id="code_ccam">
        </div>
        <div class="col">
                {% for t in tags %}
                    <input type = "checkbox" value = "{{t.id}}" name = "tag" >{{t}}<br>
                {% endfor %}
        </div>


    </div>

<div class="form-group col-md">
    <!-- <label for="inputPassword4">Password</label> -->
    <button id="show" class="btn btn-dark" onclick="display_results(), get_fields()">   go   </button>
</div>

<br>


<button onclick = "generate_csv()">export excel</button>
<button id="show_fields">Champs</button>


<div id="display_fields"></div>


<div id="result">

</div>


<script>
    $(document).ready(function (){
        display_results()
    })


//load models links to an operation. 
//right after loaded, get defautl fields saved in protocole, and check the boxes !
function get_fields(){
    $.ajax({		
        type: "POST",
        url: "{% url 'protocole:list-models-fields' %}", 
        data: {'csrfmiddlewaretoken': '{{ csrf_token }}'},
        success : function(data) {
            $('#display_fields').html(data);
            //click_checkboxes(fields);
            $("#display_fields").hide()
            //console.log(data); // log the returned json to the console
            // $("#search_result").prepend("<li><strong>"+ data[0]['pk'] +"</strong> - <em> "+data['prenom']+"</em> - <span> "+data.dob+"</span></li>");
            // alert(data)
        },
        error : function(resultat, statut, erreur){
            console.log(resultat)
            console.log(erreur)
        },
    });
}



//propagate all fields linked to a protocole to click the corresponding checkboxes on load
function click_checkboxes(json){
    //json is a json format text containing all fields saved !
    $('#fields input[type=checkbox]').each(function() {
        var model = $(this).attr('id');
        $('#'+model+" input[type=checkbox]").each(function(){
            //check if checkbox is in corresponding array
            if (jQuery.inArray( $(this).attr('value'), json[model]) > -1) {
                $( $(this)).prop( "checked", true );
            }
        })
    });
}


//export in excel : create a form with hidden fields, and open it in a new window !
//it is not possible in ajax to generate a file !
function generate_csv(){

    var protocoles = [];
    $('#protocoles input:checked').each(function() {
        protocoles.push(this.value);
    });

    //parse div => save div id if model as the key in a dict
    //then get all checkboxes of the div
    var models_fields = {}

    //parse all div inside the div id = "fields"
    $('#fields div[id]').each(function() {
        //get div id
        var model = this.id
        //get all the div checkboxes, and save them in an array !
        var fields = [];
        $('#'+model+' input:checked').each(function() {
            fields.push(this.value);
        });
        //if there is entries in the array, update object
        if (fields.length > 0) {
            models_fields[model] = fields
        }

    })
    //Submit form in JS
    var form = document.createElement("form");
    form.setAttribute("method", "post");
    form.setAttribute("action", "{% url 'protocole:generate-excel' %}");

    form.setAttribute("target", "view");

    var hiddenField = document.createElement("input"); 
    hiddenField.setAttribute("type", "hidden");
    hiddenField.setAttribute("name", "protocoles");
    hiddenField.setAttribute("value", JSON.stringify(protocoles));
    form.appendChild(hiddenField);
    document.body.appendChild(form);
    
    var hiddenField = document.createElement("input"); 
    hiddenField.setAttribute("type", "hidden");
    hiddenField.setAttribute("name", "models_fields");
    hiddenField.setAttribute("value", JSON.stringify(models_fields));
    form.appendChild(hiddenField);
    document.body.appendChild(form);

    var hiddenField = document.createElement("input"); 
    hiddenField.setAttribute("type", "hidden");
    hiddenField.setAttribute("name", "operation_list");
    hiddenField.setAttribute("value", JSON.stringify(operation_list));
    form.appendChild(hiddenField);
    document.body.appendChild(form);

    var Token=document.createElement("input");
    Token.setAttribute("type","hidden");
    Token.setAttribute("name","csrfmiddlewaretoken");
    Token.setAttribute("value","{{csrf_token}}");
    form.appendChild(Token);
    document.body.appendChild(form);

    window.open('', 'view');

    form.submit();
}


$(document).ready(function(){
    $("#show_fields").click(function(){
        $("#display_fields").toggle();
    })
});

    //udpate charts depending of dates
    function display_results(){

        codes = $('#code_ccam').val()
  
        var tags = [];
        $("input[name='tag']:checked").each(function() {
            tags.push($(this).val());
        });
        tags = JSON.stringify(tags)
       //console.log(tags)
        //console.log(codes)
        
        start = $("#start").val()
        end = $("#end").val()
        $.ajax({
            type: "GET",
            cache: false,
            url: "{% url 'dashboard:extraction-result' %}", 
            data:   { 'csrfmiddlewaretoken': '{{csrf_token}}', 
                    'start':start, 
                    'end':end, 
                    'codes':codes,
                    'tags':tags,
                    },
    //		dataType = 'html',
            success : function(data) {
                data = JSON.parse(data)
                $("#result").html(data.corps); 
                operation_list = data.operations_list
                console.log(operation_list)
            },
            error : function(resultat, statut, erreur){
                console.log(resultat)
            },
        });
    };

    
    $("#show").click(function (){
        display_results();
    });

    //load current year by default
    $(document).ready(function(){
        $("#start").val(moment().startOf('year').format('YYYY-MM-DD'))
        $("#end").val(moment().format("YYYY-MM-DD"));
        display_results();
    });
    
    //set dates automatically
    $(document).ready(function () {

        $("#filter_previous_mois").click(function(){
            $("#start").val(moment().startOf('month').subtract(1, 'month').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('month').subtract(1, 'month').format('YYYY-MM-DD'));
        });
        $("#filter_mois").click(function(){
            $("#start").val(moment().startOf('month').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('month').format('YYYY-MM-DD'));
        });
        $("#filter_annee").click(function(){
            $("#start").val(moment().startOf('year').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('year').format('YYYY-MM-DD'));
        });
        $("#filter_12_mois_glissant").click(function(){
            $("#start").val(moment().startOf('month').subtract(12, 'month').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('month').format('YYYY-MM-DD'));
        });
        
    });

</script>






{% endblock content %}

