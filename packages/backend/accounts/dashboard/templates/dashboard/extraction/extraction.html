
{% load static %}
{% block content %}


<link rel="stylesheet" href="//cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css" />
<script src = "//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>

<script>
    $( document ).ready(function() {
      
      //sort table
      $('#myTable').DataTable({
        paging: false,
        // "order": [[ 4, "desc" ]]
        //order by multiple columns
        order:  [[ 3, 'desc' ], [ 4, 'desc' ]],
        });
  
  });
  


</script>




<h6>Resultats</h6>


{% if operations %}
    
    Nombre de lignes: {{operations.count}}
    
    <table class="table table-striped" id = "myTable">
        <thead>
            <tr>
                <th>Nom</th>
                <th>Date operation</th>
                <th>operateur</th>
                <th>Titre</th>
            </tr>
        </thead>
    
        <tbody>
        {% for o in operations %}
            <tr>
                <td><a href="{% url 'patient:detail-patient' o.zkf_patient.id %}" target="_blank">{{ o.zkf_patient.nom}} {{ o.zkf_patient.prenom}}</a></td> 
                <td><a href="{% url 'operation:detail-operation' o.id %}" target="_blank">{{o.operation_date|date:"Y/m/d"}}</a></td>
                <td>
                    {% for op in o.operateurs.all %}
                        {{op}},
                    {% endfor %}
                </td>
                <td>{{o.titre_operation|truncatewords:10}} </td> 
            </tr>
        {% endfor %}
    
        </tbody>
    </table>

{% endif %}

{% endblock content %}

