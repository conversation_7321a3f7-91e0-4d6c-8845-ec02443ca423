{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}

<link rel="stylesheet" href="//code.jquery.com/ui/1.13.1/themes/base/jquery-ui.css">

<script src="https://code.jquery.com/jquery-3.6.0.js"></script>
<script src="https://code.jquery.com/ui/1.13.1/jquery-ui.js"></script>
<script>

	$(function(){
		$(".vDateField").datepicker({
			dateFormat: "yy-mm-dd"
		});
	});

</script>

<h4>
	Vacations 
</h4>

<form class="form" id="id_create_vacation" method="post">
		{% csrf_token %}
		{% for hidden_field in form.hidden_fields %}
			{{ hidden_field }}
		{% endfor %}
		<br>
		{{form.errors}}

		{{ form.as_p }}
		
		
	<input type="submit" value="OK">
</form>


{% endblock content %}

