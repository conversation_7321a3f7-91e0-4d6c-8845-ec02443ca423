{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}
<!-- https://cdnjs.com/libraries/jquery-datetimepicker
https://xdsoft.net/jqplugins/datetimepicker/
-->
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.1/themes/base/jquery-ui.css">

<script src="https://code.jquery.com/jquery-3.6.0.js"></script>
<script src="https://code.jquery.com/ui/1.13.1/jquery-ui.js"></script>

<link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-datetimepicker/2.5.20/jquery.datetimepicker.css" rel="stylesheet" />
<!--Scripts-->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-datetimepicker/2.5.20/jquery.datetimepicker.full.min.js"></script>


<script>

	$(document).ready(function(){
		$(".vDateField").datepicker({
			dateFormat: "yy-mm-dd"
		});
	
		$('.dateTime').datetimepicker({
			format:'Y-m-d H:i'
		});
	 });

</script>

<h4>
	Vacations 
</h4>

<form class="form" id="id_create_absence" method="post">
		{% csrf_token %}
		{% for hidden_field in form.hidden_fields %}
			{{ hidden_field }}
		{% endfor %}
		<br>
		{{form.errors}}

		{{ form.as_p }}
		
		
	<input type="submit" value="OK">
</form>


{% endblock content %}

