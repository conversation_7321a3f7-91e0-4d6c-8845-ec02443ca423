{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}


<h4>
	Vacations <a href = "{% url 'accounts:create-vacation' %}" class = "btn btn-primary" >+</a>
</h4>

<div class="container">
	<div class="row">
	  <div class="col">
			<h6>Vacations en cours</h6>
			{% if vacations %}
				

				{% for v in vacations %}
				<p>
					
					<a href = "{% url 'accounts:detail-vacation' v.id %}">Detail</a>  <a href="{% url 'accounts:edit-vacation' v.id %}" clas = "btn btn-primary">Edit</a> {{v.id}}<br>
					{{v.site}}<br>
					{{v}}<br>
					Periode: {{v.debut_periode|date:'d/m/Y'}}
					{% if v.fin_periode %}
						 - {{v.fin_periode|date:'d/m/Y'}}
					{% else %}
						Pas de fin de periode
					{% endif %}
					{% comment %} Jour de la semaine: {{v.jour_semaine}}, {{v.debut_vacation|date:'H:i'}} - {{v.fin_vacation|date:'H:i'}}<br>
					semaine paire-impaire: {{v.repetition_semaine}}<br>
					Debut periode: {{v.debut_periode}}
					{% if v.fin_periode %}
						{{v.fin_periode}}
					{% else %}
						Pas de fin de periode
					{% endif %} {% endcomment %}
				</p>

				{% endfor %}
			{% else %}
				Pas de vacations
			{% endif %}
	  </div>

	  <div class="col">
		<h6>Vacations en à venir</h6>
		
		{% if vacations_a_venir %}
				
				{% for v in vacations_a_venir %}
				<p>
					
					<a href = "{% url 'accounts:detail-vacation' v.id %}">Detail</a>  <a href="{% url 'accounts:edit-vacation' v.id %}" clas = "btn btn-primary">Edit</a><br>
					{{v.site}}<br>
					{{v}}
					<br>
					{{v}}<br>
					Periode: {{v.debut_periode|date:'d/m/Y'}}
					{% if v.fin_periode %}
						 - {{v.fin_periode|date:'d/m/Y'}}
					{% else %}
						Pas de fin de periode
					{% endif %}
					{% comment %} Jour de la semaine: {{v.jour_semaine}}, {{v.debut_vacation|date:'H:i'}} - {{v.fin_vacation|date:'H:i'}}<br>
					semaine paire-impaire: {{v.repetition_semaine}}<br>
					Debut periode: {{v.debut_periode}}
					{% if v.fin_periode %}
						{{v.fin_periode}}
					{% else %}
						Pas de fin de periode
					{% endif %} {% endcomment %}
				</p>

				{% endfor %}
			{% else %}
				Pas de vacations
			{% endif %}
		</p>
	  </div>

	  <div class="col">
		<h6>Vacations passées</h6>
		{% if vacations_ancienne %}

				{% for v in vacations_ancienne %}
				<p>
					
					<a href = "{% url 'accounts:detail-vacation' v.id %}">Detail</a>  <a href="{% url 'accounts:edit-vacation' v.id %}" clas = "btn btn-primary">Edit</a><br>
					{{v.site}}<br>
					{{v}}<br>
					Periode: {{v.debut_periode|date:'d/m/Y'}}
					{% if v.fin_periode %}
						 - {{v.fin_periode|date:'d/m/Y'}}
					{% else %}
						Pas de fin de periode
					{% endif %}
					{% comment %} Jour de la semaine: {{v.jour_semaine}}, {{v.debut_vacation|date:'H:i'}} - {{v.fin_vacation|date:'H:i'}}<br>
					semaine paire-impaire: {{v.repetition_semaine}}<br>
					Debut periode: {{v.debut_periode}}
					{% if v.fin_periode %}
						{{v.fin_periode}}
					{% else %}
						Pas de fin de periode
					{% endif %} {% endcomment %}
				</p>

				{% endfor %}
			{% else %}
				Pas de vacations
			{% endif %}
		</p>
	  </div>
	</div>
  </div>






{% endblock content %}

