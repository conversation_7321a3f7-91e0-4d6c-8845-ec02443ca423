
{% load static %}
{% block content %}


{% comment %} https://www.bootdey.com/snippets/view/time-table#html {% endcomment %}
<style>
	body{
		margin-top:20px;
	}
	.bg-light-gray {
		background-color: #f7f7f7;
	}
	.table-bordered thead td, .table-bordered thead th {
		border-bottom-width: 2px;
	}
	.table thead th {
		vertical-align: bottom;
		border-bottom: 2px solid #dee2e6;
	}
	.table-bordered td, .table-bordered th {
		border: 1px solid #dee2e6;
	}
	
	
	.bg-sky.box-shadow {
		box-shadow: 0px 5px 0px 0px #00a2a7
	}
	
	.bg-orange.box-shadow {
		box-shadow: 0px 5px 0px 0px #af4305
	}
	
	.bg-green.box-shadow {
		box-shadow: 0px 5px 0px 0px #4ca520
	}
	
	.bg-yellow.box-shadow {
		box-shadow: 0px 5px 0px 0px #dcbf02
	}
	
	.bg-pink.box-shadow {
		box-shadow: 0px 5px 0px 0px #e82d8b
	}
	
	.bg-purple.box-shadow {
		box-shadow: 0px 5px 0px 0px #8343e8
	}
	
	.bg-lightred.box-shadow {
		box-shadow: 0px 5px 0px 0px #d84213
	}
	
	
	.bg-sky {
		background-color: #02c2c7
	}
	
	.bg-orange {
		background-color: #e95601
	}
	
	.bg-green {
		background-color: #5bbd2a
	}
	
	.bg-yellow {
		background-color: #f0d001
	}
	
	.bg-pink {
		background-color: #ff48a4
	}
	
	.bg-purple {
		background-color: #9d60ff
	}
	
	.bg-lightred {
		background-color: #ff5722
	}
	
	.padding-15px-lr {
		padding-left: 15px;
		padding-right: 15px;
	}
	.padding-5px-tb {
		padding-top: 5px;
		padding-bottom: 5px;
	}
	.margin-10px-bottom {
		margin-bottom: 10px;
	}
	.border-radius-5 {
		border-radius: 5px;
	}
	
	.margin-10px-top {
		margin-top: 10px;
	}
	.font-size14 {
		font-size: 14px;
	}
	
	.text-light-gray {
		color: #d6d5d5;
	}
	.font-size13 {
		font-size: 13px;
	}
	
	.table-bordered td, .table-bordered th {
		border: 1px solid #dee2e6;
	}
	.table td, .table th {
		padding: .75rem;
		vertical-align: top;
		border-top: 1px solid #dee2e6;
	}

	span svg {
		fill: #3b5998;
		height: 1em;
		vertical-align: bottom; /* ADDED */
	  }


</style>

<script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script>


<div class="container-fluid">
	<div class="timetable-img text-center">
		<img src="img/content/timetable.png" alt="">
	</div>
	<div class="table-responsive">
		A implémenter : Semaine paire - impaire ! Numero de semaine: 
		<table class="table table-bordered text-center">
			<thead>
				<tr class="bg-light-gray">
					<th class="text-uppercase">Site</th>
					<th class="text-uppercase">User</th>
					{% for day in days %}
						<th class="text-uppercase">{{day|date:"l d F Y"}}</th>
					{% endfor %}
				</tr>
			</thead>
			<tbody>

					{% for site, users in schedule.items %}
					<!-- https://stackoverflow.com/questions/49022282/row-wise-duplicate-data-need-to-be-eliminatedrowspan-using-django-python -->
							<!--<span class="bg-sky padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16 xs-font-size13"></span>
							<div class="margin-10px-top font-size14">{{horaire}}</div>
							-->
							
							{% for u, vacations in users.items %}
							<tr class="align-middle">

								{% if forloop.first %}
								   <td rowspan="{{ users|length }}" class="align-middle"> 
									 {{ site }}
									</td>
								{% endif %}
								
								<td>{{ u }}</td>

								<!-- Parse vacations datetime objects -->
								{% for jour, vacation in vacations.items %}
									<td>
											{% for v in vacation %}
											<div style="text-align: center;display: flex; align-items: center; ">
												{{v.type_vacation}}&nbsp;&nbsp;
													<span>{{v.debut_vacation|date:"H:i"}}</span>
													<span>
														<svg version="1.1" id="line_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="{{v.vacation_duration}}" height="5px" xml:space="preserve">
															<path class="path2" fill="#01a09e" stroke-width="3" stroke="#01a09e" d="M0 0 l1120 0"/>
														</svg>	
													</span>
													<span>{{v.fin_vacation|date:"H:i"}}&nbsp;&nbsp;</span>
												{% endfor %}
											</div>
									</td>
								{% endfor %}

							</tr>
							{% endfor %}
						
					{% endfor %}


				<!--
				<tr>
					<td class="align-middle">10:00am</td>
					<td>
						<span class="bg-yellow padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Music</span>
						<div class="margin-10px-top font-size14">10:00-11:00</div>
						<div class="font-size13 text-light-gray">Ivana Wong</div>
					</td>
					<td class="bg-light-gray">

					</td>
					<td>
						<span class="bg-purple padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Art</span>
						<div class="margin-10px-top font-size14">10:00-11:00</div>
						<div class="font-size13 text-light-gray">Kate Alley</div>
					</td>
					<td>
						<span class="bg-green padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Yoga</span>
						<div class="margin-10px-top font-size14">10:00-11:00</div>
						<div class="font-size13 text-light-gray">Marta Healy</div>
					</td>
					<td>
						<span class="bg-pink padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">English</span>
						<div class="margin-10px-top font-size14">10:00-11:00</div>
						<div class="font-size13 text-light-gray">James Smith</div>
					</td>
					<td class="bg-light-gray">

					</td>
				</tr>

				<tr>
					<td class="align-middle">11:00am</td>
					<td>
						<span class="bg-lightred padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Break</span>
						<div class="margin-10px-top font-size14">11:00-12:00</div>
					</td>
					<td>
						<span class="bg-lightred padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Break</span>
						<div class="margin-10px-top font-size14">11:00-12:00</div>
					</td>
					<td>
						<span class="bg-lightred padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Break</span>
						<div class="margin-10px-top font-size14">11:00-12:00</div>
					</td>
					<td>
						<span class="bg-lightred padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Break</span>
						<div class="margin-10px-top font-size14">11:00-12:00</div>
					</td>
					<td>
						<span class="bg-lightred padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Break</span>
						<div class="margin-10px-top font-size14">11:00-12:00</div>
					</td>
					<td>
						<span class="bg-lightred padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Break</span>
						<div class="margin-10px-top font-size14">11:00-12:00</div>
					</td>
				</tr>

				<tr>
					<td class="align-middle">12:00pm</td>
					<td class="bg-light-gray">

					</td>
					<td>
						<span class="bg-purple padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Art</span>
						<div class="margin-10px-top font-size14">12:00-1:00</div>
						<div class="font-size13 text-light-gray">Kate Alley</div>
					</td>
					<td>
						<span class="bg-sky padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Dance</span>
						<div class="margin-10px-top font-size14">12:00-1:00</div>
						<div class="font-size13 text-light-gray">Ivana Wong</div>
					</td>
					<td>
						<span class="bg-yellow padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Music</span>
						<div class="margin-10px-top font-size14">12:00-1:00</div>
						<div class="font-size13 text-light-gray">Ivana Wong</div>
					</td>
					<td class="bg-light-gray">

					</td>
					<td>
						<span class="bg-green padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Yoga</span>
						<div class="margin-10px-top font-size14">12:00-1:00</div>
						<div class="font-size13 text-light-gray">Marta Healy</div>
					</td>
				</tr>

				<tr>
					<td class="align-middle">01:00pm</td>
					<td>
						<span class="bg-pink padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">English</span>
						<div class="margin-10px-top font-size14">1:00-2:00</div>
						<div class="font-size13 text-light-gray">James Smith</div>
					</td>
					<td>
						<span class="bg-yellow padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Music</span>
						<div class="margin-10px-top font-size14">1:00-2:00</div>
						<div class="font-size13 text-light-gray">Ivana Wong</div>
					</td>
					<td class="bg-light-gray">

					</td>
					<td>
						<span class="bg-pink padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">English</span>
						<div class="margin-10px-top font-size14">1:00-2:00</div>
						<div class="font-size13 text-light-gray">James Smith</div>
					</td>
					<td>
						<span class="bg-green padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Yoga</span>
						<div class="margin-10px-top font-size14">1:00-2:00</div>
						<div class="font-size13 text-light-gray">Marta Healy</div>
					</td>
					<td>
						<span class="bg-yellow padding-5px-tb padding-15px-lr border-radius-5 margin-10px-bottom text-white font-size16  xs-font-size13">Music</span>
						<div class="margin-10px-top font-size14">1:00-2:00</div>
						<div class="font-size13 text-light-gray">Ivana Wong</div>
					</td>
				</tr>
				-->
			</tbody>
		</table>
	</div>
</div>


{% endblock content %}

