{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}

<script>

	//Ajax function to add to print later a file in the print manager
	function display_vacations(){		
		url = "{% url 'dashboard:affichage-vacations' %}"
		var users = [];
		$('input[name="name_user_checkbox"]:checked').each(function() {
			users.push($(this).val());
		});

		var sites = [];
		$('input[name="name_site_checkbox"]:checked').each(function() {
			sites.push($(this).val());
		});
		console.log(sites)
		$.ajax({
			type: "GET",
			url: url,
			data: { 'csrfmiddlewaretoken': '{{csrf_token}}', 
					'users' : JSON.stringify(users),
					'sites' : JSON.stringify(sites)
					}, 
	//		dataType = 'html',
			success : function(data) {
				$("#id_vacations_table").html(data)
			},
			error : function(resultat, statut, erreur){
				console.log(erreur)
			},
		});
	};

	$(document).ready(function(){
		display_vacations()
		$("#id_filtres").hide()
		$("#id_send_data").click(function(){
			display_vacations()
			$("#id_filter").hide()
			$("#id_send_data").hide()
			$("#id_filtres").show()
		})

		$("#id_filtres").click(function(){
			$("#id_filter").show()
			$("#id_filtres").hide()
			$("#id_send_data").show()
		})
	});

</script>
week
<button class="btn btn-warning" id="id_send_data">Valider</button>
<button class="btn btn-warning" id="id_filtres">Filtres</button>

<div class="container" id = "id_filter">
	<div class="row">
		<div class="col">
			{% if users %}
				{% for role, values in users.items %}
					<h6>{{role}}</h6>

						{% for user in values %}
							<div class="form-check">
								<input class="form-check-input" name="name_user_checkbox" type="checkbox" value="{{user.id}}" >
								<label class="form-check-label" for="defaultCheck1">
									{{user}}
								</label>
							</div>
						{% endfor %}
				{% endfor %}
			{% endif %}
		</div>

		<div class="col">
			{% if sites %}
				{% for site, values in sites_users.items %}
					<h6>{{site}}</h6>
						{% for user in values %}
							<div class="form-check">
								<input class="form-check-input" name="name_site_checkbox" type="checkbox" value="{{user.id}}">
								<label class="form-check-label" for="defaultCheck1">
									{{user}}
								</label>
							</div>
						{% endfor %}
				{% endfor %}
			{% endif %}
		</div>
		{% comment %} Sites {% endcomment %}
		<div class="col">
			{% if sites %}
				{% for site in sites %}

					<div class="form-check">
						<input class="form-check-input" name="name_site_checkbox" type="checkbox" value="{{site.id}}">
						<label class="form-check-label" for="defaultCheck1">
							{{site}}
						</label>
					</div>
				{% endfor %}
			{% endif %}
		</div>

	</div> <!-- <div class="container"> -->
</div> <!-- <div class="row"> -->






<div id = "id_vacations_table"></div>



{% endblock content %}

