{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}


<h4>
	Absences <a href = "{% url 'accounts:create-absence' %}" class = "btn btn-primary" >+</a>
</h4>

<div class="container">
	<div class="row">
	  <div class="col">
			<h6>Absences à venir</h6>
			{% if absences %}
				
				{% for a in absences %}
				<p>
					
					{% comment %} <a href = "{% url 'accounts:detail-vacation' v.id %}">Detail</a>  <a href="{% url 'accounts:edit-vacation' v.id %}" clas = "btn btn-primary">Edit</a> {{v.id}}<br> {% endcomment %}
					<b>Du {{a.date_heure_start|date:"d/m/Y H:i"}} au {{a.date_heure_end|date:"d/m/Y H:i"}}:</b> {{a.motif}},<br>
				</p>

				{% endfor %}
			{% else %}
				Pas de vacations
			{% endif %}
	  </div>

	  <div class="col">
		<h6>Absences passées</h6>
		
		{% if absences_passees %}
				
				{% for a in absences_passees %}
					<p>
						<b>Fin: {{a.date_heure_end|date:"d/m/Y H:i"}}</b> {{a.motif}}, {{a.date_heure_start|date:"d/m/Y H:i"}}, {{a.date_heure_end|date:"d/m/Y H:i"}}<br>
					</p>
				{% endfor %}
			{% else %}
				Pas d'absence
			{% endif %}
		</p>
		</div>

	</div>
  </div>






{% endblock content %}

