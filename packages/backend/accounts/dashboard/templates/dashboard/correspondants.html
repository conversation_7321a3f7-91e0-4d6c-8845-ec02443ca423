{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}

{% if messages %}
<ul class="messages">
	{% for message in messages %}
	<div class="alert alert-primary alert-dismissible fade show mx-auto mw-75" style="width: 800px" role="alert">
		<li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</li>
<!-- 		<button type="button" class="close" data-dismiss="alert" aria-label="Close"> -->
<!-- 		<span aria-hidden="true">&times;</span> -->
	<div>
	{% endfor %}
</ul>
{% endif %}

<h4>
	Contacts <a href = "{% url 'correspondant:add-correspondant' %}" class = "btn btn-primary" >+</a>
</h4>

<div class = "content">
	<div class = "row">
		<div class = "col">
			Rechercher un Correspondant
			<input type = "text" id = "search_input" class = "form-control w-auto"><br>
		
			<div id="search_result"></div>
		</div>

		<div class = "col" id = "id_specialite">
			<h6>Exporter correspondants en excel <button class = "btn btn-outline-primary btn-sm" onclick = "generate_excel()"> Export </button></h6>
			{% for s in specialite %}
				<input type = "checkbox" value = "{{s.id}}" name = "{{s.spe}}" >{{s.spe}}<br>
			{% endfor %}
		</div>
	</div>
</div>


<script>
	//Script to search patient
	$(document).ready(function() {
		$("#search_input").val('')

		function search_patient (text){
			if (text.length >= 3)
				$.ajax({		
					type: "POST",
					url: "{% url 'dashboard:query-correspondant' %}", 
					data: {'input': text, 'csrfmiddlewaretoken': '{{ csrf_token }}'},
					success : function(data) {
						// $('#search_result').html(data)
						//console.log(data); // log the returned json to the console
						// $("#search_result").prepend("<li><strong>"+ data[0]['pk'] +"</strong> - <em> "+data['prenom']+"</em> - <span> "+data.dob+"</span></li>");
						$("#search_result").html(data)
					},
					error : function(resultat, statut, erreur){
						console.log(resultat)
						console.log(erreur)
					},
				});
			else
				$("#search_result").html('')
		}
		
		$("#search_input").keyup(function(){
			text = $(this).val();
			search_patient(text)
		});
	});


//export in excel : create a form with hidden fields, and open it in a new window !
//it is not possible in ajax to generate a file !
function generate_excel(){
    var specialites = [];
    $('#id_specialite input:checked').each(function() {
        specialites.push(this.value);
    });

    //Submit form in JS
    var form = document.createElement("form");
    form.setAttribute("method", "post");
    form.setAttribute("action", "{% url 'correspondant:export-correspondant' %}");

    form.setAttribute("target", "view");

    var hiddenField = document.createElement("input"); 
    hiddenField.setAttribute("type", "hidden");
    hiddenField.setAttribute("name", "specialites");
    hiddenField.setAttribute("value", JSON.stringify(specialites));
    form.appendChild(hiddenField);
    document.body.appendChild(form);

    var Token=document.createElement("input");
    Token.setAttribute("type","hidden");
    Token.setAttribute("name","csrfmiddlewaretoken");
    Token.setAttribute("value","{{csrf_token}}");
    form.appendChild(Token);
    document.body.appendChild(form);

    window.open('', 'view');

    form.submit();
}

</script>


{% endblock content %}

