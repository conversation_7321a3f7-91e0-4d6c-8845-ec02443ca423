{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}

{% if messages %}

	<ul class="messages">
		{% for message in messages %}
		<div class="alert alert-primary alert-dismissible fade show mx-auto mw-75" style="width: 800px" role="alert">
			<li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</li>
	<!-- 		<button type="button" class="close" data-dismiss="alert" aria-label="Close"> -->
	<!-- 		<span aria-hidden="true">&times;</span> -->
		<div>
		{% endfor %}
	</ul>

{% endif %}


<!-- Button to upload multiple CR at once -->

<!-- Upload form. Note enctype attribute! -->
<form class="form" method="post" enctype="multipart/form-data">
	{% csrf_token %}

	{% if form_picture.errors %}
		{% for field in form_picture %}
			{% for error in field.errors %}
				<p> {{ error }} </p>
			{% endfor %}
		{% endfor %}
	{% endif %}
	
	<p>{{ form_cr.non_field_errors }}</p>
	<p>{{ form_cr.docfile.label_tag }} </p>
	<p> {{ form_cr.docfile.errors }} {{ form_cr.docfile }} </p>

	{{ form_cr.cr_file.label_tag }}
	{% comment %} {% render_field form_cr.cr_file  class="form-control w-auto" placeholder=form_cr.cr_file.label %} {% endcomment %}
	{% if field.help_text %}
		<small class="form-text text-muted">{{ form_cr.cr_file.help_text }}</small>
	{% endif %}

	<br>

	<p><input type="submit" value="Upload" /></p>

	<a href="{% url 'dashboard:secretariat' %}">Cancel</a>

</form>

<!-- Frame to display the list of CR -->

<!-- Frame to display CR details ! + validate CR -->

<!-- Once CR is validated: remove !  -->

<!-- When leave: delete all remaining files ! -->



{% endblock content %}

