{% extends "base_generic.html" %}

{% load static %}
{% block content %}

{% include "dashboard/dashboard_navbar.html" %}


{% if not cros %} {% comment %} si il n'y a pas de compte rendu type crée {% endcomment %}
	<h4>
		Cros <a href = "{% url 'dashboard:import-cros-type' %}" class = "btn btn-primary" >Importer les CRO type</a>
	</h4>
{% endif %}

<h4>
	Cros <a href = "{% url 'operation:add-cro-type' %}" class = "btn btn-primary" >+</a>
</h4>



<p>
	{% if cros %}
		{% for c in cros %}
			<a href = "{% url 'operation:detail-cro-type' c.id %}">{{c.cro_type_titre}}  </a>  <br>
		{% endfor %}
	{% else %}
		Pas de Cro type
	{% endif %}
</p>





{% endblock content %}

