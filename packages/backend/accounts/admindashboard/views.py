from django.views.generic import (
    TemplateView,
)
from django.contrib.auth.mixins import LoginRequiredMixin


class AdminDashboard(LoginRequiredMixin, TemplateView):
    template_name = "admin/index.html"


# """
# class Statistiques (LoginRequiredMixin, TemplateView):
#     template_name = "dashboard/statistiques/statistiques.html"
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         # start = date.today().replace(day=1) + relativedelta(months=-13)
#         # end = date.today()
#         start = self.request.GET['start']
#         end = self.request.GET['end']
#         cs_stat = (consultations_stat(start, end, self.request.user))
#         #print (cs_stat)
#         context["consultations_mois"] = json.dumps(cs_stat)
#         context["cs_stat"] = {"mean" : cs_stat['mean'], "median" : cs_stat['median'], "min":cs_stat['min'], "max": cs_stat['max']}
#         context["operations_mois"] = json.dumps(operations_stat(start, end, self.request.user))
#
#         context['operation_by_lieu_consultation'] = json.dumps(operation_by_consultation_lieu(start, end, self.request.user))
#         context['lieu_consultation'] = json.dumps(consultations_stat(start, end, self.request.user))
#
#         return context
#
# class StatistiquesFiltre(LoginRequiredMixin, TemplateView):
#     template_name = "dashboard/statistiques/statistiques_filtre.html"
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         return context
#
# class Correspondants (LoginRequiredMixin, ListView):
#     template_name = "dashboard/correspondants.html"
#     context_object_name = "correspondants"
#     model = Contact
#     queryset = Contact.objects.filter ( zkf_contactcategorie__contact_categorie = "Correspondant" )
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         #get all specialite
#         specialite = Specialite.objects.all()
#         context['specialite'] = specialite
#         return context
#
#
# class Cros(LoginRequiredMixin, ListView):
#     template_name = "dashboard/cros.html"
#     context_object_name = "cros"
#     model = CroType
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         queryset = CroType.objects.filter( zkf_user = self.request.user)
#         return queryset
#
# class ImportCros(LoginRequiredMixin, ListView):
#     template_name = "dashboard/cros.html"
#     context_object_name = "cros"
#     model = CroType
#
#     def get_queryset(self):
#
#         #insert logique somewhere....
#
#         ####1/ get user specialite active
#         user_spe_principale = self.request.user.profile.specialite_active.spe
#         specialite = '_'.join( user_spe_principale.split(' ') ).lower()
#
#         ###2/ get all cros type of the specialite
#         model_cros = apps.get_model( specialite, 'crotype')
#         cros = model_cros.objects.all()
#
#         ####3/ Copy the cros in the user cros type !
#         for c in cros:
#             o = CroType(zkf_user=self.request.user, cro_type_titre=c.titre, cro_type=c.corps, nom_modele_specialite=specialite)
#             o.save()
#
#         queryset = super().get_queryset()
#         queryset = CroType.objects.filter( zkf_user = self.request.user)
#         return queryset
#
# class Ordonnances(LoginRequiredMixin, ListView):
#     template_name = "dashboard/ordonnances.html"
#     context_object_name = "ordonnance"
#     model = OrdonnanceTemplate
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         queryset = OrdonnanceTemplate.objects.filter( zkf_user = self.request.user)
#         return queryset
#
# class ConsignesSurveillance(LoginRequiredMixin, ListView):
#     template_name = "dashboard/consignes_surveillance.html"
#     context_object_name = "consigne"
#     model = ConsigneSortie
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         # queryset = ConsigneSortie.objects.filter( zkf_user = self.request.user)
#         return queryset
#
#
# class DocumentsLibres(LoginRequiredMixin, ListView):
#     template_name = "dashboard/docs_libres.html"
#     context_object_name = "doc"
#     model = DocumentLibreTemplate
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         # queryset = DocumentLibre.objects.filter( zkf_user = self.request.user)
#         return queryset
#
# class AssuranceSanteList(LoginRequiredMixin, ListView):
#     template_name = "dashboard/mutuelles.html"
#     context_object_name = "mutuelle"
#     model = AssuranceSante
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         # queryset = DocumentLibre.objects.filter( zkf_user = self.request.user)
#         return queryset
#
#
# class ProtocolesList(LoginRequiredMixin, ListView):
#     template_name = "dashboard/protocoles.html"
#     context_object_name = "protocole"
#     model = Protocole
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         queryset = queryset.filter( zkf_environnement_protocole = self.request.user.profile.zkf_environnement_user)
#         return queryset
#
# class ConsultationTypeList(LoginRequiredMixin, ListView):
#     template_name = "dashboard/consultation_type.html"
#     context_object_name = "consultation"
#     model = ConsultationType
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#         queryset = queryset.filter( zkf_user = self.request.user)
#         return queryset
#
#
# class ExtractionResult (LoginRequiredMixin, TemplateView):
#     template_name = "dashboard/extraction/extraction.html"
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         start = self.request.GET['start']
#         end = self.request.GET['end']
#         tag =  json.loads(self.request.GET['tags'])
#         code = self.request.GET['codes']
#         code = [x.strip().upper() for x in code.split(",")]
#
#         operations = Operation.objects.filter(
#                                                 Q(operation_date__gte = start, operation_date__lte = end)
#                                                 &
#                                                 (
#                                                     (Q(tags__in = tag))
#                                                     |(Q(operationdevisccam__acte_ccam__in = code))
#                                                 )
#                                             )
#         context['operations'] = operations
#         print (operations, start, end, tag, code)
#         return context
#
# class ExtractionFiltre(LoginRequiredMixin, TemplateView):
#     template_name = "dashboard/extraction/extraction_filtre.html"
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context['tags'] = MyCustomTag.objects.all()
#         return context
#
# class Geographie(LoginRequiredMixin, TemplateView):
#     template_name = "dashboard/geographie.html"
#
#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         #get the localisation of all the patients of the current user
#         adresses = Adresse.objects.filter(zkf_patient__praticien_referent__in = [self.request.user])
#
#         #group by city and calculate the percentage by city !
#         ##1/ count the number of patients by city
#         total = adresses.count()
#         adresses = adresses.values('ville', 'code_postal').annotate(percent=(Count('ville')))
#         adresses = [a for a in adresses]
#
#         for a in adresses:
#             #calculate the percentage of patient by city
#             a['percent'] = a['percent']/total
#
#             #convert the city to a gps coordinate
#             try:
#                 city = Ville.objects.get(nom_ville = a['ville'], code_postal = a['code_postal'])
#             except:
#                 city = None
#
#             if city:
#                 a['gps'] = city.coordonnees_gps
#             else:
#                 del a
#         #sort list of dicts
#         adresses.sort(key= lambda x:x['percent'], reverse=True)
#         context['adresses'] = json.dumps(adresses)
#
#         return context
#
# """
