from django.urls import path
from accounts.views import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DetailUserProfile,
    EditAbsence,
    EditUserProfile,
    LoadLieu,
    LoadPraticiensLies,
    SavePraticiensLies,
)

from .views import (
    login_view,
    register_view,
    logout_view,
    DetailVacation,
    CreateVacation,
    EditVacation,
)

app_name = "accounts"  # define an app name for redirections in templates


urlpatterns = [
    path("login/", login_view, name="login"),
    path("register/", register_view, name="register"),
    path("logout/", logout_view, name="logout"),
    # load praticiens lies saved in preferences
    path(
        "load_praticiens_lies/",
        LoadPraticiensLies.as_view(),
        name="load_praticiens_lies",
    ),
    # save praticiens lies in preferences
    path(
        "save_praticiens_lies/",
        SavePraticiensLies.as_view(),
        name="save_praticiens_lies",
    ),
    # user profile
    path(
        "detail-profile/<slug:username>",
        DetailUserProfile.as_view(),
        name="detail-profile",
    ),
    path(
        "edit-profile/<slug:username>", EditUserProfile.as_view(), name="edit-profile"
    ),
    # load lieux when site change
    path("load_lieu/", LoadLieu.as_view(), name="load_lieu"),
    # vacations
    path("detail-vacation/<uuid:pk>", DetailVacation.as_view(), name="detail-vacation"),
    path("edit-vacation/<uuid:pk>", EditVacation.as_view(), name="edit-vacation"),
    path("create-vacation/", CreateVacation.as_view(), name="create-vacation"),
    # absences
    path("edit-absence/<uuid:pk>", EditAbsence.as_view(), name="edit-absence"),
    path("create-absence/", CreateAbsence.as_view(), name="create-absence"),
]
