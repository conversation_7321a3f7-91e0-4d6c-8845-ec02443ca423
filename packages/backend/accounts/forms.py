from django import forms
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from django.http import HttpResponseRedirect
from django.forms.models import inlineformset_factory

from accounts.models import RendezVous, Absence, Profile, Preferences_user

# import user filtered by spe
from accounts.scripts.filter_user import (
    get_operateur,
    get_users_group_filtered,
)
from schedule.models import Vacation


class UsersLoginForm(forms.Form):
    username = forms.CharField()
    password = forms.CharField(
        widget=forms.PasswordInput,
    )

    def __init__(self, *args, **kwargs):
        super(UsersLoginForm, self).__init__(*args, **kwargs)
        self.fields["username"].widget.attrs.update(
            {"class": "form-control", "name": "username"}
        )
        self.fields["password"].widget.attrs.update(
            {"class": "form-control", "name": "password"}
        )

    def clean(self, *args, **keyargs):
        username = self.cleaned_data.get("username")
        password = self.cleaned_data.get("password")

        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise forms.ValidationError("This user does not exists")
            if not user.check_password(password):
                raise forms.ValidationError("Incorrect Password")
            if not user.is_active:
                raise forms.ValidationError("User is no longer active")

        return super(UsersLoginForm, self).clean(*args, **keyargs)


class UsersRegisterForm(forms.ModelForm):
    class Meta:
        model = User
        fields = [
            "username",
            "email",
            "confirm_email",
            "password",
        ]

    username = forms.CharField()
    email = forms.EmailField(label="Email")
    confirm_email = forms.EmailField(label="Confirm Email")
    password = forms.CharField(widget=forms.PasswordInput)

    def __init__(self, *args, **kwargs):
        super(UsersRegisterForm, self).__init__(*args, **kwargs)
        self.fields["username"].widget.attrs.update(
            {"class": "form-control", "name": "username"}
        )
        self.fields["email"].widget.attrs.update(
            {"class": "form-control", "name": "email"}
        )
        self.fields["confirm_email"].widget.attrs.update(
            {"class": "form-control", "name": "confirm_email"}
        )
        self.fields["password"].widget.attrs.update(
            {"class": "form-control", "name": "password"}
        )

    def clean(self, *args, **keyargs):
        email = self.cleaned_data.get("email")
        confirm_email = self.cleaned_data.get("confirm_email")
        username = self.cleaned_data.get("username")
        password = self.cleaned_data.get("password")

        if email != confirm_email:
            raise forms.ValidationError("Email must match")

        email_qs = User.objects.filter(email=email)
        if email_qs.exists():
            raise forms.ValidationError("Email is already registered")

        username_qs = User.objects.filter(username=username)
        if username_qs.exists():
            raise forms.ValidationError("User with this username already registered")

        if len(password) < 8:  # you can add more validations for password
            raise forms.ValidationError("Password must be greater than 8 characters")

        # return super(UsersRegisterForm, self).clean(*args, **keyargs)
        return HttpResponseRedirect("vous ne pouvez pas vous inscrire vous meme")


class UserAddRdvForm(forms.ModelForm):
    class Meta:
        model = RendezVous
        fields = ["zkf_user", "date_heure_debut", "motif"]

    def __init__(self, user, *args, **kwargs):
        super(UserAddRdvForm, self).__init__(*args, **kwargs)
        self.fields["zkf_user"].queryset = get_operateur(user)


class UserAddAbsenceForm(forms.ModelForm):
    class Meta:
        model = Absence
        fields = ["zkf_user", "date_heure_start", "date_heure_end", "motif"]

    zkf_user = forms.ModelMultipleChoiceField(
        queryset=None, widget=forms.SelectMultiple
    )

    # Need to clean the field before validation => as we change the zkf_user widget, return a queryset instead of an object !
    def clean_zkf_user(self):
        return self.cleaned_data["zkf_user"][0]

    def __init__(self, user, *args, **kwargs):
        super(UserAddAbsenceForm, self).__init__(*args, **kwargs)
        # self.fields['zkf_user'] = forms.MultipleChoiceField(widget=forms.SelectMultiple)
        self.fields["zkf_user"].queryset = get_operateur(user)


class UseraccountForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ["first_name", "last_name", "email"]

    def __init__(self, *args, **kwargs):
        super(UseraccountForm, self).__init__(*args, **kwargs)


# subclass BaseFormset to add user to formsets
# class UserFormsetSubClass(BaseFormSet):
#     def __init__ (self, *args, **kwargs):
#         user = kwargs.pop('user')
#         super(UserFormsetSubClass, self).__init__(*args, **kwargs)
#     def _construct_forms(self):
#         self.forms = []
#         for i in xrange(self.total_form_count()):
#             self.forms.append(self._construct_forms(i, user = self.user))


class UserProfileForm(forms.ModelForm):
    class Meta:
        model = Profile
        fields = [
            "praticiens_lies",
            "zkf_environnement_lie",
            "specialite_profile",
            "specialite_active",
            "service",
            "site",
            "groupe_facturation",
            "entete",
            "titre",
            "conventionnement",
            "activite_ccam",
            "rpps",
            "entete_ordonnance",
        ]

    def __init__(self, *args, **kwargs):
        user = kwargs.get("instance")
        super(UserProfileForm, self).__init__(*args, **kwargs)
        if user:
            self.fields["praticiens_lies"].queryset = get_users_group_filtered(user)


class UserPreferenceForm(forms.ModelForm):
    class Meta:
        model = Preferences_user
        fields = [
            "affichage_service",
            "affichage_service_status",
            "report_hebdo",
            "impression_recto_verso",
        ]

    def __init__(self, *args, **kwargs):
        # user = kwargs["instance"]
        super(UserPreferenceForm, self).__init__(*args, **kwargs)
        # self.fields['zkf_user'] = forms.MultipleChoiceField(widget=forms.SelectMultiple)
        # self.fields['praticiens_lies'].queryset = get_operateur(user)


# fields to exclude
exclusion = [
    "deleted",
    "deleted_by",
    "deleted_on",
    "created_by",
    "created_on",
    "modified_by",
    "modified_on",
]

ProfileFormset = inlineformset_factory(
    User,
    Profile,
    extra=1,
    exclude=exclusion,
    fk_name="user",
    form=UserProfileForm,
)
PreferencesFormset = inlineformset_factory(
    User,
    Preferences_user,
    extra=1,
    exclude=exclusion,
    fk_name="user",
    form=UserPreferenceForm,
)


"""
vacations
"""


class CreateVacationForm(forms.ModelForm):
    class Meta:
        model = Vacation
        fields = [
            "zkf_user",
            "type_vacation",
            "site",
            "jour_semaine",
            "debut_vacation",
            "fin_vacation",
            "repetition_semaine",
            "debut_periode",
            "fin_periode",
            "deleted",
        ]

    def __init__(self, *args, **kwargs):
        super(CreateVacationForm, self).__init__(*args, **kwargs)
        # list all fields which are DateField type
        dates = []
        time = []
        for field in self.fields:
            # https://www.appsloveworld.com/django/100/157/how-to-get-field-type-in-forms-py-not-in-template-django?expand_article=1
            f = Vacation._meta.get_field(field)
            if f.get_internal_type() == "DateField":
                dates.append(f.name)
            if f.get_internal_type() == "TimeField":
                time.append(f.name)
                # self.fields[f.name].widget = forms.DateField

        # set class
        # https://www.letscodemore.com/blog/how-to-add-class-and-other-atrributes-to-form-fields-in-django/
        for visible in self.visible_fields():
            visible.field.widget.attrs["class"] = "form-control"
            if visible.name in dates:
                visible.field.widget.attrs["class"] = "form-control vDateField"
                visible.field.widget.attrs["type"] = "date"

            if visible.name in time:
                visible.field.widget.attrs["class"] = "form-control time"
                visible.field.widget.attrs["type"] = "time"
            # visible.field.widget.attrs['placeholder'] = visible.field.label


"""
Absences
"""


class CreateAbsenceForm(forms.ModelForm):
    class Meta:
        model = Absence
        fields = ["zkf_user", "date_heure_start", "date_heure_end", "motif", "deleted"]

    def __init__(self, *args, **kwargs):
        super(CreateAbsenceForm, self).__init__(*args, **kwargs)
        # list all fields which are DateField type
        dates = []
        datetime = []
        for field in self.fields:
            # https://www.appsloveworld.com/django/100/157/how-to-get-field-type-in-forms-py-not-in-template-django?expand_article=1
            f = Absence._meta.get_field(field)
            if f.get_internal_type() == "DateField":
                dates.append(f.name)
            if f.get_internal_type() == "DateTimeField":
                datetime.append(f.name)
                # self.fields[f.name].widget = forms.DateField

        # set class
        # https://www.letscodemore.com/blog/how-to-add-class-and-other-atrributes-to-form-fields-in-django/
        for visible in self.visible_fields():
            visible.field.widget.attrs["class"] = "form-control"
            if visible.name in dates:
                visible.field.widget.attrs["class"] = "form-control vDateField"
                visible.field.widget.attrs["type"] = "date"
            if visible.name in datetime:
                visible.field.widget.attrs["class"] = "form-control datetime"
                visible.field.widget.attrs["type"] = "date"
            # visible.field.widget.attrs['placeholder'] = visible.field.label
