from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
import uuid
from comptabilite.models import GroupeFacturation

from audit.models import Auditable

"""
Environnement:
Create an environnement for each client: can be a single user, or a full team.
Each environnement can have multiple users, and multiples sites (operations, consultations...)
"""


class Environnement(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom_environnement = models.Char<PERSON>ield(max_length=256)
    # INS API structure type for this environment
    INS_STRUCT_TYPES = (
        ("FINESS", "FINESS"),
        ("SIREN", "SIREN"),
        ("SIRET", "SIRET"),
    )
    ins_struct_type = models.Char<PERSON>ield(
        max_length=6,
        choices=INS_STRUCT_TYPES,
        default="FINESS",
        help_text="Type de structure pour appel API INS",
    )
    # INS API structure number for this environment
    ins_struct_number = models.CharField(
        max_length=30,
        blank=True,
        null=True,
        help_text="Numéro de structure pour appel API INS",
    )

    def __str__(self):
        return self.nom_environnement


class Specialite(Auditable):  # list of specialites, and link it to user !
    class Meta:
        ordering = [
            "spe",
        ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # Nom de la specialité
    spe = models.CharField(
        max_length=200,
        null=False,
    )

    # nom du medecin specialiste
    spe_medecin = models.CharField(
        max_length=200,
        null=False,
    )
    CHIRURGIE = "CHIRURGICAL"
    MEDICAL = "MEDICAL"
    MEDICOCHIRURGICAL = "MEDICOCHIRURGICAL"

    TYPE_SPE = (
        (CHIRURGIE, "Chirurgical"),
        (MEDICAL, "Medical"),
    )
    # si la spécialité est médicale, chirurgicale ou les 2 => permet de définir des affichages conditionnels ! (ex: pas d'opération pour les spécialités médicales)
    type_specialite = models.CharField(
        max_length=200,
        null=False,
        blank=False,
        choices=TYPE_SPE,
    )

    # definir à quelle app chaque spécialité est liée => antecedents et présentation propre à chaque spécialité !
    data_app = models.CharField(max_length=200, null=False, blank=False)
    omop_code = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return self.spe


class Service(Auditable):  # list of services, link it to specialites!
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    site_service = models.ForeignKey("accounts.Site", on_delete=models.PROTECT)
    nom_service = models.CharField(
        max_length=200,
        null=False,
    )
    specialite_service = models.ManyToManyField(Specialite)
    template_document = models.TextField(
        blank=True
    )  # for printing ordonnances, docs etc...=>define a  text to insert in docs !

    def __str__(self):
        return self.nom_service


"""
Stationnement patiente: can be a room, a box, a service (radiologie) => every place where a patient can be parked for a short or a long time !
"""


class StationnementPatient(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_service = models.ForeignKey(Service, on_delete=models.CASCADE)
    nom_stationnement = models.CharField(
        max_length=200,
        null=False,
    )
    description_stationnement = models.TextField(null=True, blank=True)
    multiple_patient = models.BooleanField(default=False)


class EnteteDocument(
    Auditable
):  # entete des documents (haut de page): propre à chaque utilisateur, spécialité, service et environnement !
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    titre = models.CharField(max_length=256, null=False, blank=False)
    # specialite_entete = models.ManyToManyField(Specialite)
    # site_entete = models.ForeignKey('accounts.Site', on_delete = models.PROTECT)
    texte = models.TextField()

    def __str__(self):
        return self.titre


class Site(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # a site is always linked to an environnement !
    zkf_environnement_site = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    nom_site = models.CharField(
        max_length=200,
    )
    adresse_site = models.CharField(
        max_length=256,
    )
    zip_code_site = models.IntegerField()
    ville_site = models.CharField(
        max_length=200,
    )
    telephone_site = models.CharField(max_length=30, null=True, blank=True)
    adeli_site = models.CharField(max_length=30, null=True, blank=True)

    # colors for schedule
    operation_schedule_color = models.CharField(max_length=8, default="#B45F04")
    consultation_schedule_color = models.CharField(max_length=8, default="#669999")

    def __str__(self):
        return self.nom_site


class Lieu(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # a lieu is always linked to an environnement !
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    zkf_site = models.ForeignKey(Site, on_delete=models.CASCADE)
    nom_lieu = models.CharField(
        max_length=200,
    )
    acces_lieu = models.TextField(null=False, blank=True)
    description_lieu = models.TextField(null=False, blank=True)

    def __str__(self):
        return self.nom_lieu


class Secretariat(
    Auditable
):  # Secretariat: peuvent être partagés, liés à un environnement !
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    zkf_site = models.ManyToManyField(Site)
    nom_secretariat = models.CharField(
        max_length=200,
        null=False,
    )
    specialite_secretariat = models.ManyToManyField(Specialite)
    numero_secretariat = models.CharField(
        max_length=200,
    )

    def __str__(self):
        return self.nom_secretariat


"""
User models
"""


# https://stackoverflow.com/questions/********/django-customize-the-user-models-return-field
# https://stackoverflow.com/questions/7510849/django-how-to-display-users-full-name-in-filteredselectmultiple
# subclass User with proxy = True to avoid creating a new table
class CustomUser(User):
    class Meta:
        proxy = True

    def __str__(self):
        return self.get_full_name()


def __unicode__(self):
    return self.get_full_name()


# Énumération des types d'événements (à utiliser comme clés)
class NotificationEvents:
    REPORT_PATIENT_ADD = "compterenducorrespondant_patient_added"
    OPERATION_PATIENT_CREATED = "operation_patient_created"
    ABSENCE_USER_CREATED = "absence_user_created"
    PROTOCOLE_PATIENT_ADDED = "patient_added_in_protocol"
    CONVOCATION_PATIENT_COMPTERENDU = "convocation_patient_compterendu"
    USER_ADDED_IN_PROTOCOL = "user_added_in_protocol"

    CHOICES = [
        (
            REPORT_PATIENT_ADD,
            "Compte rendu patient ajouté",
            "Notification envoyée au(x) praticien(s) referent(s) du patient",
        ),
        (
            OPERATION_PATIENT_CREATED,
            "Opération patient créée",
            "Notification envoyée au(x) secretaire(s) liée(s) au praticien",
        ),
        (
            ABSENCE_USER_CREATED,
            "Absence utilisateur créée",
            "Notification envoyée au(x) secretaire(s) liée(s) au praticien",
        ),
        (
            PROTOCOLE_PATIENT_ADDED,
            "Patient ajouté au protocole",
            "Notification envoyée aux utilisateurs liés au protocole",
        ),
        (
            USER_ADDED_IN_PROTOCOL,
            "Utilisateur ajouté dans le protocole",
            "Notification envoyée aux utilisateurs ajoutés comme référents dans le protocole",
        ),
        (
            CONVOCATION_PATIENT_COMPTERENDU,
            "Convoquer un patient sur demande d'un correspondant",
            "Notification envoyée au(x) praticien(s) referent(s) du patient si besoin de convoquer le patient",
        ),
    ]

    @classmethod
    def all_keys(cls):
        return [choice[0] for choice in cls.CHOICES]


# Énumération des canaux de notification
class NotificationChannels:
    NOTIF = "notification"
    EMAIL = "email"
    SMS = "sms"

    CHOICES = [
        (NOTIF, "Notification"),
        (EMAIL, "Email"),
        (SMS, "SMS"),
    ]

    @classmethod
    def all_keys(cls):
        return [choice[0] for choice in cls.CHOICES]


class Preferences_user(Auditable):

    class Meta:
        verbose_name = "Préférences utilisateur"
        verbose_name_plural = "Préférences utilisateurs"

    # id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # uuid = models.UUIDField(default=uuid.uuid4, editable=False, blank=True, null=True)

    # choose how the first page (vue globale du service) is displayed: only all patients or user specific patients !
    SPECIALITE = "SPE"
    USER = "USE"

    ROLE_CHOICES = (
        (SPECIALITE, "Specialite"),
        (USER, "User"),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    # pref for affichage of the main page !
    affichage_service = models.CharField(
        max_length=5,
        choices=ROLE_CHOICES,
        default=SPECIALITE,
    )
    # average duration of a consultation
    # for now: 15 minutes
    consultation_duree = models.DurationField(
        default="00:15:00",
        help_text="Durée moyenne d'une consultation (HH:MM:SS)",
    )

    # choose if the service first page is static (meaning the page always come with the 'affichage_service choice'
    # Or "Dynamic": always stay at the last state !
    STATIC = "STA"
    DYNAMIC = "DYN"

    ROLE_CHOICES = (
        (STATIC, "Static"),
        (DYNAMIC, "Dynamique"),
    )
    affichage_service_status = models.CharField(
        max_length=5,
        choices=ROLE_CHOICES,
        default=DYNAMIC,
    )
    report_hebdo = models.BooleanField(default=True)

    # list of selected users which are linked to the current user
    # For instance: a secretary is consulting with a specific pratictioner=>does not want to uncheck/check the practitioner each time he loads the page
    # so when a practitioner or a list of practitioner is selected: kept in memory !!! -> dynamic field
    current_praticiens_lies_selectionnes = models.ManyToManyField(
        User,
        related_name="%(app_label)s_%(class)s_praticiens_lies_selectionnes",
        blank=True,
    )

    # Ordonnance par défaut: template choisis quand rédaction d'une ordonnance sans selectionner de template !
    ordonnance_default = models.ForeignKey(
        "document.OrdonnanceTemplate", on_delete=models.CASCADE, null=True, blank=True
    )

    # impression R/V by default
    impression_recto_verso = models.BooleanField(default=True)

    # notifications
    # Utilisation de JSONField pour stocker toutes les préférences
    # Django 3.1+ intègre JSONField, pour les versions antérieures utilisez django.contrib.postgres.fields.JSONField
    preference_notification = models.JSONField(default=dict)

    @classmethod
    def create_default_preferences_notifications(cls):
        """Crée un dictionnaire avec toutes les préférences activées par défaut"""
        preferences = {}

        # Pour chaque type d'événement
        for event in NotificationChannels.all_keys():
            preferences[channel] = {}

            # Pour chaque canal
            for channel in NotificationEvents.all_keys():
                # Par défaut, tout est activé
                preferences[channel][event] = True

        return preferences

    @classmethod
    def create_default_notifications(cls, user):
        """Crée les préférences par défaut pour un utilisateur"""
        preferences = cls.create_default_preferences_notifications()
        return cls.objects.create(user=user, preferences=preferences)

    def notification_get_all_preferences(self):
        """Retourne toutes les préférences dans un format structuré pour l'affichage"""
        result = []

        for channel_key, channel_name in NotificationChannels.CHOICES:
            channel_data = {"key": event_key, "name": channel_name, "events": []}

            for event_key, event_name in NotificationEvents.CHOICES:
                channel_data["event"].append(
                    {
                        "key": event_key,
                        "name": event_name,
                        "enabled": self.is_enabled(event_key, event_name),
                    }
                )

            result.append(channel_data)

        return result

    def __str__(self):
        return self.user.username


# https://simpleisbetterthancomplex.com/tutorial/2016/07/22/how-to-extend-django-user-model.html
class Profile(Auditable):
    # id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # uuid = models.UUIDField(default=uuid.uuid4, editable=False, blank=True, null=True)
    SECRETAIRE = "SEC"
    MEDECIN = "MED"
    INFIRMIER = "INF"

    ROLE_CHOICES = (
        (SECRETAIRE, "Secretaire"),
        (MEDECIN, "Medecin"),
        (INFIRMIER, "Infirmier"),
    )

    MONSIEUR = "Mr"
    DOCTEUR = "Dr"
    MADAME = "Mme"

    TITRE = (
        (MONSIEUR, "Monsieur"),
        (DOCTEUR, "Docteur"),
        (MADAME, "Madame"),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE)

    # if user is a secretaire, user is linked with many surgeons to edit and add patients !!!
    praticiens_lies = models.ManyToManyField(
        User, related_name="%(app_label)s_%(class)s_praticiens_lies", blank=True
    )

    # environnement whom the user belongs to ?
    zkf_environnement_user = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT, null=True, blank=True
    )
    # environnements lies who has autorization to access the data:
    zkf_environnement_lie = models.ManyToManyField(
        "accounts.Environnement",
        blank=True,
        related_name="%(app_label)s_%(class)s_environnement_lie",
    )
    keycloak_id = models.CharField(max_length=200, blank=True)

    secretaire = models.CharField(max_length=200, blank=True)
    secretariat = models.ManyToManyField("accounts.Secretariat", blank=True)
    # which spe the user has access to ?
    specialite_profile = models.ManyToManyField(Specialite)
    # specialite which is mainly used by the user !
    specialite_active = models.ForeignKey(
        Specialite,
        on_delete=models.PROTECT,
        related_name="%(app_label)s_%(class)s_specialite_active",
        null=True,
    )

    # service the user belongs to...
    service = models.ManyToManyField(Service)
    # sites the user operates in...
    site = models.ManyToManyField(Site)
    # Groupe de facturation: obligatoire !
    groupe_facturation = models.ManyToManyField(GroupeFacturation, blank=True)

    # entete:
    entete = models.ForeignKey(
        "accounts.EnteteDocument", on_delete=models.PROTECT, null=True, blank=True
    )

    # role : quel est la fonction/poste de l'utilisateur (secretaire, medecin, infirmière, etudiant...)
    role = models.CharField(
        max_length=3,
        blank=True,
        choices=ROLE_CHOICES,
        default=MEDECIN,
    )
    titre = models.CharField(
        max_length=90,
        blank=True,
        choices=TITRE,
        default=DOCTEUR,
    )

    conventionnement = models.ForeignKey(
        "ccam.R_tb_23", on_delete=models.PROTECT, blank=True, null=True
    )
    activite_ccam = models.ForeignKey(
        "ccam.R_activite", on_delete=models.PROTECT, blank=True, null=True
    )
    modificateurs_default = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text="Modificateurs par défaut pour les actes CCAM: lettres séparées par une virgule. Ex: J,K ",
    )

    rpps = models.BigIntegerField(blank=True, null=True)
    annuaire_sante_verification = models.DateTimeField(null=True, blank=True)
    annuaire_sante_last_attempt = models.DateTimeField(null=True, blank=True)
    entete_ordonnance = models.TextField(null=False, blank=True)

    last_seen = models.DateTimeField(auto_now_add=False, blank=True, null=True)

    def __str__(self):
        return f"{self.user.username} profile"

    def titre_name(self):
        return f"{self.titre}. {self.user.last_name}"

    # A voir pour quel utilisation ces lignes de code...!
    @receiver(post_save, sender=User)
    def create_user_profile(sender, instance, created, **kwargs):
        if created:
            Profile.objects.create(user=instance)

    @receiver(post_save, sender=User)
    def save_user_profile(sender, instance, **kwargs):
        instance.profile.save()

    # Add these two new signal receivers for Preferences_user
    @receiver(post_save, sender=User)
    def create_user_preferences(sender, instance, created, **kwargs):
        if created:
            Preferences_user.objects.create(user=instance)

    @receiver(post_save, sender=User)
    def save_user_preferences(sender, instance, **kwargs):
        # Check if preferences_user exists before trying to save
        # This handles cases where it might not exist yet if the signal runs out of order
        # or if the user was created before this signal was implemented.
        if hasattr(instance, "preferences_user"):
            instance.preferences_user.save()
        else:
            # Optionally create it here if it doesn't exist, though the create signal should handle it
            Preferences_user.objects.get_or_create(user=instance)

    def related_users(self):
        # get secretaire...->personne liée autre que celles déclarées !
        users_praticiens = self.user.accounts_profile_praticiens_lies.all()
        # filter user by praticiens_lies
        users = User.objects.all().filter(profile__in=users_praticiens)
        # combine with users liés
        users_lies = self.praticiens_lies.all()
        users = users.union(users_lies)

        if self.user not in users:
            users = users.union(User.objects.filter(id=self.user.id))

        return users

    # get operations en attente:
    # fonction to return if operation is "a programmer":
    def a_programmer_count(self):
        # Import in function to avoid circular import conflict
        # https://stackoverflow.com/questions/********/django-getting-models-with-apps-get-model-on-models-py
        from operation.models import Operation

        operations = Operation.objects.filter(
            operateur_new__zkf_user__in=[self.user], a_programmer=True, deleted=False
        ).count()
        return operations


class ProfileAdministratif(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE)


"""
Intervenant:
-peut être rattaché à un utilisateur (qui lui est unique +++)
-est unique dans un environnement
-associé à sites et service
-spé principales et secondaires
-peut (à terme devra) être relié à un contact
"""


class Intervenant(Auditable):
    class Meta:
        unique_together = ["zkf_environnement", "nom", "prenom"]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement",
        on_delete=models.PROTECT,
    )
    zkf_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    zkf_contact = models.ForeignKey(
        "contact.Contact", on_delete=models.PROTECT, null=True, blank=True
    )
    # Lier à annuaire ? -> a la creation: rechercher dans annuaire et lier automatiquement ?!
    titre = models.CharField(max_length=256, null=True, blank=True)
    nom = models.CharField(max_length=256, null=True, blank=True)
    prenom = models.CharField(max_length=256, null=True, blank=True)
    specialite_principale = models.ForeignKey(
        Specialite,
        on_delete=models.PROTECT,
        related_name="specialite_principale",
        null=True,
        blank=True,
    )
    specialite_secondaire = models.ManyToManyField(
        Specialite, related_name="specialite_secondaire", blank=True
    )
    service = models.ManyToManyField(Service, blank=True)
    site = models.ManyToManyField(Site, blank=True)
    is_active = models.BooleanField(default=True)
    groupe = models.ManyToManyField("auth.Group", blank=True)

    date_debut = models.DateField(null=True, blank=True)
    date_fin = models.DateField(null=True, blank=True)
    remplacant = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.nom}  {self.prenom}"

    def nom_politesse(self):
        """Return the polite name of the intervenant."""
        if self.titre:
            return f"{self.titre} {self.nom}"
        return f"{self.nom} {self.prenom}"


class Absence(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_user = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
    )
    date_heure_start = models.DateTimeField(blank=False)
    date_heure_end = models.DateTimeField(blank=False)
    motif = models.CharField(max_length=500, blank=False)


class RendezVous(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_user = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
    )
    date_heure_debut = models.DateTimeField(blank=False)
    date_heure_fin = models.DateTimeField(blank=False)
    motif = models.CharField(max_length=500, blank=False)


class PerformanceMachineLearning(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_user = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
    )
    nom_classifier = models.CharField(max_length=256, null=True, blank=True)
    score_classifier = models.TextField(null=True, blank=True)
    date_analyse = models.DateTimeField(auto_now_add=True)
