from datetime import timedelta as td
from django.utils import timezone
from django.utils.timezone import localtime

from django.contrib.auth.signals import user_logged_in  # user logs in

from .models import Profile

from django.contrib.auth import logout

# https://stackoverflow.com/questions/953879/how-to-force-a-user-logout-in-django
# force a user to re-log in if last seen is >> 30'


class ForceLogoutMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # One-time configuration and initialization.

    def __call__(self, request):
        # if user logs in:
        # https://stackoverflow.com/questions/36263857/calling-a-method-when-the-user-login-django
        def set_status_online(sender, user, request, **kwargs):
            request.user.profile.last_seen = timezone.now()
            request.user.save()

        user_logged_in.connect(set_status_online)

        # Code to be executed for each request before
        # the view (and later middleware) are called.
        if request.user.is_authenticated and (
            localtime(request.user.profile.last_seen)
        ) < (timezone.now() - td(minutes=30)):
            request.user.profile.last_seen = timezone.now()
            request.user.save()
            logout(request)
        response = self.get_response(request)

        return response


# https://github.com/kdahlhaus/django-last-seen/blob/master/last_seen/models.py
# https://stackoverflow.com/questions/********/django-get-last-user-visit-date
class AccountLoginMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.user.is_authenticated:
            # last_activity = request.session.get("last-activity")
            # print (last_activity)
            # print (settings.LOGIN_INTERVAL)

            # too_old_time = timezone.now() - td(seconds=settings.LOGIN_INTERVAL)
            # if not last_activity or parse(last_activity) < too_old_time:
            # print ('vue !')
            Profile.objects.filter(user__username=request.user.username).update(
                last_seen=timezone.now(),
            )
            # login_count=F('login_count') + 1)

            request.session["last-activity"] = timezone.now().isoformat()

            # print (request.session['last-activity'] )
            # print ('session')

        response = self.get_response(request)

        return response
