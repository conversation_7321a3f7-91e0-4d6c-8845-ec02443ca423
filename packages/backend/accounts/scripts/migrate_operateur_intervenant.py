from operation.models import Operation
from accounts.models import Intervenant

"""
<PERSON><PERSON><PERSON> to migrate from user as operateur, anesth and aide operatoire to intervenant !
to be used once !

"""


def migrate_fk_intervenant():
    # get all operations
    operations = Operation.objects.all()
    i = 0
    for operation in operations:
        # get the operateur
        operateurs = operation.operateurs.all()
        # get the aide operatoires
        aide_operatoires = operation.aide_operatoire.all()
        # get the anesthesiste
        anesthesiste = operation.anesthesiste

        # check if intervenant exists
        if operateurs:
            for user in operateurs:
                try:
                    intervenant = Intervenant.objects.get(
                        nom=user.last_name, prenom=user.first_name
                    )
                    operation.operateur_new.add(intervenant)
                except Exception as e:
                    print(e)
                    intervenant = Intervenant.objects.create(
                        specialite_principale=user.profile.specialite_active,
                        zkf_user=user,
                        nom=user.last_name,
                        prenom=user.first_name,
                        zkf_environnement=operation.zkf_patient.zkf_environnement_patient,
                    )
                    operation.operateur_new.add(intervenant)
                    for site in user.profile.site.all():
                        intervenant.site.add(site)

        if anesthesiste:
            try:
                intervenant = Intervenant.objects.get(
                    nom=anesthesiste.last_name, prenom=anesthesiste.first_name
                )
                operation.anesthesiste_new.add(intervenant)
            except Exception as e:
                print(e)
                intervenant = Intervenant.objects.create(
                    specialite_principale=anesthesiste.profile.specialite_active,
                    zkf_user=anesthesiste,
                    nom=anesthesiste.last_name,
                    prenom=anesthesiste.first_name,
                    zkf_environnement=operation.zkf_patient.zkf_environnement_patient,
                )
                operation.anesthesiste_new.add(intervenant)
                for site in anesthesiste.profile.site.all():
                    intervenant.site.add(site)

        if aide_operatoires:
            for user in aide_operatoires:
                try:
                    intervenant = Intervenant.objects.get(
                        nom=user.last_name, prenom=user.first_name
                    )
                    operation.aides_operatoire.add(intervenant)
                except Exception as e:
                    print(e)
                    intervenant = Intervenant.objects.create(
                        zkf_user=user,
                        nom=user.last_name,
                        prenom=user.first_name,
                        zkf_environnement=operation.zkf_patient.zkf_environnement_patient,
                    )
                    operation.aides_operatoire.add(intervenant)
                    for site in user.profile.site.all():
                        intervenant.site.add(site)

        i += 1
        print(i)

    return "Migration done"
