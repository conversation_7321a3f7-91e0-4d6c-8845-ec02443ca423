from django.db.models import Q
import pandas as pd
import json

from consultation.models import Consultation
from operation.models import Operation


def get_consultations(start, end, user):
    qs = Consultation.objects.filter(
        consultation_date__gte=start,
        consultation_date__lte=end,
        deleted=False,
        redacteur=user,
    ).exclude(etat_consultation="annule")
    return qs


def get_operations(start, end, user):
    qs = Operation.objects.filter(
        operation_date__gte=start,
        operation_date__lte=end,
        deleted=False,
        redacteur=user,
    )
    return qs


# count number of consultations
def consultations_stat(start, end, user):
    consultations = get_consultations(start, end, user)
    df = pd.DataFrame(
        list(
            consultations.values(
                "consultation_date",
                "etat_consultation",
                "created_by__last_name",
                "created_on",
                "zkf_site__nom_site",
            )
        )
    )
    df["consultation_date"] = pd.to_datetime(df["consultation_date"], utc=True)
    df["created_on"] = pd.to_datetime(df["created_on"], utc=True)

    # group consultations by month
    # https://stackoverflow.com/questions/38792122/how-to-group-and-count-rows-by-month-and-year-using-pandas
    count = (
        df["consultation_date"]
        .groupby(df.consultation_date.dt.to_period("M"))
        .agg("count")
    )

    # aggregate by lieu
    # lieu = df['consultation_date'].groupby(df.consultation_date.dt.to_period("M"), 'zkf_site__nom_site').agg('count')
    lieu = (
        df["zkf_site__nom_site"]
        .groupby([df.consultation_date.dt.to_period("M"), df.zkf_site__nom_site])
        .agg("count")
    )
    # convert count (which is a series) to a dataframe with dates/lieu and count
    df_data = lieu.to_frame(name="count").reset_index()
    df_data = df_data.astype(str)
    # for JS use, a dataset is : date=>one site and count.
    # then, we have to convert the dataframe to Different dataset (as many as sites de consultations !)
    sites = df_data.zkf_site__nom_site.unique()
    dates = df_data.consultation_date.unique()

    # create a dict with date = key, value = list of dict (lieu=>count)
    dates_dict = {}
    for d in dates:
        dates_dict[d] = {}
        for s in sites:
            if (
                (df_data["consultation_date"] == d)
                & (df_data["zkf_site__nom_site"] == s)
            ).any():
                dates_dict[d][s] = df_data.loc[
                    (
                        (df_data["consultation_date"] == d)
                        & (df_data["zkf_site__nom_site"] == s)
                    )
                ]["count"].values[0]
            else:
                dates_dict[d][s] = 0
    lieu = []
    frames = []
    for key, value in dates_dict.items():
        lieu.append(key)
        frames.append(value)
    final_data = pd.DataFrame(frames)

    # https://stackoverflow.com/questions/34800343/python-pandas-convert-type-from-pandas-period-to-string
    dates = list((count.index.to_series().astype(str)))

    """
    Get average number of consultations for a day
    """
    cs = df.groupby(df.consultation_date).count()
    mean_cs = cs["etat_consultation"].mean()
    median_cs = cs["etat_consultation"].median()
    max_cs = cs["etat_consultation"].max()
    min_cs = cs["etat_consultation"].min()

    data = {
        "label": dates,
        "data": list(count),
        "lieu": json.dumps(final_data.to_dict(orient="list")),
        "mean": str(round(mean_cs, 2)),
        "median": str(round(median_cs, 2)),
        "max": str(max_cs),
        "min": str(min_cs),
    }

    return data


# Count number of operations
def operations_stat(start, end, user):
    operations = get_operations(start, end, user)
    df = pd.DataFrame(
        list(operations.values("operation_date", "created_by__last_name", "created_on"))
    )
    df["operation_date"] = pd.to_datetime(df["operation_date"], utc=True)
    df["created_on"] = pd.to_datetime(df["created_on"], utc=True)

    # https://stackoverflow.com/questions/38792122/how-to-group-and-count-rows-by-month-and-year-using-pandas
    count = (
        df["operation_date"].groupby(df.operation_date.dt.to_period("M")).agg("count")
    )
    # https://stackoverflow.com/questions/34800343/python-pandas-convert-type-from-pandas-period-to-string
    dates = list((count.index.to_series().astype(str)))

    data = {"label": dates, "data": list(count)}
    return data


# calculate the proportions of operations by lieu de consultation
# -> For each operation: get the last consultation lieu !
def operation_by_consultation_lieu(start, end, user):
    operations = get_operations(start, end, user)

    df = pd.DataFrame(
        list(operations.values("operation_date", "created_by__last_name", "created_on"))
    )
    df["operation_date"] = pd.to_datetime(df["operation_date"], utc=True)

    # parse operations, and get the latest consultation lieu and date
    last_consultation = []
    lieu = []
    for o in operations:
        consultation = Consultation.objects.filter(
            zkf_patient=o.zkf_patient, consultation_date__lte=o.operation_date
        )

        if consultation.count() > 0:
            consultation = consultation.latest("consultation_date")
            last_consultation.append(consultation.consultation_date)
            lieu.append(consultation.zkf_site.nom_site)
        else:
            last_consultation.append(None)
            lieu.append(None)

    df["last_consultation"] = last_consultation
    df["lieu_consultation"] = lieu

    # count number of operations in each lieu de consultation
    # https://stackoverflow.com/questions/38792122/how-to-group-and-count-rows-by-month-and-year-using-pandas
    count = (
        df["lieu_consultation"]
        .groupby([df.operation_date.dt.to_period("M"), df.lieu_consultation])
        .agg("count")
    )

    # convert count (which is a series) to a dataframe with dates/lieu and count
    df_data = count.to_frame(name="count").reset_index()
    df_data = df_data.astype(str)

    # for JS use, a dataset is : date=>one site and count.
    # then, we have to convert the dataframe to Different dataset (as many as sites de consultations !)
    sites = df_data.lieu_consultation.unique()
    dates = df_data.operation_date.unique()

    # create a dict with date = key, value = list of dict (lieu=>count)
    dates_dict = {}
    for d in dates:
        dates_dict[d] = {}
        for s in sites:
            if (
                (df_data["operation_date"] == d) & (df_data["lieu_consultation"] == s)
            ).any():
                dates_dict[d][s] = df_data.loc[
                    (
                        (df_data["operation_date"] == d)
                        & (df_data["lieu_consultation"] == s)
                    )
                ]["count"].values[0]
            else:
                dates_dict[d][s] = 0
    lieu = []
    frames = []
    for key, value in dates_dict.items():
        lieu.append(key)
        frames.append(value)
    final_data = pd.DataFrame(frames)

    # https://stackoverflow.com/questions/34800343/python-pandas-convert-type-from-pandas-period-to-string
    # convert index to a list of dates => labels
    # dates = list((dates.index.to_series().astype(str)))

    # dates is a numpy nd array -> convert it to list
    dates = dates.tolist()

    data = {"label": dates, "data": json.dumps(final_data.to_dict(orient="list"))}

    return data


# mean delai entre consultation et operation
# if delai <= 1 semain = >urgence relative
# if no consultation ou <=48h: urgence vital
def delai_operation_consultation(start, end, user):
    operations = get_operations(start, end, user)
    df = pd.DataFrame(
        list(operations.values("operation_date", "created_by__last_name", "created_on"))
    )
    df["operation_date"] = pd.to_datetime(df["operation_date"], utc=True)

    # parse operations, and get the latest consultation date

    # add it to the dataframe

    return
