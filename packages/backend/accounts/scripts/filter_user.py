from django.contrib.auth.models import User
from accounts.models import CustomUser
from collections import defaultdict
from accounts.scripts.filter_intervenant import get_site


def get_all_praticiens_lies(user):
    praticiens_environnements_lies = User.objects.filter(
        profile__zkf_environnement_lie__in=[user.profile.zkf_environnement_user]
    ).distinct()
    praticiens_lies = user.profile.praticiens_lies.all()
    results = praticiens_lies.union(praticiens_environnements_lies)
    if results.count() == 0:
        results = User.objects.filter(id=user.id)
    return results


def get_users_group_filtered(user):
    linked_users = CustomUser.objects.filter(
        profile__zkf_environnement_user=user.profile.zkf_environnement_user,
        profile__deleted=False,
    )
    linked_users = linked_users.filter(user_groups__in=user.groups.all())
    linked_users = linked_users.distinct()  # make the querylist with unique values
    return linked_users


def get_user_secretaire(user, operation=None, hospitalisation=None, consultation=None):
    secretaires = User.objects.filter(
        profile__zkf_environnement_user=user.profile.zkf_environnement_user,
        profile__deleted=False,
        is_active=True,
    )
    secretaires = secretaires.filter(groups__id__in=[2])

    return secretaires


def get_user_arc(user, operation=None, hospitalisation=None, consultation=None):
    arc = User.objects.filter(
        profile__zkf_environnement_user=user.profile.zkf_environnement_user,
        profile__deleted=False,
        is_active=True,
    )
    # site = list(user.profile.site.all().values("nom_site"))
    arc = arc.filter(groups__id__in=[3])
    # arc = arc.filter(profile__site__nom_site__in = site )

    return arc


def get_operateur(user, operation=None, hospitalisation=None, consultation=None):
    site = get_site(operation, hospitalisation, consultation)

    # Use Custom User which subclass User -> return full name !
    # https://stackoverflow.com/questions/7510849/django-how-to-display-users-full-name-in-filteredselectmultiple
    operateurs = CustomUser.objects.all()
    operateurs = operateurs.filter(
        profile__zkf_environnement_user=user.profile.zkf_environnement_user
    )
    operateurs = operateurs.filter(
        profile__specialite_profile__spe__in=user.profile.specialite_profile.all().values(
            "spe"
        )
    )
    operateurs = operateurs.filter(
        profile__deleted=False
    )  # .filter(profile__role="MED")
    # operateurs = operateurs.filter(
    #     profile__site__nom_site__in=user.profile.site.all().values("nom_site")
    # )
    # filter by site
    if site:
        operateurs = operateurs.filter(profile__site__nom_site__in=[site])
    operateurs = operateurs.filter(groups__name__in=["medecin"])
    operateurs = operateurs.filter(is_active=True)

    operateurs = operateurs.distinct()  # make the querylist with unique values

    return operateurs


#
#
def get_anesthesiste(user):
    anesthesistes = CustomUser.objects.filter(
        profile__specialite_profile__spe="Anesthesie"
    ).filter(profile__deleted=False)
    anesthesistes = anesthesistes.filter(
        profile__zkf_environnement_user=user.profile.zkf_environnement_user
    )
    anesthesistes = anesthesistes.filter(
        profile__site__nom_site__in=user.profile.site.all().values("nom_site")
    )
    anesthesistes = anesthesistes.filter(groups__name__in=["medecin"])
    anesthesistes = anesthesistes.distinct()  # make the querylist with unique values
    return anesthesistes


def get_aide_operatoire(user):
    aide_operatoire = CustomUser.objects.filter(
        profile__zkf_environnement_user=user.profile.zkf_environnement_user,
        profile__deleted=False,
    )
    aide_operatoire = aide_operatoire.filter(groups__name__in=["Infirmier"])
    aide_operatoire = aide_operatoire.filter(
        profile__site__nom_site__in=user.profile.site.all().values("nom_site")
    )
    aide_operatoire = (
        aide_operatoire.distinct()
    )  # make the querylist with unique values
    return aide_operatoire


# retrieve linked users to a user by site !
# user = current logged in user
def get_users_env(user):
    linked_users = CustomUser.objects.filter(
        profile__zkf_environnement_user=user.profile.zkf_environnement_user,
        profile__deleted=False,
    )
    # linked_users = linked_users.filter(profile__site__nom_site__in = user.profile.site.all().values('nom_site'))
    linked_users = linked_users.distinct()  # make the querylist with unique values
    return linked_users


# retrieve linked users to a user, ordered by role !
# user = current logged in user
def get_linked_users_by_role(user):
    linked_users = get_users_env(user)
    # https: // stackoverflow.com / questions / 7811556 / how - do - i - convert - a - django - queryset - into - list - of - dicts
    # get user group:
    # user.groups.all()
    # group: https://stackoverflow.com/questions/57071717/how-to-fill-nested-dictionary-with-values-of-a-queryset-as-a-list-in-django

    result = defaultdict(list)
    for user in linked_users:
        groups = user.groups.all()
        for group in groups:
            result[group.name].append(user)

    return dict(result)


# retrieve linked users to a user, ordered by site !
# user = current logged in user
def get_linked_users_by_site(user):
    linked_users = get_users_env(user)
    # https: // stackoverflow.com / questions / 7811556 / how - do - i - convert - a - django - queryset - into - list - of - dicts
    # group: https://stackoverflow.com/questions/57071717/how-to-fill-nested-dictionary-with-values-of-a-queryset-as-a-list-in-django

    result = defaultdict(list)
    for user in linked_users:
        # get user sites:
        sites = user.profile.site.all()
        for site in sites:
            result[site].append(user)

    return dict(result)
