from accounts.models import Intervenant
from operation.models import Operation
from hospitalisation.models import Hospitalisation
from consultation.models import Consultation
from django.db.models import Q
import datetime


def get_site(operation=None, hospitalisation=None, consultation=None):
    site = None
    if isinstance(operation, str):
        operation = str(operation)
    if isinstance(operation, Operation):
        operation = operation.id

    if operation is not None:
        operation = Operation.objects.get(id=operation)
        site = (
            operation.zkf_hospitalisation.service_hospitalisation.site_service.nom_site
        )
    if hospitalisation is not None:
        hospitalisation = Hospitalisation.objects.get(id=hospitalisation)
        site = hospitalisation.service_hospitalisation.site_service.nom_site
    if consultation is not None:
        consultation = Consultation.objects.get(id=consultation)
        site = consultation.zkf_site.nom_site
    return site


def get_intervenant_operateur(
    user, intervenants=None, operation=None, hospitalisation=None, consultation=None
):

    site = get_site(operation, hospitalisation, consultation)

    if intervenants is not None:
        operateurs = intervenants
    elif intervenants is None:
        operateurs = Intervenant.objects.filter(
            zkf_environnement=user.profile.zkf_environnement_user,
            is_active=True,
            deleted=False,
        )

    operateurs = operateurs.filter(specialite_principale=user.profile.specialite_active)
    operateurs = operateurs.filter(
        site__nom_site__in=user.profile.site.all().values("nom_site")
    )
    # check if remplacant/end date
    # check if there is no end date for the contract or if the end date is in the future or intervenant is remplacant
    operateurs = operateurs.filter(
        Q(date_fin__isnull=True) | Q(date_fin__gte=datetime.datetime.now())
    )
    operateurs = operateurs.exclude(remplacant=True)

    if site:
        operateurs = operateurs.filter(site__nom_site__in=[site])

    operateurs = operateurs.distinct()  # make the querylist with unique values

    return operateurs


def get_intervenant_anesthesiste(
    user, intervenants=None, operation=None, hospitalisation=None, consultation=None
):
    site = get_site(operation, hospitalisation, consultation)

    if intervenants is not None:
        anesthesistes = intervenants
    else:
        anesthesistes = Intervenant.objects.filter(
            zkf_environnement=user.profile.zkf_environnement_user,
            is_active=True,
            deleted=False,
        )
    anesthesistes = anesthesistes.filter(
        specialite_principale__spe="Anesthesie",
    )
    # check if remplacant/end date
    # check if there is no end date for the contract or if the end date is in the future or intervenant is remplacant
    anesthesistes = anesthesistes.filter(
        Q(date_fin__isnull=True) | Q(date_fin__gte=datetime.datetime.now())
    )
    anesthesistes = anesthesistes.exclude(remplacant=True)

    if site:
        anesthesistes = anesthesistes.filter(site__nom_site__in=[site])
    # anesthesistes = anesthesistes.filter(groups__name__in=['medecin'], )
    anesthesistes = anesthesistes.distinct()  # make the querylist with unique values

    return anesthesistes


def get_intervenant_aide_operatoire(
    user, intervenants=None, operation=None, hospitalisation=None, consultation=None
):
    site = get_site(operation, hospitalisation, consultation)
    if isinstance(operation, str):
        operation = str(operation)
    if isinstance(operation, Operation):
        operation = operation.id

    # Initialize aide_operatoire based on whether intervenants is provided
    if intervenants is not None:
        aide_operatoire = intervenants
    else:
        aide_operatoire = Intervenant.objects.filter(
            zkf_environnement=user.profile.zkf_environnement_user,
            deleted=False,
            is_active=True,
        )

    # check if remplacant/end date
    # check if there is no end date for the contract or if the end date is in the future or intervenant is remplacant
    aide_operatoire = aide_operatoire.filter(
        Q(date_fin__isnull=True) | Q(date_fin__gte=datetime.datetime.now())
    )
    aide_operatoire = aide_operatoire.exclude(remplacant=True)

    if site is not None:
        aide_operatoire = aide_operatoire.filter(site__nom_site__in=[site])

    aide_operatoire = aide_operatoire.filter(
        groupe__name__in=["IBODE"],
    )
    aide_operatoire = aide_operatoire.distinct()
    return aide_operatoire
