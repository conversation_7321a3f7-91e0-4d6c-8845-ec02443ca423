from django.db.models import Q
import pandas as pd
from typing import cast

from consultation.models import Consultation
from operation.models import Operation


def report_consultations(start, end, user):
    consultations = Consultation.objects.filter(
        consultation_date__gte=start,
        consultation_date__lte=end,
        deleted=False,
        redacteur=user,
    )
    df = pd.DataFrame(
        list(
            consultations.values(
                "consultation_date",
                "etat_consultation",
                "created_by__last_name",
                "created_on",
                "zkf_site__nom_site",
            )
        )
    )
    if not df.empty:
        # Use the pandas API correctly
        df["consultation_date"] = pd.to_datetime(df["consultation_date"], utc=True)
        # df['created_on'] = pd.to_datetime(df['created_on'], utc=True)

        df["delai_creation_consultation"] = (
            df["consultation_date"] - df["created_on"]
        ).dt.days

        # cross_tab_lieu = pd.crosstab([df.created_by__last_name,  df.etat_consultation, ], df.zkf_site__nom_site, margins=True, )
        # cross_tabl_lieu = pd.pivot_table(df, index=['created_by__last_name','consultation_date' ], values='delai_creation_consultation, etat_consultation, zkf_site__nom_site', aggfunc='mean')

        cross_tab_lieu = pd.crosstab(
            [
                df.etat_consultation,
            ],
            df.zkf_site__nom_site,
            margins=True,
        )
        return cross_tab_lieu
    else:
        return "Pas de consultations"


def report_operations(start, end, user):
    operations_queryset = Operation.objects.filter(
        operation_date__gte=start,
        operation_date__lte=end,
        deleted=False,
        operateurs__in=[user],
    )
    # print (operations)
    operations_list = operations_queryset.values(
        "id",
        "operation_date",
        "zkf_hospitalisation__service_hospitalisation__site_service__nom_site",
    )
    operations_list = list(operations_list)
    # display complications de chaque operation:
    for o in operations_queryset:
        # check if there is a table for complications post op in hospitalisation:
        specialite = o.zkf_hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))
        model_specialite = "ComplicationPostOperatoire" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        if hasattr(o, str(model_specialite.lower())):
            # print(getattr(o, str(model_specialite.lower())))
            complication = getattr(o, str(model_specialite.lower()))
            # add complication to the operations_list
            for sub in operations_list:
                if sub["id"] == o.id:
                    sub["complication"] = complication.complication
                    break

    df = pd.DataFrame(operations_list)
    # rename columns
    for col in df.columns:
        col_name = col.split("__")[-1]
        # col_name = col_name.split('_')
        # col_name = ' '.join(col_name)
        # print(col_name)
        df.rename(columns={col: col_name}, inplace=True)

    # pd.DataFrame(list(
    #     operations.values('operation_date', 'complication', 'zkf_hospitalisation__service_hospitalisation__site_service__nom_site' )))
    # print (df)
    if not df.empty:
        # df['operation_date'] = pd.to_datetime(df['operation_date'], utc=True)
        # df['created_on'] = pd.to_datetime(df['created_on'], utc=True)

        # df['delai_creation_consultation'] = (df['consultation_date'] - df['created_on']).dt.days

        # cross_tab_lieu = pd.crosstab([df.created_by__last_name,  df.etat_consultation, ], df.zkf_site__nom_site, margins=True, )
        # cross_tabl_lieu = pd.pivot_table(df, index=['created_by__last_name','consultation_date' ], values='delai_creation_consultation, etat_consultation, zkf_site__nom_site', aggfunc='mean')

        # https://stackoverflow.com/questions/33303314/confusing-behaviour-of-pandas-crosstab-function-with-dataframe-containing-nan
        # fillna to replace None by "missing"
        cross_tab_lieu = pd.crosstab(
            [
                df.complication.fillna("missing"),
            ],
            df.nom_site,
            margins=True,
        )
        # print(cross_tab_lieu)

        return cross_tab_lieu

    else:
        return "Pas d'operations"
