# Create your tasks here
from __future__ import absolute_import, unicode_literals
from celery import shared_task
from django.contrib.auth.models import User
from django.template.loader import render_to_string

from django.core.mail import EmailMultiAlternatives


from datetime import date, timedelta

from .scripts.user_report import report_consultations, report_operations


@shared_task
def report_stat():
    today = date.today()
    start = today - timedelta(days=today.weekday(), weeks=1)
    end = start + timedelta(days=6)

    users = User.objects.all()

    for u in users:
        # print(u)
        try:
            report_consultation = report_consultations(
                start=start, end=end, user=u
            )  # .to_html(escape=False)
            report_operation = report_operations(
                start=start, end=end, user=u
            )  # .to_html(escape=False)

            if u.preferences_user.report_hebdo:
                subject, from_email, to = (
                    "hello",
                    "<EMAIL>",
                    u.email,
                )
                text_content = "Hebdo consultations report"
                # insert template : "dashboard/statistiques/reporting_hebdo.html"
                html_content = render_to_string(
                    "dashboard/statistiques/reporting_hebdo.html",
                    {
                        "consultations_stat": report_consultation,
                        "operations_stat": report_operation,
                    },
                )  # report_consultations(start, end, u).to_html(escape = False)
                msg = EmailMultiAlternatives(subject, text_content, from_email, [to])
                msg.attach_alternative(html_content, "text/html")
                msg.send()
                # print (html_content)
        except Exception as error:
            print(error)

    return
