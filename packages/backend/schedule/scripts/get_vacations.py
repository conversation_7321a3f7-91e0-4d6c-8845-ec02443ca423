import datetime
from schedule.models import Vacation
from django.db.models import Q


""" with a given date, get the vacations of the user """


def get_vacations(user, start_date=None, end_date=None):

    # vacation is defined by a type, start date and end date and a week day + even and odd week, and a start and end date
    """
    jour_semaine = models.PositiveSmallIntegerField(help_text="Lundi:1 Dimanche:7")
    debut_vacation = models.TimeField(
        auto_now=False, auto_now_add=False, null=False, blank=False
    )
    fin_vacation = models.TimeField(
        auto_now=False, auto_now_add=False, null=False, blank=False
    )
    REPETITION = ((PAIRE, "Paire"), (IMPAIRE, "Impaire"), (TOUTES, "Toutes"))
    repetition_semaine = models.CharField(
        max_length=15, choices=REPETITION, default=TOUTES, null=False, blank=False
    )

    # start and end of the recurrency of the vacation
    debut_periode = models.DateField(
        auto_now=False, auto_now_add=False, null=False, blank=False
    )
    fin_periode = models.DateField(
        auto_now=False, auto_now_add=False, null=True, blank=True
    ) => can be null if there is no end date
    """

    vacations = Vacation.objects.filter(
        zkf_user=user,
        debut_periode__lte=start_date,
    ).filter(Q(fin_periode__gte=end_date) | Q(fin_periode__isnull=True))
    # 3/ then we can convert the vacations to a day and return a list of dictionaries
    # [ {date: {type: "operation/consultation", start: "08:00", end: "18:00"}}, ... ]
    vacations_list = []
    # Go through each day in the interval
    current_date = start_date
    # print (f"Getting vacations for user: {user} from {start_date.isoformat()} to {end_date.isoformat()}", vacations)
    while current_date <= end_date:

        week_number = current_date.isocalendar()[1]
        weekday = current_date.isoweekday()  # Monday=1 ... Sunday=7

        for vac in vacations:
            # Check day match
            if vac.jour_semaine != weekday:
                continue

            # Check repetition logic
            if vac.repetition_semaine == Vacation.PAIRE and week_number % 2 != 0:
                continue
            if vac.repetition_semaine == Vacation.IMPAIRE and week_number % 2 != 1:
                continue

            # Check vacation period bounds
            if not (vac.debut_periode <= current_date <= (vac.fin_periode or end_date)):
                continue

            vacations_list.append(
                {
                    "date": current_date.isoformat(),
                    "type": vac.type_vacation.lower(),  # or use display: vac.get_type_vacation_display()
                    "start_time": vac.debut_vacation.strftime("%H:%M"),
                    "end_time": vac.fin_vacation.strftime("%H:%M"),
                    # Garder aussi les anciennes clés pour compatibilité
                    "start": vac.debut_vacation.strftime("%H:%M"),
                    "end": vac.fin_vacation.strftime("%H:%M"),
                }
            )

        current_date += datetime.timedelta(days=1)

    return vacations_list
