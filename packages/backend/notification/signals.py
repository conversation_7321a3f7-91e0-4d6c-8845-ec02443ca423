from django import dispatch
from django.dispatch import receiver
from notification.models import Notification
from django.utils.translation import gettext as _
from datetime import datetime
import uuid


def check_notification_double(items):
    """
    Check if a notification already exists in the database
    """
    obj_id_array = [item.obj_object_id for item in items]
    recipients = [item.zkf_recipient for item in items]

    notification = Notification.objects.filter(
        obj_object_id__in=obj_id_array, zkf_recipient__in=recipients
    )

    if notification.exists():
        notification.update(created_on=datetime.now())
        for n in notification:
            duplicates = [
                x
                for x in items
                if x.obj_object_id == n.obj_object_id
                and x.zkf_recipient == n.zkf_recipient
                and x.nf_type == n.nf_type
            ]
            for d in duplicates:
                items.remove(d)

    return items


notification_signal = dispatch.Signal()
#     providing_args=[
#         "recipient",
#         "recipient_list",
#         "actor",
#         "actor_text",
#         "actor_url",
#         "verb",
#         "description",
#         "nf_type",
#         "target",
#         "target_text",
#         "target_url",
#         "obj",
#         "obj_text",
#         "obj_url",
#         "extra",
#     ]
# )


@receiver(notification_signal, dispatch_uid="notify_user")
def notifier(sender, **kwargs):
    # recipients = kwargs.pop("recipients", None)

    recipients = kwargs.pop("recipients", None)
    zkf_environnement = sender.profile.zkf_environnement_user
    group_uuid = uuid.uuid4()
    flagged = kwargs.pop("flagged", False)
    level = kwargs.pop("level", "info")
    doc_link = kwargs.pop("doc_link", None)

    verb = kwargs.pop("verb", None)
    title = kwargs.pop("title", None)
    description = kwargs.pop("description", None)
    nf_type = kwargs.pop("nf_type", "default")

    # actor = kwargs.pop("actor", None)
    actor_text = kwargs.pop("actor_text", None)
    actor_url = kwargs.pop("actor_url", None)

    target = kwargs.pop("target", None)
    target_text = kwargs.pop("target_text", None)
    target_url = kwargs.pop("target_url", None)

    obj = kwargs.pop("obj_content_object", None)
    obj_text = kwargs.pop("obj_text", None)
    obj_url = kwargs.pop("obj_url", None)

    extra = kwargs.pop("extra", None)

    # if recipients and recipient_list:
    #     raise TypeError(
    #         _(
    #             "You must specify either a single recipient or a list"
    #             " of recipients, not both."
    #         )
    #     )
    # elif not recipients and not recipient_list:
    #     raise TypeError(_("You must specify the recipient of the notification."))

    # check if recipients is a list and is not empty:
    if recipients and not isinstance(recipients, list):
        raise TypeError(
            _("Supplied recipient is not an instance of list, or is empty.")
        )

    # if not actor and not actor_text:
    #     raise TypeError(_("Actor not specified."))

    if not verb:
        raise TypeError(_("Verb not specified."))

    # if recipient_list and not isinstance(recipient_list, list):
    #     raise TypeError(_("Supplied recipient is not an instance of list."))

    # convert recipient to a list for simplification purpose
    # if recipients and not recipient_list:
    #     recipient_list = [recipients]
    # if recipient:
    #
    #     notification = Notification(
    #         recipient=recipient,
    #         verb=verb,
    #         description=description,
    #         nf_type=nf_type,
    #         # actor=actor,
    #         actor_text=actor_text,
    #         actor_url_text=actor_url,
    #         target_content_object=target,
    #         target_text=target_text,
    #         target_url_text=target_url,
    #         obj_content_object=obj,
    #         obj_text=obj_text,
    #         obj_url_text=obj_url,
    #         extra=extra,
    #     )
    #     saved_notification = notification.save()
    # else:

    # routes for details view
    routes = {
        "operation": "api/operation/",
        "hospitalisation": "api/hospitalisation/",
        "compterenducorrespondant": "api/document/",
        # staff...
    }

    # if recipients is not empty:
    if recipients:
        notifications = []

        for recipient in recipients:
            notifications.append(
                Notification(
                    # recipients=recipients,
                    zkf_recipient=recipient,
                    zkf_environnement=zkf_environnement,
                    group_id=group_uuid,
                    verb=verb,
                    title=title,
                    description=description,
                    nf_type=nf_type,
                    flagged=flagged,
                    level=level,
                    doc_link=doc_link,
                    # actor=actor,
                    actor_text=actor_text,
                    actor_url_text=actor_url,
                    target_content_object=target,
                    target_text=target_text,
                    target_url_text=target_url,
                    obj_content_object=obj,
                    obj_text=obj_text,
                    obj_url_text=obj_url,
                    extra=extra,
                    route_view_object=(
                        # don't forget to add the route for the new object in the dict on top
                        (routes.get(obj.__class__.__name__.lower(), "") + str(obj.id))
                        if obj
                        and routes.get(obj.__class__.__name__.lower()) is not None
                        else None
                    ),
                )
            )

        # # Check for doublon notifications
        notifications_updated = check_notification_double(notifications)
        #
        saved_notification = Notification.objects.bulk_create(notifications_updated)

        # create recipients manytomany:
        for notification in saved_notification:
            notification.recipients.add(*recipients)

        return saved_notification

    else:
        return None
