import logging
import os
from django.http import HttpResponse
from django.conf import settings
from django.contrib.auth.models import User
from notification.signals import notification_signal
import logging
from typing import Any, List, Optional, Dict
from dataclasses import dataclass

from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.core.mail import EmailMessage
from django.db.models import QuerySet

from accounts.models import NotificationEvents, NotificationChannels
from django.core.mail import EmailMessage
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from document.models.audio_recording import AudioRecording
from document.models.compte_rendu_correspondant import CompteRenduCorrespondant
from notification.models import Notification
from notification.signals import notification_signal
from operation.models import Operation, Protocole
from patient.models import Patient

User = get_user_model()
logger = logging.getLogger(__name__)


# verifie que: users_preferences est un dictionnaire et est vide
def check_preference(channel: str, event: str, user_preferences: dict) -> bool:
    # Si user_preferences est vide, retourne True
    if not user_preferences and type(user_preferences) is dict:
        return True

    # Vérifie si le channel existe et a des préférences d'événements
    if channel in user_preferences and "events" in user_preferences.get(channel, {}):
        # Si l'événement existe et est défini à False, retourne False
        if event in user_preferences[channel]["events"]:
            return user_preferences[channel]["events"][event]

    # Dans tous les autres cas, retourne True
    return True

def send_email(
    subject: str,
    text: str,
    recipients: List[User],
    from_email: str,
    template: str,
    object: Any = None,
    ) -> None:

    if template:
        # Render the email template with context
        text = render_to_string(
            template,
            set_context(object),
        )
    
    # Filter out empty email addresses
    recipients = [user.email for user in recipients if user.email]
    
    if recipients:
        try:
            # Prepare the email message
            msg = EmailMultiAlternatives(
                subject, "", from_email, recipients
            )

            msg.attach_alternative(text, "text/html")
            msg.send()
        except Exception as e:
            logger.error(
                f"Error sending email: {e}. Subject: {subject}, Recipients: {recipients}"
            )
            # Optionally, you can raise an exception or handle it as needed
            return HttpResponse(
                f"Failed to send email: {e}",
                status=500,
            )
    
    # return success status
    return HttpResponse(
        "Email sent successfully.",
        status=200,
    )

def set_context(object: Any) -> Dict[str, Any]:
    """
    Set the context for the notification.
    Args:
        object (Any): The object to set the context for.
    Returns:
        dict: Dictionary of context parameters.
    """
    context = dict()
    context["object"] = object
    context["logo"] = os.path.join(
        str(settings.BASE_DIR), "assets/icons/meiflow-logo.png"
    )
    # Add any additional context parameters here
    return context


def filter_user(
    users: list,
) -> dict:
    """
        Remove inactive users from the list of users.
        Filter users based on their notification preferences.
        Args:
            users (list): List of users to filter.
            sms (bool): Include users who want SMS notifications.
            push (bool): Include users who want push notifications.
            email (bool): Include users who want email notifications.
        Returns:
            dict: Dictionary of filtered users:
            {
                channel: {
                    event: [user1, user2, ...],
                    ...
                }
            }
    -"""
    # remove inactive users
    users = [user for user in users if user.is_active] if users else []

    # Filter users based on their notification preferences

    preferences = {}

    # Pour chaque type d'événement
    for channel in NotificationChannels.all_keys():
        preferences[channel] = {}

        # Pour chaque canal
        for event in NotificationEvents.all_keys():
            preferences[channel][event] = []
            # Parse users
            for user in users:
                # check if user has preferences
                if hasattr(user, "preferences_user"):
                    user_preferences = user.preferences_user.preference_notification
                    if check_preference(channel, event, user_preferences):
                        # Si l'utilisateur a choisi de recevoir des notifications par ce canal
                        if channel == "email":
                            # check if user has email
                            if user.email:
                                preferences[channel][event].append(user)
                        else:
                            # Pour les autres canaux, on ajoute directement l'utilisateur
                            preferences[channel][event].append(user)
                else:
                    # Si l'utilisateur n'a pas de préférences, on l'ajoute par défaut
                    preferences[channel][event].append(user)
    # delete duplicates
    # for each channel and event, remove duplicates
    for channel in preferences:
        for event in preferences[channel]:
            preferences[channel][event] = list(set(preferences[channel][event]))

    return preferences


def parameters_notification(
    object: Any,
    action: str,
) -> dict:
    """
    Get the parameters for the notification.
    Returns:
        dict: Dictionary of parameters for the notification.
    """
    parameters = dict()

    parameters["operation_patient_created"]["notification"] = {
        "verb": "Operation crée",
        "title": "Operation crée",
        "description": "Une operation a été crée pour le patient ",
        "level": "WARNING",
    }
    parameters["operation_patient_created"]["sms"] = {
        "verb": "Operation crée",
        "title": "Operation crée",
        "description": "Une operation a été crée pour le patient ",
        "level": "WARNING",
    }
    parameters["operation_patient_created"]["email"] = {
        "verb": "Operation crée",
        "title": "Operation crée",
        "description": "Une operation a été crée pour le patient ",
        "level": "WARNING",
    }
    # ...

    return parameters


def notification(
    sender: User,
    recipients: List[User],
    obj: Any,
):
    # notification
    recipients = filter_user(recipients)["notification"]["operation_create"]

    notification_signal.send(
        sender=sender,
        recipients=recipients,
        obj_content_object=obj,
        verb=parameters_notification(obj, "operation_patient_created")["notification"][
            "verb"
        ],
        nf_type="cr_operation",
        title="Operation crée",
        description=parameters_notification(obj, "operation_patient_created")[
            "notification"
        ]["description"],
        target=obj,
        level="WARNING",
    )
    # sms
    recipients = filter_user(recipients)["sms"]["operation_patient_created"]
    if recipients:
        text = "Une operation a été crée pour le patient "

    # email
    # recipients = filter_user(recipients)['email']['operation_create']
    # if recipients:
    #     subject = "Operation crée"
    #     text = "Une operation a été crée pour un patient "
    #     text += "par " + str(operation.created_by.get_full_name())
    #     text += "le " + str(operation.created_on)
    #     text += "programmée le:" + str(operation.operation_date.strftime("%d/%m/%Y"))

    # #     email = EmailMessage(
    #         "deletation de la base",
    #         "deletion de la base ccam",
    #         "<EMAIL>",
    #         ["<EMAIL>"],
    #     )

    #     email.send()


def notification_operation_create(
    sender: User,
    operation: Operation,
    recipients: List[User],
    **kwargs: Optional[Dict[str, Any]],
):
    # notification
    recipients = filter_user(recipients)["notification"]["operation_patient_created"]
    notification_signal.send(
        sender=sender,
        recipients=recipients,
        obj_content_object=operation,
        verb="Operation crée",
        nf_type="cr_operation",
        title="Operation crée",
        description="Une operation a été crée pour le patient par "
        + str(operation.created_by)
        + str(operation.zkf_patient.full_name())
        + "le "
        + str(operation.operation_date),
        target=operation,
        level="WARNING",
    )
    # email
    recipients = filter_user(recipients)["email"]["operation_patient_created"]
    # add mrouer for test
    template = os.path.join(
                settings.BASE_DIR,
                "templates",
                "emails",
                "notifications",
                "operation.html",
            )
    text = None
    recipients = filter_user(recipients)["email"]["operation_patient_created"]
    send_email(
        subject="Operation crée",
        text=text,
        recipients=recipients,
        from_email="<EMAIL>",
        template=template,
        object=operation,)
       
    # sms
    recipients = filter_user(recipients)["sms"]["operation_patient_created"]

    return HttpResponse(
        "Notification sent successfully.",
        status=200,
    )


def notification_upload_compte_rendu_correspondant(
    sender: User,
    compte_rendu_correspondant: CompteRenduCorrespondant,
    recipients: List[User],
    level: str = "INFO",
):
    # notification
    if recipients:
        notification_signal.send(
            sender=sender,
            recipients=filter_user(recipients)["notification"][
                "compterenducorrespondant_patient_added"
            ],
            verb="Compte rendu ajouté",
            nf_type="cr_correspondant",
            obj_content_object=compte_rendu_correspondant,
            title="Compte rendu ajouté",
            description="Un compte rendu a été ajouté à votre dossier médical pour le patient "
            + str(compte_rendu_correspondant.zkf_patient.full_name()),
            doc_link="/api/media/" + str(compte_rendu_correspondant.examen_file),
            target=compte_rendu_correspondant.zkf_patient,
            level=level,
            # obj_patient = str(self.object.zkf_patient.full_name()), ->field non created !!!!
            # extra='extra',
        )

    # email
    template = os.path.join(
        settings.BASE_DIR,
        "templates",
        "emails",
        "notifications",
        "compte_rendu_correspondant.html",
    )
    recipients = filter_user(recipients)["email"][
            "compterenducorrespondant_patient_added"
        ]
    text = None
    send_email(
        subject="Compte rendu ajouté",
        text=text,
        recipients=recipients,
        from_email="<EMAIL>",
        template=template, 
        object=compte_rendu_correspondant,
    )
   

    # sms
    recipients = filter_user(recipients)["sms"][
        "compterenducorrespondant_patient_added"
    ]

    return


def notification_add_patient_protocole(
    sender: User,
    patient: Patient,
    protocole: Protocole,
    recipients: List[User],
    level: str = "INFO",
):
    # notification
    notification_signal.send(
        sender=sender,
        recipients=filter_user(recipients)["notification"]["patient_added_in_protocol"],
        verb="Patient ajouté à un protocole",
        nf_type="patient_added_in_protocol",
        obj_content_object=protocole,
        title="Patient ajouté au protocole",
        description=f"{patient.full_name()} a été ajouté au protocole {protocole.libelle}",
        # doc_link = "api/media/" + str(compte_rendu_correspondant.examen_file),
        target=patient,
        level=level,
        # obj_patient = str(self.object.zkf_patient.full_name()), ->field non created !!!!
        # extra='extra',
    )
    # email
    recipients = filter_user(recipients)["email"]["patient_added_in_protocol"]
    if recipients:
        # email
        text = render_to_string(
            os.path.join(
                settings.BASE_DIR,
                "templates",
                "emails",
                "notifications",
                "protocole.html",
            ),
            set_context(protocole),
        )
        recipients = filter_user(recipients)["email"]["patient_added_in_protocol"]
        if recipients:
            subject = "Patient ajouté au protocole" + protocole.libelle
            text = text
            from_email = "<EMAIL>"
            msg = EmailMultiAlternatives(
                subject, "", from_email, [user.email for user in recipients]
            )
            msg.attach_alternative(text, "text/html")
            msg.send()
    # sms
    recipients = filter_user(recipients)["sms"]["convocation_patient_compterendu"]

    return


def notification_convocation_patient_compterendu(
    sender: User,
    patient: Patient,
    compte_rendu: CompteRenduCorrespondant,
    recipients: List[User],
    level: str = "WARNING",
):
    # notification
    recipients = filter_user(recipients)["notification"][
        "convocation_patient_compterendu"
    ]
    notification_signal.send(
        sender=sender,
        recipients=recipients,
        obj_content_object=compte_rendu,
        verb="Patient à convoquer",
        nf_type="patient_convocation",
        title="Patient à convoquer",
        description="Patient à convoquer sur demande d'une compte rendu ",
        target=compte_rendu,
        level="WARNING",
    )
    # email
    recipients = filter_user(recipients)["email"]["convocation_patient_compterendu"]
    if recipients:
        # email
        text = render_to_string(
            os.path.join(
                settings.BASE_DIR,
                "templates",
                "emails",
                "notifications",
                "convocation.html",
            ),
            set_context(compte_rendu),
        )
        recipients = filter_user(recipients)["email"]["convocation_patient_compterendu"]
        if recipients:
            subject = "Patient à convoquer"
            text = text
            from_email = "<EMAIL>"
            msg = EmailMultiAlternatives(
                subject, "", from_email, [user.email for user in recipients]
            )
            msg.attach_alternative(text, "text/html")
            msg.send()
    # sms
    recipients = filter_user(recipients)["sms"]["convocation_patient_compterendu"]

    return


# Notifications for Audio Deletion Process


def send_audio_deletion_warning_notification(
    audio_recording: AudioRecording,
    recipients: List[User],
    deletion_date_display: str,
    consultation_status_display: str,
):
    """
    Sends a warning notification that an audio recording is scheduled for deletion.
    Expects 'recipients' to be a list, typically containing the single user to be notified.
    The first user in the list provides the context for 'zkf_environnement'.
    """
    if recipients and recipients[0]:
        sender_for_context = recipients[0]
        # Prepare detailed information for description
        default_audio_title = ""
        default_consultation_name = "Consultation inconnue"
        default_consultation_date_str = "date inconnue"
        default_patient_name = "Patient inconnu"

        # Initialize with defaults
        audio_title = default_audio_title
        consultation_name = default_consultation_name
        consultation_date_str = default_consultation_date_str
        patient_name = default_patient_name

        if audio_recording:
            audio_title = (
                audio_recording.title if audio_recording.title else default_audio_title
            )
            consultation_obj = audio_recording.consultation
            if consultation_obj:
                consultation_name = (
                    consultation_obj.motif_consultation
                    if consultation_obj.motif_consultation
                    else default_consultation_name
                )
                consultation_date_val = consultation_obj.consultation_date
                consultation_date_str = (
                    consultation_date_val.strftime("%d/%m/%Y")
                    if consultation_date_val
                    else default_consultation_date_str
                )
                patient_obj = consultation_obj.zkf_patient
                if patient_obj:
                    # Attempt to get patient's full name using common object attributes/methods
                    if hasattr(patient_obj, "full_name_short") and callable(
                        patient_obj.full_name_short
                    ):
                        name_val = patient_obj.full_name_short()
                        patient_name = name_val if name_val else default_patient_name
                    # else: patient_name remains default_patient_name if patient_obj exists but name cannot be determined

        base_description_part = f"L'audio '{audio_title}' de la consultation '{consultation_name}' du {consultation_date_str} du patient '{patient_name}'"

        notification_signal.send(
            sender=sender_for_context,
            recipients=recipients,
            verb="Avertissement: Suppression Audio Programmée",
            title="Avertissement Suppression Audio",
            description=f"{base_description_part} sera examiné pour suppression le {deletion_date_display} si la consultation reste '{consultation_status_display}' et inchangée.",
            nf_type="audio_deletion_scheduled_validated",
            obj_content_object=audio_recording,
            target=audio_recording.consultation,
            level="WARNING",
        )


def send_audio_deletion_cancelled_notification(
    audio_recording: AudioRecording, recipients: List[User]
):
    """
    Finds and updates an existing audio deletion warning notification to reflect cancellation.
    If no original warning is found for a recipient, an error is logged.
    """
    if not recipients:
        logger.warning(
            f"send_audio_deletion_cancelled_notification called with no recipients for audio {audio_recording.id if audio_recording else 'Unknown'}"
        )
        return

    if not audio_recording:
        logger.error(
            "send_audio_deletion_cancelled_notification called with no audio_recording object."
        )
        return

    try:
        audio_recording_content_type = ContentType.objects.get_for_model(
            audio_recording
        )
    except Exception as e:
        logger.error(
            f"Failed to get ContentType for AudioRecording: {e}. Cannot update cancellation notifications for audio {audio_recording.id}.",
            exc_info=True,
        )
        return

    warning_nf_types = [
        "audio_deletion_scheduled_validated",
        "audio_deletion_scheduled_inactive",
        "AUDIO_DELETION_WARNING_7DAYS",  # Include old type for broader compatibility
    ]

    for recipient_user in recipients:
        if not recipient_user:
            logger.warning(
                f"Skipping null recipient in send_audio_deletion_cancelled_notification for audio {audio_recording.id}"
            )
            continue
        try:
            notification_to_update = (
                Notification.objects.filter(
                    zkf_recipient=recipient_user,
                    obj_content_type=audio_recording_content_type,
                    obj_object_id=audio_recording.pk,
                    nf_type__in=warning_nf_types,
                )
                .order_by("-created_on")
                .first()
            )

            if notification_to_update:
                notification_to_update.verb = "Annulation: Suppression Audio Programmée"
                notification_to_update.title = "Annulation Suppression Audio"
                audio_name = (
                    audio_recording.file.name
                    if audio_recording.file and hasattr(audio_recording.file, "name")
                    else "Fichier inconnu"
                )
                consult_id_str = (
                    str(audio_recording.consultation.id)
                    if audio_recording.consultation
                    and hasattr(audio_recording.consultation, "id")
                    else "ID inconnu"
                )

                description = (
                    f"Annulation suppression audio '{audio_name}' (consult. {consult_id_str}). "
                    f"Motif: Changement d'état."
                )
                if len(description) > 255:
                    description = description[:252] + "..."

                notification_to_update.description = description
                notification_to_update.nf_type = "audio_deletion_cancelled"
                notification_to_update.level = "INFO"
                notification_to_update.read = False
                # Consider if notification_to_update.deleted should be set to False if it could be True

                update_fields_list = [
                    "verb",
                    "title",
                    "description",
                    "nf_type",
                    "level",
                    "read",
                ]
                notification_to_update.save(update_fields=update_fields_list)
                logger.info(
                    f"Updated notification {notification_to_update.id} to cancellation for audio {audio_recording.id}, recipient {recipient_user.id}"
                )
            else:
                logger.error(
                    f"Could not find original warning notification for audio {audio_recording.id} "
                    f"(consultation {audio_recording.consultation.id if audio_recording.consultation else 'ID consultation inconnu'}) "
                    f"and recipient {recipient_user.id} to update for cancellation."
                )
        except Exception as e:
            logger.error(
                f"Error updating notification for audio {audio_recording.id}, recipient {recipient_user.id}: {e}",
                exc_info=True,
            )


def send_audio_deletion_inactivity_warning_notification(
    audio_recording: AudioRecording, recipients: List[User], deletion_date_display: str
):
    """
    Sends a warning notification that an audio recording is scheduled for deletion due to inactivity.
    Expects 'recipients' to be a list, typically containing the single user to be notified.
    The first user in the list provides the context for 'zkf_environnement'.
    """
    if not recipients or not recipients[0]:
        logger.warning(
            f"send_audio_deletion_inactivity_warning_notification called with no valid recipients for audio {audio_recording.id if audio_recording else 'Unknown'}"
        )
        return

    if not audio_recording:
        logger.error(
            "send_audio_deletion_inactivity_warning_notification called with no audio_recording object."
        )
        return

    sender_for_context = recipients[0]
    # Prepare detailed information for description
    default_audio_title = ""
    default_consultation_name = "Consultation inconnue"
    default_consultation_date_str = "date inconnue"
    default_patient_name = "Patient inconnu"

    # Initialize with defaults
    audio_title = default_audio_title
    consultation_name = default_consultation_name
    consultation_date_str = default_consultation_date_str
    patient_name = default_patient_name

    if audio_recording:
        audio_title = (
            audio_recording.title if audio_recording.title else default_audio_title
        )
        consultation_obj = audio_recording.consultation
        if consultation_obj:
            consultation_name = (
                consultation_obj.motif_consultation
                if consultation_obj.motif_consultation
                else default_consultation_name
            )
            consultation_date_val = consultation_obj.consultation_date
            consultation_date_str = (
                consultation_date_val.strftime("%d/%m/%Y")
                if consultation_date_val
                else default_consultation_date_str
            )
            patient_obj = consultation_obj.zkf_patient
            if patient_obj:
                if hasattr(patient_obj, "full_name_short") and callable(
                    patient_obj.full_name_short
                ):
                    name_val = patient_obj.full_name_short()
                    patient_name = name_val if name_val else default_patient_name
                # If no name found, patient_name remains default_patient_name

    base_description_part = f"L'audio '{audio_title}' de la consultation '{consultation_name}' du {consultation_date_str} du patient '{patient_name}'"

    description = f"{base_description_part} sera supprimé autour du {deletion_date_display} en raison d'une inactivité prolongée de la consultation associée."
    if len(description) > 255:
        description = description[:252] + "..."

    notification_signal.send(
        sender=sender_for_context,
        recipients=recipients,
        verb="Avertissement: Suppression Audio (Inactivité)",
        title="Avertissement Suppression Audio (Inactivité)",
        description=description,
        nf_type="audio_deletion_scheduled_inactive",
        obj_content_object=audio_recording,
        target=audio_recording.consultation,
        level="WARNING",
    )


def send_audio_deleted_confirmation_notification(
    audio_recording: AudioRecording, recipients: List[User], reason_for_deletion: str
):
    """
    Sends a confirmation notification that an audio recording has been deleted.
    'reason_for_deletion' should be one of the constants from AudioRecording.DELETION_REASON_*
    Expects 'recipients' to be a list, typically containing the single user to be notified.
    The first user in the list provides the context for 'zkf_environnement'.
    """
    if recipients and recipients[0]:
        sender_for_context = recipients[0]
        nf_type = "audio_deleted"  # Use the generic existing type for all deletion confirmations

    # Default verb and title, description will be set based on the reason or a generic fallback
    # Prepare detailed information for description
    default_audio_title = ""
    default_consultation_name = "Consultation inconnue"
    default_consultation_date_str = "date inconnue"
    default_patient_name = "Patient inconnu"

    # Initialize with defaults
    audio_title = default_audio_title
    consultation_name = default_consultation_name
    consultation_date_str = default_consultation_date_str
    patient_name = default_patient_name

    if audio_recording:  # audio_recording is expected to be non-None for this function
        # Get audio title (prefer audio_recording.title, fallback to file.name)
        audio_title = (
            audio_recording.title if audio_recording.title else default_audio_title
        )

        consultation_obj = audio_recording.consultation
        if consultation_obj:
            # Get consultation name
            consultation_name = (
                consultation_obj.motif_consultation
                if consultation_obj.motif_consultation
                else default_consultation_name
            )

            # Get and format consultation date
            consultation_date_val = consultation_obj.consultation_date
            consultation_date_str = (
                consultation_date_val.strftime("%d/%m/%Y")
                if consultation_date_val
                else default_consultation_date_str
            )

            patient_obj = consultation_obj.zkf_patient
            if patient_obj:
                # Attempt to get patient's full name using common object attributes/methods
                if hasattr(patient_obj, "full_name_short") and callable(
                    patient_obj.full_name_short
                ):
                    name_val = patient_obj.full_name_short()
                    patient_name = name_val if name_val else default_patient_name
                # else: patient_name remains default_patient_name if patient_obj exists but name cannot be determined
    # This part forms the base of your new description
    base_description_part = f"L'audio '{audio_title}' de la consultation '{consultation_name}' du {consultation_date_str} du patient '{patient_name}'"

    verb = "Confirmation: Audio Supprimé"
    title = "Audio Supprimé"
    description = (
        f"{base_description_part} a été supprimé. Raison: {reason_for_deletion}."
    )

    # Customize verb and description based on the known reasons
    if reason_for_deletion == "inactivity":
        verb = "Confirmation: Audio Supprimé (Inactivité)"
        description = f"{base_description_part} a été supprimé en raison d'une inactivité prolongée (consultation non validée/envoyée depuis plus de 90 jours)."
    elif reason_for_deletion == "validated_consultation_7_day_rule":
        verb = "Confirmation: Audio Supprimé (Règle des 7 jours)"
        description = f"{base_description_part} a été supprimé car la consultation associée, initialement validée/envoyée, est restée inchangée pendant 7 jours après l'avertissement."
    else:
        # Log if the reason is not one of the specifically handled ones, but the default description above will be used.
        logger.warning(
            f"Audio deletion confirmation for audio {audio_recording.id} with an unspecified or new reason: '{reason_for_deletion}'. Using a generic description."
        )

    for recipient_user in recipients:
        notification_signal.send(
            sender=recipient_user,
            recipients=[recipient_user],
            verb=verb,
            title=verb,  # Using verb as title for consistency
            description=description,
            nf_type=nf_type,
            obj_content_object=audio_recording,
            target=audio_recording.consultation,
            level="INFO",
        )
