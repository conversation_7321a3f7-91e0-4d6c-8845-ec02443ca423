import logging
import os
import ffmpeg
import tempfile
from google.cloud import speech_v1p1beta1 as speech
from spi.services import ISpeechToTextService

logger = logging.getLogger(__name__)


class SpeechToTextService(ISpeechToTextService):
    def transcribe_audio_instance(self, instance):
        """
        Transcribe the audio file using Speech-to-Text API,
        detect the codec using ffmpeg, and update the transcription field.
        """
        try:
            audio_path = instance.file.path
            if not os.path.exists(audio_path):
                logger.error("Audio file does not exist: %s", audio_path)
                return
            file_size = os.path.getsize(audio_path)
            logger.info(
                "Preparing to send audio file '%s' (size: %d bytes) to Google Speech-to-Text",
                audio_path,
                file_size,
            )
            try:
                probe = ffmpeg.probe(audio_path)
                codec_name = probe["streams"][0]["codec_name"]
                logger.info("Detected audio codec for '%s': %s", audio_path, codec_name)
            except Exception as ffmpeg_exc:
                logger.warning(
                    "Could not detect codec for '%s', defaulting to LINEAR16. Error: %s",
                    audio_path,
                    str(ffmpeg_exc),
                )
                codec_name = None

            # Always convert to WAV (PCM) if not already in PCM format
            file_to_transcribe = audio_path
            tmp_wav = None
            encoding = (
                speech.RecognitionConfig.AudioEncoding.LINEAR16
            )  # Default to LINEAR16
            try:
                if codec_name != "pcm_s16le":
                    with tempfile.NamedTemporaryFile(
                        suffix=".wav", delete=False
                    ) as tmp_file:
                        tmp_wav = tmp_file.name
                    (
                        ffmpeg.input(audio_path)
                        .output(
                            tmp_wav, format="wav", acodec="pcm_s16le", ar="16000", ac=1
                        )
                        .overwrite_output()
                        .run(quiet=True)
                    )
                    logger.info("Temporary WAV file created: %s", tmp_wav)
                    file_to_transcribe = tmp_wav
                    encoding = (
                        speech.RecognitionConfig.AudioEncoding.LINEAR16
                    )  # Force encoding for WAV
            except Exception as conv_exc:
                logger.exception("Error during WAV conversion: %s", conv_exc)
                file_to_transcribe = audio_path  # fallback

            client = speech.SpeechClient()
            # Log the file and encoding used for transcription
            logger.info(
                "[DEBUG] Sending file to Google Speech-to-Text: %s | encoding: %s",
                file_to_transcribe,
                encoding,
            )
            with open(file_to_transcribe, "rb") as audio_file:
                content = audio_file.read()

            logger.info("[DEBUG] File read successfully.")
            audio = speech.RecognitionAudio(content=content)
            config = speech.RecognitionConfig(
                encoding=encoding,
                language_code="fr-FR",
                enable_automatic_punctuation=True,
                model="latest_long",
                use_enhanced=True,
            )

            # Get duration (in seconds) using ffmpeg
            try:
                probe = ffmpeg.probe(file_to_transcribe)
                duration = float(probe["streams"][0]["duration"])
            except Exception:
                duration = None

            # If duration > 60s, split audio into segments and transcribe each segment separately
            transcript = ""
            temp_segments = []
            segment_length = 59  # seconds (must be < 60)
            if duration is not None and duration > 60:
                logger.info(
                    "Audio longer than 60s, splitting into segments of 59s for transcription."
                )
                try:
                    num_segments = int(duration // segment_length) + 1
                    for i in range(num_segments):
                        start = i * segment_length
                        temp_segment = tempfile.NamedTemporaryFile(
                            suffix=".wav", delete=False
                        )
                        temp_segment.close()
                        (
                            ffmpeg.input(file_to_transcribe, ss=start, t=segment_length)
                            .output(
                                temp_segment.name,
                                format="wav",
                                acodec="pcm_s16le",
                                ar="16000",
                                ac=1,
                            )
                            .overwrite_output()
                            .run(quiet=True)
                        )
                        temp_segments.append(temp_segment.name)
                    logger.info(
                        "Created %d temporary segments for transcription.",
                        len(temp_segments),
                    )
                    for idx, segment_path in enumerate(temp_segments):
                        logger.info(
                            "Transcribing segment %d/%d: %s",
                            idx + 1,
                            len(temp_segments),
                            segment_path,
                        )
                        with open(segment_path, "rb") as audio_file:
                            segment_content = audio_file.read()
                        segment_audio = speech.RecognitionAudio(content=segment_content)
                        segment_response = client.recognize(
                            config=config, audio=segment_audio
                        )
                        segment_transcript = " ".join(
                            [
                                result.alternatives[0].transcript
                                for result in segment_response.results
                            ]
                        )
                        transcript += " " + segment_transcript
                finally:
                    # Clean up all temporary segment files
                    for segment_path in temp_segments:
                        try:
                            os.remove(segment_path)
                            logger.info("Temporary segment deleted: %s", segment_path)
                        except Exception as clean_exc:
                            logger.warning(
                                "Error while deleting temporary segment: %s", clean_exc
                            )
            else:
                logger.info("Audio ≤ 60s, using recognize")
                with open(file_to_transcribe, "rb") as audio_file:
                    content = audio_file.read()
                audio = speech.RecognitionAudio(content=content)
                response = client.recognize(config=config, audio=audio)
                transcript = " ".join(
                    [result.alternatives[0].transcript for result in response.results]
                )

            logger.info(
                "Extracted transcript for AudioRecording id=%s: '%s'",
                instance.id,
                transcript.strip(),
            )
            instance.transcription = transcript.strip()
            instance.save(update_fields=["transcription"])
            logger.info("Transcription saved for AudioRecording id=%s", instance.id)
            # Only perform raw transcription and saving here. Correction is handled separately.
            # Clean up temporary file if created
            try:
                if tmp_wav and os.path.exists(tmp_wav):
                    os.remove(tmp_wav)
                    logger.info("Temporary file deleted: %s", tmp_wav)
            except Exception as clean_exc:
                logger.warning("Error while deleting temporary file: %s", clean_exc)
        except Exception as e:
            logger.exception(
                "Failed to transcribe audio for AudioRecording id=%s: %s",
                instance.id,
                str(e),
            )
