import os
import json
import logging
from mistralai import Mistral  # Aligning with documentation v1.x
from django.conf import settings

from typing import Optional, List, Dict, Tuple
from overrides import override

from models.document import DocumentMetadata, Specialite, PatientTraits, ContactTraits
from patient.models import Patient
from contact.models import Contact
from spi.repositories import IContactRepository, IPatientRepository
from spi.services import INLPService


# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NLPService_AI_Mistral(INLPService):
    def __init__(
        self,
        patient_repository: IPatientRepository,
        contact_repository: IContactRepository,
    ) -> None:
        self._patient_repository = patient_repository
        self._contact_repository = contact_repository

        # Utiliser os.getenv pour récupérer la clé API. S'assurer qu'elle est chargée en amont.
        api_key = settings.MISTRAL_API_KEY
        if not api_key:
            logger.error(
                "La variable d'environnement MISTRAL_API_KEY n'est pas définie."
            )
            raise ValueError(
                "La variable d'environnement MISTRAL_API_KEY n'est pas définie."
            )

        self._client = Mistral(api_key=api_key)  # Aligning with documentation v1.x
        # Choisir un modèle Mistral (ex: mistral-large-latest, mistral-small-latest)
        self._model_name = settings.MISTRAL_MODEL
        logger.info(
            f"NLPService_AI_Mistral initialisé avec le modèle : {self._model_name}"
        )

    def _build_extraction_prompt(self, text: str) -> str:
        """Construit le prompt pour l'extraction de métadonnées via l'API Mistral."""
        prompt = f"""\
        Tu es un assistant spécialisé dans l'analyse de documents médicaux.
        Extrais les informations suivantes du texte ci-dessous et réponds UNIQUEMENT avec un objet JSON valide.
        Ne fournis aucune explication ou texte supplémentaire avant ou après le JSON.

        Informations à extraire :
        1.  `document_date`: La date principale du document (ex: date de consultation, d'examen, de compte rendu). Retourne la date au format AAAA-MM-JJ. Si plusieurs dates sont présentes, choisis la plus pertinente pour le contexte du document. Si aucune date claire n'est trouvée, retourne null.
        2.  `type`: Le type de document. Choisis PARMI les options suivantes : "CONSULTATION", "HOSPITALISATION", "EXAMEN_COMPLEMENTAIRE", "ORDONNANCE_TRAITEMENT". Si aucun type clair ne correspond, retourne "CONSULTATION" par défaut.
        3.  `specialties`: Une liste des noms de spécialités médicales mentionnées dans le document (ex: ["Cardiologie", "Pneumologie"]). Si aucune n'est mentionnée, retourne une liste vide [].
        4.  `patient_should_be_contacted`: Un booléen (true/false) indiquant si le document suggère explicitement qu'une convocation spécifique est nécessaire pour le patient. l'auteur du document demande clairement à son correspondant de convoquer le patient.
        5.  `patient_last_name`: Le nom de famille du patient mentionné dans le document. Retourne null si non trouvé.
        6.  `patient_first_name`: Le prénom du patient mentionné dans le document. Retourne null si non trouvé.
        7.  `patient_dob`: La date de naissance du patient mentionnée dans le document, au format AAAA-MM-JJ. Retourne null si non trouvée.
        8.  `author_last_name`: Le nom de famille de l'auteur/médecin signataire du document. Retourne null si non trouvé.
        9.  `author_first_name`: Le prénom de l'auteur/médecin signataire du document. Retourne null si non trouvé.

        Format JSON attendu :
        {{
            "document_date": "AAAA-MM-JJ" | null,
            "type": "CONSULTATION" | "HOSPITALISATION" | "EXAMEN_COMPLEMENTAIRE" | "ORDONNANCE_TRAITEMENT" | "BIOLOGIE",
            "specialties": ["string", ...],
            "patient_should_be_contacted": true | false,
            "patient_last_name": "string" | null,
            "patient_first_name": "string" | null,
            "patient_dob": "AAAA-MM-JJ" | null,
            "author_last_name": "string" | null,
            "author_first_name": "string" | null
        }}

        Texte du document :
        ---
        {text}
        ---

        Réponds uniquement avec le JSON.
        """
        return prompt

    def _call_mistral_api(self, prompt: str) -> Optional[str]:
        """Appelle l'API Mistral avec le prompt fourni et retourne la réponse brute."""
        if not self._client:
            logger.error("Client Mistral non initialisé. Impossible d'appeler l'API.")
            # Lever une exception spécifique pourrait être mieux ici
            raise ConnectionError("Client Mistral non initialisé.")

        try:
            messages = [
                # Using dict format for messages as per v1.x examples
                {"role": "user", "content": prompt}
            ]
            # Appel à l'API Mistral
            # Utiliser le modèle configuré et la méthode correcte
            chat_response = (
                self._client.chat.complete(  # Correction: Utiliser chat.complete
                    model=self._model_name,
                    messages=messages,
                    response_format={
                        "type": "json_object"
                    },  # Rétablir la demande de format JSON
                )
            )

            # Vérifier si la réponse contient des choix et extraire le contenu
            if chat_response.choices:
                response_content = chat_response.choices[0].message.content
                logger.debug(f"Réponse brute de Mistral: {response_content}")
                if isinstance(response_content, str):
                    return response_content
            else:
                logger.error("Réponse inattendue de Mistral: Aucun choix trouvé.")
                # Lever une exception ou retourner une chaîne vide/None selon la gestion préférée
                raise ValueError("Réponse inattendue de Mistral: Aucun choix trouvé.")

        except (
            Exception
        ) as e:  # TODO: Capturer des exceptions plus spécifiques de Mistral si disponibles
            logger.error(f"Erreur lors de l'appel à l'API Mistral: {e}", exc_info=True)
            # Relancer l'exception pour qu'elle soit gérée par l'appelant
            raise

    def _parse_mistral_response(self, response_content: str) -> tuple[dict, dict]:
        """Parse la réponse JSON de Mistral, valide les champs et retourne les données extraites."""
        extracted_data = {
            "document_date": None,
            "type": "CONSULTATION",  # Default ou à déterminer
            "specialties": [],
            "patient_should_be_contacted": False,
        }
        extracted_traits = {
            "patient_last_name": None,
            "patient_first_name": None,
            "patient_dob": None,
            "author_last_name": None,
            "author_first_name": None,
        }

        try:
            parsed_json = json.loads(response_content)

            # --- Validation et Extraction des Métadonnées Principales ---
            if isinstance(parsed_json.get("document_date"), str):
                extracted_data["document_date"] = parsed_json.get("document_date")
            # Utiliser .get avec défaut pour type si besoin, ou laisser la valeur initiale
            doc_type = parsed_json.get("type")
            if isinstance(doc_type, str) and doc_type in [
                "CONSULTATION",
                "HOSPITALISATION",
                "EXAMEN_COMPLEMENTAIRE",
                "ORDONNANCE_TRAITEMENT",
            ]:  # Ajout des types possibles définis dans le prompt
                extracted_data["type"] = doc_type

            specialties = parsed_json.get("specialties")
            if isinstance(specialties, list):
                extracted_data["specialties"] = [
                    str(s) for s in specialties if isinstance(s, str)
                ]

            should_contact = parsed_json.get("patient_should_be_contacted")
            if isinstance(should_contact, bool):
                extracted_data["patient_should_be_contacted"] = should_contact

            # --- Validation et Extraction des Traits Patient/Auteur ---
            extracted_traits["patient_last_name"] = (
                parsed_json.get("patient_last_name")
                if isinstance(parsed_json.get("patient_last_name"), str)
                else None
            )
            extracted_traits["patient_first_name"] = (
                parsed_json.get("patient_first_name")
                if isinstance(parsed_json.get("patient_first_name"), str)
                else None
            )
            extracted_traits["patient_dob"] = (
                parsed_json.get("patient_dob")
                if isinstance(parsed_json.get("patient_dob"), str)
                else None
            )
            extracted_traits["author_last_name"] = (
                parsed_json.get("author_last_name")
                if isinstance(parsed_json.get("author_last_name"), str)
                else None
            )
            extracted_traits["author_first_name"] = (
                parsed_json.get("author_first_name")
                if isinstance(parsed_json.get("author_first_name"), str)
                else None
            )

            logger.debug(
                f"Données parsées avec succès: {extracted_data}, Traits: {extracted_traits}"
            )
            return extracted_data, extracted_traits

        except json.JSONDecodeError:
            logger.error(
                f"La réponse de Mistral n'est pas un JSON valide : {response_content}",
                exc_info=True,
            )
            # Retourner les dictionnaires vides/par défaut en cas d'erreur de parsing
            return extracted_data, extracted_traits
        except Exception as e:
            logger.error(f"Erreur inattendue lors du parsing JSON: {e}", exc_info=True)
            # Retourner les dictionnaires vides/par défaut en cas d'erreur inattendue
            return extracted_data, extracted_traits

    def _find_patient(self, extracted_traits: dict) -> Patient | None:
        """Recherche un patient en base de données à partir des traits extraits."""
        patient_nom = extracted_traits.get("patient_last_name")
        patient_prenom = extracted_traits.get("patient_first_name")
        patient_ddn_str = extracted_traits.get("patient_dob")

        if not (patient_nom or patient_prenom or patient_ddn_str):
            logger.info(
                "Aucun trait suffisant (nom/prénom/DOB) extrait pour rechercher le patient."
            )
            return None

        # logger.info(f"Tentative de recherche du patient extrait : {patient_prenom} {patient_nom} (DOB: {patient_ddn_str})")
        patient_traits_for_search = PatientTraits(
            last_name=patient_nom.strip() if patient_nom else None,
            first_name=patient_prenom.strip() if patient_prenom else None,
            dob=(
                patient_ddn_str.strip() if patient_ddn_str else None
            ),  # TODO: Valider format date
            sex=None,  # Non extrait
        )
        # logger.info(f"Recherche du patient avec traits nettoyés: {patient_traits_for_search}")

        try:
            found_patients = self._patient_repository.get_patient_by_details(
                patient_traits_for_search
            )
            if found_patients:
                patient = found_patients[0]  # On prend le premier trouvé
                # logger.info(f"Patient trouvé en base via traits extraits: {patient}")
                return patient
            else:
                # logger.info("Aucun patient correspondant aux traits extraits n'a été trouvé en base.")
                return None
        except Exception as repo_e:
            logger.error(
                f"Erreur lors de la recherche du patient par traits: {repo_e}",
                exc_info=True,
            )
            return None  # En cas d'erreur repo, on retourne None

    def _find_author(self, extracted_traits: dict) -> Contact | None:
        """Recherche un auteur (Contact) en base de données à partir des traits extraits."""
        author_nom = extracted_traits.get("author_last_name")
        author_prenom = extracted_traits.get("author_first_name")

        if not author_nom:  # Au moins un nom est nécessaire pour la recherche auteur
            logger.info("Aucun nom d'auteur extrait pour lancer la recherche.")
            return None

        # logger.info(f"Tentative de recherche de l'auteur extrait : {author_prenom} {author_nom}")
        author_traits = ContactTraits(
            last_name=author_nom.strip(),  # Nom est requis ici
            first_name=author_prenom.strip() if author_prenom else None,
        )

        try:
            found_contacts = self._contact_repository.get_contact_by_traits(
                author_traits
            )
            if found_contacts:
                author = found_contacts[0]  # On prend le premier trouvé
                logger.info(f"Auteur trouvé en base via traits extraits: {author}")
                return author
            else:
                logger.info(
                    "Aucun auteur correspondant aux traits extraits n'a été trouvé en base."
                )
                return None
        except Exception as repo_e:
            logger.error(
                f"Erreur lors de la recherche de l'auteur par traits: {repo_e}",
                exc_info=True,
            )
            return None  # En cas d'erreur repo, on retourne None

    def _get_author_specialties(self, author: Contact | None) -> list[Specialite]:
        """Récupère les spécialités associées à un auteur (Contact) trouvé en base."""
        if not author or not isinstance(author, Contact):
            logger.info(
                "Aucun auteur trouvé ou auteur invalide, impossible de récupérer les spécialités."
            )
            return []

        # logger.info(f"Tentative de récupération des spécialités pour l'auteur trouvé: {author}")
        try:
            correspondant = author.correspondant
            if correspondant:
                db_specialties = list(correspondant.specialite.all())
                if db_specialties:
                    logger.info(
                        f"Spécialités trouvées en base pour l'auteur {author}: {[s.spe for s in db_specialties]}"
                    )
                    return db_specialties
                else:
                    logger.info(
                        f"Aucune spécialité associée au correspondant de l'auteur {author} trouvée en base."
                    )
            else:
                logger.warning(f"L'auteur {author} n'a pas de correspondant associé.")
        except AttributeError as e:
            logger.error(
                f"Erreur d'attribut lors de l'accès aux spécialités de l'auteur {author}: {e}"
            )
        except Exception as e:
            logger.error(
                f"Erreur inattendue lors de la récupération des spécialités de l'auteur {author}: {e}",
                exc_info=True,
            )

        return []  # Retourne une liste vide en cas d'erreur ou si rien n'est trouvé

    @override
    def extract_document_metadata(self, text: str) -> DocumentMetadata:
        """Extrait les métadonnées d'un document en utilisant l'API Mistral.
        Identifie l'auteur et le patient à partir du texte et les recherche en base de données.
        Retourne un objet DocumentMetadata.
        """
        prompt = self._build_extraction_prompt(text)
        patient = None  # Initialisation
        author = None  # Initialisation
        extracted_data = {}  # Initialisation
        extracted_traits = {}  # Initialisation

        try:
            # 1. Appel API
            response_content = self._call_mistral_api(prompt)

            # 2. Parsing de la réponse
            extracted_data, extracted_traits = self._parse_mistral_response(
                response_content
            )

            # Log des données extraites pour le débogage
            logger.info(f"Données principales extraites: {extracted_data}")
            logger.info(f"Traits Patient/Auteur extraits: {extracted_traits}")

            # 3. Recherche du Patient en BDD via nouvelle méthode
            patient = self._find_patient(extracted_traits)

            # 4. Recherche de l'Auteur en BDD via nouvelle méthode
            author = self._find_author(extracted_traits)

            # --- Détermination des spécialités basée sur l'auteur trouvé ---
            final_specialties = self._get_author_specialties(author)

            # --- Création de l'objet DocumentMetadata ---
            metadata = DocumentMetadata(
                patient=patient,  # Utilisation du patient trouvé
                auteur=author,  # Utilisation de l'auteur trouvé
                type=extracted_data.get(
                    "type", "CONSULTATION"
                ),  # Utiliser get avec défaut
                document_date=extracted_data.get("document_date") or "",  # Utiliser get
                specialties=final_specialties,  # Spécialités de l'auteur trouvé
                patient_should_be_contacted=extracted_data.get(
                    "patient_should_be_contacted", False
                ),  # Utiliser get
                notification=True,  # Valeur par défaut
            )

            return metadata

        except Exception as e:
            logger.error(
                f"Erreur globale dans extract_document_metadata: {e}", exc_info=True
            )
            # Retourner un objet metadata vide ou par défaut en cas d'erreur majeure
            return DocumentMetadata(
                specialties=[]
            )  # Assurez-vous que specialties est une liste

    # --- Implémentations Stub pour les autres méthodes INLPService ---
    # Ces méthodes ne sont pas utilisées par l'approche IA pour extract_document_metadata
    # mais doivent être présentes pour respecter l'interface.

    # Implémentations des méthodes abstraites manquantes de INLPService


# Exemple d'utilisation (pourrait être dans un autre fichier ou script de test)
# if __name__ == \"__main__\":
