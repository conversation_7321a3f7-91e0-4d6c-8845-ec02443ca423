import logging
from mistralai import Mistral
from django.conf import settings
from spi.services import ICorrectionTextService

logger = logging.getLogger(__name__)
MODEL_NAME = settings.MISTRAL_MODEL


class CorrectionTextService(ICorrectionTextService):
    def correct_medical_transcription(self, transcription: str) -> str:
        """
        Sends the raw medical transcription to Mistral AI using the SDK and returns the corrected version.
        """
        # If the transcription is empty or only whitespace, return an empty string immediately
        if not transcription or transcription.isspace():
            logger.info("Transcription is empty or whitespace, returning empty string.")
            return ""

        api_key = settings.MISTRAL_API_KEY
        if not api_key:
            logger.error("MISTRAL_API_KEY is not set in settings.")
            raise ValueError("MISTRAL_API_KEY is not set in settings.")
        logger.info(f"MISTRAL_MODEL_utilisé: {MODEL_NAME}")
        client = Mistral(api_key=api_key)
        prompt = (
            "Tu es un(e) secrétaire médical(e) expérimenté(e), spécialiste de la rédaction de comptes-rendus médicaux professionnels et lisibles.\n"
            "Voici la transcription brute d’une consultation, générée par reconnaissance vocale. Elle peut contenir :\n"
            "- des erreurs de transcription,\n"
            "- des mots inadaptés ou incorrects,\n"
            "- des phrases mal formulées ou floues.\n\n"
            "Ta tâche est de Transformer ce texte en un courrier médical formel et structuré clair et fidèle au contenu clinique, SANS JAMAIS RIEN INVENTER.\n\n"
            "Instructions STRICTES :\n"
            "- Structurer le texte en paragraphes clairs et concis.\n"
            "- Utiliser un langage médical précis et approprié.\n"
            "- Corrige les erreurs de transcription et reformule les phrases pour une clarté et une fluidité maximales.\n"
            "- Utilise impérativement les termes médicaux appropriés et précis.\n"
            "- Améliore la syntaxe, la grammaire et la lisibilité générale du texte.\n"
            "- **Structure le compte-rendu en paragraphes clairs et distincts lorsque cela améliore la lisibilité. Utilise des sauts de ligne pour séparer les idées ou sections distinctes.**\n"
            "- **NE PAS commencer la réponse par des formules introductives telles que 'Compte rendu médical', 'Transcription corrigée :', le nom du patient, ou toute autre salutation ou titre. Commence DIRECTEMENT par le contenu clinique corrigé.**\n"
            "- **Il est ABSOLUMENT IMPÉRATIF de ne RIEN inventer ni ajouter d'informations qui ne sont pas explicitement présentes dans la transcription fournie. Si un détail n'est pas dans le texte source, il ne doit PAS apparaître dans ta réponse.**\n"
            "- Ta réponse doit contenir UNIQUEMENT le texte médical corrigé. Aucun commentaire, aucune note, aucune explication supplémentaire.\n\n"
            "Texte à corriger :\n"
            f"{transcription}\n"
        )
        try:
            messages = [{"role": "user", "content": prompt}]
            chat_response = client.chat.complete(
                model=MODEL_NAME, 
                messages=messages,
                temperature=0.7
            )
            if chat_response.choices:
                response_content = chat_response.choices[0].message.content
                logger.info("Mistral LLM correction successful.")
                return response_content.strip()
            else:
                logger.error("Unexpected response from Mistral: No choices found.")
                raise ValueError("Unexpected response from Mistral: No choices found.")
        except Exception as e:
            logger.error(f"Error during Mistral LLM correction: {e}", exc_info=True)
            raise
