from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Optional


from codage.ccam.models import R_acte, ActeDetails
from models.document import DocumentMetadata


class IActeService(ABC):

    @abstractmethod
    def get_acte_details(
        self, acte, activite, phase, grille: Optional[int] = None
    ) -> ActeDetails:
        """
        Get acte details
        """
        ...

    @abstractmethod
    def get_associations(self, aa_code1) -> List[R_acte]:
        """
        List actes associated with aa_code1
        """
        ...


class ITextExtractionService(ABC):

    @abstractmethod
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from file.
        As a first implementation, we will only support PDF and DOCX and TXT files.
        """
        ...


class ISpeechToTextService(ABC):
    @abstractmethod
    def transcribe_audio_instance(self, instance):
        """
        Transcribe the audio file using Speech-to-Text API,
        detect the codec using ffmpeg, and update the transcription field.

        """
        ...


class ICorrectionTextService(ABC):
    @abstractmethod
    def correct_medical_transcription(self, transcription: str) -> str:
        """
        Sends the raw medical transcription to Mistral AI using the SDK and returns the corrected version.
        """
        ...


class INLPService(ABC):

    @abstractmethod
    def extract_document_metadata(self, text: str) -> DocumentMetadata:
        """
        Default method for document metadata extraction.

        Args:
            text (str): Text to extract metadata from

        Returns:
            Optional[DocumentMetadata]: Extracted metadata or None if no metadata found
        """
        ...
