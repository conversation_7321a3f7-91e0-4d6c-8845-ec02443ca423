#!/usr/bin/env python3
"""
Simple test script to validate the statistics functions
"""


def test_consultation_statistics():
    from statistiques.consultation.statistiques_consultation import (
        unified_consultations_statistics,
    )

    # Test with empty data
    result = unified_consultations_statistics([])
    print("Consultation stats (empty):", result)

    # Test with sample data
    sample_consultations = [
        {
            "consultation_date": "2024-01-15",
            "consultation_debut_prevu": "09:00:00",
            "consultation_fin_prevu": "09:30:00",
            "consultation_debut_reel": "09:05:00",
            "consultation_fin_reel": "09:35:00",
            "etat_consultation": "finie",
        },
        {
            "consultation_date": "2024-01-15",
            "consultation_debut_prevu": "14:00:00",
            "consultation_fin_prevu": "14:30:00",
            "consultation_debut_reel": "14:10:00",
            "consultation_fin_reel": "14:40:00",
            "etat_consultation": "finie",
        },
    ]

    result = unified_consultations_statistics(sample_consultations)
    print("Consultation stats (sample):", result)


def test_operations_statistics():
    from statistiques.operation.statistiques_operation import (
        unified_operations_statistics,
    )

    # Test with empty data
    result = unified_operations_statistics([])
    print("Operations stats (empty):", result)

    # Test with sample data
    sample_operations = [
        {
            "operation_date": "2024-01-15",
            "operation_start": "2024-01-15 09:00:00",
            "operation_end": "2024-01-15 10:30:00",
            "urgence": "programme",
            "annulation_operation": False,
            "a_programmer": False,
        },
        {
            "operation_date": "2024-01-15",
            "operation_start": "2024-01-15 11:00:00",
            "operation_end": "2024-01-15 12:30:00",
            "urgence": "urgence",
            "annulation_operation": False,
            "a_programmer": False,
        },
    ]

    result = unified_operations_statistics(sample_operations)
    print("Operations stats (sample):", result)


if __name__ == "__main__":
    print("Testing consultation statistics...")
    test_consultation_statistics()
    print("\nTesting operations statistics...")
    test_operations_statistics()
