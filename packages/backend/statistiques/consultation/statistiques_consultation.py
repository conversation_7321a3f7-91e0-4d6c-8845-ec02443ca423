"""
Generate statistics for consultations:
-> basic ones
-> user cannot choose

Number of consultations: 
-separate morning and afternoon if applicable
-by day
-by week
-by month
...
compare 2 date range
Mean duration
mean start in morning
mean start in afternoon
mean last end time by day
mean time between two consultations
mean "finie", "non_venu", "annule"...
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
from django.utils import timezone
from schedule.scripts.get_vacations import get_vacations


def to_datetime_naive(dt_input):
    """Convert to pandas datetime and ensure it's timezone-naive"""
    try:
        dt = pd.to_datetime(dt_input)

        # Si c'est une série pandas avec timezone
        if isinstance(dt, pd.Series):
            if hasattr(dt.dtype, "tz") and dt.dtype.tz is not None:
                # Vérifier si c'est vraiment une série datetime avant d'utiliser .dt
                if pd.api.types.is_datetime64_any_dtype(dt):
                    return dt.dt.tz_localize(None)
                else:
                    return dt.apply(
                        lambda x: (
                            x.replace(tzinfo=None)
                            if hasattr(x, "tz") and x.tz is not None
                            else x
                        )
                    )
            else:
                return dt
        # Si c'est un timestamp unique avec timezone
        elif hasattr(dt, "tz") and dt.tz is not None:
            return dt.replace(tzinfo=None)
        # Si c'est une série pandas sans timezone, on n'a rien à faire
        return dt
    except Exception as e:
        # En cas d'erreur, retourner l'input original
        print(f"Warning: to_datetime_naive failed for {dt_input}: {e}")
        return dt_input


def localize_datetime(dt):
    """Convertit un datetime pandas en timezone locale (Europe/Paris) pour affichage uniquement"""
    try:
        if isinstance(dt, str):
            dt = pd.to_datetime(dt)

        # Si c'est un pandas Timestamp
        if isinstance(dt, pd.Timestamp):
            # Si c'est déjà aware
            if dt.tz is not None:
                if str(dt.tz) == "UTC":
                    paris_tz = pytz.timezone("Europe/Paris")
                    return dt.tz_convert(paris_tz)
                return dt
            else:
                # Naive timestamp, supposer UTC et convertir
                paris_tz = pytz.timezone("Europe/Paris")
                utc_dt = dt.tz_localize("UTC")
                return utc_dt.tz_convert(paris_tz)

        # Si c'est un datetime Python standard
        if hasattr(dt, "tzinfo"):
            if dt.tzinfo is None:
                # Naive datetime, supposer UTC
                utc_dt = pytz.utc.localize(dt)
                paris_tz = pytz.timezone("Europe/Paris")
                return utc_dt.astimezone(paris_tz)
            else:
                # Déjà aware
                paris_tz = pytz.timezone("Europe/Paris")
                return dt.astimezone(paris_tz)

        return dt
    except Exception as e:
        return dt


def match_consultation_to_vacation(consultation_debut_prevu, vacations_list):
    """
    Détermine à quelle vacation appartient une consultation selon la logique suivante:
    Une consultation appartient à une vacation si consultation_debut_prevu est dans l'intervalle de la vacation
    IMPORTANT: Seules les vacations de type "consultation" sont considérées
    """
    if not vacations_list:
        return None, None, None

    # Filtrer uniquement les vacations de type "consultation"
    consultation_vacations = [
        v for v in vacations_list if v.get("type", "").lower() in ["consultation"]
    ]

    if not consultation_vacations:
        return None, None, None

    # Convertir en datetime si nécessaire et extraire l'heure
    if isinstance(consultation_debut_prevu, str):
        consultation_debut_prevu = to_datetime_naive(consultation_debut_prevu)
        consult_start_time = consultation_debut_prevu.time()
    elif hasattr(consultation_debut_prevu, "time"):
        # C'est déjà un datetime pandas/python
        consult_start_time = consultation_debut_prevu.time()
    elif hasattr(consultation_debut_prevu, "hour"):
        # C'est déjà un objet time
        consult_start_time = consultation_debut_prevu
    else:
        # Essayer de convertir
        try:
            consultation_debut_prevu = to_datetime_naive(consultation_debut_prevu)
            consult_start_time = consultation_debut_prevu.time()
        except:
            return None, None, None

    for vacation in consultation_vacations:
        vac_start_str = vacation.get("start_time", "")
        vac_end_str = vacation.get("end_time", "")

        if not vac_start_str or not vac_end_str:
            continue

        # Convertir les heures de vacation en time objects
        try:
            vac_start_time = pd.to_datetime(vac_start_str).time()
            vac_end_time = pd.to_datetime(vac_end_str).time()
        except:
            continue

        # Vérifier si l'heure de début prévue est dans la vacation
        if vac_start_time <= consult_start_time <= vac_end_time:
            return vacation, "in_vacation", 0

    return None, "no_vacation", None


def calculate_consultation_vacation_metrics(
    consultation_debut_prevu,
    consultation_fin_prevu,
    consultation_debut_reel,
    consultation_fin_reel,
    consultation_arrive,
    matched_vacation,
    match_type,
):
    """
    Calcule les métriques de vacation pour une consultation:
    - Retard/avance par rapport au début prévu
    - Retard/avance patient (arrivée vs début prévu)
    - Durée consultation vs durée prévue
    """
    metrics = {
        "vacation_start": None,
        "vacation_end": None,
        "match_type": match_type,
        "delay_from_planned_minutes": None,
        "patient_arrival_delay_minutes": None,
        "consultation_duration_minutes": None,
        "planned_duration_minutes": None,
    }

    # Traitement des infos vacation (si disponibles)
    if matched_vacation:
        vac_start_str = matched_vacation.get("start_time", "")
        vac_end_str = matched_vacation.get("end_time", "")

        if vac_start_str and vac_end_str:
            metrics["vacation_start"] = vac_start_str
            metrics["vacation_end"] = vac_end_str

    # Calculer le retard/avance par rapport au début prévu
    if consultation_debut_reel and consultation_debut_prevu:
        try:
            if isinstance(consultation_debut_reel, str):
                consultation_debut_reel = pd.to_datetime(consultation_debut_reel)
            if isinstance(consultation_debut_prevu, str):
                consultation_debut_prevu = pd.to_datetime(consultation_debut_prevu)

            # S'assurer que les datetime sont naive
            if (
                hasattr(consultation_debut_reel, "tz")
                and consultation_debut_reel.tz is not None
            ):
                consultation_debut_reel = consultation_debut_reel.replace(tzinfo=None)
            if (
                hasattr(consultation_debut_prevu, "tz")
                and consultation_debut_prevu.tz is not None
            ):
                consultation_debut_prevu = consultation_debut_prevu.replace(tzinfo=None)

            delay_minutes = (
                consultation_debut_reel - consultation_debut_prevu
            ).total_seconds() / 60
            metrics["delay_from_planned_minutes"] = delay_minutes
        except Exception as e:
            print(f"Debug: Error calculating consultation delay: {e}")
            pass
    else:
        print(
            f"Debug: Missing consultation times - debut_reel: {consultation_debut_reel}, debut_prevu: {consultation_debut_prevu}"
        )

    # Calculer le retard/avance du patient (arrivée vs début prévu)
    if consultation_arrive and consultation_debut_prevu:
        try:
            if isinstance(consultation_arrive, str):
                consultation_arrive = pd.to_datetime(consultation_arrive)
            if isinstance(consultation_debut_prevu, str):
                consultation_debut_prevu = pd.to_datetime(consultation_debut_prevu)

            # S'assurer que les datetime sont naive
            if (
                hasattr(consultation_arrive, "tz")
                and consultation_arrive.tz is not None
            ):
                consultation_arrive = consultation_arrive.replace(tzinfo=None)
            if (
                hasattr(consultation_debut_prevu, "tz")
                and consultation_debut_prevu.tz is not None
            ):
                consultation_debut_prevu = consultation_debut_prevu.replace(tzinfo=None)

            arrival_delay_minutes = (
                consultation_arrive - consultation_debut_prevu
            ).total_seconds() / 60
            metrics["patient_arrival_delay_minutes"] = arrival_delay_minutes
        except Exception as e:
            print(f"Debug: Error calculating patient delay: {e}")
            pass
    else:
        print(
            f"Debug: Missing patient arrival times - arrive: {consultation_arrive}, debut_prevu: {consultation_debut_prevu}"
        )

    # Calculer la durée réelle de consultation
    if consultation_debut_reel and consultation_fin_reel:
        try:
            if isinstance(consultation_debut_reel, str):
                consultation_debut_reel = pd.to_datetime(consultation_debut_reel)
            if isinstance(consultation_fin_reel, str):
                consultation_fin_reel = pd.to_datetime(consultation_fin_reel)

            # S'assurer que les datetime sont naive
            if (
                hasattr(consultation_debut_reel, "tz")
                and consultation_debut_reel.tz is not None
            ):
                consultation_debut_reel = consultation_debut_reel.replace(tzinfo=None)
            if (
                hasattr(consultation_fin_reel, "tz")
                and consultation_fin_reel.tz is not None
            ):
                consultation_fin_reel = consultation_fin_reel.replace(tzinfo=None)

            duration_minutes = (
                consultation_fin_reel - consultation_debut_reel
            ).total_seconds() / 60
            metrics["consultation_duration_minutes"] = duration_minutes
        except:
            pass

    # Calculer la durée prévue de consultation
    if consultation_debut_prevu and consultation_fin_prevu:
        try:
            if isinstance(consultation_debut_prevu, str):
                consultation_debut_prevu = pd.to_datetime(consultation_debut_prevu)
            if isinstance(consultation_fin_prevu, str):
                consultation_fin_prevu = pd.to_datetime(consultation_fin_prevu)

            # S'assurer que les datetime sont naive
            if (
                hasattr(consultation_debut_prevu, "tz")
                and consultation_debut_prevu.tz is not None
            ):
                consultation_debut_prevu = consultation_debut_prevu.replace(tzinfo=None)
            if (
                hasattr(consultation_fin_prevu, "tz")
                and consultation_fin_prevu.tz is not None
            ):
                consultation_fin_prevu = consultation_fin_prevu.replace(tzinfo=None)

            planned_duration_minutes = (
                consultation_fin_prevu - consultation_debut_prevu
            ).total_seconds() / 60
            metrics["planned_duration_minutes"] = planned_duration_minutes
        except:
            pass

    return metrics


def consultation_statistics_main(
    consultations, user=None, start_date=None, end_date=None, date_intervals=None
):
    """
    Statistiques unifiées pour les consultations avec calculs vacation complets:
    - Matching vacation pour chaque consultation
    - Première/dernière consultation vs vacation
    - Temps entre consultations
    - Taux de remplissage vacation
    - Ponctualité patients et consultations
    """

    # Si on a une liste d'intervalles, traiter la comparaison
    if date_intervals and isinstance(date_intervals, list):
        return _analyze_multiple_intervals_consultations(
            consultations, user, date_intervals
        )

    # Create DataFrame from consultations queryset
    if hasattr(consultations, "values"):
        # Récupérer les champs nécessaires y compris le nom du patient
        df = pd.DataFrame(
            list(
                consultations.values(
                    "id",
                    "consultation_date",
                    "consultation_debut_prevu",
                    "consultation_fin_prevu",
                    "consultation_debut_reel",
                    "consultation_fin_reel",
                    "consultation_arrive",
                    "etat_consultation",
                    "zkf_patient__nom",
                    "zkf_patient__prenom",
                )
            )
        )
        # Créer un nom complet pour l'affichage
        if "zkf_patient__nom" in df.columns and "zkf_patient__prenom" in df.columns:
            df["patient_nom"] = df.apply(
                lambda row: (
                    f"{row['zkf_patient__prenom']} {row['zkf_patient__nom']}"
                    if pd.notna(row["zkf_patient__nom"])
                    and pd.notna(row["zkf_patient__prenom"])
                    else row.get("zkf_patient__nom", "Patient inconnu")
                ),
                axis=1,
            )
    else:
        df = pd.DataFrame(consultations)

    if df.empty:
        return {"message": "Pas de consultations pour cette période"}

    # Get vacation data
    vacation_data = None
    if user and start_date and end_date:
        try:
            vacation_data = get_vacations(
                user, start_date=start_date, end_date=end_date
            )
        except Exception as e:
            print(f"Warning: Could not fetch vacation data: {e}")
            vacation_data = None

    # Convert date/time columns
    time_columns = [
        "consultation_debut_prevu",
        "consultation_fin_prevu",
        "consultation_debut_reel",
        "consultation_fin_reel",
        "consultation_arrive",
    ]
    date_columns = ["consultation_date"]

    # Convertir les dates
    for col in date_columns:
        if col in df.columns:
            try:
                df[col] = to_datetime_naive(df[col])
                print(f"Debug: Converted {col} to datetime, dtype: {df[col].dtype}")
            except Exception as e:
                print(f"Warning: Failed to convert {col} to datetime: {e}")
                # Essayer de forcer la conversion
                try:
                    df[col] = pd.to_datetime(df[col], errors="coerce")
                    print(f"Debug: Forced conversion of {col}, dtype: {df[col].dtype}")
                except:
                    print(f"Error: Could not convert {col} at all, keeping original")

    # Combiner date et heure pour créer des datetime complets
    # Gardons les colonnes time originales et créons de nouvelles colonnes datetime
    if "consultation_date" in df.columns:
        for col in time_columns:
            if col in df.columns and df[col].notna().any():
                # Créer des datetime en combinant date et heure
                try:
                    print(
                        f"Debug: Processing time column {col}, sample values: {df[col].head(3).tolist() if not df[col].empty else 'empty'}"
                    )
                    df[f"{col}_full"] = df.apply(
                        lambda row: (
                            pd.to_datetime(
                                f"{row['consultation_date'].date()} {row[col]}"
                            )
                            if pd.notna(row["consultation_date"])
                            and pd.notna(row[col])
                            and hasattr(row["consultation_date"], "date")
                            else None
                        ),
                        axis=1,
                    )
                    print(f"Debug: Created {col}_full column successfully")
                except Exception as e:
                    print(f"Warning: Could not convert {col} to datetime: {e}")
                    # Créer une colonne vide si la conversion échoue
                    df[f"{col}_full"] = None

    # Determine if single date or date range
    is_single_date = _is_single_date_analysis_consultations(df, start_date, end_date)

    if is_single_date:
        return _analyze_single_date_consultations(df, vacation_data, start_date)
    else:
        return _analyze_date_range_consultations(
            df, vacation_data, start_date, end_date
        )


def _is_single_date_analysis_consultations(df, start_date, end_date):
    """Détermine s'il s'agit d'une analyse sur une seule date"""
    if start_date and end_date:
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        return (end_dt - start_dt).days <= 1

    if "consultation_date" in df.columns:
        if hasattr(
            df["consultation_date"], "dt"
        ) and pd.api.types.is_datetime64_any_dtype(df["consultation_date"]):
            unique_dates = df["consultation_date"].dt.date.nunique()
        else:
            unique_dates = (
                df["consultation_date"]
                .apply(lambda x: x.date() if hasattr(x, "date") else x)
                .nunique()
            )
        return unique_dates <= 1

    return True


def _analyze_single_date_consultations(df, vacation_data, target_date=None):
    """Analyse pour une date unique avec nouveau système de matching vacation pour consultations"""

    result = {
        "analysis_type": "single_date",
        "date": target_date.strftime("%Y-%m-%d") if target_date else "date_unique",
        "total_consultations": len(df),
    }

    # Filtrer les consultations avec heures valides
    consultations_with_times = df.dropna(subset=["consultation_debut_prevu"])

    if consultations_with_times.empty:
        result["error"] = "Aucune consultation avec horaires valides"
        return convert_to_json_serializable(result)

    # Trier par heure de début prévue
    consultations_sorted = consultations_with_times.sort_values(
        "consultation_debut_prevu"
    )

    # Pour avoir une date de base pour les calculs
    first_consultation_start = consultations_sorted.iloc[0]["consultation_debut_prevu"]

    # Nouveau système de matching vacation pour chaque consultation
    consultations_with_vacation = []
    vacation_metrics_summary = {
        "total_consultations": len(consultations_sorted),
        "consultations_with_vacation": 0,
        "consultations_without_vacation": 0,
        "match_types": {},
        "total_consultation_delay": 0,
        "total_patient_delay": 0,
        "on_time_consultations": 0,
        "late_consultations": 0,
        "early_consultations": 0,
        "on_time_patients": 0,
        "late_patients": 0,
        "early_patients": 0,
    }

    for _, consultation in consultations_sorted.iterrows():
        try:
            # Utiliser les colonnes time originales pour le matching vacation
            consult_debut_prevu_time = consultation["consultation_debut_prevu"]
            consult_fin_prevu_time = consultation.get("consultation_fin_prevu")
            consult_debut_reel_time = consultation.get("consultation_debut_reel")
            consult_fin_reel_time = consultation.get("consultation_fin_reel")
            consult_arrive_time = consultation.get("consultation_arrive")

            # Utiliser les colonnes datetime pour les calculs
            consult_debut_prevu_full = consultation.get("consultation_debut_prevu_full")
            consult_fin_prevu_full = consultation.get("consultation_fin_prevu_full")
            consult_debut_reel_full = consultation.get("consultation_debut_reel_full")
            consult_fin_reel_full = consultation.get("consultation_fin_reel_full")
            consult_arrive_full = consultation.get("consultation_arrive_full")

            # Matcher la consultation avec une vacation (utilise les time)
            matched_vacation, match_type, _ = match_consultation_to_vacation(
                consult_debut_prevu_time, vacation_data
            )

            # Debug: Log data before calling vacation metrics calculation
            debut_prevu_param = (
                consult_debut_prevu_full
                if consult_debut_prevu_full is not None
                else consult_debut_prevu_time
            )
            debut_reel_param = (
                consult_debut_reel_full
                if consult_debut_reel_full is not None
                else consult_debut_reel_time
            )
            arrive_param = (
                consult_arrive_full
                if consult_arrive_full is not None
                else consult_arrive_time
            )

            print(
                f"Debug: Consultation {consultation.get('id', 'unknown')} - Params being passed:"
            )
            print(
                f"  debut_prevu: {debut_prevu_param} (type: {type(debut_prevu_param)})"
            )
            print(f"  debut_reel: {debut_reel_param} (type: {type(debut_reel_param)})")
            print(f"  arrive: {arrive_param} (type: {type(arrive_param)})")

            # Calculer les métriques de vacation (utilise les datetime full ou time si full n'existe pas)
            vacation_metrics = calculate_consultation_vacation_metrics(
                debut_prevu_param,
                (
                    consult_fin_prevu_full
                    if consult_fin_prevu_full is not None
                    else consult_fin_prevu_time
                ),
                debut_reel_param,
                (
                    consult_fin_reel_full
                    if consult_fin_reel_full is not None
                    else consult_fin_reel_time
                ),
                arrive_param,
                matched_vacation,
                match_type,
            )

            print(f"Debug: Returned vacation_metrics: {vacation_metrics}")

            # Ajouter l'info vacation à la consultation
            consultation_info = {
                "consultation_id": consultation.get("id", "unknown"),
                "consultation_debut_prevu": (
                    localize_datetime(consult_debut_prevu_time).strftime("%H:%M")
                    if consult_debut_prevu_time
                    else None
                ),
                "consultation_debut_reel": (
                    localize_datetime(consult_debut_reel_time).strftime("%H:%M")
                    if consult_debut_reel_time
                    else None
                ),
                "consultation_fin_reel": (
                    localize_datetime(consult_fin_reel_time).strftime("%H:%M")
                    if consult_fin_reel_time
                    else None
                ),
                "consultation_arrive": (
                    localize_datetime(consult_arrive_time).strftime("%H:%M")
                    if consult_arrive_time
                    else None
                ),
                "patient_nom": consultation.get("patient_nom", ""),
                "etat_consultation": consultation.get("etat_consultation", ""),
                "vacation_metrics": vacation_metrics,
            }
            consultations_with_vacation.append(consultation_info)

            # Mettre à jour le résumé
            if matched_vacation:
                vacation_metrics_summary["consultations_with_vacation"] += 1

                # Compter les types de match
                if match_type not in vacation_metrics_summary["match_types"]:
                    vacation_metrics_summary["match_types"][match_type] = 0
                vacation_metrics_summary["match_types"][match_type] += 1
            else:
                vacation_metrics_summary["consultations_without_vacation"] += 1

            # Analyser ponctualité consultation (que vacation soit trouvée ou non)
            if (
                vacation_metrics
                and vacation_metrics.get("delay_from_planned_minutes") is not None
            ):
                delay = vacation_metrics["delay_from_planned_minutes"]
                # Vérifier que la valeur n'est pas NaN
                if not pd.isna(delay) and not np.isnan(delay):
                    vacation_metrics_summary["total_consultation_delay"] += delay

                    if delay > 5:
                        vacation_metrics_summary["late_consultations"] += 1
                    elif delay < -5:
                        vacation_metrics_summary["early_consultations"] += 1
                    else:
                        vacation_metrics_summary["on_time_consultations"] += 1
                else:
                    print(
                        f"Debug: Skipping consultation delay calculation - delay is NaN: {delay}"
                    )

            # Analyser ponctualité patient (que vacation soit trouvée ou non)
            if (
                vacation_metrics
                and vacation_metrics.get("patient_arrival_delay_minutes") is not None
            ):
                patient_delay = vacation_metrics["patient_arrival_delay_minutes"]
                # Vérifier que la valeur n'est pas NaN
                if not pd.isna(patient_delay) and not np.isnan(patient_delay):
                    vacation_metrics_summary["total_patient_delay"] += patient_delay

                    if patient_delay > 5:
                        vacation_metrics_summary["late_patients"] += 1
                    elif patient_delay < -5:
                        vacation_metrics_summary["early_patients"] += 1
                    else:
                        vacation_metrics_summary["on_time_patients"] += 1
                else:
                    print(
                        f"Debug: Skipping patient delay calculation - delay is NaN: {patient_delay}"
                    )

        except Exception as e:
            # En cas d'erreur, compter comme consultation sans vacation pour éviter de perdre le comptage
            print(
                f"Warning: Error processing consultation {consultation.get('id', 'unknown')}: {e}"
            )

            # Créer une consultation info basique
            consultation_info = {
                "consultation_id": consultation.get("id", "unknown"),
                "consultation_debut_prevu": str(
                    consultation.get("consultation_debut_prevu", "N/A")
                ),
                "consultation_debut_reel": None,
                "consultation_fin_reel": None,
                "consultation_arrive": None,
                "patient_nom": consultation.get("patient_nom", ""),
                "etat_consultation": consultation.get("etat_consultation", ""),
                "vacation_metrics": None,
            }
            consultations_with_vacation.append(consultation_info)
            vacation_metrics_summary["consultations_without_vacation"] += 1

    # Calculer les moyennes finales
    total_consultations_with_delays = (
        vacation_metrics_summary["on_time_consultations"]
        + vacation_metrics_summary["late_consultations"]
        + vacation_metrics_summary["early_consultations"]
    )
    total_patients_with_delays = (
        vacation_metrics_summary["on_time_patients"]
        + vacation_metrics_summary["late_patients"]
        + vacation_metrics_summary["early_patients"]
    )

    print(
        f"Debug: Final delay calculation - Total consultations with delays: {total_consultations_with_delays}"
    )
    print(
        f"Debug: Final delay calculation - Total patients with delays: {total_patients_with_delays}"
    )
    print(
        f"Debug: Final delay calculation - Total consultation delay sum: {vacation_metrics_summary['total_consultation_delay']}"
    )
    print(
        f"Debug: Final delay calculation - Total patient delay sum: {vacation_metrics_summary['total_patient_delay']}"
    )

    if total_consultations_with_delays > 0:
        vacation_metrics_summary["average_consultation_delay"] = (
            vacation_metrics_summary["total_consultation_delay"]
            / total_consultations_with_delays
        )
    else:
        vacation_metrics_summary["average_consultation_delay"] = 0

    if total_patients_with_delays > 0:
        vacation_metrics_summary["average_patient_delay"] = (
            vacation_metrics_summary["total_patient_delay"] / total_patients_with_delays
        )
    else:
        vacation_metrics_summary["average_patient_delay"] = 0

    # Analyser les premières/dernières consultations par vacation et calculs complémentaires
    vacation_first_last_analysis = []
    inter_consultation_metrics = {}

    if vacation_data:
        # Filtrer les vacations consultation
        consultation_vacations = [
            v for v in vacation_data if v.get("type", "").lower() in ["consultation"]
        ]

        for vacation in consultation_vacations:
            vac_start_str = vacation.get("start_time", "")
            vac_end_str = vacation.get("end_time", "")

            if not vac_start_str or not vac_end_str:
                continue

            # Trouver toutes les consultations de cette vacation
            vacation_consultations = []
            for consult_info in consultations_with_vacation:
                if (
                    consult_info.get("vacation_metrics")
                    and consult_info["vacation_metrics"].get("vacation_start")
                    == vac_start_str
                    and consult_info["vacation_metrics"].get("vacation_end")
                    == vac_end_str
                ):
                    vacation_consultations.append(consult_info)

            if vacation_consultations:
                # Trier par heure de début prévu pour trouver première/dernière consultation
                vacation_consultations.sort(key=lambda x: x["consultation_debut_prevu"])
                first_consult = vacation_consultations[0]
                last_consult = vacation_consultations[-1]

                # Calculer retard/avance première consultation vs début vacation théorique
                try:
                    vacation_start_theoretical = (
                        f"{first_consultation_start.date()} {vac_start_str}"
                    )
                    vacation_start_dt = pd.to_datetime(vacation_start_theoretical)
                    if (
                        hasattr(vacation_start_dt, "tz")
                        and vacation_start_dt.tz is not None
                    ):
                        vacation_start_dt = vacation_start_dt.replace(tzinfo=None)

                    # Convertir l'heure de première consultation
                    first_consult_time = first_consult["consultation_debut_prevu"]
                    first_consult_dt = pd.to_datetime(
                        f"{first_consultation_start.date()} {first_consult_time}"
                    )
                    if (
                        hasattr(first_consult_dt, "tz")
                        and first_consult_dt.tz is not None
                    ):
                        first_consult_dt = first_consult_dt.replace(tzinfo=None)

                    delay_first_minutes = (
                        first_consult_dt - vacation_start_dt
                    ).total_seconds() / 60

                    # Calculer retard/avance dernière consultation vs fin vacation théorique
                    vacation_end_theoretical = (
                        f"{first_consultation_start.date()} {vac_end_str}"
                    )
                    vacation_end_dt = pd.to_datetime(vacation_end_theoretical)
                    if (
                        hasattr(vacation_end_dt, "tz")
                        and vacation_end_dt.tz is not None
                    ):
                        vacation_end_dt = vacation_end_dt.replace(tzinfo=None)

                    # Utiliser l'heure de fin réelle si disponible, sinon prévue
                    last_consult_end = (
                        last_consult.get("consultation_fin_reel")
                        or last_consult["consultation_debut_prevu"]
                    )
                    last_consult_end_dt = pd.to_datetime(
                        f"{first_consultation_start.date()} {last_consult_end}"
                    )
                    if (
                        hasattr(last_consult_end_dt, "tz")
                        and last_consult_end_dt.tz is not None
                    ):
                        last_consult_end_dt = last_consult_end_dt.replace(tzinfo=None)

                    delay_last_minutes = (
                        last_consult_end_dt - vacation_end_dt
                    ).total_seconds() / 60

                    vacation_first_last_analysis.append(
                        {
                            "vacation_start": vac_start_str,
                            "vacation_end": vac_end_str,
                            "first_consultation_time": first_consult[
                                "consultation_debut_prevu"
                            ],
                            "last_consultation_end_time": last_consult_end,
                            "delay_first_vs_vacation_minutes": delay_first_minutes,
                            "delay_last_vs_vacation_minutes": delay_last_minutes,
                            "first_delay_status": (
                                "À l'heure"
                                if delay_first_minutes == 0
                                else (
                                    f"Retard de {int(abs(delay_first_minutes))}min"
                                    if delay_first_minutes > 0
                                    else f"Avance de {int(abs(delay_first_minutes))}min"
                                )
                            ),
                            "last_delay_status": (
                                "À l'heure"
                                if delay_last_minutes == 0
                                else (
                                    f"Dépassement de {int(abs(delay_last_minutes))}min"
                                    if delay_last_minutes > 0
                                    else f"Fin {int(abs(delay_last_minutes))}min avant"
                                )
                            ),
                            "consultations_count": len(vacation_consultations),
                        }
                    )
                except:
                    continue

    vacation_metrics_summary["vacation_first_last_analysis"] = (
        vacation_first_last_analysis
    )

    # Calculer les temps inter-consultations
    if len(consultations_with_vacation) > 1:
        # Trier les consultations par heure de début prévu/réel
        sorted_consultations = sorted(
            consultations_with_vacation, key=lambda x: x["consultation_debut_prevu"]
        )
        inter_consultation_times = []

        for i in range(len(sorted_consultations) - 1):
            current_consult = sorted_consultations[i]
            next_consult = sorted_consultations[i + 1]

            # Utiliser consultation_fin_prevu de la consultation actuelle et consultation_debut_reel de la suivante
            try:
                # Pour la fin, utiliser l'heure réelle si disponible, sinon prévue
                current_end = current_consult.get("consultation_fin_reel")
                if not current_end:
                    # Si pas de fin réelle, estimer avec début prévu + durée moyenne (15min par défaut)
                    current_start = current_consult["consultation_debut_prevu"]
                    current_start_dt = pd.to_datetime(
                        f"{first_consultation_start.date()} {current_start}"
                    )
                    current_end_dt = current_start_dt + timedelta(
                        minutes=15
                    )  # Durée estimée
                    current_end = current_end_dt.strftime("%H:%M")

                next_start = (
                    next_consult.get("consultation_debut_reel")
                    or next_consult["consultation_debut_prevu"]
                )

                # Convertir en datetime pour calcul
                current_end_dt = pd.to_datetime(
                    f"{first_consultation_start.date()} {current_end}"
                )
                next_start_dt = pd.to_datetime(
                    f"{first_consultation_start.date()} {next_start}"
                )

                if hasattr(current_end_dt, "tz") and current_end_dt.tz is not None:
                    current_end_dt = current_end_dt.replace(tzinfo=None)
                if hasattr(next_start_dt, "tz") and next_start_dt.tz is not None:
                    next_start_dt = next_start_dt.replace(tzinfo=None)

                interval_minutes = (next_start_dt - current_end_dt).total_seconds() / 60
                inter_consultation_times.append(interval_minutes)
            except:
                continue

        if inter_consultation_times:
            inter_consultation_metrics = {
                "total_inter_consultation_time_minutes": sum(inter_consultation_times),
                "mean_inter_consultation_time_minutes": np.mean(
                    inter_consultation_times
                ),
                "min_inter_consultation_time_minutes": min(inter_consultation_times),
                "max_inter_consultation_time_minutes": max(inter_consultation_times),
                "intervals_count": len(inter_consultation_times),
            }

    # Calculer le taux de remplissage par vacation
    vacation_occupation_details = []
    if vacation_data:
        consultation_vacations = [
            v for v in vacation_data if v.get("type", "").lower() in ["consultation"]
        ]

        for vacation in consultation_vacations:
            vac_start_str = vacation.get("start_time", "")
            vac_end_str = vacation.get("end_time", "")

            if not vac_start_str or not vac_end_str:
                continue

            try:
                # Calculer la durée de la vacation
                vac_start_dt = pd.to_datetime(
                    f"{first_consultation_start.date()} {vac_start_str}"
                )
                vac_end_dt = pd.to_datetime(
                    f"{first_consultation_start.date()} {vac_end_str}"
                )

                if hasattr(vac_start_dt, "tz") and vac_start_dt.tz is not None:
                    vac_start_dt = vac_start_dt.replace(tzinfo=None)
                if hasattr(vac_end_dt, "tz") and vac_end_dt.tz is not None:
                    vac_end_dt = vac_end_dt.replace(tzinfo=None)

                vacation_duration_minutes = (
                    vac_end_dt - vac_start_dt
                ).total_seconds() / 60

                # Trouver toutes les consultations de cette vacation et sommer leurs durées
                vacation_consultation_time = 0
                consultation_count = 0
                for consult_info in consultations_with_vacation:
                    if (
                        consult_info.get("vacation_metrics")
                        and consult_info["vacation_metrics"].get("vacation_start")
                        == vac_start_str
                        and consult_info["vacation_metrics"].get("vacation_end")
                        == vac_end_str
                    ):

                        consultation_count += 1
                        # Utiliser la durée réelle si disponible
                        duration = consult_info["vacation_metrics"].get(
                            "consultation_duration_minutes"
                        )
                        if duration:
                            vacation_consultation_time += duration
                        else:
                            # Durée estimée si pas de données réelles
                            vacation_consultation_time += 15  # 15 minutes par défaut

                # Calculer le taux de remplissage
                if vacation_duration_minutes > 0:
                    occupation_rate = (
                        vacation_consultation_time / vacation_duration_minutes
                    ) * 100
                    vacation_occupation_details.append(
                        {
                            "vacation_start": vac_start_str,
                            "vacation_end": vac_end_str,
                            "vacation_duration_minutes": vacation_duration_minutes,
                            "total_consultation_time_minutes": vacation_consultation_time,
                            "occupation_rate_percent": occupation_rate,
                            "consultations_count": consultation_count,
                        }
                    )
            except:
                continue

    # Calculer les min/max des retards et avances
    consultation_delays = []
    patient_delays = []

    for consult_info in consultations_with_vacation:
        if consult_info.get("vacation_metrics"):
            metrics = consult_info["vacation_metrics"]

            # Retards consultations
            if metrics.get("delay_from_planned_minutes") is not None:
                consultation_delays.append(metrics["delay_from_planned_minutes"])

            # Retards patients
            if metrics.get("patient_arrival_delay_minutes") is not None:
                patient_delays.append(metrics["patient_arrival_delay_minutes"])

    delay_extremes = {}
    if consultation_delays and len(consultation_delays) > 0:
        min_consultation_delay = min(consultation_delays)
        max_consultation_delay = max(consultation_delays)
        delay_extremes["consultation_delays"] = {
            "min_delay_minutes": min_consultation_delay,
            "max_delay_minutes": max_consultation_delay,
            "min_delay_status": (
                f"Avance max: {abs(min_consultation_delay):.0f}min"
                if min_consultation_delay < 0
                else f"Min retard: {min_consultation_delay:.0f}min"
            ),
            "max_delay_status": (
                f"Max retard: {max_consultation_delay:.0f}min"
                if max_consultation_delay > 0
                else f"Min avance: {abs(max_consultation_delay):.0f}min"
            ),
        }

    if patient_delays and len(patient_delays) > 0:
        min_patient_delay = min(patient_delays)
        max_patient_delay = max(patient_delays)
        delay_extremes["patient_delays"] = {
            "min_delay_minutes": min_patient_delay,
            "max_delay_minutes": max_patient_delay,
            "min_delay_status": (
                f"Avance max: {abs(min_patient_delay):.0f}min"
                if min_patient_delay < 0
                else f"Min retard: {min_patient_delay:.0f}min"
            ),
            "max_delay_status": (
                f"Max retard: {max_patient_delay:.0f}min"
                if max_patient_delay > 0
                else f"Min avance: {abs(max_patient_delay):.0f}min"
            ),
        }

    # Mettre à jour les métriques finales
    vacation_metrics_summary.update(
        {
            "inter_consultation_metrics": inter_consultation_metrics,
            "vacation_occupation_details": vacation_occupation_details,
            "delay_extremes": delay_extremes,
        }
    )

    result.update(
        {
            "vacation_analysis": vacation_metrics_summary,
            "consultations_detail": consultations_with_vacation,
            "available_vacations": (
                [
                    v
                    for v in vacation_data
                    if v.get("type", "").lower() in ["consultation"]
                ]
                if vacation_data
                else []
            ),
            "vacation_count": (
                len(
                    [
                        v
                        for v in vacation_data
                        if v.get("type", "").lower() in ["consultation"]
                    ]
                )
                if vacation_data
                else 0
            ),
        }
    )

    return convert_to_json_serializable(result)


def _analyze_date_range_consultations(df, vacation_data, start_date, end_date):
    """Analyse pour un intervalle de dates pour consultations - valeurs moyennes avec sous-classement"""

    result = {
        "analysis_type": "date_range",
        "start_date": start_date.strftime("%Y-%m-%d") if start_date else None,
        "end_date": end_date.strftime("%Y-%m-%d") if end_date else None,
        "total_consultations": len(df),
    }

    # Déterminer la granularité d'analyse
    if start_date and end_date:
        date_range = (end_date - start_date).days
    else:
        if "consultation_date" in df.columns:
            if hasattr(
                df["consultation_date"], "dt"
            ) and pd.api.types.is_datetime64_any_dtype(df["consultation_date"]):
                date_range = df["consultation_date"].dt.date.nunique()
            else:
                date_range = (
                    df["consultation_date"]
                    .apply(lambda x: x.date() if hasattr(x, "date") else x)
                    .nunique()
                )
        else:
            date_range = 1

    if date_range <= 7:
        grouping = "day"
    elif date_range <= 31:
        grouping = "week"
    elif date_range <= 365:
        grouping = "month"
    else:
        grouping = "year"

    result["grouping"] = grouping

    # Filtrer les consultations avec horaires valides
    consultations_with_times = df.dropna(subset=["consultation_debut_prevu"])

    # S'assurer que consultation_debut_prevu est en format datetime
    if (
        not consultations_with_times.empty
        and "consultation_debut_prevu" in consultations_with_times.columns
    ):
        try:
            # Vérifier si c'est déjà datetime
            if not pd.api.types.is_datetime64_any_dtype(
                consultations_with_times["consultation_debut_prevu"]
            ):
                print(
                    f"Debug: consultation_debut_prevu is not datetime, current dtype: {consultations_with_times['consultation_debut_prevu'].dtype}"
                )
                # Essayer de le convertir
                consultations_with_times["consultation_debut_prevu"] = pd.to_datetime(
                    consultations_with_times["consultation_debut_prevu"],
                    errors="coerce",
                )
                print(
                    f"Debug: Converted consultation_debut_prevu to datetime, new dtype: {consultations_with_times['consultation_debut_prevu'].dtype}"
                )
            else:
                print(
                    f"Debug: consultation_debut_prevu is already datetime: {consultations_with_times['consultation_debut_prevu'].dtype}"
                )
        except Exception as e:
            print(f"Error: Could not process consultation_debut_prevu column: {e}")

    if consultations_with_times.empty:
        result["error"] = "Aucune consultation avec horaires valides"
        return convert_to_json_serializable(result)

    # Ajouter colonnes pour le groupement
    consultations_with_times = consultations_with_times.copy()
    if hasattr(
        consultations_with_times["consultation_debut_prevu"], "dt"
    ) and pd.api.types.is_datetime64_any_dtype(
        consultations_with_times["consultation_debut_prevu"]
    ):
        consultations_with_times["weekday"] = consultations_with_times[
            "consultation_debut_prevu"
        ].dt.day_name()
        consultations_with_times["week"] = (
            consultations_with_times["consultation_debut_prevu"].dt.isocalendar().week
        )
        consultations_with_times["month"] = consultations_with_times[
            "consultation_debut_prevu"
        ].dt.month
        consultations_with_times["year"] = consultations_with_times[
            "consultation_debut_prevu"
        ].dt.year
    else:
        consultations_with_times["weekday"] = consultations_with_times[
            "consultation_debut_prevu"
        ].apply(lambda x: x.strftime("%A") if hasattr(x, "strftime") else "Unknown")
        consultations_with_times["week"] = consultations_with_times[
            "consultation_debut_prevu"
        ].apply(lambda x: x.isocalendar()[1] if hasattr(x, "isocalendar") else 1)
        consultations_with_times["month"] = consultations_with_times[
            "consultation_debut_prevu"
        ].apply(lambda x: x.month if hasattr(x, "month") else 1)
        consultations_with_times["year"] = consultations_with_times[
            "consultation_debut_prevu"
        ].apply(lambda x: x.year if hasattr(x, "year") else 2024)

    # Analyse globale (moyennes sur toute la période)
    global_stats = _calculate_global_averages_consultations(
        consultations_with_times, vacation_data
    )
    result["global_averages"] = global_stats

    # Analyse par sous-période
    grouped_analysis = _calculate_grouped_analysis_consultations(
        consultations_with_times, grouping, vacation_data
    )
    result["grouped_analysis"] = grouped_analysis

    # Analyse par jour de la semaine
    weekday_analysis = _calculate_weekday_analysis_consultations(
        consultations_with_times, vacation_data
    )
    result["weekday_analysis"] = weekday_analysis

    return convert_to_json_serializable(result)


def _analyze_multiple_intervals_consultations(consultations, user, date_intervals):
    """Analyse comparative de plusieurs intervalles de dates pour consultations"""

    result = {
        "analysis_type": "multiple_intervals",
        "intervals_count": len(date_intervals),
        "intervals_analysis": [],
        "comparative_summary": {},
    }

    intervals_results = []

    for i, interval in enumerate(date_intervals):
        start_date = pd.to_datetime(interval["start"])
        end_date = pd.to_datetime(interval["end"])
        label = interval.get("label", f"Période {i+1}")

        if hasattr(consultations, "filter"):
            interval_consultations = consultations.filter(
                consultation_date__gte=start_date.date(),
                consultation_date__lte=end_date.date(),
            )
        else:
            df = (
                pd.DataFrame(consultations)
                if not isinstance(consultations, pd.DataFrame)
                else consultations
            )
            if "consultation_date" in df.columns:
                df["consultation_date"] = to_datetime_naive(df["consultation_date"])
                interval_consultations = df[
                    (df["consultation_date"] >= start_date)
                    & (df["consultation_date"] <= end_date)
                ]
            else:
                interval_consultations = df

        interval_analysis = consultation_statistics_main(
            consultations=interval_consultations,
            user=user,
            start_date=start_date,
            end_date=end_date,
        )

        interval_analysis["interval_info"] = {
            "label": label,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "duration_days": (end_date - start_date).days + 1,
            "interval_index": i,
        }

        intervals_results.append(interval_analysis)

    result["intervals_analysis"] = intervals_results
    result["comparative_summary"] = _generate_comparative_summary_consultations(
        intervals_results
    )

    return convert_to_json_serializable(result)


def unified_consultations_statistics(
    consultations, user=None, start_date=None, end_date=None, date_intervals=None
):
    """
    Point d'entrée principal pour les statistiques de consultations.
    Fonction simplifiée qui utilise consultation_statistics_main.
    """
    return consultation_statistics_main(
        consultations=consultations,
        user=user,
        start_date=start_date,
        end_date=end_date,
        date_intervals=date_intervals,
    )


def consultations_statistics(
    consultations, user=None, start_date=None, end_date=None, date_intervals=None
):
    """
    Alias pour la compatibilité avec l'ancien code.
    """
    return consultation_statistics_main(
        consultations=consultations,
        user=user,
        start_date=start_date,
        end_date=end_date,
        date_intervals=date_intervals,
    )


def _calculate_global_averages_consultations(df, vacation_data):
    """Calcule les moyennes globales pour consultations avec métriques vacation"""

    # Grouper par date pour calculer les moyennes journalières
    daily_stats = []

    if hasattr(
        df["consultation_debut_prevu"], "dt"
    ) and pd.api.types.is_datetime64_any_dtype(df["consultation_debut_prevu"]):
        date_groups = df.groupby(df["consultation_debut_prevu"].dt.date)
    else:
        df["temp_date"] = df["consultation_debut_prevu"].apply(
            lambda x: x.date() if hasattr(x, "date") else x
        )
        date_groups = df.groupby("temp_date")

    for date, day_df in date_groups:
        day_stats = _calculate_daily_stats_consultations(day_df, vacation_data, date)
        if day_stats:
            daily_stats.append(day_stats)

    if not daily_stats:
        return {}

    # Calculer les moyennes des stats journalières
    stats_df = pd.DataFrame(daily_stats)

    return {
        # Métriques vacation consultations
        "mean_delay_from_vacation_start_minutes": stats_df[
            "delay_from_vacation_start_minutes"
        ].mean(),
        "mean_vacation_occupation_rate_percent": stats_df[
            "vacation_occupation_rate_percent"
        ].mean(),
        # Autres métriques
        "mean_first_consultation_time": _calculate_mean_time(
            stats_df["first_consultation_time"]
        ),
        "mean_last_consultation_time": _calculate_mean_time(
            stats_df["last_consultation_time"]
        ),
        "mean_inter_consultation_time_minutes": stats_df[
            "mean_inter_consultation_time_minutes"
        ].mean(),
        "mean_consultation_duration_minutes": stats_df[
            "mean_consultation_duration_minutes"
        ].mean(),
        "mean_consultations_per_day": stats_df["consultations_count"].mean(),
        "total_days": len(daily_stats),
    }


def _calculate_daily_stats_consultations(day_df, vacation_data, date):
    """Calcule les statistiques pour une journée avec calculs vacation pour consultations"""

    if day_df.empty:
        return None

    # Trier par heure de début
    day_df_sorted = day_df.sort_values("consultation_debut_prevu")

    # Vacation info pour cette date
    vacation_start = None
    vacation_end = None
    if vacation_data:
        for vacation in vacation_data:
            vacation_start_str = vacation.get("start_time", "")
            vacation_end_str = vacation.get("end_time", "")
            vacation_type = vacation.get("type", "").lower()

            if (
                vacation_start_str
                and vacation_end_str
                and vacation_type in ["consultation"]
            ):
                vacation_start = pd.to_datetime(f"{date} {vacation_start_str}")
                vacation_end = pd.to_datetime(f"{date} {vacation_end_str}")
                break

    # Calculs avec logique consultation
    first_consultation_start = day_df_sorted.iloc[0]["consultation_debut_prevu"]
    last_consultation_end = day_df_sorted.iloc[-1][
        "consultation_debut_prevu"
    ]  # ou fin_prevu si disponible

    # RETARD/AVANCE par rapport au début de vacation
    delay_from_vacation_start = None
    if vacation_start:
        delay_from_vacation_start = (
            first_consultation_start - vacation_start
        ).total_seconds() / 60

    # Durées inter-consultations
    inter_consultation_durations = []
    for i in range(len(day_df_sorted) - 1):
        end_current = day_df_sorted.iloc[i][
            "consultation_debut_prevu"
        ]  # ou fin_prevu + durée estimée
        start_next = day_df_sorted.iloc[i + 1]["consultation_debut_prevu"]
        duration = (start_next - end_current).total_seconds() / 60
        inter_consultation_durations.append(duration)

    # Durées consultations estimées (15min par défaut si pas de données réelles)
    estimated_consultation_duration = 15  # minutes par consultation
    total_consultation_time = len(day_df_sorted) * estimated_consultation_duration

    # TAUX D'OCCUPATION corrigé pour consultations
    occupation_rate_percent = None
    if vacation_start and vacation_end:
        vacation_duration = (vacation_end - vacation_start).total_seconds() / 60
        occupation_rate_percent = (
            (total_consultation_time / vacation_duration) * 100
            if vacation_duration > 0
            else 0
        )

    return {
        "date": date.strftime("%Y-%m-%d"),
        "consultations_count": len(day_df_sorted),
        # Calculs vacation
        "delay_from_vacation_start_minutes": delay_from_vacation_start,
        "vacation_occupation_rate_percent": occupation_rate_percent,
        # Autres métriques
        "first_consultation_time": first_consultation_start.strftime("%H:%M"),
        "last_consultation_time": last_consultation_end.strftime("%H:%M"),
        "mean_inter_consultation_time_minutes": (
            np.mean(inter_consultation_durations) if inter_consultation_durations else 0
        ),
        "mean_consultation_duration_minutes": estimated_consultation_duration,
        "total_consultation_time_minutes": total_consultation_time,
    }


def _calculate_grouped_analysis_consultations(df, grouping, vacation_data):
    """Analyse groupée selon la granularité choisie pour consultations"""

    grouped_results = []

    if grouping == "day":
        if hasattr(
            df["consultation_debut_prevu"], "dt"
        ) and pd.api.types.is_datetime64_any_dtype(df["consultation_debut_prevu"]):
            date_groups = df.groupby(df["consultation_debut_prevu"].dt.date)
        else:
            df["temp_date"] = df["consultation_debut_prevu"].apply(
                lambda x: x.date() if hasattr(x, "date") else x
            )
            date_groups = df.groupby("temp_date")

        for date, day_df in date_groups:
            day_stats = _calculate_daily_stats_consultations(
                day_df, vacation_data, date
            )
            if day_stats:
                day_stats["period"] = date.strftime("%Y-%m-%d")
                day_stats["weekday"] = day_df.iloc[0]["weekday"]
                grouped_results.append(day_stats)

    elif grouping == "week":
        for (year, week), week_df in df.groupby([df["year"], df["week"]]):
            week_stats = _calculate_period_stats_consultations(week_df, vacation_data)
            if week_stats:
                week_stats["period"] = f"{year}-W{week:02d}"
                week_stats["year"] = year
                week_stats["week"] = week
                grouped_results.append(week_stats)

    elif grouping == "month":
        for (year, month), month_df in df.groupby([df["year"], df["month"]]):
            month_stats = _calculate_period_stats_consultations(month_df, vacation_data)
            if month_stats:
                month_stats["period"] = f"{year}-{month:02d}"
                month_stats["year"] = year
                month_stats["month"] = month
                grouped_results.append(month_stats)

    elif grouping == "year":
        for year, year_df in df.groupby(df["year"]):
            year_stats = _calculate_period_stats_consultations(year_df, vacation_data)
            if year_stats:
                year_stats["period"] = str(year)
                year_stats["year"] = year
                grouped_results.append(year_stats)

    return grouped_results


def _calculate_period_stats_consultations(period_df, vacation_data):
    """Calcule les statistiques moyennes pour une période pour consultations"""

    # Grouper par date dans la période
    daily_stats = []
    if hasattr(
        period_df["consultation_debut_prevu"], "dt"
    ) and pd.api.types.is_datetime64_any_dtype(period_df["consultation_debut_prevu"]):
        date_groups = period_df.groupby(period_df["consultation_debut_prevu"].dt.date)
    else:
        period_df["temp_date"] = period_df["consultation_debut_prevu"].apply(
            lambda x: x.date() if hasattr(x, "date") else x
        )
        date_groups = period_df.groupby("temp_date")

    for date, day_df in date_groups:
        day_stats = _calculate_daily_stats_consultations(day_df, vacation_data, date)
        if day_stats:
            daily_stats.append(day_stats)

    if not daily_stats:
        return None

    # Moyennes des stats journalières
    stats_df = pd.DataFrame(daily_stats)

    return {
        "days_count": len(daily_stats),
        "total_consultations": stats_df["consultations_count"].sum(),
        "mean_consultations_per_day": stats_df["consultations_count"].mean(),
        # Métriques vacation consultations
        "mean_delay_from_vacation_start_minutes": stats_df[
            "delay_from_vacation_start_minutes"
        ].mean(),
        "mean_vacation_occupation_rate_percent": stats_df[
            "vacation_occupation_rate_percent"
        ].mean(),
        # Autres métriques
        "mean_first_consultation_time": _calculate_mean_time(
            stats_df["first_consultation_time"]
        ),
        "mean_last_consultation_time": _calculate_mean_time(
            stats_df["last_consultation_time"]
        ),
        "mean_inter_consultation_time_minutes": stats_df[
            "mean_inter_consultation_time_minutes"
        ].mean(),
        "mean_consultation_duration_minutes": stats_df[
            "mean_consultation_duration_minutes"
        ].mean(),
    }


def _calculate_weekday_analysis_consultations(df, vacation_data):
    """Analyse par jour de la semaine pour consultations"""

    weekday_results = []

    for weekday, weekday_df in df.groupby("weekday"):
        weekday_stats = _calculate_period_stats_consultations(weekday_df, vacation_data)
        if weekday_stats:
            weekday_stats["weekday"] = weekday
            weekday_results.append(weekday_stats)

    return weekday_results


def _generate_comparative_summary_consultations(intervals_results):
    """Génère un résumé comparatif pour consultations avec métriques vacation"""

    if not intervals_results:
        return {}

    summary = {
        "metrics_comparison": {},
        "best_performance": {},
        "worst_performance": {},
        "trends": {},
    }

    # Extraire les métriques clés de chaque intervalle
    metrics_data = []

    for interval_result in intervals_results:
        interval_info = interval_result.get("interval_info", {})

        # Récupérer les métriques selon le type d'analyse
        if interval_result.get("analysis_type") == "single_date":
            metrics = {
                "label": interval_info.get("label", "Unknown"),
                "start_date": interval_info.get("start_date"),
                "total_consultations": interval_result.get("total_consultations", 0),
                # Métriques vacation consultations
                "vacation_occupation_rate": (
                    interval_result.get("vacation_analysis", {})
                    .get("vacation_occupation_details", [{}])[0]
                    .get("occupation_rate_percent")
                    if interval_result.get("vacation_analysis", {}).get(
                        "vacation_occupation_details"
                    )
                    else None
                ),
                "average_consultation_delay": interval_result.get(
                    "vacation_analysis", {}
                ).get("average_consultation_delay"),
                "average_patient_delay": interval_result.get(
                    "vacation_analysis", {}
                ).get("average_patient_delay"),
                # Métriques ponctualité
                "on_time_consultations": interval_result.get(
                    "vacation_analysis", {}
                ).get("on_time_consultations", 0),
                "late_consultations": interval_result.get("vacation_analysis", {}).get(
                    "late_consultations", 0
                ),
                "on_time_patients": interval_result.get("vacation_analysis", {}).get(
                    "on_time_patients", 0
                ),
                "late_patients": interval_result.get("vacation_analysis", {}).get(
                    "late_patients", 0
                ),
            }
        else:
            # Pour les intervalles de dates
            global_avg = interval_result.get("global_averages", {})
            metrics = {
                "label": interval_info.get("label", "Unknown"),
                "start_date": interval_info.get("start_date"),
                "total_consultations": interval_result.get("total_consultations", 0),
                # Métriques vacation consultations
                "vacation_occupation_rate": global_avg.get(
                    "mean_vacation_occupation_rate_percent"
                ),
                "delay_from_vacation_start": global_avg.get(
                    "mean_delay_from_vacation_start_minutes"
                ),
                # Autres métriques
                "first_consultation_time": global_avg.get(
                    "mean_first_consultation_time"
                ),
                "last_consultation_time": global_avg.get("mean_last_consultation_time"),
                "mean_inter_consultation_time": global_avg.get(
                    "mean_inter_consultation_time_minutes"
                ),
                "mean_consultation_duration": global_avg.get(
                    "mean_consultation_duration_minutes"
                ),
                "mean_consultations_per_day": global_avg.get(
                    "mean_consultations_per_day"
                ),
                "total_days": global_avg.get("total_days"),
            }

        metrics_data.append(metrics)

    if not metrics_data:
        return summary

    # Créer DataFrame pour les comparaisons
    df_metrics = pd.DataFrame(metrics_data)

    # Comparaison des métriques
    numeric_columns = df_metrics.select_dtypes(include=[np.number]).columns

    for col in numeric_columns:
        if col in df_metrics.columns:
            col_data = df_metrics[col].dropna()
            if not col_data.empty:
                summary["metrics_comparison"][col] = {
                    "values": col_data.tolist(),
                    "labels": df_metrics.loc[col_data.index, "label"].tolist(),
                    "mean": float(col_data.mean()),
                    "min": float(col_data.min()),
                    "max": float(col_data.max()),
                    "std": float(col_data.std()) if len(col_data) > 1 else 0,
                }

    # Identifier les meilleures et pires performances
    performance_metrics = [
        "total_consultations",
        "vacation_occupation_rate",
        "on_time_consultations",
    ]

    for metric in performance_metrics:
        if metric in df_metrics.columns:
            metric_data = df_metrics[metric].dropna()
            if not metric_data.empty:
                # Meilleure performance
                best_idx = metric_data.idxmax()
                summary["best_performance"][metric] = {
                    "label": df_metrics.loc[best_idx, "label"],
                    "value": float(metric_data.loc[best_idx]),
                    "date": df_metrics.loc[best_idx, "start_date"],
                }

                # Pire performance
                worst_idx = metric_data.idxmin()
                summary["worst_performance"][metric] = {
                    "label": df_metrics.loc[worst_idx, "label"],
                    "value": float(metric_data.loc[worst_idx]),
                    "date": df_metrics.loc[worst_idx, "start_date"],
                }

    return summary


def _calculate_mean_time(time_series):
    """Calcule la moyenne des heures (format HH:MM)"""
    try:
        minutes = []
        for time_str in time_series.dropna():
            if isinstance(time_str, str) and ":" in time_str:
                hours, mins = map(int, time_str.split(":"))
                total_minutes = hours * 60 + mins
                minutes.append(total_minutes)

        if minutes:
            mean_minutes = int(np.mean(minutes))
            hours = mean_minutes // 60
            mins = mean_minutes % 60
            return f"{hours:02d}:{mins:02d}"

        return None
    except:
        return None


def convert_to_json_serializable(obj):
    """Convert pandas/numpy types to JSON serializable types"""
    if isinstance(obj, dict):
        return {str(k): convert_to_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    elif isinstance(obj, pd.Timestamp):
        return obj.strftime("%Y-%m-%d")
    elif isinstance(obj, (np.integer, np.int64)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64)):
        # Gérer les valeurs NaN et infinity
        if np.isnan(obj) or np.isinf(obj):
            return None
        return float(obj)
    elif isinstance(obj, float):
        # Gérer les float Python standards
        if (
            obj != obj or obj == float("inf") or obj == float("-inf")
        ):  # obj != obj est un test pour NaN
            return None
        return obj
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif pd.isna(obj):
        return None
    else:
        return obj
