from operation.models import Operation
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.utils.dateparse import parse_datetime
from django.utils import timezone
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import pytz
from schedule.scripts.get_vacations import get_vacations


def to_datetime_naive(dt_input):
    """Convert to pandas datetime and ensure it's timezone-naive"""
    dt = pd.to_datetime(dt_input)

    # Si c'est une série pandas avec timezone
    if (
        isinstance(dt, pd.Series)
        and hasattr(dt.dtype, "tz")
        and dt.dtype.tz is not None
    ):
        if hasattr(dt, "dt"):
            return dt.dt.tz_localize(None)
        else:
            return dt.apply(
                lambda x: (
                    x.replace(tzinfo=None)
                    if hasattr(x, "tz") and x.tz is not None
                    else x
                )
            )
    # Si c'est un timestamp unique avec timezone
    elif hasattr(dt, "tz") and dt.tz is not None:
        return dt.replace(tzinfo=None)
    # Si c'est une série pandas sans timezone, on n'a rien à faire
    return dt


def localize_datetime(dt):
    """Convertit un datetime pandas en timezone locale (Europe/Paris) pour affichage uniquement"""
    try:
        if isinstance(dt, str):
            dt = pd.to_datetime(dt)

        # Si c'est un pandas Timestamp
        if isinstance(dt, pd.Timestamp):
            # Si c'est déjà aware
            if dt.tz is not None:
                if str(dt.tz) == "UTC":
                    paris_tz = pytz.timezone("Europe/Paris")
                    return dt.tz_convert(paris_tz)
                return dt
            else:
                # Naive timestamp, supposer UTC et convertir
                paris_tz = pytz.timezone("Europe/Paris")
                utc_dt = dt.tz_localize("UTC")
                return utc_dt.tz_convert(paris_tz)

        # Si c'est un datetime Python standard
        if hasattr(dt, "tzinfo"):
            if dt.tzinfo is None:
                # Naive datetime, supposer UTC
                utc_dt = pytz.utc.localize(dt)
                paris_tz = pytz.timezone("Europe/Paris")
                return utc_dt.astimezone(paris_tz)
            else:
                # Déjà aware
                paris_tz = pytz.timezone("Europe/Paris")
                return dt.astimezone(paris_tz)

        return dt
    except Exception as e:
        return dt


def match_operation_to_vacation(operation_start, operation_end, vacations_list):
    """
    Détermine à quelle vacation appartient une opération selon la logique suivante:
    1. Si début ET fin dans vacation: OK
    2. Si début dans vacation: OK
    3. Si début avant vacation mais fin dans vacation: OK
    4. Sinon: trouver la vacation avec le plus petit timedelta (max 8h)
    IMPORTANT: Seules les vacations de type "opération" sont considérées
    """
    if not vacations_list:
        return None, None, None

    # Filtrer uniquement les vacations de type "opération"
    operation_vacations = [
        v
        for v in vacations_list
        if v.get("type", "").lower() in ["opération", "operation"]
    ]

    if not operation_vacations:
        return None, None, None

    # Convertir en datetime si nécessaire et s'assurer qu'ils sont timezone-naive
    if isinstance(operation_start, str):
        operation_start = to_datetime_naive(operation_start)
    if isinstance(operation_end, str):
        operation_end = to_datetime_naive(operation_end)

    # Extraire juste l'heure pour la comparaison
    op_start_time = operation_start.time()
    op_end_time = operation_end.time()

    best_vacation = None
    best_match_type = None
    min_delta = 999999  # Large number instead of infinity

    for vacation in operation_vacations:
        vac_start_str = vacation.get("start_time", "")
        vac_end_str = vacation.get("end_time", "")

        if not vac_start_str or not vac_end_str:
            continue

        # Convertir les heures de vacation en time objects
        try:
            vac_start_time = pd.to_datetime(vac_start_str).time()
            vac_end_time = pd.to_datetime(vac_end_str).time()
        except:
            continue

        # Cas 1: début ET fin dans vacation
        if (
            vac_start_time <= op_start_time <= vac_end_time
            and vac_start_time <= op_end_time <= vac_end_time
        ):
            return vacation, "both_in_vacation", 0

        # Cas 2: début dans vacation
        if vac_start_time <= op_start_time <= vac_end_time:
            return vacation, "start_in_vacation", 0

        # Cas 3: début avant vacation mais fin dans vacation
        if (
            op_start_time < vac_start_time
            and vac_start_time <= op_end_time <= vac_end_time
        ):
            return vacation, "end_in_vacation", 0

    # Cas 4: Aucun match direct, chercher la vacation la plus proche
    for vacation in operation_vacations:
        vac_start_str = vacation.get("start_time", "")
        vac_end_str = vacation.get("end_time", "")

        if not vac_start_str or not vac_end_str:
            continue

        try:
            vac_start_time = pd.to_datetime(vac_start_str).time()
            vac_end_time = pd.to_datetime(vac_end_str).time()

            # Calculer les deltas en minutes
            # Delta au début de vacation
            vac_start_dt = to_datetime_naive(
                f"{operation_start.date()} {vac_start_time}"
            )
            delta_start = abs((operation_start - vac_start_dt).total_seconds() / 60)

            # Delta à la fin de vacation
            vac_end_dt = to_datetime_naive(f"{operation_start.date()} {vac_end_time}")
            delta_end = abs((operation_end - vac_end_dt).total_seconds() / 60)

            # Prendre le plus petit delta
            current_delta = min(delta_start, delta_end)

            if current_delta < min_delta:
                min_delta = current_delta
                best_vacation = vacation
                best_match_type = "closest_match"

        except:
            continue

    # Si le timedelta est supérieur à 8h (480 minutes), pas de vacation associée
    if min_delta > 480:  # 8 heures en minutes
        return None, "Hors vacations", min_delta

    return best_vacation, best_match_type, min_delta


def calculate_vacation_metrics(
    operation_start, operation_end, matched_vacation, match_type, delta
):
    """
    Calcule les métriques de vacation pour une opération:
    - Taux d'occupation
    - Retard/avance par rapport au début
    - Débordement par rapport à la fin
    """
    if not matched_vacation:
        return {
            "vacation_start": None,
            "vacation_end": None,
            "vacation_duration_minutes": None,
            "occupation_rate_percent": None,
            "delay_from_start_minutes": None,
            "overrun_from_end_minutes": None,
            "match_type": match_type,
            "delta_minutes": delta,
        }

    vac_start_str = matched_vacation.get("start_time", "")
    vac_end_str = matched_vacation.get("end_time", "")

    if not vac_start_str or not vac_end_str:
        return None

    try:
        # Construire les datetime de vacation
        base_date = operation_start.date()
        vacation_start = to_datetime_naive(f"{base_date} {vac_start_str}")
        vacation_end = to_datetime_naive(f"{base_date} {vac_end_str}")

        # Durée de vacation en minutes
        vacation_duration = (vacation_end - vacation_start).total_seconds() / 60

        # Durée de l'opération en minutes
        operation_duration = (operation_end - operation_start).total_seconds() / 60
    except Exception as e:
        raise

    # Taux d'occupation de cette opération sur la vacation (protection division par zéro)
    if (
        vacation_duration > 0
        and not np.isinf(vacation_duration)
        and not np.isnan(vacation_duration)
    ):
        occupation_rate = operation_duration / vacation_duration * 100
    else:
        occupation_rate = 0

    # Retard/avance par rapport au début de vacation (positif = retard)
    try:
        delay_from_start = (operation_start - vacation_start).total_seconds() / 60
    except Exception as e:
        delay_from_start = 0

    # Débordement par rapport à la fin de vacation (positif = débordement)
    try:
        overrun_from_end = (operation_end - vacation_end).total_seconds() / 60
    except Exception as e:
        overrun_from_end = 0

    return {
        "vacation_start": vacation_start.strftime("%H:%M"),
        "vacation_end": vacation_end.strftime("%H:%M"),
        "vacation_duration_minutes": vacation_duration,
        "occupation_rate_percent": occupation_rate,
        "delay_from_start_minutes": delay_from_start,
        "overrun_from_end_minutes": overrun_from_end,
        "match_type": match_type,
        "delta_minutes": delta,
    }


def unified_operations_statistics(
    operations, user=None, start_date=None, end_date=None, date_intervals=None
):
    """
    Statistiques unifiées pour les opérations avec calculs vacation corrigés:
    - Retard/avance = operation_start - debut_vacation
    - Taux d'occupation = durée_opératoire_totale / durée_vacation
    - Dépassement = fin_dernière_opération - fin_vacation
    """

    # Si on a une liste d'intervalles, traiter la comparaison
    if date_intervals and isinstance(date_intervals, list):
        return _analyze_multiple_intervals(operations, user, date_intervals)

    # Create DataFrame from operations queryset
    if hasattr(operations, "values"):
        df = pd.DataFrame(list(operations.values()))
    else:
        df = pd.DataFrame(operations)

    if df.empty:
        return {"message": "Pas d'opérations pour cette période"}

    # Get vacation data
    vacation_data = None
    if user and start_date and end_date:
        try:
            vacation_data = get_vacations(
                user, start_date=start_date, end_date=end_date
            )
        except Exception as e:
            print(f"Warning: Could not fetch vacation data: {e}")
            vacation_data = None

    # Convert date/time columns
    datetime_columns = ["operation_start", "operation_end"]
    date_columns = ["operation_date"]

    for col in date_columns:
        if col in df.columns:
            df[col] = to_datetime_naive(df[col])

    for col in datetime_columns:
        if col in df.columns:
            df[col] = to_datetime_naive(df[col])

    # Determine if single date or date range
    is_single_date = _is_single_date_analysis(df, start_date, end_date)

    if is_single_date:
        return _analyze_single_date(df, vacation_data, start_date)
    else:
        return _analyze_date_range(df, vacation_data, start_date, end_date)


def _analyze_single_date(df, vacation_data, target_date=None):
    """Analyse pour une date unique avec nouveau système de matching vacation"""

    result = {
        "analysis_type": "single_date",
        "date": target_date.strftime("%Y-%m-%d") if target_date else "date_unique",
        "total_operations": len(df),
    }

    # Filtrer les opérations avec heures valides
    ops_with_times = df.dropna(subset=["operation_start", "operation_end"])

    if ops_with_times.empty:
        result["error"] = "Aucune opération avec horaires valides"
        return convert_to_json_serializable(result)

    # Trier par heure de début
    ops_sorted = ops_with_times.sort_values("operation_start")

    # Calculer les métriques globales traditionnelles d'abord (pour avoir first_operation_start)
    first_operation_start = ops_sorted.iloc[0]["operation_start"]
    last_operation_end = ops_sorted.iloc[-1]["operation_end"]

    # Nouveau système de matching vacation pour chaque opération
    operations_with_vacation = []
    vacation_metrics_summary = {
        "total_operations": len(ops_sorted),
        "operations_with_vacation": 0,
        "operations_without_vacation": 0,
        "match_types": {},
        "total_occupation_rate": 0,
        "average_delay": 0,
        "average_overrun": 0,
    }

    for _, operation in ops_sorted.iterrows():
        try:
            op_start = operation["operation_start"]
            op_end = operation["operation_end"]

            # Matcher l'opération avec une vacation
            matched_vacation, match_type, delta = match_operation_to_vacation(
                op_start, op_end, vacation_data
            )

            # Calculer les métriques de vacation
            vacation_metrics = calculate_vacation_metrics(
                op_start, op_end, matched_vacation, match_type, delta
            )

            # Ajouter l'info vacation à l'opération
            operation_info = {
                "operation_id": operation.get("id", "unknown"),
                "operation_title": operation.get(
                    "titre_operation", "Opération sans titre"
                ),
                "operation_start": localize_datetime(op_start).strftime("%H:%M"),
                "operation_end": localize_datetime(op_end).strftime("%H:%M"),
                "duration_minutes": (op_end - op_start).total_seconds() / 60,
                "vacation_metrics": vacation_metrics,
            }
            operations_with_vacation.append(operation_info)

            # Mettre à jour le résumé
            if matched_vacation:
                vacation_metrics_summary["operations_with_vacation"] += 1

                # Compter les types de match
                if match_type not in vacation_metrics_summary["match_types"]:
                    vacation_metrics_summary["match_types"][match_type] = 0
                vacation_metrics_summary["match_types"][match_type] += 1

                # Additionner pour les moyennes
                if vacation_metrics["occupation_rate_percent"]:
                    vacation_metrics_summary[
                        "total_occupation_rate"
                    ] += vacation_metrics["occupation_rate_percent"]
                if vacation_metrics["delay_from_start_minutes"]:
                    vacation_metrics_summary["average_delay"] += vacation_metrics[
                        "delay_from_start_minutes"
                    ]
                if vacation_metrics["overrun_from_end_minutes"]:
                    vacation_metrics_summary["average_overrun"] += vacation_metrics[
                        "overrun_from_end_minutes"
                    ]
            else:
                vacation_metrics_summary["operations_without_vacation"] += 1

        except Exception as e:
            # En cas d'erreur, compter comme opération sans vacation pour éviter de perdre le comptage
            print(
                f"Warning: Error processing operation {operation.get('id', 'unknown')}: {e}"
            )

            # Créer une opération info basique
            operation_info = {
                "operation_id": operation.get("id", "unknown"),
                "operation_title": operation.get(
                    "titre_operation", "Opération sans titre"
                ),
                "operation_start": str(operation.get("operation_start", "N/A")),
                "operation_end": str(operation.get("operation_end", "N/A")),
                "duration_minutes": 0,
                "vacation_metrics": None,
            }
            operations_with_vacation.append(operation_info)
            vacation_metrics_summary["operations_without_vacation"] += 1

    # Calculer les moyennes finales en évitant les divisions par zéro
    ops_with_vac = vacation_metrics_summary["operations_with_vacation"]
    if ops_with_vac > 0:
        vacation_metrics_summary["average_occupation_rate"] = (
            vacation_metrics_summary["total_occupation_rate"] / ops_with_vac
        )
        vacation_metrics_summary["average_delay"] = (
            vacation_metrics_summary["average_delay"] / ops_with_vac
        )
        vacation_metrics_summary["average_overrun"] = (
            vacation_metrics_summary["average_overrun"] / ops_with_vac
        )
    else:
        vacation_metrics_summary["average_occupation_rate"] = 0
        vacation_metrics_summary["average_delay"] = 0
        vacation_metrics_summary["average_overrun"] = 0

    # Analyser les premières incisions par vacation
    vacation_first_incisions = []
    if vacation_data:
        # Filtrer les vacations opératoires
        operation_vacations = [
            v
            for v in vacation_data
            if v.get("type", "").lower() in ["opération", "operation"]
        ]

        for vacation in operation_vacations:
            vac_start_str = vacation.get("start_time", "")
            vac_end_str = vacation.get("end_time", "")

            if not vac_start_str or not vac_end_str:
                continue

            # Trouver toutes les opérations de cette vacation
            vacation_operations = []
            for op_info in operations_with_vacation:
                if (
                    op_info.get("vacation_metrics")
                    and op_info["vacation_metrics"].get("vacation_start")
                    == vac_start_str
                    and op_info["vacation_metrics"].get("vacation_end") == vac_end_str
                ):
                    vacation_operations.append(op_info)

            if vacation_operations:
                # Trier par heure de début pour trouver la première incision
                vacation_operations.sort(key=lambda x: x["operation_start"])
                first_op = vacation_operations[0]

                # Calculer le retard/avance de la première incision par rapport à l'heure théorique
                vacation_start_theoretical = (
                    f"{first_operation_start.date()} {vac_start_str}"
                )
                vacation_start_dt = pd.to_datetime(vacation_start_theoretical)
                if (
                    hasattr(vacation_start_dt, "tz")
                    and vacation_start_dt.tz is not None
                ):
                    vacation_start_dt = vacation_start_dt.replace(tzinfo=None)

                # Convertir l'heure de première incision en datetime pour calcul
                first_incision_time = first_op["operation_start"]
                first_incision_dt = pd.to_datetime(
                    f"{first_operation_start.date()} {first_incision_time}"
                )
                if (
                    hasattr(first_incision_dt, "tz")
                    and first_incision_dt.tz is not None
                ):
                    first_incision_dt = first_incision_dt.replace(tzinfo=None)

                delay_minutes = (
                    first_incision_dt - vacation_start_dt
                ).total_seconds() / 60

                vacation_first_incisions.append(
                    {
                        "vacation_start": vac_start_str,
                        "vacation_end": vac_end_str,
                        "first_incision_time": first_incision_time,
                        "theoretical_start": vac_start_str,
                        "delay_minutes": delay_minutes,
                        "operations_count": len(vacation_operations),
                        "delay_status": (
                            "À l'heure"
                            if delay_minutes == 0
                            else (
                                f"Retard de {int(abs(delay_minutes))}min"
                                if delay_minutes > 0
                                else f"Avance de {int(abs(delay_minutes))}min"
                            )
                        ),
                    }
                )

    vacation_metrics_summary["vacation_first_incisions"] = vacation_first_incisions

    # Calculer les métriques opératoires globales
    global_operative_metrics = {}

    # 1. Durées opératoires individuelles et analyse jour/nuit
    operation_durations = []
    night_operations_count = 0

    for op_info in operations_with_vacation:
        duration = op_info.get("duration_minutes", 0)
        if duration > 0:
            operation_durations.append(duration)

        # Analyser si c'est une opération de nuit
        # Critères: incision ou fin après 20h, OU fin avant 8h
        try:
            op_start_str = op_info.get("operation_start", "")
            op_end_str = op_info.get("operation_end", "")

            if op_start_str and op_end_str:
                # Convertir les heures en objets time pour comparaison
                start_time = pd.to_datetime(op_start_str, format="%H:%M").time()
                end_time = pd.to_datetime(op_end_str, format="%H:%M").time()

                # Définir les heures de référence
                night_start = pd.to_datetime("20:00", format="%H:%M").time()  # 20h
                day_start = pd.to_datetime("08:00", format="%H:%M").time()  # 8h

                # Vérifier les critères de nuit
                is_night_operation = False

                # Critère 1: Incision après 20h
                if start_time >= night_start:
                    is_night_operation = True

                # Critère 2: Fin après 20h
                if end_time >= night_start:
                    is_night_operation = True

                # Critère 3: Fin avant 8h (opération de nuit qui se termine tôt le matin)
                if end_time < day_start:
                    is_night_operation = True

                if is_night_operation:
                    night_operations_count += 1

        except:
            # En cas d'erreur de parsing, ne pas compter comme nuit
            continue

    if operation_durations:
        global_operative_metrics.update(
            {
                "total_operative_time_minutes": sum(operation_durations),
                "mean_operative_duration_minutes": np.mean(operation_durations),
                "min_operative_duration_minutes": min(operation_durations),
                "max_operative_duration_minutes": max(operation_durations),
                "operations_with_duration_count": len(operation_durations),
                "night_operations_count": night_operations_count,
                "day_operations_count": len(operation_durations)
                - night_operations_count,
            }
        )
    else:
        global_operative_metrics.update(
            {
                "total_operative_time_minutes": 0,
                "mean_operative_duration_minutes": 0,
                "min_operative_duration_minutes": 0,
                "max_operative_duration_minutes": 0,
                "operations_with_duration_count": 0,
                "night_operations_count": 0,
                "day_operations_count": 0,
            }
        )

    # 2. Temps entre opérations
    if len(operations_with_vacation) > 1:
        # Trier les opérations par heure de début pour calculer les intervalles
        sorted_ops = sorted(
            operations_with_vacation, key=lambda x: x["operation_start"]
        )
        inter_operation_times = []

        for i in range(len(sorted_ops) - 1):
            current_end = sorted_ops[i]["operation_end"]
            next_start = sorted_ops[i + 1]["operation_start"]

            # Convertir en datetime pour calcul
            try:
                current_end_dt = pd.to_datetime(
                    f"{first_operation_start.date()} {current_end}"
                )
                next_start_dt = pd.to_datetime(
                    f"{first_operation_start.date()} {next_start}"
                )

                if hasattr(current_end_dt, "tz") and current_end_dt.tz is not None:
                    current_end_dt = current_end_dt.replace(tzinfo=None)
                if hasattr(next_start_dt, "tz") and next_start_dt.tz is not None:
                    next_start_dt = next_start_dt.replace(tzinfo=None)

                interval_minutes = (next_start_dt - current_end_dt).total_seconds() / 60
                inter_operation_times.append(interval_minutes)
            except:
                continue

        if inter_operation_times:
            global_operative_metrics.update(
                {
                    "total_inter_operation_time_minutes": sum(inter_operation_times),
                    "mean_inter_operation_time_minutes": np.mean(inter_operation_times),
                    "inter_operation_intervals_count": len(inter_operation_times),
                }
            )
        else:
            global_operative_metrics.update(
                {
                    "total_inter_operation_time_minutes": 0,
                    "mean_inter_operation_time_minutes": 0,
                    "inter_operation_intervals_count": 0,
                }
            )
    else:
        global_operative_metrics.update(
            {
                "total_inter_operation_time_minutes": 0,
                "mean_inter_operation_time_minutes": 0,
                "inter_operation_intervals_count": 0,
            }
        )

    # 3. Taux d'occupation par vacation (corrigé)
    vacation_occupation_rates = []
    if vacation_data:
        operation_vacations = [
            v
            for v in vacation_data
            if v.get("type", "").lower() in ["opération", "operation"]
        ]

        for vacation in operation_vacations:
            vac_start_str = vacation.get("start_time", "")
            vac_end_str = vacation.get("end_time", "")

            if not vac_start_str or not vac_end_str:
                continue

            # Calculer la durée de la vacation
            try:
                vac_start_dt = pd.to_datetime(
                    f"{first_operation_start.date()} {vac_start_str}"
                )
                vac_end_dt = pd.to_datetime(
                    f"{first_operation_start.date()} {vac_end_str}"
                )

                if hasattr(vac_start_dt, "tz") and vac_start_dt.tz is not None:
                    vac_start_dt = vac_start_dt.replace(tzinfo=None)
                if hasattr(vac_end_dt, "tz") and vac_end_dt.tz is not None:
                    vac_end_dt = vac_end_dt.replace(tzinfo=None)

                vacation_duration_minutes = (
                    vac_end_dt - vac_start_dt
                ).total_seconds() / 60

                # Trouver toutes les opérations de cette vacation et sommer leurs durées
                vacation_operative_time = 0
                for op_info in operations_with_vacation:
                    if (
                        op_info.get("vacation_metrics")
                        and op_info["vacation_metrics"].get("vacation_start")
                        == vac_start_str
                        and op_info["vacation_metrics"].get("vacation_end")
                        == vac_end_str
                    ):
                        vacation_operative_time += op_info.get("duration_minutes", 0)

                # Calculer le taux d'occupation réel
                if vacation_duration_minutes > 0:
                    occupation_rate = (
                        vacation_operative_time / vacation_duration_minutes
                    ) * 100
                    vacation_occupation_rates.append(
                        {
                            "vacation_start": vac_start_str,
                            "vacation_end": vac_end_str,
                            "vacation_duration_minutes": vacation_duration_minutes,
                            "total_operative_time_minutes": vacation_operative_time,
                            "occupation_rate_percent": occupation_rate,
                        }
                    )
            except:
                continue

    global_operative_metrics["vacation_occupation_details"] = vacation_occupation_rates

    # Ajouter les métriques globales au résumé vacation
    vacation_metrics_summary.update(global_operative_metrics)

    # Durée moyenne entre 2 opérations (ancienne méthode pour compatibilité)
    inter_operation_durations = []
    for i in range(len(ops_sorted) - 1):
        end_current = ops_sorted.iloc[i]["operation_end"]
        start_next = ops_sorted.iloc[i + 1]["operation_start"]
        duration = (start_next - end_current).total_seconds() / 60
        inter_operation_durations.append(duration)

    # Protection contre les valeurs infinies et NaN
    if inter_operation_durations:
        mean_value = np.mean(inter_operation_durations)
        mean_inter_operation_time = (
            mean_value if not np.isinf(mean_value) and not np.isnan(mean_value) else 0
        )
    else:
        mean_inter_operation_time = 0

    # Durée opératoire moyenne et totale (avec protection)
    try:
        # S'assurer que les colonnes sont des datetime valides
        if (
            "operation_start" in ops_sorted.columns
            and "operation_end" in ops_sorted.columns
        ):
            duration_diff = ops_sorted["operation_end"] - ops_sorted["operation_start"]
            if (
                hasattr(duration_diff, "dt")
                and hasattr(duration_diff.dtype, "type")
                and pd.api.types.is_timedelta64_dtype(duration_diff)
            ):
                operation_durations = duration_diff.dt.total_seconds() / 60
            else:
                # Si pas d'accesseur .dt, essayer la conversion directe
                operation_durations = duration_diff.apply(
                    lambda x: (
                        x.total_seconds() / 60 if hasattr(x, "total_seconds") else 0
                    )
                )
        else:
            operation_durations = pd.Series([0])
    except Exception as e:
        print(f"Error calculating operation durations: {e}")
        operation_durations = pd.Series([0])
    mean_op_duration = operation_durations.mean()
    mean_operative_duration = (
        mean_op_duration
        if not np.isinf(mean_op_duration) and not np.isnan(mean_op_duration)
        else 0
    )

    total_op_time = operation_durations.sum()
    total_operative_time = (
        total_op_time
        if not np.isinf(total_op_time) and not np.isnan(total_op_time)
        else 0
    )

    # Durée totale de la session opératoire
    session_duration = (last_operation_end - first_operation_start).total_seconds() / 60
    result.update(
        {
            # Nouvelle analyse vacation avec matching amélioré
            "vacation_analysis": vacation_metrics_summary,
            "operations_detail": operations_with_vacation,
            # Métriques globales traditionnelles
            "first_incision_time": localize_datetime(first_operation_start).strftime(
                "%H:%M"
            ),
            "last_closure_time": localize_datetime(last_operation_end).strftime(
                "%H:%M"
            ),
            "mean_inter_operation_time_minutes": mean_inter_operation_time,
            "mean_operative_duration_minutes": mean_operative_duration,
            "session_duration_minutes": session_duration,
            "total_operative_time_minutes": total_operative_time,
            # Informations vacation disponibles (uniquement opératoires)
            "available_vacations": (
                [
                    v
                    for v in vacation_data
                    if v.get("type", "").lower() in ["opération", "operation"]
                ]
                if vacation_data
                else []
            ),
            "vacation_count": (
                len(
                    [
                        v
                        for v in vacation_data
                        if v.get("type", "").lower() in ["opération", "operation"]
                    ]
                )
                if vacation_data
                else 0
            ),
        }
    )

    return convert_to_json_serializable(result)


def _interpret_delay(delay_minutes):
    """Interprète le retard/avance"""
    if delay_minutes > 0:
        return f"Retard de {delay_minutes:.1f} minutes"
    elif delay_minutes < 0:
        return f"Avance de {abs(delay_minutes):.1f} minutes"
    else:
        return "À l'heure"


def _interpret_overrun(overrun_minutes):
    """Interprète le dépassement"""
    if overrun_minutes > 0:
        return f"Dépassement de {overrun_minutes:.1f} minutes"
    elif overrun_minutes < 0:
        return f"Fin {abs(overrun_minutes):.1f} minutes avant la vacation"
    else:
        return "Fin exactement à l'heure de vacation"


def _calculate_daily_stats(day_df, vacation_data, date):
    """Calcule les statistiques pour une journée avec calculs vacation corrigés"""

    if day_df.empty:
        return None

    # Trier par heure de début
    day_df_sorted = day_df.sort_values("operation_start")

    # Vacation info pour cette date
    vacation_start = None
    vacation_end = None
    if vacation_data:
        for vacation in vacation_data:
            vacation_start_str = vacation.get("start_time", "")
            vacation_end_str = vacation.get("end_time", "")

            if vacation_start_str and vacation_end_str:
                vacation_start = pd.to_datetime(f"{date} {vacation_start_str}")
                vacation_end = pd.to_datetime(f"{date} {vacation_end_str}")
                break

    # Calculs avec logique corrigée
    first_operation_start = day_df_sorted.iloc[0]["operation_start"]
    last_operation_end = day_df_sorted.iloc[-1]["operation_end"]

    # RETARD/AVANCE par rapport au début de vacation
    delay_from_vacation_start = None
    if vacation_start:
        delay_from_vacation_start = (
            first_operation_start - vacation_start
        ).total_seconds() / 60

    # Durées inter-opérations
    inter_op_durations = []
    for i in range(len(day_df_sorted) - 1):
        end_current = day_df_sorted.iloc[i]["operation_end"]
        start_next = day_df_sorted.iloc[i + 1]["operation_start"]
        duration = (start_next - end_current).total_seconds() / 60
        inter_op_durations.append(duration)

    # Durées opératoires
    try:
        duration_diff = (
            day_df_sorted["operation_end"] - day_df_sorted["operation_start"]
        )
        if (
            hasattr(duration_diff, "dt")
            and hasattr(duration_diff.dtype, "type")
            and pd.api.types.is_timedelta64_dtype(duration_diff)
        ):
            op_durations = duration_diff.dt.total_seconds() / 60
        else:
            op_durations = duration_diff.apply(
                lambda x: x.total_seconds() / 60 if hasattr(x, "total_seconds") else 0
            )
    except Exception as e:
        print(f"Error calculating operation durations: {e}")
        op_durations = pd.Series([0])
    total_operative_time = op_durations.sum()

    # DÉPASSEMENT par rapport à la fin de vacation
    overrun_from_vacation_end = None
    if vacation_end:
        overrun_from_vacation_end = (
            last_operation_end - vacation_end
        ).total_seconds() / 60

    # TAUX D'OCCUPATION corrigé
    occupation_rate_percent = None
    if vacation_start and vacation_end:
        vacation_duration = (vacation_end - vacation_start).total_seconds() / 60
        occupation_rate_percent = (
            (total_operative_time / vacation_duration) * 100
            if vacation_duration > 0
            else 0
        )

    return {
        "date": date.strftime("%Y-%m-%d"),
        "operations_count": len(day_df_sorted),
        # Calculs corrigés
        "delay_from_vacation_start_minutes": delay_from_vacation_start,
        "overrun_from_vacation_end_minutes": overrun_from_vacation_end,
        "vacation_occupation_rate_percent": occupation_rate_percent,
        # Autres métriques
        "first_incision_time": first_operation_start.strftime("%H:%M"),
        "last_closure_time": last_operation_end.strftime("%H:%M"),
        "mean_inter_operation_time_minutes": (
            np.mean(inter_op_durations) if inter_op_durations else 0
        ),
        "mean_operative_duration_minutes": op_durations.mean(),
        "total_operative_time_minutes": total_operative_time,
    }


def _calculate_global_averages(df, vacation_data):
    """Calcule les moyennes globales avec métriques vacation corrigées"""

    # Grouper par date pour calculer les moyennes journalières
    daily_stats = []

    try:
        if "operation_start" in df.columns and not df.empty:
            if hasattr(
                df["operation_start"], "dt"
            ) and pd.api.types.is_datetime64_any_dtype(df["operation_start"]):
                date_groups = df.groupby(df["operation_start"].dt.date)
            else:
                # Fallback si pas d'accesseur .dt
                df["temp_date"] = df["operation_start"].apply(
                    lambda x: x.date() if hasattr(x, "date") else x
                )
                date_groups = df.groupby("temp_date")
        else:
            date_groups = []
    except Exception as e:
        print(f"Error grouping by date: {e}")
        date_groups = []

    for date, day_df in date_groups:
        day_stats = _calculate_daily_stats(day_df, vacation_data, date)
        if day_stats:
            daily_stats.append(day_stats)

    if not daily_stats:
        return {}

    # Calculer les moyennes des stats journalières
    stats_df = pd.DataFrame(daily_stats)

    return {
        # Métriques vacation corrigées
        "mean_delay_from_vacation_start_minutes": stats_df[
            "delay_from_vacation_start_minutes"
        ].mean(),
        "mean_overrun_from_vacation_end_minutes": stats_df[
            "overrun_from_vacation_end_minutes"
        ].mean(),
        "mean_vacation_occupation_rate_percent": stats_df[
            "vacation_occupation_rate_percent"
        ].mean(),
        # Autres métriques
        "mean_first_incision_time": _calculate_mean_time(
            stats_df["first_incision_time"]
        ),
        "mean_last_closure_time": _calculate_mean_time(stats_df["last_closure_time"]),
        "mean_inter_operation_time_minutes": stats_df[
            "mean_inter_operation_time_minutes"
        ].mean(),
        "mean_operative_duration_minutes": stats_df[
            "mean_operative_duration_minutes"
        ].mean(),
        "mean_operations_per_day": stats_df["operations_count"].mean(),
        "total_days": len(daily_stats),
    }


def _calculate_period_stats(period_df, vacation_data):
    """Calcule les statistiques moyennes pour une période avec métriques vacation corrigées"""

    # Grouper par date dans la période
    daily_stats = []
    try:
        if "operation_start" in period_df.columns and not period_df.empty:
            if hasattr(period_df["operation_start"], "dt") and hasattr(
                period_df["operation_start"].dtype, "tz"
            ):
                date_groups = period_df.groupby(period_df["operation_start"].dt.date)
            else:
                period_df["temp_date"] = period_df["operation_start"].apply(
                    lambda x: x.date() if hasattr(x, "date") else x
                )
                date_groups = period_df.groupby("temp_date")
        else:
            date_groups = []
    except Exception as e:
        print(f"Error grouping by date: {e}")
        date_groups = []

    for date, day_df in date_groups:
        day_stats = _calculate_daily_stats(day_df, vacation_data, date)
        if day_stats:
            daily_stats.append(day_stats)

    if not daily_stats:
        return None

    # Moyennes des stats journalières
    stats_df = pd.DataFrame(daily_stats)

    return {
        "days_count": len(daily_stats),
        "total_operations": stats_df["operations_count"].sum(),
        "mean_operations_per_day": stats_df["operations_count"].mean(),
        # Métriques vacation corrigées
        "mean_delay_from_vacation_start_minutes": stats_df[
            "delay_from_vacation_start_minutes"
        ].mean(),
        "mean_overrun_from_vacation_end_minutes": stats_df[
            "overrun_from_vacation_end_minutes"
        ].mean(),
        "mean_vacation_occupation_rate_percent": stats_df[
            "vacation_occupation_rate_percent"
        ].mean(),
        # Autres métriques
        "mean_first_incision_time": _calculate_mean_time(
            stats_df["first_incision_time"]
        ),
        "mean_last_closure_time": _calculate_mean_time(stats_df["last_closure_time"]),
        "mean_inter_operation_time_minutes": stats_df[
            "mean_inter_operation_time_minutes"
        ].mean(),
        "mean_operative_duration_minutes": stats_df[
            "mean_operative_duration_minutes"
        ].mean(),
    }


def _generate_comparative_summary(intervals_results):
    """Génère un résumé comparatif avec métriques vacation corrigées"""

    if not intervals_results:
        return {}

    summary = {
        "metrics_comparison": {},
        "best_performance": {},
        "worst_performance": {},
        "trends": {},
    }

    # Extraire les métriques clés de chaque intervalle
    metrics_data = []

    for interval_result in intervals_results:
        interval_info = interval_result.get("interval_info", {})

        # Récupérer les métriques selon le type d'analyse
        if interval_result.get("analysis_type") == "single_date":
            metrics = {
                "label": interval_info.get("label", "Unknown"),
                "start_date": interval_info.get("start_date"),
                "total_operations": interval_result.get("total_operations", 0),
                # Métriques vacation corrigées
                "delay_from_vacation_start": interval_result.get(
                    "delay_from_vacation_start_minutes"
                ),
                "overrun_from_vacation_end": interval_result.get(
                    "overrun_from_vacation_end_minutes"
                ),
                "vacation_occupation_rate": interval_result.get(
                    "vacation_occupation_rate_percent"
                ),
                # Autres métriques
                "first_incision_time": interval_result.get("first_incision_time"),
                "last_closure_time": interval_result.get("last_closure_time"),
                "mean_inter_operation_time": interval_result.get(
                    "mean_inter_operation_time_minutes"
                ),
                "mean_operative_duration": interval_result.get(
                    "mean_operative_duration_minutes"
                ),
                "session_duration": interval_result.get("session_duration_minutes"),
                "total_operative_time": interval_result.get(
                    "total_operative_time_minutes"
                ),
            }
        else:
            # Pour les intervalles de dates
            global_avg = interval_result.get("global_averages", {})
            metrics = {
                "label": interval_info.get("label", "Unknown"),
                "start_date": interval_info.get("start_date"),
                "total_operations": interval_result.get("total_operations", 0),
                # Métriques vacation corrigées
                "delay_from_vacation_start": global_avg.get(
                    "mean_delay_from_vacation_start_minutes"
                ),
                "overrun_from_vacation_end": global_avg.get(
                    "mean_overrun_from_vacation_end_minutes"
                ),
                "vacation_occupation_rate": global_avg.get(
                    "mean_vacation_occupation_rate_percent"
                ),
                # Autres métriques
                "first_incision_time": global_avg.get("mean_first_incision_time"),
                "last_closure_time": global_avg.get("mean_last_closure_time"),
                "mean_inter_operation_time": global_avg.get(
                    "mean_inter_operation_time_minutes"
                ),
                "mean_operative_duration": global_avg.get(
                    "mean_operative_duration_minutes"
                ),
                "mean_operations_per_day": global_avg.get("mean_operations_per_day"),
                "total_days": global_avg.get("total_days"),
            }

        metrics_data.append(metrics)

    if not metrics_data:
        return summary

    # Créer DataFrame pour les comparaisons
    df_metrics = pd.DataFrame(metrics_data)

    # Comparaison des métriques avec noms corrigés
    numeric_columns = df_metrics.select_dtypes(include=[np.number]).columns

    for col in numeric_columns:
        if col in df_metrics.columns:
            col_data = df_metrics[col].dropna()
            if not col_data.empty:
                summary["metrics_comparison"][col] = {
                    "values": col_data.tolist(),
                    "labels": df_metrics.loc[col_data.index, "label"].tolist(),
                    "mean": float(col_data.mean()),
                    "min": float(col_data.min()),
                    "max": float(col_data.max()),
                    "std": float(col_data.std()) if len(col_data) > 1 else 0,
                }

    # Identifier les meilleures et pires performances avec métriques corrigées
    performance_metrics = [
        "total_operations",
        "mean_operative_duration",
        "vacation_occupation_rate",
    ]

    for metric in performance_metrics:
        if metric in df_metrics.columns:
            metric_data = df_metrics[metric].dropna()
            if not metric_data.empty:
                # Meilleure performance
                best_idx = metric_data.idxmax()
                summary["best_performance"][metric] = {
                    "label": df_metrics.loc[best_idx, "label"],
                    "value": float(metric_data.loc[best_idx]),
                    "date": df_metrics.loc[best_idx, "start_date"],
                }

                # Pire performance
                worst_idx = metric_data.idxmin()
                summary["worst_performance"][metric] = {
                    "label": df_metrics.loc[worst_idx, "label"],
                    "value": float(metric_data.loc[worst_idx]),
                    "date": df_metrics.loc[worst_idx, "start_date"],
                }

    return summary


# Fonctions utilitaires inchangées
def _analyze_multiple_intervals(operations, user, date_intervals):
    """Analyse comparative de plusieurs intervalles de dates"""

    result = {
        "analysis_type": "multiple_intervals",
        "intervals_count": len(date_intervals),
        "intervals_analysis": [],
        "comparative_summary": {},
    }

    intervals_results = []

    for i, interval in enumerate(date_intervals):
        start_date = pd.to_datetime(interval["start"])
        end_date = pd.to_datetime(interval["end"])
        label = interval.get("label", f"Période {i+1}")

        if hasattr(operations, "filter"):
            interval_operations = operations.filter(
                operation_date__gte=start_date.date(),
                operation_date__lte=end_date.date(),
            )
        else:
            df = (
                pd.DataFrame(operations)
                if not isinstance(operations, pd.DataFrame)
                else operations
            )
            if "operation_date" in df.columns:
                df["operation_date"] = pd.to_datetime(df["operation_date"])
                interval_operations = df[
                    (df["operation_date"] >= start_date)
                    & (df["operation_date"] <= end_date)
                ]
            else:
                interval_operations = df

        interval_analysis = unified_operations_statistics(
            operations=interval_operations,
            user=user,
            start_date=start_date,
            end_date=end_date,
        )

        interval_analysis["interval_info"] = {
            "label": label,
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d"),
            "duration_days": (end_date - start_date).days + 1,
            "interval_index": i,
        }

        intervals_results.append(interval_analysis)

    result["intervals_analysis"] = intervals_results
    result["comparative_summary"] = _generate_comparative_summary(intervals_results)

    return convert_to_json_serializable(result)


def _is_single_date_analysis(df, start_date, end_date):
    """Détermine s'il s'agit d'une analyse sur une seule date"""
    if start_date and end_date:
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        return (end_dt - start_dt).days <= 1

    if "operation_date" in df.columns:
        if hasattr(df["operation_date"], "dt") and hasattr(
            df["operation_date"].dtype, "tz"
        ):
            unique_dates = df["operation_date"].dt.date.nunique()
        else:
            unique_dates = (
                df["operation_date"]
                .apply(lambda x: x.date() if hasattr(x, "date") else x)
                .nunique()
            )
        return unique_dates <= 1

    return True


def _analyze_date_range(df, vacation_data, start_date, end_date):
    """Analyse pour un intervalle de dates - valeurs moyennes avec sous-classement"""

    result = {
        "analysis_type": "date_range",
        "start_date": start_date.strftime("%Y-%m-%d") if start_date else None,
        "end_date": end_date.strftime("%Y-%m-%d") if end_date else None,
        "total_operations": len(df),
    }

    # Déterminer la granularité d'analyse
    if start_date and end_date:
        date_range = (end_date - start_date).days
    else:
        if "operation_date" in df.columns:
            if hasattr(df["operation_date"], "dt") and hasattr(
                df["operation_date"].dtype, "tz"
            ):
                date_range = df["operation_date"].dt.date.nunique()
            else:
                date_range = (
                    df["operation_date"]
                    .apply(lambda x: x.date() if hasattr(x, "date") else x)
                    .nunique()
                )
        else:
            date_range = 1

    if date_range <= 7:
        grouping = "day"
    elif date_range <= 31:
        grouping = "week"
    elif date_range <= 365:
        grouping = "month"
    else:
        grouping = "year"

    result["grouping"] = grouping

    # Filtrer les opérations avec horaires valides
    ops_with_times = df.dropna(subset=["operation_start", "operation_end"])

    if ops_with_times.empty:
        result["error"] = "Aucune opération avec horaires valides"
        return convert_to_json_serializable(result)

    # Ajouter colonnes pour le groupement
    ops_with_times = ops_with_times.copy()
    if hasattr(ops_with_times["operation_start"], "dt") and hasattr(
        ops_with_times["operation_start"].dtype, "tz"
    ):
        ops_with_times["weekday"] = ops_with_times["operation_start"].dt.day_name()
        ops_with_times["week"] = ops_with_times["operation_start"].dt.isocalendar().week
        ops_with_times["month"] = ops_with_times["operation_start"].dt.month
        ops_with_times["year"] = ops_with_times["operation_start"].dt.year
    else:
        ops_with_times["weekday"] = ops_with_times["operation_start"].apply(
            lambda x: x.strftime("%A") if hasattr(x, "strftime") else "Unknown"
        )
        ops_with_times["week"] = ops_with_times["operation_start"].apply(
            lambda x: x.isocalendar()[1] if hasattr(x, "isocalendar") else 1
        )
        ops_with_times["month"] = ops_with_times["operation_start"].apply(
            lambda x: x.month if hasattr(x, "month") else 1
        )
        ops_with_times["year"] = ops_with_times["operation_start"].apply(
            lambda x: x.year if hasattr(x, "year") else 2024
        )

    # Analyse globale (moyennes sur toute la période)
    global_stats = _calculate_global_averages(ops_with_times, vacation_data)
    result["global_averages"] = global_stats

    # Analyse par sous-période
    grouped_analysis = _calculate_grouped_analysis(
        ops_with_times, grouping, vacation_data
    )
    result["grouped_analysis"] = grouped_analysis

    # Analyse par jour de la semaine
    weekday_analysis = _calculate_weekday_analysis(ops_with_times, vacation_data)
    result["weekday_analysis"] = weekday_analysis

    return convert_to_json_serializable(result)


def _calculate_grouped_analysis(df, grouping, vacation_data):
    """Analyse groupée selon la granularité choisie"""

    grouped_results = []

    if grouping == "day":
        if hasattr(df["operation_start"], "dt") and hasattr(
            df["operation_start"].dtype, "tz"
        ):
            date_groups = df.groupby(df["operation_start"].dt.date)
        else:
            df["temp_date"] = df["operation_start"].apply(
                lambda x: x.date() if hasattr(x, "date") else x
            )
            date_groups = df.groupby("temp_date")

        for date, day_df in date_groups:
            day_stats = _calculate_daily_stats(day_df, vacation_data, date)
            if day_stats:
                day_stats["period"] = date.strftime("%Y-%m-%d")
                day_stats["weekday"] = day_df.iloc[0]["weekday"]
                grouped_results.append(day_stats)

    elif grouping == "week":
        for (year, week), week_df in df.groupby([df["year"], df["week"]]):
            week_stats = _calculate_period_stats(week_df, vacation_data)
            if week_stats:
                week_stats["period"] = f"{year}-W{week:02d}"
                week_stats["year"] = year
                week_stats["week"] = week
                grouped_results.append(week_stats)

    elif grouping == "month":
        for (year, month), month_df in df.groupby([df["year"], df["month"]]):
            month_stats = _calculate_period_stats(month_df, vacation_data)
            if month_stats:
                month_stats["period"] = f"{year}-{month:02d}"
                month_stats["year"] = year
                month_stats["month"] = month
                grouped_results.append(month_stats)

    elif grouping == "year":
        for year, year_df in df.groupby(df["year"]):
            year_stats = _calculate_period_stats(year_df, vacation_data)
            if year_stats:
                year_stats["period"] = str(year)
                year_stats["year"] = year
                grouped_results.append(year_stats)

    return grouped_results


def _calculate_weekday_analysis(df, vacation_data):
    """Analyse par jour de la semaine"""

    weekday_results = []

    for weekday, weekday_df in df.groupby("weekday"):
        weekday_stats = _calculate_period_stats(weekday_df, vacation_data)
        if weekday_stats:
            weekday_stats["weekday"] = weekday
            weekday_results.append(weekday_stats)

    return weekday_results


def _calculate_mean_time(time_series):
    """Calcule la moyenne des heures (format HH:MM)"""
    try:
        minutes = []
        for time_str in time_series.dropna():
            if isinstance(time_str, str) and ":" in time_str:
                hours, mins = map(int, time_str.split(":"))
                total_minutes = hours * 60 + mins
                minutes.append(total_minutes)

        if minutes:
            mean_minutes = int(np.mean(minutes))
            hours = mean_minutes // 60
            mins = mean_minutes % 60
            return f"{hours:02d}:{mins:02d}"

        return None
    except:
        return None


def convert_to_json_serializable(obj):
    """Convert pandas/numpy types to JSON serializable types"""
    if isinstance(obj, dict):
        return {str(k): convert_to_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    elif isinstance(obj, pd.Timestamp):
        return obj.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(obj, (np.integer, np.int64)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64)):
        # Handle infinite and NaN values
        if np.isinf(obj) or np.isnan(obj):
            return None
        return float(obj)
    elif isinstance(obj, float):
        # Handle Python float infinity and NaN
        if (
            obj == float("inf") or obj == float("-inf") or obj != obj
        ):  # obj != obj is NaN check
            return None
        return obj
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif pd.isna(obj):
        return None
    else:
        return obj


# Exemple d'utilisation avec différents types d'analyse
"""
# 1. Date unique
POST /api/operations-analysis/
{
    "start_date": "2024-01-15",
    "user_id": 123
}

# 2. Intervalle de dates
POST /api/operations-analysis/
{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "user_id": 123
}

# 3. Comparaison d'intervalles multiples
POST /api/operations-analysis/
{
    "date_intervals": [
        {
            "start": "2024-01-01",
            "end": "2024-01-07",
            "label": "Première semaine janvier"
        },
        {
            "start": "2024-01-08",
            "end": "2024-01-14",
            "label": "Deuxième semaine janvier"
        },
        {
            "start": "2024-02-01",
            "end": "2024-02-07",
            "label": "Première semaine février"
        }
    ],
    "user_id": 123
}
"""
