from __future__ import absolute_import, unicode_literals
import logging
from celery import shared_task
from notification.models import Notification
from django.contrib.contenttypes.models import ContentType
from document.models.audio_recording import AudioRecording
from impl.services.SpeechToTextService import SpeechToTextService
from impl.services.CorrectionTextService import CorrectionTextService
from django.utils import timezone
from datetime import timedelta
from django.db.models import F, Q
from document.models.audio_recording import (
    AudioRecordingStatus,
)  # Corrected import path
from consultation.models import Consultation  # Assuming Consultation model path
from notification.signals import notification_signal
from notification.scripts_notifications.situation import (
    send_audio_deletion_warning_notification,
    send_audio_deletion_cancelled_notification,
    send_audio_deleted_confirmation_notification,
    send_audio_deletion_inactivity_warning_notification,
)
from django.conf import settings
import os

logger = logging.getLogger(__name__)


@shared_task
def transcribe_audio_task(instance_id):
    """
    Celery task to transcribe audio asynchronously for the given AudioRecording instance ID.
    """
    instance = AudioRecording.objects.get(id=instance_id)
    service = SpeechToTextService()
    service.transcribe_audio_instance(instance)


@shared_task
def correct_transcription_task(audio_recording_id):
    """
    Celery task to correct the transcription of an AudioRecording using Mistral LLM.
    """
    instance = AudioRecording.objects.filter(id=audio_recording_id).first()
    if not instance or not instance.transcription:
        return
    # Do not re-correct if already corrected
    if instance.transcription_corrected:
        return
    service = CorrectionTextService()
    corrected = service.correct_medical_transcription(instance.transcription)
    instance.transcription_corrected = corrected
    instance.save(update_fields=["transcription_corrected"])


@shared_task
def execute_audio_deletion_task(audio_recording_id, reason_for_deletion):
    """
    Celery task to delete the audio file and update its status to DELETED.
    Sends a confirmation notification.
    'reason_for_deletion' can be 'validated_consultation_legacy', 'inactivity', or 'validated_consultation_7_day_rule'.
    """
    try:
        audio_recording = AudioRecording.objects.get(id=audio_recording_id)

        if audio_recording.status == AudioRecordingStatus.DELETED.value:
            print(f"Audio {audio_recording_id} is already deleted.")
            return

        if audio_recording.file and hasattr(audio_recording.file, "path"):
            file_path = audio_recording.file.path
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"Successfully deleted physical file: {file_path}")
            else:
                print(f"Physical file not found at path: {file_path}")
        else:
            print(
                f"No file associated with audio recording {audio_recording_id} or path not available."
            )

        audio_recording.status = AudioRecordingStatus.DELETED.value
        audio_recording.file = None  # Vider la référence au fichier

        audio_recording.save(update_fields=["status", "file"])

        recipient_user = None
        if (
            audio_recording.consultation
            and hasattr(audio_recording.consultation, "patient")
            and audio_recording.consultation.patient
            and hasattr(audio_recording.consultation.patient, "user")
        ):
            recipient_user = audio_recording.consultation.patient.user
        elif audio_recording.auteur:
            recipient_user = audio_recording.auteur
        else:
            print(
                f"Could not determine recipient for audio {audio_recording.id} deletion confirmation notification."
            )

        if recipient_user:
            notification_verb = "Audio Supprimé"
            nf_type = "AUDIO_DELETED_GENERIC"
            description_details = {
                "validated_consultation_legacy": f"associé à une consultation validée (ancienne règle) a été supprimé comme prévu.",
                "inactivity": f"a été supprimé en raison de 30 jours d'inactivité sur la consultation associée.",
                "validated_consultation_7_day_rule": f"associé à une consultation validée/envoyée a été supprimé après la période de vérification de 7 jours.",
            }
            logger.info(
                f"Notification: Audio deletion confirmation sent for AudioRecording ID {audio_recording.id} to user {recipient_user.id}. Reason: {reason_for_deletion}"
            )
            send_audio_deleted_confirmation_notification(
                audio_recording=audio_recording,
                recipients=[recipient_user],
                reason_for_deletion=reason_for_deletion,  # Pass the reason key directly
            )
        print(
            f"Audio {audio_recording_id} has been deleted. Reason: {reason_for_deletion}"
        )

    except AudioRecording.DoesNotExist:
        print(f"AudioRecording with id {audio_recording_id} not found for deletion.")
    except Exception as e:
        print(
            f"Error in execute_audio_deletion_task for audio {audio_recording_id}: {e}"
        )


@shared_task
def cleanup_inactive_audio_files_task():
    """
    Tâche Celery périodique pour gérer le cycle de vie des audios liés aux consultations inactives.
    1. Envoie un avertissement de suppression 7 jours avant si la consultation est inactive depuis 23 jours.
    2. Supprime l'audio si la consultation est toujours inactive et que l'avertissement a été envoyé il y a 7 jours (total 30 jours d'inactivité).
    3. Annule l'avertissement de suppression si la consultation redevient active.
    """
    logger.info(
        f"CELERY TASK: Starting cleanup_inactive_audio_files_task at {timezone.now()}"
    )
    now = timezone.now()

    # --- Définitions des seuils de temps ---
    warning_threshold_days = 23
    deletion_threshold_days = 30
    warning_period_days = deletion_threshold_days - warning_threshold_days

    warning_date_cutoff = now - timedelta(days=warning_threshold_days)
    deletion_date_cutoff = now - timedelta(days=deletion_threshold_days)
    warning_sent_cutoff = now - timedelta(days=warning_period_days)

    logger.info(
        f"Running cleanup_inactive_audio_files_task at {now}. Warning cutoff: {warning_date_cutoff}, Deletion cutoff: {deletion_date_cutoff}, Warning sent before: {warning_sent_cutoff}"
    )

    # --- Partie 1: Identifier les audios nécessitant un avertissement de suppression pour inactivité ---
    consultations_needing_warning_scan = Consultation.objects.filter(
        modified_on__lt=warning_date_cutoff
    )

    for consultation in consultations_needing_warning_scan:
        audios_for_inactivity_warning = (
            AudioRecording.objects.filter(
                consultation=consultation,
                # On envoie un avertissement si la consultation est inactive depuis au moins `warning_threshold_days` (23 jours)
                # et que l'audio n'a pas déjà un statut de suppression en cours.
                # Si un audio a été "raté" et est inactif depuis > 30 jours sans avertissement,
                # il recevra un avertissement maintenant et sera supprimé 7 jours plus tard si toujours inactif.
            )
            .exclude(
                Q(status=AudioRecordingStatus.DELETED.value)
                | Q(status=AudioRecordingStatus.PENDING_DELETION_INACTIVE.value)
                | Q(
                    status=AudioRecordingStatus.PENDING_DELETION_7DAY_WARNING_SENT.value
                )
            )
            .select_related("auteur", "consultation__zkf_redacteur")
        )

        for audio_recording in audios_for_inactivity_warning:
            logger.info(
                f"Audio ID {audio_recording.id} (Consultation {consultation.id}) needs inactivity warning. Consultation modified_on: {consultation.modified_on}"
            )
            audio_recording.status = (
                AudioRecordingStatus.PENDING_DELETION_INACTIVE.value
            )
            audio_recording.warning_sent_at = now
            audio_recording.save(update_fields=["status", "warning_sent_at"])

            recipient_user = audio_recording.auteur or (
                audio_recording.consultation
                and audio_recording.consultation.zkf_redacteur
            )

            if recipient_user:
                try:
                    audio_content_type = ContentType.objects.get_for_model(
                        AudioRecording
                    )
                    deleted_count, _ = Notification.objects.filter(
                        zkf_recipient=recipient_user,
                        nf_type="audio_deletion_cancelled",
                        obj_content_type=audio_content_type,
                        obj_object_id=str(audio_recording.id),
                    ).delete()
                    if deleted_count > 0:
                        logger.info(
                            f"Deleted {deleted_count} 'audio_deletion_cancelled' notifications for Audio ID {audio_recording.id} before sending inactivity warning."
                        )
                except Exception as e:
                    logger.error(
                        f"Error deleting 'audio_deletion_cancelled' notifications for Audio ID {audio_recording.id} during inactivity warning: {e}"
                    )

                deletion_due_date_display = (
                    now + timedelta(days=warning_period_days)
                ).strftime("%d/%m/%Y")
                send_audio_deletion_inactivity_warning_notification(
                    audio_recording=audio_recording,
                    recipients=[recipient_user],
                    deletion_date_display=deletion_due_date_display,
                )
                logger.info(
                    f"Inactivity warning sent for Audio ID {audio_recording.id}. Scheduled for deletion around {deletion_due_date_display}."
                )

    # --- Partie 2: Identifier les audios à supprimer pour inactivité (avertissement envoyé il y a `warning_period_days`) ---
    audios_to_delete_for_inactivity = AudioRecording.objects.filter(
        status=AudioRecordingStatus.PENDING_DELETION_INACTIVE.value,
        warning_sent_at__lte=warning_sent_cutoff,
        consultation__modified_on__lt=deletion_date_cutoff,
    ).select_related("consultation")

    for audio_recording in audios_to_delete_for_inactivity:
        logger.info(
            f"Initiating deletion for inactive Audio ID {audio_recording.id} (Consultation {audio_recording.consultation.id}). Warning sent at: {audio_recording.warning_sent_at}, Consultation modified_on: {audio_recording.consultation.modified_on}"
        )
        execute_audio_deletion_task.delay(str(audio_recording.id), "inactivity")

    # --- Partie 3: Gérer les cas où une consultation redevient active APRÈS un avertissement d'inactivité ---
    audios_to_revert_inactivity_warning = AudioRecording.objects.filter(
        status=AudioRecordingStatus.PENDING_DELETION_INACTIVE.value,
        consultation__modified_on__gte=warning_date_cutoff,
    ).select_related("consultation", "auteur", "consultation__zkf_redacteur")

    for audio_recording in audios_to_revert_inactivity_warning:
        logger.info(
            f"Consultation for Audio ID {audio_recording.id} (Consultation {audio_recording.consultation.id}) became active again. Consultation modified_on: {audio_recording.consultation.modified_on}. Reverting inactivity warning."
        )
        previous_status = AudioRecordingStatus.VALIDATED.value

        audio_recording.status = previous_status
        audio_recording.warning_sent_at = None
        audio_recording.save(update_fields=["status", "warning_sent_at"])

        recipient_user = audio_recording.auteur or (
            audio_recording.consultation and audio_recording.consultation.zkf_redacteur
        )

        if recipient_user:
            send_audio_deletion_cancelled_notification(
                audio_recording=audio_recording, recipients=[recipient_user]
            )
            logger.info(
                f"Inactivity deletion cancelled notification sent for Audio ID {audio_recording.id}."
            )

    logger.info("Finished cleanup_inactive_audio_files_task.")


@shared_task
def check_audio_for_validated_consultations_task():
    """
    Tâche Celery périodique pour gérer le cycle de vie des audios liés aux consultations validées/envoyées.
    1. Envoie un avertissement de suppression 7 jours avant si la consultation est VALIDE/ENVOYE.
    2. Supprime l'audio si la consultation est toujours VALIDE/ENVOYE et inchangée (modified_on) après 7 jours depuis l'avertissement.
    3. Annule la suppression (réinitialise le statut et warning_sent_at) si la consultation n'est plus VALIDE/ENVOYE.
    """
    logger.info(
        f"CELERY TASK: Starting check_audio_for_validated_consultations_task at {timezone.now()}"
    )
    now = timezone.now()
    seven_days_from_now_display = (now + timedelta(days=7)).strftime("%d/%m/%Y")
    seven_days_ago = now - timedelta(days=7)

    target_consultation_statuses = [Consultation.ENVOYE, Consultation.VALIDE]

    # --- Partie 1: Identifier les audios nécessitant un avertissement de suppression ---
    audios_needing_warning = (
        AudioRecording.objects.filter(
            consultation__statut_courrier_consultation__in=target_consultation_statuses
        )
        .exclude(
            Q(status=AudioRecordingStatus.PENDING_DELETION_7DAY_WARNING_SENT.value)
            | Q(status=AudioRecordingStatus.DELETED.value)
            | Q(status=AudioRecordingStatus.PENDING_DELETION_INACTIVE.value)
        )
        .select_related("consultation", "auteur", "consultation__zkf_redacteur")
    )

    for audio_recording in audios_needing_warning:
        audio_recording.status = (
            AudioRecordingStatus.PENDING_DELETION_7DAY_WARNING_SENT.value
        )
        audio_recording.warning_sent_at = now
        audio_recording.save(update_fields=["status", "warning_sent_at"])

        recipient_user = audio_recording.auteur or (
            audio_recording.consultation and audio_recording.consultation.zkf_redacteur
        )

        if recipient_user:
            # Supprimer les notifications 'audio_deletion_cancelled' existantes pour cet audio et ce destinataire
            try:
                audio_content_type = ContentType.objects.get_for_model(AudioRecording)
                existing_cancelled_notifications = Notification.objects.filter(
                    zkf_recipient=recipient_user,
                    nf_type="audio_deletion_cancelled",
                    obj_content_type=audio_content_type,
                    obj_object_id=str(audio_recording.id),
                )
                if existing_cancelled_notifications.exists():
                    count = existing_cancelled_notifications.count()
                    existing_cancelled_notifications.delete()
                    logger.info(
                        f"Deleted {count} existing 'audio_deletion_cancelled' notification(s) for AudioRecording ID {audio_recording.id} and user {recipient_user.id}."
                    )
            except ContentType.DoesNotExist:
                logger.error(
                    f"ContentType for AudioRecording not found. Cannot delete 'audio_deletion_cancelled' notifications for AudioRecording ID {audio_recording.id}."
                )
            except Exception as e:
                logger.error(
                    f"Error deleting 'audio_deletion_cancelled' notifications for AudioRecording ID {audio_recording.id}: {e}"
                )

            logger.info(
                f"Notification: Audio deletion warning sent for AudioRecording ID {audio_recording.id} to user {recipient_user.id}"
            )
            send_audio_deletion_warning_notification(
                audio_recording=audio_recording,
                recipients=[recipient_user],
                deletion_date_display=seven_days_from_now_display,
                consultation_status_display=audio_recording.consultation.get_statut_courrier_consultation_display(),
            )
        print(
            f"Avertissement de suppression (7 jours) envoyé pour l'audio {audio_recording.id}, consultation {audio_recording.consultation.id}"
        )

    # --- Partie 2: Identifier les audios à supprimer ---
    audios_to_delete = AudioRecording.objects.filter(
        status=AudioRecordingStatus.PENDING_DELETION_7DAY_WARNING_SENT.value,
        consultation__statut_courrier_consultation__in=target_consultation_statuses,
        warning_sent_at__lte=seven_days_ago,
        consultation__modified_on__lte=seven_days_ago,
    ).select_related("consultation")

    for audio_recording in audios_to_delete:
        print(
            f"Lancement de la suppression pour l'audio {audio_recording.id} (consultation {audio_recording.consultation.id}) après vérification des 7 jours."
        )
        execute_audio_deletion_task.delay(
            str(audio_recording.id), "validated_consultation_7_day_rule"
        )

    # --- Partie 3: Gérer les cas de dévalidation ---
    audios_to_revert = (
        AudioRecording.objects.filter(
            status=AudioRecordingStatus.PENDING_DELETION_7DAY_WARNING_SENT.value
        )
        .exclude(
            consultation__statut_courrier_consultation__in=target_consultation_statuses
        )
        .select_related("consultation", "auteur", "consultation__zkf_redacteur")
    )

    for audio_recording in audios_to_revert:
        previous_status = (
            AudioRecordingStatus.VALIDATED.value
        )  # Default fallback status
        # Potentially, one could try to infer a more accurate previous status if needed, e.g., based on other fields or a history log.
        audio_recording.status = previous_status
        audio_recording.warning_sent_at = None
        audio_recording.save(update_fields=["status", "warning_sent_at"])

        recipient_user = audio_recording.auteur or (
            audio_recording.consultation and audio_recording.consultation.zkf_redacteur
        )

        if recipient_user:
            logger.info(
                f"Notification: Audio deletion cancellation sent for AudioRecording ID {audio_recording.id} to user {recipient_user.id} due to status/date change."
            )
            send_audio_deletion_cancelled_notification(
                audio_recording=audio_recording, recipients=[recipient_user]
            )
        print(
            f"Suppression annulée pour l'audio {audio_recording.id} car la consultation {audio_recording.consultation.id} n'est plus VALIDE/ENVOYE."
        )

    print("Finished check_audio_for_validated_consultations_task.")
