from django.urls import path
from api.document.audio_recording.views import AudioRecordingViewSet
from rest_framework.routers import DefaultRouter

# DRF router for standard CRUD endpoints (if needed elsewhere)
router = DefaultRouter()
router.register(r"", AudioRecordingViewSet, basename="audio-recording")

# Custom endpoints (like in compte_rendu_correspondant)
urlpatterns = [
    # Example: upload audio for a specific patient
    path(
        r"upload-audio/<uuid:patient_pk>",
        AudioRecordingViewSet.as_view({"post": "create"}),
        name="upload-audio-for-patient",
    ),
    # Example: update audio recording by pk
    path(
        r"update-audio/<uuid:pk>",
        AudioRecordingViewSet.as_view({"put": "update"}),
        name="update-audio-recording",
    ),
]

# Add DRF router URLs for compatibility
urlpatterns += router.urls
