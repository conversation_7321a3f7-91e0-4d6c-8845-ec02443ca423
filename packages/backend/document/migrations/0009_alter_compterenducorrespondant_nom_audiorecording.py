# Generated by Django 5.1.7 on 2025-04-24 13:50

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("consultation", "0006_merge_20250402_1853"),
        ("document", "0008_compterenducorrespondant_should_be_contacted_and_more"),
        ("hospitalisation", "0005_hospitalisation_consignes_sortie"),
        ("patient", "0003_patient_note_opposition_data_exploitation"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name="compterenducorrespondant",
            name="nom",
            field=models.CharField(
                blank=True,
                help_text="Nom descriptif du document",
                max_length=256,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="AudioRecording",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("file", models.FileField(upload_to="audio/")),
                (
                    "auteur",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audio_recordings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "consultation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audio_recordings",
                        to="consultation.consultation",
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audio_recordings",
                        to="document.document",
                    ),
                ),
                (
                    "hospitalisation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audio_recordings",
                        to="hospitalisation.hospitalisation",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="audio_recordings",
                        to="patient.patient",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
