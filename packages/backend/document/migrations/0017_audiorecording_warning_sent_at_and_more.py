# Generated by Django 5.1.7 on 2025-05-30 17:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("document", "0016_alter_audiorecording_created_at_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="audiorecording",
            name="warning_sent_at",
            field=models.DateTimeField(
                blank=True,
                help_text="Timestamp of when the 7-day deletion warning was sent.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="audiorecording",
            name="status",
            field=models.CharField(
                choices=[
                    ("unverified", "UNVERIFIED"),
                    ("pending_validation", "PENDING_VALIDATION"),
                    ("validated", "VALIDATED"),
                    ("pending_deletion_validated", "PENDING_DELETION_VALIDATED"),
                    ("pending_deletion_inactive", "PENDING_DELETION_INACTIVE"),
                    (
                        "pending_deletion_7day_warning_sent",
                        "PENDING_DELETION_7DAY_WARNING_SENT",
                    ),
                    ("deleted", "DELETED"),
                ],
                default="unverified",
                help_text="Status of the audio recording validation and lifecycle.",
                max_length=50,
            ),
        ),
    ]
