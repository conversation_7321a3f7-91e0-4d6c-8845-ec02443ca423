# packages/backend/document/signals/audio_recording_transcription.py
import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from document.models.audio_recording import AudioRecording
from consultation.models import Consultation
from document.tasks import transcribe_audio_task, correct_transcription_task
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)

# Status constants for clarity, assuming these are the string values in your enum/CharField
STATUS_UNVERIFIED = "unverified"
STATUS_PENDING_VALIDATION = "pending_validation"


@receiver(post_save, sender=AudioRecording)
def transcribe_audio_on_save(sender, instance, created, **kwargs):
    """
    Signal handler that triggers asynchronous audio transcription via Celery
    when a new AudioRecording is created.
    """
    if created:
        # Only trigger the Celery task. All transcription logic is handled asynchronously.
        transcribe_audio_task.delay(instance.id)


@receiver(post_save, sender=AudioRecording)
def trigger_transcription_correction_if_needed(sender, instance, **kwargs):
    """
    Triggers a Celery task to correct the transcription if it is present
    and not already corrected.
    This signal acts on any save, not just creation.
    """
    # Check if transcription exists and corrected version does not
    if instance.transcription and not instance.transcription_corrected:
        # Check if a correction task is already running or has failed previously
        # might be good to check status here too, e.g., if status indicates correction is pending/failed.
        # For now, simple check:
        correct_transcription_task.delay(instance.id)


@receiver(post_save, sender=AudioRecording)
def append_transcription_to_courrier_on_correction(sender, instance, **kwargs):
    """
    Appends the corrected transcription to the linked consultation's courrier
    if the transcription_corrected field is populated and the status is 'unverified'.
    Updates the AudioRecording status to 'pending_validation' afterwards.
    If the consultation courrier is already 'envoye' or 'valide', it's not modified.
    The consultation courrier status is updated to 'transcrit' if modified.
    """
    # Proceed only if transcription is corrected and status is 'unverified'
    if instance.transcription_corrected and instance.status == STATUS_UNVERIFIED:

        # Ensure there's a linked consultation
        # Accessing via instance.consultation should work if the ForeignKey is set up typically
        consultation = instance.consultation
        if not consultation:
            logger.warning(
                f"AudioRecording {instance.id} has no linked consultation. "
                f"Cannot append transcription to courrier."
            )
            return

        # Check if consultation courrier is already finalized
        if consultation.statut_courrier_consultation in [
            Consultation.ENVOYE,
            Consultation.VALIDE,
        ]:
            logger.info(
                f"Consultation {consultation.id} courrier is already '{consultation.statut_courrier_consultation}'. "
                f"Skipping transcription append from AudioRecording {instance.id}."
            )
            # Optionally, still change status of AudioRecording to 'pending_validation'
            # if an empty corrected transcription means it's processed.
            # This ensures the audio recording isn't re-processed for courrier append.
            instance.status = STATUS_PENDING_VALIDATION
            instance.save(update_fields=["status"])
            logger.info(
                f"Updated status of AudioRecording {instance.id} to {STATUS_PENDING_VALIDATION} "
                f"even though courrier was not appended."
            )
            return

        # 1. Prepare the corrected transcription text
        formatted_date = instance.created_at.strftime("%d/%m/%Y %H:%M")
        # Ensure transcription_corrected is not None before replacing
        corrected_text_raw = (
            instance.transcription_corrected if instance.transcription_corrected else ""
        )
        # Format paragraphs separated by newline characters with <br>
        formatted_corrected_text_only = corrected_text_raw.replace("\n", "<br>")

        # 2. Check if courrier_consultation is empty or only whitespace
        if (
            not consultation.courrier_consultation
            or not consultation.courrier_consultation.strip()
        ):
            # This is the first transcription being added to an empty courrier
            # Do not add the "Modif enregistrement audio" header
            consultation.courrier_consultation = formatted_corrected_text_only
        else:
            # courrier_consultation already has content, so this is an addition/modification
            # Add the header for modification notice
            header = (
                f"<p><span style='color: red;'>Modif enregistrement audio - {formatted_date}</span></p>"
                "<p><span style='color: red;'>----------------------------------------------</span></p>"
            )
            # Combine header and the new corrected text
            text_to_append = f"{header}\n{formatted_corrected_text_only}"
            consultation.courrier_consultation += f"<br><br>{text_to_append}"

        # Update consultation status to 'transcrit'
        consultation.statut_courrier_consultation = Consultation.TRANSCRIT
        # Update the modified_on timestamp
        consultation.modified_on = timezone.now()
        consultation.save(
            update_fields=[
                "courrier_consultation",
                "statut_courrier_consultation",
                "modified_on",
            ]
        )

        # Update AudioRecording status to 'pending_validation'
        instance.status = STATUS_PENDING_VALIDATION
        instance.save(update_fields=["status"])
        logger.info(
            f"Appended transcription from AudioRecording {instance.id} to Consultation {consultation.id} "
            f"and updated its status to {Consultation.TRANSCRIT}. "
            f"Updated AudioRecording status to {STATUS_PENDING_VALIDATION}."
        )
