import uuid
from django.db import models
from django.conf import settings
from datetime import datetime
from enum import Enum


# Utilitaire pour le chemin dynamique
def audio_upload_path(instance, filename):
    # Stocke dans audio/YYYY/MM/DD/filename
    return (
        f"audio/{instance.created_at.strftime('%Y/%m/%d')}/{filename}"
        if instance.created_at
        else f"audio/{datetime.now().strftime('%Y/%m/%d')}/{filename}"
    )


class AudioRecordingStatus(Enum):
    UNVERIFIED = "unverified"
    PENDING_VALIDATION = "pending_validation"
    VALIDATED = "validated"
    PENDING_DELETION_VALIDATED = "pending_deletion_validated"  # Legacy or direct pending deletion after validation
    PENDING_DELETION_INACTIVE = (
        "pending_deletion_inactive"  # Consultation inactive, pending deletion
    )
    PENDING_DELETION_7DAY_WARNING_SENT = "pending_deletion_7day_warning_sent"  # 7-day deletion warning sent for validated/sent consultation
    DELETED = "deleted"  # Audio file has been deleted


class AudioRecording(models.Model):
    # Stores the raw or formatted transcription text generated from the audio file
    transcription = models.TextField(
        null=True, blank=True, help_text="Transcription of the audio recording."
    )
    # Stores the LLM-corrected medical transcription
    transcription_corrected = models.TextField(
        null=True, blank=True, help_text="LLM-corrected medical transcription."
    )
    # Title of the audio recording
    title = models.CharField(
        max_length=200, blank=True, null=True, help_text="Title of the audio recording."
    )
    # Status of the audio recording validation
    status = models.CharField(
        max_length=50,
        choices=[(status.value, status.name) for status in AudioRecordingStatus],
        default=AudioRecordingStatus.UNVERIFIED.value,
        help_text="Status of the audio recording validation and lifecycle.",
    )
    # Timestamp for when the 7-day deletion warning was sent
    warning_sent_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp of when the 7-day deletion warning was sent.",
    )

    class Meta:
        ordering = ["-created_at"]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # Date and time when the audio recording was created
    created_at = models.DateTimeField(auto_now_add=True, help_text="Date of creation.")

    auteur = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="audio_recordings",
    )
    # Lien vers le document parent
    document = models.ForeignKey(
        "document.Document",  # adapte le nom du modèle si besoin
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="audio_recordings",
    )
    # Lien vers la consultation (optionnel)
    consultation = models.ForeignKey(
        "consultation.Consultation",  # adapte selon ton app
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="audio_recordings",
    )
    # Lien vers l'hospitalisation (optionnel)
    hospitalisation = models.ForeignKey(
        "hospitalisation.Hospitalisation",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="audio_recordings",
    )
    # Lien vers le patient
    patient = models.ForeignKey(
        "patient.Patient",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="audio_recordings",
    )
    # Fichier audio stocké dans media/audio/YYYY/MM/DD/
    file = models.FileField(upload_to=audio_upload_path)

    def __str__(self):
        return f"Audio #{self.pk} - {self.file.name}"
