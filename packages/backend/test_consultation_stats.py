#!/usr/bin/env python
"""
Test script to verify consultation statistics .dt accessor fixes
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "babylone.settings.development")
django.setup()

import pandas as pd
from statistiques.consultation.statistiques_consultation import (
    unified_consultations_statistics,
    to_datetime_naive,
)


def test_dt_accessor_fixes():
    """Test the consultation statistics functions to ensure no .dt accessor errors"""

    print("Testing consultation statistics .dt accessor fixes...")

    # Test 1: Test to_datetime_naive function
    print("\n1. Testing to_datetime_naive function...")
    try:
        # Test with string
        result1 = to_datetime_naive("2024-01-15 10:30:00")
        print("   OK String input: {} (type: {})".format(result1, type(result1)))

        # Test with pandas Series
        series_test = pd.Series(["2024-01-15 10:30:00", "2024-01-15 11:30:00"])
        result2 = to_datetime_naive(series_test)
        print("   OK Series input: {} (type: {})".format(result2.head(), type(result2)))

        # Test with mixed timezone data
        tz_series = pd.to_datetime(series_test).dt.tz_localize("UTC")
        result3 = to_datetime_naive(tz_series)
        print(
            "   OK Timezone-aware input: {} (type: {})".format(
                result3.head(), type(result3)
            )
        )

    except Exception as e:
        print("   FAIL to_datetime_naive test failed: {}".format(e))
        return False

    # Test 2: Test with sample data
    print("\n2. Testing with sample consultation data...")
    try:
        # Create sample consultation data
        sample_data = [
            {
                "id": 1,
                "consultation_date": "2024-01-15",
                "consultation_debut_prevu": "10:30:00",
                "consultation_fin_prevu": "11:00:00",
                "consultation_debut_reel": "10:35:00",
                "consultation_fin_reel": "11:05:00",
                "consultation_arrive": "10:25:00",
                "etat_consultation": "finie",
                "zkf_patient__nom": "Test",
                "zkf_patient__prenom": "Patient",
            },
            {
                "id": 2,
                "consultation_date": "2024-01-15",
                "consultation_debut_prevu": "11:00:00",
                "consultation_fin_prevu": "11:30:00",
                "consultation_debut_reel": "11:05:00",
                "consultation_fin_reel": "11:35:00",
                "consultation_arrive": "10:55:00",
                "etat_consultation": "finie",
                "zkf_patient__nom": "Test2",
                "zkf_patient__prenom": "Patient2",
            },
        ]

        # Test with mock data (no database required)
        from datetime import datetime, date

        result = unified_consultations_statistics(
            consultations=sample_data,
            user=1,
            start_date=date(2024, 1, 15),
            end_date=date(2024, 1, 15),
        )

        print("   OK Sample data processing successful!")
        print("   Result type: {}".format(type(result)))
        print("   Has analysis_type: {}".format("analysis_type" in result))

    except Exception as e:
        print("   FAIL Sample data test failed: {}".format(e))
        import traceback

        traceback.print_exc()
        return False

    print("\nSUCCESS: All consultation statistics tests passed!")
    print("INFO: .dt accessor errors should be resolved!")
    return True


if __name__ == "__main__":
    success = test_dt_accessor_fixes()
    sys.exit(0 if success else 1)
