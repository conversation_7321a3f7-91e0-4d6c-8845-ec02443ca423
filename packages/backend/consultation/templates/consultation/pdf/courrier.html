{% extends "impression/general.html" %}
{% load routeur_antecedents %} <!-- tag to redirect antecedents templates and view to the appropriate app !-->


{% block prescripteur %}
	<small>	
		{{consultation.zkf_redacteur.profile.entete_ordonnance|safe}}<br>
		RPPS: {{consultation.zkf_redacteur.profile.rpps}}
	</small>
{% endblock prescripteur %}


{% block adresse %}
<br>
	<div style = "text-align: center">
		<small>
			{{consultation.zkf_site.nom_site}}<br>
			{{consultation.zkf_site.adresse_site}}<br>
			{{consultation.zkf_site.zip_code_site}} {{consultation.zkf_site.ville_site}}<br>
			{{consultation.zkf_site.telephone_site}}
		</small>
	</div>
{% endblock adresse %}

<style>

ul {
	line-height: 70%;
} 
</style>

<!-- Title of the block ! -->

{% block title %}

<table class="header">
    <tr>
      <!-- Infos patient à gauche -->
      <td style="width: 25%; border:1px solid black; border-radius: 15px; padding: 5px;">
        {% include 'patient/identification.html' %}
      </td>

      <!-- Cellule centrale pour titre -->
      <td style="width: 40%;" class="title_crh"></td>

      <td style="width: 30%;">
		<!-- CORRESPONDANTS -->
		<div style="text-align: right; font-size: 8pt; font-weight: normal;">
			{% if consultation.zkf_patient.correspondants.all.exists %}
				{% for c in consultation.zkf_patient.correspondants.all %}
					<div style="margin: 3pt 0; line-height: 1.2;">
						{% if c.structure %}
							{{ c.structure }} – {{ c.politesse }}<br>
						{% else %}
							{{ c.politesse }}<br>
						{% endif %}
			
						<small>
							{% for s in c.correspondant.specialite.all %}
							{{ s.spe_medecin }}{% if not forloop.last %}, {% endif %}
							{% endfor %}
				
							{% for v in c.adresse_set.all %}
							{{ v.code_postal }} {{ v.ville|title }}{% if not forloop.last %}, {% endif %}
							{% endfor %}
						</small>
					</div>
				{% endfor %}
	{% endif %}
  </div>
	  </td>
    </tr>
  </table>

  

{% endblock title %}

<!-- Identification of the patient -->
{% block identification %}
	<p>
		<div style="text-align:right; font-size: 90%">
			{{consultation.zkf_site.ville_site}}, le {{consultation.consultation_date|date:"d/m/Y"}}
		</div>
	</p>

{% endblock identification %}

<!-- Main content of the courrier -->
{% block content %}

<!-- Consultations post opératoire pour: -->


{% if consultation.operation_liee.all %}
<p>
	<br><small>
	<i>Consultation de suivi:</i><br>
		{% for o in consultation.operation_liee.all %}
			{{o.titre_operation}} le {{o.operation_date|date:"d/m/Y"}}
		{% endfor %}
</p></small>
{% endif %}

<p>	
	{{consultation.courrier_consultation|safe}}
</p>


{% endblock content %}



{% block signature %}
	<div style = "text-align:right;" >
			Dr.{{consultation.zkf_redacteur.last_name}}
	</div>

	<div style = "text-align:left;" ></div>
		<p>
			<i>
				<br><small>{{consultation.courrier_consultation_bas_page|safe}}</small>
			</i>
		</p>
	</div>

	<div style="text-align:left; font-size: 60%">

		{% if consultation.zkf_patient.correspondants.all.exists %}
		Double à: <br>
			{% for c in consultation.zkf_patient.correspondants.all %}
				
				{{c.politesse}}, 
				
				<!-- Specialité -->
				
				<small>
					{% for s in c.correspondant.specialite.all %}
						{{s.spe_medecin}}, 
					{% endfor %}
					
					<!-- VILLE -->
					{% for a in c.adresse_set.all %}
						{{a.adresse_complete}},
					{% endfor %}
				</small>
				<br>
			{% endfor %}
		{% endif %}
	
	</div>
{% endblock signature%}

