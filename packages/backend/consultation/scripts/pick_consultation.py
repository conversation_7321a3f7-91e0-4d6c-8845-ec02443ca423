# get app model from string
from consultation.models import Consultation
from patient.models import Patient
import datetime
from typing import Optional
from schedule.models import Vacation, UserEvent
from schedule.scripts.find_week import (
    find_weeks,
    check_semaine_paire_impaire,
    check_interval,
)
from django.forms.models import model_to_dict
from django.http import JsonResponse
from schedule.scripts.check_overlap import cancel_vacation_on_leave


# Generating 15 minute time interval array in python
def datetime_range(start, end, delta):
    current = start
    while current < end:
        yield current
        current += delta


"""
With a duration as input: propose a list of consultations that can be picked
"""


def new_consultation_assistant(
    patient: object,
    duration: str,
    date: str,
    user: object,
    site: Optional[object] = None,
    lieu: Optional[object] = None,
    hospitalisation: Optional[object] = None,
):  # return a list of datetimes object

    # get start and end date from the duration and the date = interval to find a consultation
    start_date = date + datetime.timedelta(days=duration - 7)
    end_date = date + datetime.timedelta(days=duration + 15)

    # get vacations in the interval
    user_vacations = list()
    vacations = Vacation.objects.filter(
        deleted=False,
        zkf_user__in=[user],
        type_vacation="CONSULTATION",
        site=site,
    )
    # get all possible dates for the consultation with time range 15'

    # 1/ get all week numbers from range of dates
    weeks = find_weeks(start_date, end_date)
    for vacation in vacations:
        for w in weeks:
            day = datetime.datetime.strptime(
                str(w.split(",")[0])
                + " "
                + str(w.split(",")[1])
                + " "
                + str(vacation.jour_semaine),
                "%Y %W %w",
            )

        # Check if the day is in the start and end period interval (like for vacations...)
        # Check if it is une semaine paire ou impaire
        if check_semaine_paire_impaire(
            day, vacation.repetition_semaine
        ) and check_interval(day, vacation.debut_periode, vacation.fin_periode):
            user_vacations.append(
                {
                    "date_vacation": day,
                    "start_vacation": vacation.debut_vacation,
                    "end_vacation": vacation.fin_vacation,
                }
            )
    # check if the vacation is during a leave period (absence): if so: return True.
    fermeture_vacation = UserEvent.objects.filter(
        deleted=False,
        users__in=[user],
        event_start__lte=end_date,
        event_end__gte=start_date,
    ).exclude(
        event_category__name="INFORMATION",
    )
    filtered_user_vacations = []
    if fermeture_vacation.exists():
        for fermeture in fermeture_vacation:
            filtered_user_vacations = [
                vacation_datetime
                for vacation_datetime in user_vacations
                if not (
                    cancel_vacation_on_leave(
                        start_vacation=datetime.datetime.combine(
                            vacation_datetime["date_vacation"],
                            vacation_datetime["start_vacation"],
                        ),
                        end_vacation=datetime.datetime.combine(
                            vacation_datetime["date_vacation"],
                            vacation_datetime["end_vacation"],
                        ),
                        start_leave=fermeture.event_start,
                        end_leave=fermeture.event_end,
                    )
                )
            ]

    # parse all vacations: check if there is a consultation in the interval starting from the beginning of the consultation to the end, by 15 to 15'
    # https://stackoverflow.com/questions/39298054/generating-15-minute-time-interval-array-in-python
    all_vacations = list()
    for filtered_vacation in filtered_user_vacations:
        # get the start and end date of the vacation
        start_vacation = filtered_vacation["date_vacation"] + datetime.timedelta(
            hours=filtered_vacation["start_vacation"].hour,
            minutes=filtered_vacation["start_vacation"].minute,
        )
        end_vacation = filtered_vacation["date_vacation"] + datetime.timedelta(
            hours=filtered_vacation["end_vacation"].hour,
            minutes=filtered_vacation["end_vacation"].minute,
        )
        # print ("start_vacation", start_vacation)
        # print ("end_vacation", end_vacation)
        # get the interval of
        dts = [
            dt.strftime("%Y-%m-%d T%H:%M Z")
            for dt in datetime_range(
                start_vacation, end_vacation, datetime.timedelta(minutes=15)
            )
        ]
        all_vacations.extend(dts)
    queryset = list()

    for slot in all_vacations:
        # print (slot)
        #  start = datetime.datetime.strptime(slot, "%Y-%m-%d T%H:%M Z")
        free_consultation_slot = Consultation.objects.filter(
            consultation_date=datetime.datetime.strptime(
                slot, "%Y-%m-%d T%H:%M Z"
            ).date(),
            zkf_redacteur=user,
            deleted=False,
            consultation_debut_prevu=datetime.datetime.strptime(
                slot, "%Y-%m-%d T%H:%M Z"
            ).time(),
        )
        # print (free_consultation_slot.exists(), free_consultation_slot)
        if not free_consultation_slot.exists():
            queryset.append(slot)

    # propose the RDV
    # The `print (queryset)` statement is printing out the list of available consultation slots that
    # can be proposed to the patient based on the input parameters provided to the
    # `new_consultation_assistant` function. These consultation slots are the datetime objects within
    # the specified duration and date range that are not already booked for the patient. The function
    # filters out any existing consultation slots for the patient and returns the list of available
    # slots for scheduling a new consultation.
    list_consultation = list()  # = list of dict

    for slot in queryset:
        list_consultation.append(
            {
                "datetime": datetime.datetime.strptime(slot, "%Y-%m-%d T%H:%M Z"),
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                },
                "site": {"id": site.id, "nom_site": site.nom_site},
                # only add lieu if it is not None
                "lieu": (
                    {"id": lieu.id, "nom_lieux": lieu.nom_lieu}
                    if lieu is not None
                    else None
                ),
            }
        )
    return list_consultation
