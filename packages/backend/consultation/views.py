from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.template.loader import render_to_string  # generate a string from a template
from django.template import Context, Template  # render string with arguments
import datetime

# from django.utils import simplejson as json

from django.views.generic import (
    CreateView,
    DetailView,
    UpdateView,
    View,
    RedirectView,
)
from django.urls import reverse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin

from django.apps import apps

from django.contrib.auth.models import User
from .forms import (
    C<PERSON><PERSON><PERSON>ultationForm,
    AddCodeConsultationForm,
    ModifyModeReglementConsultationForm,
    UploadDocumentForm,
    EditConsultationForm,
    EditCourrierForm,
    CreateConsultationTypeForm,
)
from operation.forms import (
    get_custom_inline_post_op_complication_form,
)

from .models import Consultation, CodageConsultation, ConsultationType
from patient.models import Patient
from codage.ccam.models import Ngap
from operation.models import Operation
from audit.views import AuditableMixin
from document.models.photo import Photo, Portfolio
from document.models.core import Document

import json
from django.forms.models import model_to_dict

from django.forms.models import inlineformset_factory

# get current spe
from babylone.templatetags.routeur_antecedents import get_activ_specialite


# PDF
# from weasyprint import HTML #convert pdf
# import tempfile, os #create temporary files
from xhtml2pdf import pisa
from io import BytesIO  # A stream implementation using an in-memory bytes buffer

# It inherits BufferIOBase
#######
# generate a string which will be converted in pdf !
from impression.views import create_html_string

# scripts
from patient.patient_scripts.etat import update_etat_patient
from consultation.scripts.premiere_consultation import check_premiere_consultation


# Impression
from impression.models import GestionnaireImpression

# update print list
from impression.views import print_manager_list


# generate uuid
import uuid

from operator import itemgetter

# documents
# from document.document_scripts import file_path  # script to check file path
from document.models.ordonnance import OrdonnanceTemplate
from document.document_scripts.retrieve_documents import (
    filter_recent_compte_rendu_correspondant,
    filter_recent_examen_complementaire,
)

# notifications
# from notify.signals import notify
# Disabled by Andy

# script to import all class of a specialite model.
# return exists if there is classes, and the linked class if exists
from operation.operation_scripts.get_operation_data import get_onetoone_relationship

# ML
from babylone.core_scripts.machine_learning.multi_label_text_classification import (
    pickle_file_path,
)
import pickle


class DetailConsultation(LoginRequiredMixin, DetailView):
    template_name = "consultation/detail.html"
    model = Consultation
    context_object_name = "consultation"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["patient"] = self.get_object().zkf_patient
        # filter last CR et Examens complementaires !
        context["examens_complementaires"] = filter_recent_examen_complementaire(
            self.get_object().zkf_patient
        )
        context["cr_correspondants"] = filter_recent_compte_rendu_correspondant(
            self.get_object().zkf_patient
        )

        # get all patient related consultations
        consultations = (
            Consultation.objects.filter(
                zkf_patient=self.get_object().zkf_patient, deleted=False
            )
            .exclude(etat_consultation__in=["annule", "non_venu"])
            .order_by("-consultation_date")
        )
        operations = Operation.objects.filter(
            zkf_patient=self.get_object().zkf_patient, deleted=False
        )

        # create a list of dict
        # d = defaultdict(list)
        d = []
        for c in consultations:
            d.append(
                {
                    "id": c.id,
                    "date": c.consultation_date,
                    "type": "consultation",
                    "titre": c.motif_consultation,
                    "corps": c.courrier_consultation,
                    "lien": str(
                        reverse("consultation:detail-consultation", args=(c.id,))
                    ),
                }
            )

        for o in operations:
            if o.operation_date:
                d.append(
                    {
                        "id": o.id,
                        "date": o.operation_date,
                        "type": "operation",
                        "titre": o.titre_operation,
                        "corps": o.indication_operatoire + " <br>" + o.cro,
                        "lien": str(
                            reverse("operation:detail-operation", args=(o.id,))
                        ),
                    }
                )

        # d = dict(d)
        # sort by value in the dict
        d = sorted(d, key=itemgetter("date"), reverse=True)

        context["events"] = d

        # context['operation_lie'] = self.get_object().operation_liee.all()
        # users = User.objects.all()
        # u = []
        # for user in users:
        #     if user.profile.role == "MED":
        #         df = create_df_consultation(user)
        #         if not df.empty:
        #             try:
        #                 model = build_and_evaluate(df['text'], df['ordonnances'], user=user, outpath = pickle_file_path(model = "consultation", option = "ordonnances_consultation", user = self.request.user, filename = "ordonnances_consultation_clf"))
        #                 print (user)
        #                 u.append(user)
        #             except:
        #                 pass
        #             # from django.core.mail import EmailMessage

        #             # email = EmailMessage(
        #             # 'consultaiton ML' ,
        #             # 'consultaiton ML done',
        #             # '<EMAIL>',
        #             # ['<EMAIL>'],
        #             # )
        #             # email.send()
        # print (u)
        return context


class RetrieveUpcomingConsultations(LoginRequiredMixin, View):
    def post(self, request, patient_pk, *args, **kwargs):
        patient = Patient.objects.get(id=patient_pk)
        consultations = Consultation.objects.filter(zkf_patient=patient, deleted=False)
        today = datetime.datetime.today()
        consultations = consultations.filter(consultation_date__gte=today)

        html = ""
        for c in consultations:
            variable = "{0}".format(str(c.id))
            html += '<a href="#0"  data-element ="' + variable + '">'
            html += (
                c.motif_consultation
                + " "
                + c.consultation_date.strftime("%d/%m/%Y")
                + "</a><br>"
            )
        return HttpResponse(html)


"""
Terminate consultation: 
Set consultation to "finie"
set time to end of the consultation
redirect to acceuil screen
"""


class TerminateConsultation(LoginRequiredMixin, RedirectView):
    pattern_name = "home"

    def get_redirect_url(self, *args, **kwargs):
        consultation = get_object_or_404(Consultation, pk=kwargs["pk"])

        consultation.consultation_fin_reel = datetime.datetime.now().time()
        consultation.etat_consultation = "finie"

        consultation.save()

        url = reverse(self.pattern_name)

        return url


# Load tabs
class NotesTab(LoginRequiredMixin, DetailView):
    template_name = "consultation/tabs/notes.html"
    model = Consultation
    context_object_name = "consultation"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["patient"] = Patient.objects.get(
            id=context["consultation"].zkf_patient.id
        )
        context["form"] = UploadDocumentForm
        context["consultation_type"] = ConsultationType.objects.filter(
            zkf_user=self.request.user
        )
        return context


class PhotoTab(LoginRequiredMixin, DetailView):
    template_name = "document/photo/photos_tab.html"
    model = Consultation
    context_object_name = "consultation"

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)

        photos = Photo.objects.filter(
            zkf_patient=self.object.zkf_patient,
            deleted=False,
        ).order_by("-created_on")
        context["photos"] = photos

        portfolios = Portfolio.objects.filter(
            zkf_patient=self.object.zkf_patient
        ).prefetch_related("photo_set")
        context["portfolios"] = portfolios

        html = render_to_string("document/photo/photos_tab.html", context)

        return HttpResponse(html)


"""
datas during follow up
"""


# https://stackoverflow.com/questions/40478199/how-to-save-inline-formset-user-field-in-django-using-views
class SuiviOperationConsultation(LoginRequiredMixin, DetailView):
    template_name = "consultation/tabs/suivi.html"
    model = Operation
    context_object_name = "operation"
    # form_class = EditPostOpComplicationsForm
    # success_url = None

    def get_context_data(self, **kwargs):
        # context = super().get_context_data(**kwargs)
        context = {}
        context["operation"] = Operation.objects.get(id=self.kwargs["pk"])

        # script to import all class of a specialite model.
        # return exists if there is classes, the list of models and the linked class if exists
        # from .operation_scripts.get_operation_data import get_onetoone_relationship
        classes = get_onetoone_relationship(
            user=self.request.user, obj=self.get_object()
        )

        # display models list only if no relationship is created
        if not classes["exists"]:
            context["models"] = classes["models"]

        # https://stackoverflow.com/questions/2170228/iterate-over-model-instance-field-names-and-values-in-template
        # iterate over the onetoone model class linked to the operation !
        try:
            context["object"] = model_to_dict(
                classes["onetoone_model_class"].objects.get(
                    zkf_operation=self.object.id
                )
            )
        except Exception as e:  # ObjectDoesNotExist or AttributeError:
            print(e)
            context["object"] = None

        # Get post op complications (early follow up -> general complications ! )
        # check if there is a table for complications post op in hospitalisation:
        specialite = get_activ_specialite(self.request, context)
        model_specialite = "ComplicationPostOperatoire" + "".join(
            x.capitalize() for x in specialite.split("_")
        )
        model_name = apps.get_model(specialite, model_specialite)

        # generate a custom inline_form with the choosen model: get_custom_inline_form is defined in forms
        # get field list:
        form = get_custom_inline_post_op_complication_form(model_name)

        exclusion = [
            "delete",
            "deleted",
            "deleted_by",
            "deleted_on",
            "created_by",
            "created_on",
            "modified_by",
            "modified_on",
        ]

        context["form_class"] = form
        # display complications de l'hospitalisation !!! (not each operation)
        if hasattr(self.get_object(), str(model_specialite.lower())):
            self.obj = getattr(self.get_object(), str(model_specialite.lower()))
            context["post_op_complications"] = form(
                instance=self.obj
            )  # ComplicationsFormset((self.request.POST or None), instance = self.get_object())

        # if there is no onetoone table complications created:
        else:
            obj = model_name(zkf_operation=self.get_object(), complication=False)
            self.obj = obj.save()

            context["post_op_complications"] = form(
                instance=self.obj
            )  # (self.request.POST or None))#ComplicationsFormset((self.request.POST or None), instance = self.get_object())

        # # display complications de chaque operation:
        # operations = self.get_object().operation_set.all()
        # #create an list of dicts for complications of each operation
        # operation_complications = {}
        # for o in operations:
        #     if hasattr(o, str(model_specialite.lower())):
        #         #print(getattr(o, str(model_specialite.lower())))
        #         complications = model_to_dict(getattr(o, str(model_specialite.lower())))
        #         operation_complications[o.titre_operation] = complications
        # context['complications_operations'] = operation_complications
        # print (self.kwargs)
        if "pk_consultation" in self.kwargs:
            context["consultation"] = Consultation.objects.get(
                id=self.kwargs["pk_consultation"]
            )

        return context

    def post(self, request, *args, **kwargs):
        context = self.get_context_data()
        form = context["form_class"](request.POST, instance=self.obj)
        form.save()

        return HttpResponse(
            "ok"
        )  # super(SuiviOperationConsultation, self).form_valid(form)

        # specialite = Consultation.objects.get(id =  self.kwargs['consultation_pk']).redacteur.profile.specialite_active.spe
        # app = '_'.join( specialite.split(' ') ).lower()
        # model_specialite = "ComplicationPostOperatoire" + ''.join(x.capitalize() for x in specialite.split(' ')) #app = '_'.join( user_spe_principale.split(' ') ).lower()
        # operation = Operation.objects.get(id = self.request.POST['operation_id'])

        # #get model object from string !
        # model = apps.get_model(app, model_specialite)

        # Update model instance by iterating in the fields !
        # https://stackoverflow.com/questions/67250135/why-is-it-not-possible-to-update-a-django-model-instance-like-this-instanceu
        # https://stackoverflow.com/questions/41744096/efficient-way-to-update-multiple-fields-of-django-model-object
        # bad way: cannot update object by iterating the form !!!
        # need to get the form and update it !
        # for key, value in form: #loop through the list of dict sent via the form
        # setattr(object, key, value)
        # print (value)

        # form = get_custom_inline_post_op_complication_form(model)
        # exclusion = [ 'delete','deleted','deleted_by', 'deleted_on', 'created_by', 'created_on', 'modified_by', 'modified_on',]
        # form_instance = json.loads(request.POST['form'])

        # #create a formset from inlineformset_factory with corresponding model and form
        # ComplicationsFormset = inlineformset_factory(
        #                     Operation,
        #                     model,
        #                     form=form,
        #                     max_num = 1,
        #                     extra=1,
        #                     exclude=exclusion,
        #                     )
        # formset = ComplicationsFormset(request.POST, instance = operation)
        # print (formset)
        # if formset.is_valid():
        #     formset.save()

    # def form_valid(self, form):
    #     context = self.get_context_data()

    #     form_ajax = self.request.POST["form"]
    #     form = context['post_op_complications']
    #     print (form_ajax)

    #     form.instance = form_ajax
    #     form.save()

    #     return  HttpResponse('ok') #super(SuiviOperationConsultation, self).form_valid(form)

    # return HttpResponse('ok')


# Save follow up operation during consultation suivi
class ConsultationFollowUpOperation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        specialite = Consultation.objects.get(
            id=self.kwargs["consultation_pk"]
        ).redacteur.profile.specialite_active.spe
        app = "_".join(specialite.split(" ")).lower()
        model_specialite = "ComplicationPostOperatoire" + "".join(
            x.capitalize() for x in specialite.split(" ")
        )  # app = '_'.join( user_spe_principale.split(' ') ).lower()
        operation = Operation.objects.get(id=self.request.POST["operation_id"])

        # get model object from string !
        model = apps.get_model(app, model_specialite)

        # Update model instance by iterating in the fields !
        # https://stackoverflow.com/questions/67250135/why-is-it-not-possible-to-update-a-django-model-instance-like-this-instanceu
        # https://stackoverflow.com/questions/41744096/efficient-way-to-update-multiple-fields-of-django-model-object
        # bad way: cannot update object by iterating the form !!!
        # need to get the form and update it !
        # for key, value in form: #loop through the list of dict sent via the form
        # setattr(object, key, value)
        # print (value)

        form = get_custom_inline_post_op_complication_form(model)
        exclusion = [
            "delete",
            "deleted",
            "deleted_by",
            "deleted_on",
            "created_by",
            "created_on",
            "modified_by",
            "modified_on",
        ]
        form_instance = json.loads(request.POST["form"])

        # create a formset from inlineformset_factory with corresponding model and form
        ComplicationsFormset = inlineformset_factory(
            Operation,
            model,
            form=form,
            max_num=1,
            extra=1,
            exclude=exclusion,
        )
        formset = ComplicationsFormset(request.POST, instance=operation)
        print(formset)
        if formset.is_valid():
            formset.save()

        return HttpResponse("ok")


class LoadCourrierConsultation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        consultation = Consultation.objects.get(id=self.request.POST["consultation"])
        return HttpResponse(consultation.courrier_consultation)


class SelectCourrierTypeConsultation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        def is_valid_uuid(val):
            try:
                uuid.UUID(str(val))
                return True
            except ValueError:
                return False

        consultation = Consultation.objects.get(id=self.request.POST["consultation"])

        corps = ""
        # check if id is valid (UUID)
        if is_valid_uuid(self.request.POST["courrier_type_id"]):
            courrier_type_id = ConsultationType.objects.get(
                id=self.request.POST["courrier_type_id"]
            )

            ######generate corps_document with variables
            context = {
                "patient": consultation.zkf_patient,
                "consultation": consultation,
                "corps": consultation.courrier_consultation,
            }
            t = Template(courrier_type_id.corps)
            c = Context(context)
            corps = t.render(c)

            consultation.courrier_consultation = corps
            consultation.save()

        return HttpResponse(corps)


class OrdonnancesConsultation_tab(LoginRequiredMixin, DetailView):
    template_name = "document/ordonnance/template_add_ordonnance.html"
    model = Consultation
    context_object_name = "consultation"

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(**kwargs)
        self.object = self.get_object()
        context["ordonnances"] = OrdonnanceTemplate.objects.all()

        redacteurs = User.objects.filter(
            profile__specialite_profile__spe__in=self.request.user.profile.specialite_profile.all().values(
                "spe"
            )
        ).filter(profile__deleted=False)
        redacteurs = redacteurs.distinct()  # make the querylist with unique values
        context["redacteurs"] = redacteurs

        # select redacteur by default depending of consultation redacteur
        context["redacteur_default"] = self.object.redacteur

        path = pickle_file_path(
            model="consultation",
            option="ordonnances_consultation",
            user=self.request.user,
            filename="ordonnances_consultation_clf.pkl",
        )

        try:
            pickled_clf, pickled_multilabel_binarizer = pickle.load(open(path, "rb"))
            q = self.object.motif_consultation
            # q_pred = pickled_clf.predict([q])
            proba = pickled_clf.predict_proba([q])
            # print ("proba", proba)
            t = 0.2  # threshold value
            y_pred_threshold = (proba >= t).astype(int)
            # print ("new", y_pred_new)
            # print (pickled_multilabel_binarizer.classes_)
            prediction = pickled_multilabel_binarizer.inverse_transform(
                y_pred_threshold
            )
            predictions = [
                OrdonnanceTemplate.objects.get(id=str(x)) for x in prediction[0]
            ]
            context["ordonnances_train"] = predictions
        except Exception as e:
            print(e)
            pass

        return context


class CodageConsultationTab(LoginRequiredMixin, DetailView):
    template_name = "consultation/tabs/codage.html"
    model = Consultation
    context_object_name = "consultation"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        consultation = self.get_object()
        # codes = CodageConsultation.objects.filter(
        #     zkf_consultation=consultation.id, zkf_patient=consultation.zkf_patient.id
        # )
        # context['codes'] = codes
        context["form"] = AddCodeConsultationForm
        context["form_reglement"] = ModifyModeReglementConsultationForm
        return context


class DisplayCodesConsultation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        codes = CodageConsultation.objects.filter(
            zkf_consultation=request.POST["consultation"], deleted=0
        )
        html = ""
        if codes:
            for c in codes:
                total = c.tarif_code_ngap
                if c.depassement_honoraire:
                    total = total + c.depassement_honoraire

                html += (
                    "<tr><td>"
                    + str(c.code_ngap)
                    + "</td>"
                    + "<td>"
                    + str(c.tarif_code_ngap)
                    + "</td>"
                    + "<td>"
                    + str(c.depassement_honoraire)
                    + "</td>"
                )
                html += "<td>" + str(total) + "</td>"
                html += "<td><button class = 'btn btn-outline-warning' onclick = 'deleteCode("
                html += '"' + str(c.id) + '"'
                html += ")'>-</td></tr>"
            return HttpResponse(html)
        else:
            return HttpResponse("Pas de cotations")


class AddCodeConsultation(LoginRequiredMixin, View):
    form_class = AddCodeConsultationForm

    def post(self, request, pk, *args, **kwargs):
        instance = self.form_class(request.POST)
        consultation = Consultation.objects.get(id=pk)
        code_lettre = Ngap.objects.get(id=request.POST["code_ngap"])

        if instance.is_valid():
            code = instance.save(commit=False)

            code.zkf_patient = consultation.zkf_patient
            code.zkf_consultation = consultation
            code.code_ngap = code_lettre.code
            if not code.depassement_honoraire:
                code.depassement_honoraire = 0

            code.save()
            """Update total consultation"""
            total = 0
            for code in consultation.codageconsultation_set.all().filter(deleted=0):
                total_acte = code.tarif_code_ngap + code.depassement_honoraire
                total = total + total_acte
            consultation.total_prix_consultation = total
            consultation.save()

            return HttpResponse(json.dumps(str(consultation.total_prix_consultation)))

        else:
            return HttpResponse(json.dumps("Erreure lors de l'ajout du code !"))


class DeleteCodeConsultation(LoginRequiredMixin, AuditableMixin, View):
    def post(self, request, *args, **kwargs):
        zkp = request.POST["id"]
        code = CodageConsultation.objects.get(id=zkp)
        code.deleted = 1
        code.save()
        return HttpResponse("Code supprimé.")


class ModeReglementConsultation(LoginRequiredMixin, AuditableMixin, View):
    form_class = ModifyModeReglementConsultationForm

    def post(self, request, pk, *args, **kwargs):
        consultation = Consultation.objects.get(id=pk)
        obj = self.form_class(request.POST, instance=consultation)
        if obj.is_valid():
            consult = obj.save(commit=False)
            consult.save()
            # consultation.mode_reglement =
            return HttpResponse(json.dumps(consult.mode_reglement))


class GetCodeInformation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        obj = Ngap.objects.get(id=request.POST["code"])
        data = {
            "code": obj.code,
            "description": obj.description,
            "tarif": str(obj.tarif),
        }
        return HttpResponse(json.dumps(data))


class CreateConsultation(LoginRequiredMixin, AuditableMixin, CreateView):
    template_name = "consultation/create.html"
    form_class = CreateConsultationForm
    model = Patient
    context_object_name = "consultation"
    # success_message = ('Consultation pour %(nom)s added')
    success_message = "Consultation created"

    def get_success_url(self):
        return reverse("consultation:detail-consultation", args=(self.object.id,))

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({"user": self.request.user})
        return kwargs

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(*args, **kwargs)
        self.object = self.get_object()
        context["patient"] = self.object

        return context

    def form_valid(
        self,
        form,
    ):
        patient = self.get_object()
        form.instance.zkf_patient = patient

        # update patient date etat
        update_etat_patient(patient, form.instance.consultation_date)

        # update praticien referent
        patient.praticien_referent.add(form.instance.redacteur)

        # check if first consultation
        check_premiere_consultation(patient, form.instance)

        consultation = form.save(commit=False)
        consultation.save()

        return super(CreateConsultation, self).form_valid(form)


class EditConsultation(
    LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, UpdateView
):
    template_name = "consultation/edit.html"
    model = Consultation
    context_object_name = "consultation"
    form_class = EditConsultationForm
    success_message = "Consultation updated"

    def get_success_url(self):
        return reverse("consultation:detail-consultation", args=[self.kwargs["pk"]])

    # Sending user object to the form, to verify which fields to display/remove (depending on group)
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({"user": self.request.user})
        kwargs.update({"patient": self.object.zkf_patient})
        return kwargs

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(*args, **kwargs)
        self.object = self.get_object()
        context["patient"] = self.object.zkf_patient

        # test => en dev +++ https://stackoverflow.com/questions/2233883/get-all-related-django-model-objects

        """"""
        links = [
            f
            for f in Consultation._meta.get_fields()
            if (f.one_to_many or f.one_to_one) and f.auto_created and not f.concrete
        ]
        zkf_patient = False

        for link in links:
            # get all models with a zkf_patient field !
            # print (link, hasattr(link, 'zkf_patient'))
            if hasattr(link, "zkf_patient"):
                # print (link)
                object = getattr("zkf_patient", link.name, False)
                # print (link, object)
                # change patient for all models if necessary,
                if zkf_patient:
                    setattr(object, "zkf_patient", zkf_patient)

        """"""

        if hasattr(self.object, "antecedentschirurgievasculaire") is True:
            context["antecedents"] = render_to_string(
                "chirurgie_vasculaire/detail/antecedents_patient.html",
                {"context_variable": self.object},
            )

        return context


"""Change patient associated to the consultation !!!"""


class ModifyPatientConsultation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        return


"""Schedule"""


class CheckUpcomingConsultation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        patient = get_object_or_404(Patient, id=self.request.POST["id"])
        # check if there is an upcoming consultation for the patient
        now = datetime.datetime.now()
        consultation = Consultation.objects.filter(
            zkf_patient=patient,
            deleted=False,
            etat_consultation="a_venir",
            consultation_date__gt=now,
        )
        response = {}
        if consultation.exists() and consultation.count() == 1:
            response["status"] = "Single"
            response["consultation"] = str(consultation[0].id)
            response["date_consultation"] = str(
                consultation[0].consultation_date.strftime("%d/%m/%Y")
            )
        elif consultation.exists() and consultation.count() > 1:
            response["status"] = "Multiple"
        else:
            response["status"] = False

        print(response)

        return HttpResponse(json.dumps(response))


"""update etat consultation"""


class UpdateStatutConsultation(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        id = self.request.POST["id"]
        option = self.request.POST["option"]

        consultation = get_object_or_404(Consultation, id=id)

        now = datetime.now().time()

        if option == "arrive":
            consultation.etat_consultation = "arrive"
            consultation.consultation_arrive = now

        elif option == "debutee":
            consultation.consultation_debut_reel = now
            consultation.etat_consultation = "debutee"

        elif option == "finie":
            consultation.consultation_fin_reel = now
            consultation.etat_consultation = "finie"

        consultation.save()

        return HttpResponse("consultation mise à jour")


class EditCourrier(LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, UpdateView):
    template_name = "consultation/edit/edit_courrier.html"
    model = Consultation
    context_object_name = "consultation"
    form_class = EditCourrierForm
    success_message = "Consultation updated"

    def get_success_url(self):
        return reverse("consultation:detail-consultation", args=[self.kwargs["pk"]])

    # Sending user object to the form, to verify which fields to display/remove (depending on group)
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({"user": self.request.user})
        return kwargs

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(*args, **kwargs)
        context["courrier_type"] = ConsultationType.objects.filter(
            zkf_user=self.request.user
        )
        if hasattr(self.get_object(), "antecedentschirurgievasculaire"):
            context["atcd"] = True

        # get operations greater ou equal than the consultation date !
        context["upcoming_operation"] = Operation.objects.filter(
            zkf_patient=self.object.zkf_patient,
            operation_date__gte=self.object.consultation_date,
        )
        # print (context)

        return context

    def form_valid(self, form, *args, **kwargs):
        """If the form is valid, save the associated model."""
        self.object = form.save()

        # extra = {
        #     "patient": str(self.object.zkf_patient),
        #     "apercu": str(self.object.courrier_consultation),
        #     "format": "text",
        #     "model": "consultation",
        #     "id": str(self.object.id),
        # }

        # send notification to USER when consultation en attente de validation
        # if self.object.statut_courrier_consultation == "attente_validation":
        #     notify.send(
        #         self.request.user,
        #         recipient=self.object.redacteur,
        #         actor=self.request.user,
        #         verb="Courrier de consultation en attente de validation",
        #         nf_type="notification_top",
        #         obj_text=self.object.motif_consultation
        #         + " "
        #         + self.object.consultation_date.strftime("%d/%m/%Y"),
        #         obj_url=str(
        #             reverse("consultation:edit-courrier", args=[self.object.id])
        #         ),
        #         # obj_patient = str(self.object.zkf_patient.full_name()), ->field non created !!!!
        #         extra=extra,
        #     )

        # send notification to SECRETAIRE when courrier valide
        # elif self.object.statut_courrier_consultation == "valide":
        #     #get secretaire...->personne liée autre que soit !
        #     users_praticiens = self.request.user.accounts_profile_praticiens_lies.all()
        #     #filter user by praticiens_lies and group => validate for secretaires !
        #     users = User.objects.all().filter( profile__in = users_praticiens, profile__role__in=['SEC'])
        #     # check if users have the site authorization..., exclude current user
        #     users = users.exclude(username = self.request.user)
        #     # convert users as a list
        #     users = list(users)
        #
        #     notify.send(self.request.user,
        #                 recipient_list=users,
        #                 actor=self.request.user,
        #                 verb='Courrier de consultation validé',
        #                 nf_type='notification_top',
        #                 obj_text = self.object.motif_consultation + ' ' + self.object.consultation_date.strftime("%d/%m/%Y"),
        #                 obj_url = str(reverse("consultation:edit-courrier", args=[self.object.id])),
        #                 obj_patient = str(self.object.zkf_patient.full_name()),
        #                 )

        return super().form_valid(form)


class SetCourrierEnvoye(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        consultation = Consultation.objects.get(id=request.POST["consultation_id"])
        consultation.statut_courrier_consultation = "envoye"
        consultation.save()
        return HttpResponse("Courrier marqué comme envoyé !")


"""
ATCD
"""


class GetAntecedents(LoginRequiredMixin, SuccessMessageMixin, View):
    def post(self, request, *args, **kwargs):
        consultation = Consultation.objects.get(id=request.POST["consultation"])
        if hasattr(consultation, "antecedentschirurgievasculaire"):
            html_atcd = render_to_string(
                "chirurgie_vasculaire/detail/antecedents_patient.html",
                {"context_variable": consultation},
            )
        else:
            html_atcd = ""
        return HttpResponse(html_atcd)


class ImportAtcdConsultation(
    LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, View
):
    def post(self, request, pk, *args, **kwargs):
        consultation = Consultation.objects.get(id=pk)
        patient = consultation.zkf_patient

        specialite = self.request.user.profile.specialite_active.spe
        specialite = "_".join(specialite.lower().split(" "))

        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        # 1/ check if atcd already imported.
        if hasattr(consultation, str(model_specialite).lower()):
            return HttpResponse(
                "ATCD déjà importés...Veuillez compléter manuellement les ATCD !"
            )

        # 2/ check if atcd specific for specialite created in patient
        if hasattr(patient, str(model_specialite.lower())):
            # 3/ import atcd from dedicated table !
            obj = getattr(patient, str(model_specialite.lower()), None)
            obj.zkf_patient = None  # IMPORTANT !!!: Set zkf_patient to None to  be able to duplicate the model instance (otherwise UNIQUE constraint), but don't save it...
            obj.pk = None  # duplicate object
            obj.zkf_consultation = consultation  # link it to consultation
            obj.save()

            return HttpResponse("ATCD importés !")

        else:
            return HttpResponse(
                "Il n'y a pas de table spécifique crée pour: " + specialite
            )


"""PDF"""
# Generate a PDF of the courrier de consultation


class PdfConsultation(LoginRequiredMixin, AuditableMixin, DetailView):
    template_name = ""
    model = Consultation

    def get(self, request, option=None, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        context = {
            "consultation": self.object,
            "user": request.user,
            "patient": self.object.zkf_patient,
        }
        template = Consultation.consultation_template(self.object)

        if option == "print":
            response = HttpResponse(content_type="application/pdf")
            response["Content-Transfer-Encoding"] = "binary"

            # generate a string (function is imported from impression.views)
            html_string = create_html_string(
                context=context, path=response, template=template
            )

            result = BytesIO()
            pdf = pisa.pisaDocument(BytesIO(html_string.encode("utf-8")), dest=result)

            if not pdf.err:
                return HttpResponse(result.getvalue(), content_type="application/pdf")
            else:
                return HttpResponse("Errors")

        elif option == "print-later":
            date = (
                str(self.object.consultation_date.day)
                + "/"
                + str(self.object.consultation_date.month)
                + "/"
                + str(self.object.consultation_date.year)
            )
            data = {
                "string_of_html": create_html_string(
                    context=context, template=template
                ),
                "zkf_user": request.user,
                "zkf_patient": self.object.zkf_patient,
                "titre_document": "Courrier de consultation " + date,
                "url_document": reverse(
                    "consultation:detail-consultation", args=(self.object.id,)
                ),
                "zkf_id": self.object.id,  # save zkp of consultation
                "type_document": "courrier_consultation",
            }
            # check if the document has already be added in the waiting list
            obj, created = GestionnaireImpression.objects.update_or_create(
                zkf_id=self.object.id, defaults=data
            )
            # print_manager_list(request)

            return HttpResponse("ajouté dans le manager d'impression")


class PdfConvocationConsultation(LoginRequiredMixin, AuditableMixin, DetailView):
    template_name = Consultation.convocation_consultation_template()
    model = Consultation

    def get(self, request, option=None, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        context = {"consultation": self.object, "user": request.user}

        if option == "print":
            response = HttpResponse(content_type="application/pdf")
            response["Content-Transfer-Encoding"] = "binary"

            # generate a string (function is imported from impression.views)
            html_string = create_html_string(
                context=context, path=response, template=self.template_name
            )

            result = BytesIO()
            pdf = pisa.pisaDocument(BytesIO(html_string.encode("utf-8")), dest=result)

            if not pdf.err:
                return HttpResponse(result.getvalue(), content_type="application/pdf")
            else:
                return HttpResponse("Errors")

        elif option == "print-later":
            # print("ok")
            date = (
                str(self.object.consultation_date.day)
                + "/"
                + str(self.object.consultation_date.month)
                + "/"
                + str(self.object.consultation_date.year)
            )
            data = {
                "string_of_html": create_html_string(
                    context=context, template=self.template_name
                ),
                "zkf_user": request.user,
                "zkf_patient": self.object.zkf_patient,
                "titre_document": "Convocation consultation " + date,
                "url_document": reverse(
                    "consultation:detail-consultation", args=(self.object.id,)
                ),
                "zkf_id": self.object.id,  # save zkp of consultation
                "type_document": "convocation_consultation",
            }
            # check if the document has already be added in the waiting list
            obj, created = GestionnaireImpression.objects.update_or_create(
                zkf_id=self.object.id, defaults=data
            )
            print_manager_list(request)

            return HttpResponse("ajouté dans le manager d'impression")


"""Documents"""


# return list of documents in a consultation !
class DisplayDocuments(AuditableMixin, View):
    def post(self, request, *args, **kwargs):
        documents = Document.objects.filter(
            zkf_consultation=request.POST["consultation"]
        )
        html = ""

        if documents:
            for d in documents:
                html += (
                    "<a href='"
                    + str(d.document_file.url)
                    + "' target = '_blank' >"
                    + str(d.description_document)
                    + "</a><br>"
                )

            return HttpResponse(html)
        else:
            return HttpResponse("Pas de documents ajoutés")


"""Consultation type"""


class DetailConsultationType(LoginRequiredMixin, DetailView):
    template_name = "consultation/consultation_type/detail.html"
    model = ConsultationType
    context_object_name = "consultation_type"


class CreateConsultationType(
    LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, CreateView
):
    form_class = CreateConsultationTypeForm
    success_message = "Consultation created"
    template_name = "consultation/consultation_type/edit.html"
    model = ConsultationType
    context_object_name = "consultation_type"

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data()
        patient_fields = [
            x.name
            for x in Patient._meta.get_fields()
            if (
                not x.many_to_many
                and not x.one_to_many
                and not x.one_to_one
                and not x.many_to_one
            )  # not n.one_to_many or n.one_to_one) ]
        ]
        context["patient_fields"] = patient_fields
        return context

    def get_success_url(self):
        return reverse("consultation:detail-consultation-type", args=[self.object.id])

    def form_valid(
        self,
        form,
    ):
        user = self.request.user
        form.instance.zkf_user = user

        return super(CreateConsultationType, self).form_valid(form)


class EditConsultationType(
    LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, UpdateView
):
    form_class = CreateConsultationTypeForm
    success_message = "Consultation Updated"
    template_name = "consultation/consultation_type/edit.html"
    model = ConsultationType
    context_object_name = "consultation_type"

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data()
        patient_fields = [
            x.name
            for x in Patient._meta.get_fields()
            if (
                not x.many_to_many
                and not x.one_to_many
                and not x.one_to_one
                and not x.many_to_one
            )  # not n.one_to_many or n.one_to_one) ]
        ]
        context["patient_fields"] = patient_fields

        return context

    def get_success_url(self):
        return reverse(
            "consultation:detail-consultation-type", args=[self.kwargs["pk"]]
        )
