# Consultation Model

from django.db import models
from django.urls import reverse

from patient.models import Patient
from django.contrib.auth.models import User
from audit.models import Auditable  # to integrate modifications data

import uuid
import datetime

# tags
from sort.tag.models import TagEveryWhere
from sort.tag.managers import TaggableManager


class Consultation(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_patient = models.ForeignKey(
        "patient.Patient",
        on_delete=models.PROTECT,
        related_name="consultations",
    )
    # zkf_operation = models.ForeignKey(
    # 									'operation.Operation',
    # 									on_delete = models.PROTECT,
    # 									blank = True,
    # 									null = True,
    # 									)
    # where the consultation takes place !
    zkf_site = models.ForeignKey("accounts.Site", on_delete=models.PROTECT)
    zkf_lieu = models.ForeignKey(
        "accounts.Lieu", on_delete=models.PROTECT, null=True, blank=True
    )

    consultation_date = models.DateField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )

    """Tags"""
    tags = TaggableManager(through=TagEveryWhere, blank=True)

    consultation_arrive = models.TimeField(blank=True, null=True)

    consultation_debut_prevu = models.TimeField(blank=True, null=True)
    consultation_fin_prevu = models.TimeField(blank=True, null=True)

    consultation_debut_reel = models.TimeField(blank=True, null=True)
    consultation_fin_reel = models.TimeField(blank=True, null=True)

    premiere_consultation = models.BooleanField(default=False)

    URGENT = "urgent"
    PROGRAMME = "programme"

    CHOICES_MODE_CONSULTATION = (
        (PROGRAMME, "Programmée"),
        (URGENT, "Urgence"),
    )
    mode_consultation = models.CharField(
        max_length=256, blank=True, null=True, choices=CHOICES_MODE_CONSULTATION
    )

    motif_consultation = models.TextField(
        blank=True,
        null=True,
    )
    # Ajouter un commentaire pour la consultation, affiché en Rouge en haut (pense bête...)
    reminder_consultation = models.CharField(max_length=256, blank=True, null=True)
    # linked operations with a consultation -> can be multiple operations to follow with appropriate templates !
    operation_liee = models.ManyToManyField("operation.operation", blank=True)

    observation_consultation = models.TextField(
        blank=True,
        null=True,
    )

    ENVOYE = "envoye"
    VALIDE = "valide"
    ATTENTE_VALIDATION = "attente_validation"
    NON_FAIT = "non_fait"
    NON_STATUE = "non_statue"
    TRANSCRIT = "transcrit"

    CHOICES_ETAT_COURRIER_CONSULTATION = (
        (ENVOYE, "Envoyé"),
        (VALIDE, "Validé"),
        (ATTENTE_VALIDATION, "En attente de validation"),
        (TRANSCRIT, "Transcrit (audio)"),
        (NON_FAIT, "Non fait"),
        (NON_STATUE, "Non statué"),
    )
    statut_courrier_consultation = models.CharField(
        max_length=50,
        blank=False,
        null=False,
        choices=CHOICES_ETAT_COURRIER_CONSULTATION,
        default=NON_FAIT,
    )
    courrier_consultation = models.TextField(
        blank=True,
        null=False,
    )
    courrier_consultation_bas_page = models.TextField(
        blank=True,
        null=False,
    )
    # consultation type
    consultations_type = models.ManyToManyField(
        "consultation.ConsultationType",
        db_table="consultations_type_consultation",  # Nom court pour PostgreSQL
        blank=True,
        related_name="%(app_label)s_%(class)s_consultations_type_liees",
    )

    # on considère que le redacteur est le medecin qui verra le patient en consultation<=>redacteur !!!
    # redacteur = signataire du courrier !
    zkf_redacteur = models.ForeignKey(
        User, on_delete=models.PROTECT, blank=True, null=True
    )

    A_VENIR = "a_venir"
    ARRIVE = "arrive"
    DEBUTE = "debutee"
    FINIE = "finie"
    NON_VENU = "non_venu"
    ANNULE = "annule"
    EXCUSE = "excuse"

    CHOICES_ETAT_CONSULTATION = (
        (A_VENIR, "A venir"),
        (ARRIVE, "Arrive"),
        (DEBUTE, "Debutee"),
        (FINIE, "Finie"),
        (NON_VENU, "Non venu"),
        (ANNULE, "annulée"),
        (EXCUSE, "Excusée"),
    )
    etat_consultation = models.CharField(
        max_length=50,
        blank=False,
        null=False,
        choices=CHOICES_ETAT_CONSULTATION,
        default=A_VENIR,
    )
    zkf_statut_consultation = models.ForeignKey(
        "consultation.StatutConsultation",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )

    # Facturation/Comptabilité

    CHEQUE = "cheque"
    ESPECES = "especes"
    GRATUIT = "gratuit"
    CB = "cb"
    TIERS_PAYANT = "tiers_payant"
    CMU = "CMU"

    CHOICES_REGLEMENT_CONSULTATION = (
        (CHEQUE, "Cheque"),
        (ESPECES, "Espèces"),
        (GRATUIT, "Gratuit"),
        (CB, "Carte banquaire"),
        (TIERS_PAYANT, "Tiers Payant"),
        (CMU, "cmu"),
    )

    depassement_honoraire = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )
    mode_reglement = models.CharField(
        max_length=256, choices=CHOICES_REGLEMENT_CONSULTATION, null=True, blank=True
    )
    total_prix_consultation = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )

    def __str__(self):
        if self.consultation_date:
            date = datetime.datetime.strftime(self.consultation_date, "%d/%m/%Y")
        else:
            date = "Pas de date"
        return str(date)

    def description_instance(self):
        date = datetime.datetime.strftime(self.consultation_date, "%d/%m/%Y")
        return "consultation du " + str(date)

    def link(self):
        return reverse(
            "consultation:detail-consultation",
            args=[self.id],
        )

    def consultation_template(self):
        return "consultation/pdf/courrier.html"

    def convocation_consultation_template():
        return "consultation/pdf/convocation.html"

    def template(self, type_document=None):
        if type_document == "convocation_consultation":
            template = str("consultation/pdf/convocation.html")
        elif type_document == "courrier_consultation":
            template = str("consultation/pdf/courrier.html")
        else:
            template = None
        return template

    def ponctualite(self):
        ponctualite = ""
        if self.consultation_debut_reel and self.consultation_debut_prevu:
            consultation_debut_reel = datetime.timedelta(
                hours=self.consultation_debut_reel.hour,
                minutes=self.consultation_debut_reel.minute,
                seconds=self.consultation_debut_reel.second,
            )
            consultation_debut_prevu = datetime.timedelta(
                hours=self.consultation_debut_prevu.hour,
                minutes=self.consultation_debut_prevu.minute,
                seconds=self.consultation_debut_prevu.second,
            )

            if consultation_debut_reel > consultation_debut_prevu:
                ponctualite = consultation_debut_reel - consultation_debut_prevu
            else:
                # assert end > start
                ponctualite = consultation_debut_prevu - consultation_debut_reel

        return ponctualite

    def last_consultation(self):
        consultations = Consultation.objects.filter(
            zkf_patient=self.zkf_patient, consultation_date__lt=self.consultation_date
        ).order_by("consultation_date")
        if consultations:
            last_consultation = consultations.last().consultation_date
        else:
            last_consultation = self.consultation_date
        return last_consultation


class ConsultationType(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    zkf_user = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    users = models.ManyToManyField(User, related_name="consultation_types", blank=True)
    sites = models.ManyToManyField(
        "accounts.Site", related_name="consultation_types", blank=True
    )
    lieus = models.ManyToManyField(
        "accounts.Lieu", related_name="consultation_types", blank=True
    )
    services = models.ManyToManyField(
        "accounts.Service", related_name="consultation_types", blank=True
    )

    titre = models.CharField(max_length=256)
    corps = models.TextField(blank=True, null=True)
    bas_de_page = models.TextField(blank=True, null=True)

    def __str__(self):
        return str(self.titre)


class CodageConsultation(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_consultation = models.ForeignKey(Consultation, on_delete=models.PROTECT)
    zkf_patient = models.ForeignKey(Patient, on_delete=models.PROTECT)
    code_ngap = models.CharField(
        max_length=40
        # 40 charactères is nécessaire car quand submit post, send UUID du code ngap avant de convertir en lettre...
    )
    tarif_code_ngap = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )
    depassement_honoraire = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )


# table for statut/etat of the consultation
class StatutConsultation(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    display = models.CharField(max_length=100)
    display_french = models.CharField(max_length=100)
