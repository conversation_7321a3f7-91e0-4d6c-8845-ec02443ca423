from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings


class Auditable(models.Model):
    created_on = models.DateTimeField(
        auto_now_add=True,
    )

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="%(app_label)s_%(class)s_created_by",
        # default = 0)
        null=True,
        blank=True,
    )

    modified_on = models.DateTimeField(
        auto_now=True,
        null=True,
    )
    modified_by = models.ForeignKey(
        get_user_model(),
        related_name="%(app_label)s_%(class)s_modified_by",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )

    deleted = models.BooleanField(
        default=False,
    )
    deleted_on = models.DateTimeField(
        auto_now=False,
        auto_now_add=False,
        null=True,
        blank=True,
    )
    deleted_by = models.ForeignKey(
        get_user_model(),
        related_name="%(app_label)s_%(class)s_deleted_by",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
    )

    class Meta:
        abstract = True
