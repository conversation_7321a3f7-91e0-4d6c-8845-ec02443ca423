#!/usr/bin/env python
"""
Simple test of pandas .dt accessor fixes
"""
import pandas as pd
import numpy as np


def to_datetime_naive(dt_input):
    """Convert to pandas datetime and ensure it's timezone-naive"""
    try:
        dt = pd.to_datetime(dt_input)

        # If it's a pandas series with timezone
        if isinstance(dt, pd.Series):
            if hasattr(dt.dtype, "tz") and dt.dtype.tz is not None:
                # Check if it's really a datetime series before using .dt
                if pd.api.types.is_datetime64_any_dtype(dt):
                    return dt.dt.tz_localize(None)
                else:
                    return dt.apply(
                        lambda x: (
                            x.replace(tzinfo=None)
                            if hasattr(x, "tz") and x.tz is not None
                            else x
                        )
                    )
            else:
                return dt
        # If it's a single timestamp with timezone
        elif hasattr(dt, "tz") and dt.tz is not None:
            return dt.replace(tzinfo=None)
        # If it's a pandas series without timezone, nothing to do
        return dt
    except Exception as e:
        # In case of error, return original input
        print("Warning: to_datetime_naive failed for {}: {}".format(dt_input, e))
        return dt_input


def test_dt_accessor_conditions():
    """Test different conditions for .dt accessor usage"""

    print("Testing .dt accessor conditions...")

    # Test 1: Normal datetime series
    print("\n1. Normal datetime series:")
    dt_series = pd.to_datetime(["2024-01-15 10:30:00", "2024-01-15 11:00:00"])
    print("   Type: {}".format(dt_series.dtype))
    print("   Has .dt: {}".format(hasattr(dt_series, "dt")))
    print(
        "   Is datetime64: {}".format(pd.api.types.is_datetime64_any_dtype(dt_series))
    )

    if hasattr(dt_series, "dt") and pd.api.types.is_datetime64_any_dtype(dt_series):
        try:
            result = dt_series.dt.date
            print("   SUCCESS: .dt.date worked: {}".format(result))
        except Exception as e:
            print("   FAIL: .dt.date failed: {}".format(e))

    # Test 2: String series that looks like datetime
    print("\n2. String series:")
    str_series = pd.Series(["10:30:00", "11:00:00"])
    print("   Type: {}".format(str_series.dtype))
    print("   Has .dt: {}".format(hasattr(str_series, "dt")))
    print(
        "   Is datetime64: {}".format(pd.api.types.is_datetime64_any_dtype(str_series))
    )

    if hasattr(str_series, "dt") and pd.api.types.is_datetime64_any_dtype(str_series):
        try:
            result = str_series.dt.date
            print("   This should not execute")
        except Exception as e:
            print("   Expected failure: {}".format(e))
    else:
        print("   SUCCESS: Condition correctly prevented .dt accessor usage")

    # Test 3: Convert string series to datetime
    print("\n3. Converted string series:")
    converted_series = pd.to_datetime("2024-01-15 " + str_series)
    print("   Type: {}".format(converted_series.dtype))
    print("   Has .dt: {}".format(hasattr(converted_series, "dt")))
    print(
        "   Is datetime64: {}".format(
            pd.api.types.is_datetime64_any_dtype(converted_series)
        )
    )

    if hasattr(converted_series, "dt") and pd.api.types.is_datetime64_any_dtype(
        converted_series
    ):
        try:
            result = converted_series.dt.time
            print("   SUCCESS: .dt.time worked: {}".format(result))
        except Exception as e:
            print("   FAIL: .dt.time failed: {}".format(e))

    print("\nAll tests completed!")


if __name__ == "__main__":
    test_dt_accessor_conditions()
