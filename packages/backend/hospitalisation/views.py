from django.shortcuts import get_object_or_404, redirect, render
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.db.models import Q  # making complex queries

from django.views.generic import (
    <PERSON><PERSON><PERSON>iew,
    DetailView,
    TemplateView,
    UpdateView,
    View,
)
from django.urls import reverse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from .forms import (
    EditHospitalisationForm,
    EditEntree,
    EditCrhForm,
    CreateConsigneSurveillanceForm,
    AnnulationHospitalisationForm,
    ComplicationsHospitalisationForm,
    HospitOperationAdmin,
    CreateHospitOperation,
)


from django.template import Context, Template

import datetime

from .models import Hospitalisation, Suivi, ConsigneSortie
from patient.models import Patient
from consultation.models import Consultation
from operation.models import Operation
from document.models.photo import Photo, Portfolio
from audit.views import AuditableMixin
from accounts.models import Intervenant
from audit.models import Auditable

from django.apps import apps
from accounts.scripts.filter_user import get_operateur

# get current spe
from babylone.templatetags.routeur_antecedents import get_activ_specialite
from .scripts.hospit_tabs import hospitalisation_tab

# Impression
from impression.models import GestionnaireImpression

# PDF
# from weasyprint import HTML #convert pdf
# import tempfile, os #create temporary files
from xhtml2pdf import pisa
from io import BytesIO  # A stream implementation using an in-memory bytes buffer

# It inherits BufferIOBase

# Merge PDF
from PyPDF2 import PdfFileMerger

# create temporary folder
import tempfile  # create temporary files

#######
from impression.views import create_html_string

# update print list
from impression.views import print_manager_list

# script to generate a crh
from .scripts.generate_crh import generate_crh

# ordonnances
from document.models.ordonnance import OrdonnanceTemplate

# scripts
from patient.patient_scripts.etat import update_etat_patient

# notifications
# from notify.signals import notify
# Disabled by Andy

# ML
from babylone.core_scripts.machine_learning.multi_label_text_classification import (
    pickle_file_path,
)
import pickle
from django.forms.models import model_to_dict


class Detail(LoginRequiredMixin, DetailView):
    template_name = "hospitalisation/detail.html"
    model = Hospitalisation
    context_object_name = "hospitalisation"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["patient"] = Patient.objects.get(
            id=context["hospitalisation"].zkf_patient.id
        )

        operations = Operation.objects.filter(
            zkf_hospitalisation=context["hospitalisation"].id
        )
        context["operation"] = operations

        url = self.request.META["HTTP_REFERER"]
        if operations:
            context["tab"] = hospitalisation_tab(
                operations[0].operation_date, self.get_object(), url, self.request.GET
            )
            # print (context['tab'] )
        else:
            context["tab"] = hospitalisation_tab(
                context["hospitalisation"].entree_date,
                self.get_object(),
                url,
                self.request.GET,
            )

        # user = self.request.user
        # df = create_df_hospitalisation(user)
        # model = build_and_evaluate(df['motif_hospitalisation'], df['ordonnances'], user=user, outpath = pickle_file_path(model = "hospitalisation", option = "ordonnances_hospitalisation", user = user, filename = "ordonnances_hospitalisation_clf"))

        return context


class PatientsHospitalises(LoginRequiredMixin, TemplateView):
    template_name = "hospitalisation/synthese_hospitalises.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        def service_specialite(self, request):
            hospitalisations = Hospitalisation.objects.filter(
                Q(entree_date__lte=datetime.date.today()),
                Q(sortie_date__isnull=True) | Q(sortie_date__gte=datetime.date.today()),
                Q(deleted=False),
                # get all patients that are in the same specialite of the user
                Q(
                    service_hospitalisation__specialite_service__spe__in=request.user.profile.specialite_profile.all().values(
                        "spe"
                    )
                ),
            ).order_by("service_hospitalisation")

            # Filter hospitalisations by site <=> Display patients/hospitlaisations filtered by site the user has access to !
            hospitalisations = hospitalisations.filter(
                service_hospitalisation__site_service__in=request.user.profile.site.all().values(
                    "id"
                )
            )

            # Group queryset by patient
            # https://stackoverflow.com/questions/28076222/django-how-to-get-a-list-of-queryset-grouped-by-attribute-value
            grouped = dict()
            for obj in hospitalisations:
                grouped.setdefault(obj.service_hospitalisation, []).append(obj)
            hos = grouped
            return hos

        context["hospitalisations"] = service_specialite(self, self.request)

        return context


# display suivis synthese


class DisplaySuiviSynthese(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        hospitalisation = self.request.POST["id"]
        suivis = Suivi.objects.filter(zkf_hospitalisation=hospitalisation).order_by(
            "-created_on"
        )
        html = ""
        if suivis:
            for s in suivis:
                html += (
                    datetime.datetime.strftime(s.created_on, "%d/%m/%Y %H:%M")
                    + s.texte_suivi
                    + "<br>"
                )

        return HttpResponse(html)


# insert suivi in synthese
class InsertSuiviSynthese(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        hospitalisation = Hospitalisation.objects.get(id=self.request.POST["id"])
        text_suivi = self.request.POST["suivi_txt"]
        # add suivi
        Suivi.objects.create(
            zkf_hospitalisation=hospitalisation,
            zkf_patient=hospitalisation.zkf_patient,
            texte_suivi=text_suivi,
        )

        return HttpResponse("suivi inséré")


# insert suivi synthese


class GestionAdministrativeHospitalisation(LoginRequiredMixin, UpdateView):
    template_name = "hospitalisation/gestion_administrative_hospitalisation.html"
    context_object_name = "hospitalisation"
    model = Hospitalisation
    form_class = AnnulationHospitalisationForm
    success_message = "Hospitalisaation  Updated"

    def get_success_url(self):
        return reverse("hospitalisation:detail-hospitalisation", args=(self.object.id,))

    def get_context_data(self, **kwargs):
        # we need to overwrite get_context_data
        # to make sure that our formset is rendered.
        # the difference with CreateView is that
        # on this view we pass instance argument
        # to the formset because we already have
        # the instance created
        data = super().get_context_data(**kwargs)
        if self.request.POST:
            data["operations"] = HospitOperationAdmin(
                self.request.POST, instance=self.object
            )
        else:
            data["operations"] = HospitOperationAdmin(instance=self.object)
        return data

    def form_valid(self, form):
        context = self.get_context_data()
        children = context["operations"]
        self.object = form.save()
        if children.is_valid():
            children.instance = self.object
            children.save()
        return super().form_valid(form)


class SoftDeleteHospitalisation(LoginRequiredMixin, AuditableMixin, View):
    success_message = "Hospitalisation supprimée !"

    def get(self, request, pk, *args, **kwargs):
        hospitalisation = get_object_or_404(Hospitalisation, id=pk)
        # models->soft_delete()
        hospitalisation.soft_delete(request)
        messages.success(request, self.success_message)
        return redirect("patient:detail-patient", hospitalisation.zkf_patient.id)


class AProgrammerHospitalisation(LoginRequiredMixin, AuditableMixin, View):
    success_message = "hospitalisation et operation converties en: 'A programmer' ! "

    def get(self, request, pk, *args, **kwargs):
        hospitalisation = get_object_or_404(Hospitalisation, id=pk)
        # models -> programmation()
        hospitalisation.programmation(request)
        messages.success(request, self.success_message)
        return redirect("hospitalisation:detail-hospitalisation", hospitalisation.id)


class Entree_tab(LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, UpdateView):
    template_name = "hospitalisation/tabs/entree.html"
    model = Hospitalisation
    context_object_name = "hospitalisation"
    form_class = EditEntree
    success_message = "Données sauvegardées"

    # def get(self, request, pk, *args, **kwargs):
    # 	hos = get_object_or_404(Hospitalisation, id = pk)
    # 	form = self.form_class( instance=hos)
    # 	return render(request, self.template_name, {'form': form, 'hos': hos})

    def post(self, request, pk, *args, **kwargs):
        hospitalisation = get_object_or_404(Hospitalisation, id=pk)
        form = self.form_class(request.POST, instance=hospitalisation)

        if form.is_valid():
            hospitalisation = form.save(
                commit=True
            )  # true automatically save the form and the M2M fields. Otherwie, call save() and save_m2m()
            # 			hospitalisation.save()
            # 			form.save_m2m()

            messages.success(request, self.success_message)
            # return redirect('hospitalisation:detail-hospitalisation', hospitalisation.id)
            url = reverse(
                "hospitalisation:detail-hospitalisation",
                args=[
                    hospitalisation.id,
                ],
            )
            # add a value in url for tab redirection !
            return redirect(str(url) + "?hospitalisation_tab=entree-tab")

        else:
            return render(
                request,
                self.template_name,
                {"form": form, "hospitalisation": hospitalisation},
            )
            # return HttpResponse('ok')


class Suivi_tab(LoginRequiredMixin, DetailView):
    template_name = "hospitalisation/tabs/suivi.html"
    model = Hospitalisation
    context_object_name = "hospitalisation"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # load widget for post op complications in hospitalisation
        context["form"] = ComplicationsHospitalisationForm(instance=self.get_object())

        # check if there is a table for complications post op in hospitalisation:
        specialite = get_activ_specialite(self.request, context)
        model_specialite = "ComplicationPostOperatoire" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        # print (hasattr(self.get_object(), str(model_specialite).lower()))

        # display complications de l'hospitalisation !!! (not each operation)
        if hasattr(self.get_object(), str(model_specialite.lower())):
            context["complications"] = model_to_dict(
                getattr(self.get_object(), str(model_specialite.lower()))
            )

        # display complications de chaque operation:
        operations = self.get_object().operation_set.all()
        # create an list of dicts for complications of each operation
        operation_complications = {}
        for o in operations:
            if hasattr(o, str(model_specialite.lower())):
                # print(getattr(o, str(model_specialite.lower())))
                complications = model_to_dict(getattr(o, str(model_specialite.lower())))
                operation_complications[o.titre_operation] = complications
        context["complications_operations"] = operation_complications

        return context


class photosTab(LoginRequiredMixin, DetailView):
    template_name = "document/photo/photos_tab.html"
    model = Hospitalisation
    context_object_name = "hospitalisation"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)

        photos = Photo.objects.filter(
            zkf_hospitalisation=context["hospitalisation"].id,
            deleted=False,
        ).order_by("-created_on")
        context["photos"] = photos

        portfolios = Portfolio.objects.filter(
            zkf_patient=context["hospitalisation"].zkf_patient
        ).prefetch_related("photo_set")
        context["portfolios"] = portfolios

        html = render_to_string("document/photo/photos_tab.html", context)

        return HttpResponse(html)


class Sortie_tab(LoginRequiredMixin, DetailView):
    template_name = "hospitalisation/tabs/sortie.html"
    model = Hospitalisation
    context_object_name = "hospitalisation"


class Ordonnances_tab(LoginRequiredMixin, DetailView):
    template_name = "document/ordonnance/template_add_ordonnance.html"
    model = Hospitalisation
    context_object_name = "hospitalisation"

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(**kwargs)
        self.object = self.get_object()
        context["ordonnances"] = OrdonnanceTemplate.objects.all()
        context["operations"] = Operation.objects.filter(
            zkf_hospitalisation=self.object.id
        )

        # redacteurs = User.objects.filter(profile__specialite_profile__spe__in = self.request.user.profile.specialite_profile.all().values('spe')).filter(profile__deleted = False)
        # redacteurs = redacteurs.filter(profile__user__is_active = True, profile__user__profile__role = 'MED')
        redacteurs = get_operateur(self.request.user)

        # spe_service = s.specialite_service.all().values('spe')
        # praticiens = s.profile_set.all().filter(user__is_active = True, user__profile__role = 'MED', user__profile__specialite_active__spe__in = spe_service )

        redacteurs = redacteurs.distinct()  # make the querylist with unique values
        context["redacteurs"] = redacteurs

        # select redacteur by default depending of consultation redacteur
        context["redacteur_default"] = self.object.redacteur

        # Get trained model for ordonnances in consultation
        path = pickle_file_path(
            model="hospitalisation",
            option="ordonnances_hospitalisation",
            user=self.request.user,
            filename="ordonnances_hospitalisation_clf.pkl",
        )

        try:
            pickled_clf, pickled_multilabel_binarizer = pickle.load(open(path, "rb"))
            q = self.object.motif_hospitalisation + self.object.histoire_maladie
            # q_pred = pickled_clf.predict([q])
            proba = pickled_clf.predict_proba([q])
            # print ("proba", proba)
            t = 0.2  # threshold value
            y_pred_threshold = (proba >= t).astype(int)
            # print ("new", y_pred_new)
            # print (pickled_multilabel_binarizer.classes_)
            prediction = pickled_multilabel_binarizer.inverse_transform(
                y_pred_threshold
            )
            predictions = [
                OrdonnanceTemplate.objects.get(id=str(x)) for x in prediction[0]
            ]
            context["ordonnances_train"] = predictions
        except Exception as e:
            print(e)
            pass

        return context


# update field for date entree et sortie
class UpdateField(LoginRequiredMixin, View):
    def post(self, request, hospitalisation_pk):
        hos = get_object_or_404(Hospitalisation, id=hospitalisation_pk)

        variable = request.POST["variable"]

        if variable == "" or variable == "unknown":
            variable = None

        if variable == "false" or variable == "true":
            variable = variable.capitalize()

        # print (variable)
        setattr(hos, request.POST["field_name"], variable)
        hos.save()

        return HttpResponse(getattr(hos, request.POST["field_name"]))


"""
Hospitalisation et operations complications
"""


# update complications when modifying complications in the suivi tab
class UpdateComplication(LoginRequiredMixin, View):
    def post(self, request, hospitalisation_pk, **kwargs):
        # get hospitalisation all operations
        operations = Operation.objects.filter(zkf_hospitalisation=hospitalisation_pk)
        hospitalisation = get_object_or_404(Hospitalisation, id=hospitalisation_pk)

        # get the specialite of the service where the patient is hospitalized !
        specialite = hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))

        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_complications = "ComplicationPostOperatoire" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        if self.request.POST["complication"] == "false":
            # hospitalisation post_op_complication -> false
            hospitalisation.complication_post_op = False
            hospitalisation.save()

            """
            ######set hospitalisation - complications table to no:
            if hasattr(hospitalisation, model_complications.lower()) == False:
                #if not create one to one table (spe of hospitalisation, get complications table)
                model_name = apps.get_model(specialite, model_complications)

                obj = model_name(zkf_hospitalisation=hospitalisation, complication=False)
                #parse through the class to set all binary values to false !
                for m in model_name._meta.fields:
                    # print (m)
                    # print(m.get_internal_type())
                    if m.get_internal_type() == "BooleanField":
                        setattr(obj, str(m).split('.')[-1], False)

                #set complication to false
                obj.save()

            #if there is a one to one table: update complications to no !
            else:
                obj = getattr(hospitalisation, str(model_complications.lower()))
                obj.complication = False
                #parse through the class to set all binary values to false !
                model_name = type(obj)
                for m in model_name._meta.fields:
                    if str(m.get_internal_type()) == "BooleanField":
                        setattr(obj, str(m).split('.')[-1], False)
                #set complication to false
                obj.save()
            """

            # update complications for each operation
            if operations:
                # loop through all operations linked to hospitalisation
                for o in operations:
                    # 1/Check if there is a one to one table.
                    if hasattr(o, str(model_complications.lower())) is False:
                        # 2/ if not create one to one table (spe of hospitalisation, get complications table)
                        model_name = apps.get_model(specialite, model_complications)

                        obj = model_name(zkf_operation=o, complication=False)
                        # parse through the class to set all binary values to false !
                        for m in model_name._meta.fields:
                            if str(m.get_internal_type()) == "BooleanField":
                                setattr(obj, str(m).split(".")[-1], False)
                        # set complication to false
                        obj.save()

                    # 3/ if there is a one to one table: update complications to no !
                    else:
                        obj = getattr(o, str(model_complications.lower()))
                        obj.complication = False
                        # parse through the class to set all binary values to false !
                        model_name = type(obj)
                        for m in model_name._meta.fields:
                            if str(m.get_internal_type()) == "BooleanField":
                                setattr(obj, str(m).split(".")[-1], False)
                        # set complication to false
                        obj.save()

        elif self.request.POST["complication"] == "true":
            # hospitalisation post_op_complication -> true
            hospitalisation.complication_post_op = True
            hospitalisation.save()

            """
            #update hospitalisation complication table
            if hasattr(hospitalisation, str(model_complications.lower())) is False:
                #if not create one to one table (spe of hospitalisation, get complications table)
                model_name = apps.get_model(specialite, model_complications)

                obj = model_name(zkf_hospitalisation=hospitalisation, complication=True)
                #set complication to false
                obj.save()

            #if there is a one to one table: update complications to no !
            else:
                obj = getattr(hospitalisation, str(model_complications.lower()))
                obj.complication = True
                #set complication to false
                obj.save()

            ####launch window to edit post op complications => jquery script in the template
            pass
            """

        return HttpResponse("ok")


class EditComplication(LoginRequiredMixin, DetailView):
    context_object_name = "hospitalisation"
    template_name = "hospitalisation/complications/edit_complications.html"
    # 	success_message = ('Hospitalisation pour %(nom)s added')
    model = Hospitalisation
    # success_message = ('Complications mises à jour')
    # form_class = EditComplicationHospitalisation

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["operations"] = self.get_object().operation_set.all()
        return context


class CreateHospitalisation(LoginRequiredMixin, AuditableMixin, CreateView):
    template_name = "hospitalisation/edit.html"
    form_class = EditHospitalisationForm
    # success_message = ('Hospitalisation pour %(nom)s added')
    model = Patient
    success_message = "Hospitalisation created "

    def get_context_data(self, **kwargs):
        context = super(CreateHospitalisation, self).get_context_data(**kwargs)
        # context={}
        # context['form'] = self.form_class (self.request.user, self.get_object() )
        context["createhospitoperation"] = CreateHospitOperation()
        context["patient"] = self.get_object()
        return context

    # Sending user object to the form for redacteur list !
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({"user": self.request.user, "patient": self.get_object()})
        return kwargs

    def post(self, request, *args, **kwargs):
        self.object = None
        form_class = self.get_form_class()
        form = self.get_form(form_class)
        createhospitoperation = CreateHospitOperation(self.request.POST)

        if form.is_valid() and createhospitoperation.is_valid():
            return self.form_valid(form, createhospitoperation)
        else:
            return self.form_invalid(form, createhospitoperation)

    def form_valid(self, form, createhospitoperation):
        hospitalisation = form.save(commit=False)

        hospitalisation.zkf_patient = self.get_object()

        # set redacteur by default:
        if self.request.user in form.cleaned_data["medecin_referent"]:
            hospitalisation.redacteur = self.request.user
        if form.cleaned_data["medecin_referent"].count() == 1:
            hospitalisation.redacteur = form.cleaned_data["medecin_referent"][0]

        hospitalisation.save()
        form.save_m2m()

        # saving Operation Instances
        operation = createhospitoperation.save(commit=False)
        if len(operation) > 0:
            for o in createhospitoperation:
                operation = o.save(commit=False)
                operation.zkf_hospitalisation = hospitalisation
                operation.zkf_patient = hospitalisation.zkf_patient
                operation.save()
                # save manytomany fields !
                o.save_m2m()

                if form.cleaned_data["medecin_referent"].count() == 1:
                    operation.redacteur = form.cleaned_data["medecin_referent"][0]
                    operation.save()
                    # add many to many field after saving operation
                    try:
                        operateur = Intervenant.objects.get(
                            zkf_user=form.cleaned_data["medecin_referent"][0].id
                        )
                        operation.operateur_new.add(operateur)
                    except Exception as e:
                        print(e)
                        pass
                # add one hour automatically to the created operation
                if operation.operation_start:
                    operation.operation_end = (
                        operation.operation_start + datetime.timedelta(hours=1)
                    )
                    operation.save()

        # update patient date etat
        if hospitalisation.entree_date:
            update_etat_patient(
                hospitalisation.zkf_patient, hospitalisation.entree_date
            )

        return redirect("hospitalisation:detail-hospitalisation", hospitalisation.id)

    def form_invalid(self, form, createhospitoperation):
        # print (form.errors, createhospitoperation.errors, 'erreur')
        return self.render_to_response(
            self.get_context_data(
                form=form, createhospitoperation=createhospitoperation
            )
        )


class Edit(LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, UpdateView):
    context_object_name = "hospitalisation"
    template_name = "hospitalisation/edit.html"
    # 	success_message = ('Hospitalisation pour %(nom)s added')
    model = Hospitalisation
    success_message = "Hospitalisation modified"
    form_class = EditHospitalisationForm

    def get_success_url(self):
        return reverse(
            "hospitalisation:detail-hospitalisation", args=[self.get_object().id]
        )

    # Sending user and patient object to the form for redacteur list !
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update(
            {"user": self.request.user, "patient": self.get_object().zkf_patient}
        )
        return kwargs

    # 	# #define service

    # def get(self, request, hospitalisation_pk):
    # 	hospitalisation = get_object_or_404(Hospitalisation, id = hospitalisation_pk)
    # 	form = self.form_class(request.user, hospitalisation.zkf_patient, instance=hospitalisation)
    # 	return render(request, self.template_name, {'form': form, 'hospitalisation': hospitalisation})


# 	def post(self, request, pk):
# 		hospitalisation = get_object_or_404(Hospitalisation, id = pk)
# 		form = self.form_class(request.user, hospitalisation.zkf_patient, request.POST, instance=hospitalisation)

# 		if form.is_valid():
# 			hospitalisation = form.save(commit=True) #true automatically save the form and the M2M fields. Otherwie, call save() and save_m2m()
# #			hospitalisation.save()
# #			form.save_m2m()

# 			messages.success(request, self.success_message)
# 			return redirect('hospitalisation:detail-hospitalisation', hospitalisation.id)

# 		else:
# 			return render(request, self.template_name, {'form': form, 'hospitalisation': hospitalisation})

"""
ATCD
"""


class ImportAtcd(LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, View):
    def post(self, request, pk, *args, **kwargs):
        hospitalisation = Hospitalisation.objects.get(id=pk)
        patient = hospitalisation.zkf_patient

        specialite = hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))

        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        # 1/ check if atcd already imported.
        if hasattr(hospitalisation, str(model_specialite).lower()):
            return HttpResponse(
                "ATCD déjà importés...Veuillez compléter manuellement les ATCD !"
            )

        # 2/ check if atcd specific for specialite created in patient
        if hasattr(patient, str(model_specialite.lower())):
            obj = getattr(patient, str(model_specialite.lower()), None)
            obj.zkf_patient = None  # IMPORTANT !!!: Set zkf_patient to None to  be able to duplicate the model instance (otherwise UNIQUE constraint), but don't save it...
            obj.pk = None  # duplicate object
            obj.zkf_hospitalisation = hospitalisation  # link it to hospitalisation
            obj.save()

            return HttpResponse("ATCD importés !")

        else:
            return HttpResponse(
                "Il n'y a pas de table spécifique crée pour: " + specialite
            )


"""
CRH
"""


class EditCrh(LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, UpdateView):
    context_object_name = "hospitalisation"
    template_name = "hospitalisation/crh/edit_crh.html"
    # success_message = ('Hospitalisation pour %(nom)s updated !')
    model = Hospitalisation
    success_message = "CRH  Updated"
    form_class = EditCrhForm

    def get_context_data(self, *args, **kwargs):
        context = super().get_context_data(**kwargs)
        context["consignes_surveillance"] = ConsigneSortie.objects.all()

        portfolios = Portfolio.objects.filter(
            zkf_patient=self.object.zkf_patient
        ).prefetch_related("photo_set")
        context["portfolios"] = portfolios

        context["patient"] = self.get_object().zkf_patient

        return context

    # Sending user object to the form for redacteur list !
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update({"user": self.request.user})
        return kwargs

    def get_success_url(self):
        return reverse(
            "hospitalisation:detail-hospitalisation", args=(self.kwargs["pk"],)
        )

    def form_valid(self, form, *args, **kwargs):
        """If the form is valid, save the associated model."""
        self.object = hospitalisation = form.save()
        patient = self.object.zkf_patient
        #
        # extra = {
        #     "patient": str(self.object.zkf_patient),
        #     "apercu": str(self.object.crh),
        #     "format": "text",
        #     "model": "hospitalisation",
        #     "id": str(self.object.id),
        # }
        #
        # sortie = ""

        # if self.object.sortie_date:
        # sortie = self.object.sortie_date.strftime("%d/%m/%Y")

        # send notification to USER when consultation en attente de validation
        # if self.object.statut_crh == "attente_validation" and self.object.redacteur:
        #     notify.send(
        #         self.request.user,
        #         recipient=self.object.redacteur,
        #         actor=self.request.user,
        #         verb="CRH en attente de validation",
        #         nf_type="notification_top",
        #         obj_text=self.object.motif_hospitalisation + " " + sortie,
        #         obj_url=str(reverse("hospitalisation:edit-crh", args=[self.object.id])),
        #         # obj_patient = str(self.object.zkf_patient.full_name()), ->field non created !!!!
        #         extra=extra,
        #     )

        # Actions when courrier valide
        # elif self.object.statut_crh == "valide" and self.object.redacteur :
        #     #send notification to SECRETAIRE
        #     #get secretaire...->personne liée autre que soit !
        #     users_praticiens = self.object.redacteur.accounts_profile_praticiens_lies.all() #self.request.user.accounts_profile_praticiens_lies.all()
        #     #filter user by praticiens_lies and group => validate for secretaires !
        #     users = User.objects.all().filter( profile__in = users_praticiens, profile__role__in=['SEC'])
        #     # check if users have the site authorization..., exclude current user
        #     users = users.exclude(username = self.request.user)
        #     # convert users as a list
        #     users = list(users)
        #
        #     if users:
        #         notify.send(self.request.user,
        #                     recipient_list=users,
        #                     actor=self.request.user,
        #                     verb='CRH validé',
        #                     nf_type='notification_top',
        #                     obj_text = self.object.motif_hospitalisation + ' ' + sortie,
        #                     obj_url = str(reverse("hospitalisation:edit-hospitalisation", args=[self.object.id])),
        #                     #obj_patient = str(self.object.zkf_patient.full_name()),
        #                     )

        if self.object.statut_crh == "valide":
            # Add operations to ATCD !
            # 1/ get specialite linked to hospitalisation
            specialite = self.object.specialite_referente.spe
            specialite = "_".join(specialite.lower().split(" "))

            # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
            model_atcd_specialite = "Antecedents" + "".join(
                x.capitalize() for x in specialite.split("_")
            )

            # 2/ get ATCD
            # or if no ATCD crées: create ATCD !
            if hasattr(patient, str(model_atcd_specialite.lower())):
                obj = getattr(patient, str(model_atcd_specialite.lower()), None)
            else:
                model_name = apps.get_model(specialite, model_atcd_specialite)
                obj = model_name(zkf_patient=patient)

            # 3/ get all operation titles and convert them to a string

            operations = []
            if hospitalisation.operation_set.all():
                for o in hospitalisation.operation_set.all():
                    if not o.deleted:
                        operations.append(
                            o.titre_operation
                            + " "
                            + o.operation_date.strftime("%d/%m/%Y")
                        )

            # 4/update ATCD specific de la specialite
            if operations:
                atcd = getattr(obj, "atcd_" + specialite, None)

                if atcd:
                    atcd = atcd.splitlines()
                    # print (atcd)
                    for o in operations:
                        atcd.append(o)
                    # print (atcd)
                    atcd = "\n".join(atcd)
                    # print(atcd)
                    setattr(obj, "atcd_" + specialite, atcd)
                    # print (obj)
                    # print (getattr(obj, "atcd_" + specialite))
                else:
                    setattr(obj, "atcd_" + specialite, "\n".join(operations))

                # 5/ Save model instance
                obj.save()

        return super().form_valid(form)


class SaveCrhBeforeGeneratingCrh(LoginRequiredMixin, View):
    def post(self, request, pk, *args, **kwargs):
        hospitalisation = get_object_or_404(Hospitalisation, id=pk)
        form = EditCrhForm(request.POST, instance=hospitalisation)
        # print(self.request.POST['data'])
        if form.is_valid():
            form.save()
            return HttpResponse("Saved")
        else:
            if form.errors:
                for e in form.errors:
                    print(e)
            return HttpResponse("not saved")


# generate a string as a crh and insert it into the textarea when editing crh !
class GenerateCrh(LoginRequiredMixin, AuditableMixin, SuccessMessageMixin, View):
    def post(self, request, pk, *args, **kwargs):
        # 1/ get specialite and related onetoone table
        hospitalisation = Hospitalisation.objects.get(id=pk)
        specialite = hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))
        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        # 2/ check if there is antecedents filled in !
        antecedents = None
        if hasattr(hospitalisation, str(model_specialite).lower()):
            # 3/ load antecedents
            obj = getattr(hospitalisation, str(model_specialite).lower())
            antecedents = obj

        html = generate_crh(hospitalisation, antecedents, request)
        return HttpResponse(html)


class GenerateConsigneSurveillance(LoginRequiredMixin, Auditable, View):
    def post(self, request, *args, **kwargs):
        id = request.POST["id"]
        consigne_surveillance = ConsigneSortie.objects.get(id=id)
        # text = consigne_surveillance.texte_suivi

        # generate corps_document with variables
        context = {}
        t = Template(consigne_surveillance.texte_suivi)
        c = Context(context)
        corps = t.render(c)

        return HttpResponse(corps)


class updateSortieAjax(
    LoginRequiredMixin, View
):  # udpate le jour de la sortie (glisser/deposer)
    def post(self, request, *args, **kwargs):
        pk = request.POST["id"]
        hospitalisation = Hospitalisation.objects.get(id=pk)
        today = datetime.date.today()
        hospitalisation.sortie_date = today
        hospitalisation.save()

        return HttpResponse("OK")


class PdfCrh(LoginRequiredMixin, AuditableMixin, DetailView):
    template_name = "hospitalisation/pdf/crh.html"
    model = Hospitalisation

    def get(self, request, option=None, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        context = {
            "hospitalisation": self.object,
            "user": request.user,
            "patient": self.object.zkf_patient,
        }

        if option == "print":
            response = HttpResponse(content_type="application/pdf")
            response["Content-Transfer-Encoding"] = "binary"

            # generate a string (function is imported from impression.views)
            html_string = create_html_string(
                context=context, path=response, template=Hospitalisation.template_crh()
            )

            result = BytesIO()
            pdf = pisa.pisaDocument(BytesIO(html_string.encode("utf-8")), dest=result)

            if not pdf.err:
                return HttpResponse(result.getvalue(), content_type="application/pdf")
            else:
                return HttpResponse("Errors")

        elif option == "print-later":
            context = {"hospitalisation": self.object, "user": request.user}
            data = {
                "string_of_html": create_html_string(
                    context=context, template=Hospitalisation.template_crh()
                ),
                "zkf_user": request.user,
                "zkf_patient": self.object.zkf_patient,
                "titre_document": "CRH " + str(self.object.entree_date.year),
                "url_document": reverse(
                    "hospitalisation:detail-hospitalisation", args=(self.object.id,)
                ),
                "zkf_id": self.object.id,  # save zkp of hospitalisation
                "type_document": "crh",
            }
            # check if the document has already be added in the waiting list
            obj, created = GestionnaireImpression.objects.update_or_create(
                zkf_id=self.object.id, defaults=data
            )
            print_manager_list(request)

            return HttpResponse("Le document a été ajouté.")

        elif option == "print_hospitalisation":
            response = HttpResponse(content_type="application/pdf")
            response["Content-Transfer-Encoding"] = "binary"

            # append CROs
            operations = self.object.operation_set.all()
            # initiate pdf merger
            final_pdf = PdfFileMerger(strict=True)

            # create a temporary folder
            with tempfile.TemporaryDirectory() as tmpdirname:
                # generate pdf for crh
                html_string = create_html_string(
                    context=context,
                    path=response,
                    template=Hospitalisation.template_crh(),
                )
                resultFile = open(tmpdirname + "/" + "crh.pdf", "w+b")
                pisa.CreatePDF(
                    html_string,  # the HTML to convert
                    dest=resultFile,  # file handle to recieve result
                )

                final_pdf.append(tmpdirname + "/" + "crh.pdf")

                i = 0
                for o in operations:
                    # load html string
                    context = {"operation": o, "user": request.user}
                    html_string = create_html_string(
                        context=context,
                        path=response,
                        template=Operation.template_cro(),
                    )

                    # Generate CROs
                    # open output file for writing (truncated binary)
                    resultFile = open(tmpdirname + "/" + str(i) + "test.pdf", "w+b")

                    # convert HTML to PDF  http://www.ordinateur.cc/Logiciel/Portable-Document-Format/154234.html
                    # https://programtalk.com/python-examples/xhtml2pdf.pisa.CreatePDF/
                    # https://xhtml2pdf.readthedocs.io/en/latest/usage.html
                    # pisaStatus = pisa.CreatePDF(
                    #     html_string, dest=resultFile  # the HTML to convert
                    # )  # file handle to recieve result

                    # close output file
                    resultFile.close()  # close output file

                    final_pdf.append(tmpdirname + "/" + str(i) + "test.pdf")
                    i += 1

                response = HttpResponse(content_type="application/pdf")
                response["Content-Transfer-Encoding"] = "binary"

                # generate pdf in response
                final_pdf.write(response)

            return response

            # if not final_pdf.err:
            # 	return response
            # else:
            # 	return HttpResponse('Errors')


class ImportTraitementEntree(LoginRequiredMixin, View):
    def post(self, request, pk, *args, **kwargs):
        # 1/ get specialite and related onetoone table
        hospitalisation = Hospitalisation.objects.get(id=pk)
        specialite = hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))
        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        # 2/ check if there is a traitmeent/antecedents filled in !
        if hasattr(hospitalisation, str(model_specialite).lower()):
            # 3/ load traitement
            obj = getattr(hospitalisation, str(model_specialite).lower())
            return HttpResponse(obj.traitement)
        else:
            return HttpResponse("pas de traitement précisé !")


class InsertConsultationCrh(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        id = self.request.POST["id"]

        consultation = Consultation.objects.get(id=str(id))

        html = ""
        html += (
            "<ul> <li> Le "
            + consultation.consultation_date.strftime("%d/%m/%Y")
            + " à "
            + consultation.consultation_debut_prevu.strftime("%H:%M")
        )
        html += " avec le Dr." + consultation.redacteur.last_name
        html += "<br> Au cabinet de consultation: <b>"
        html += (
            str(consultation.zkf_site.adresse_site)
            + " "
            + str(consultation.zkf_site.zip_code_site)
            + " "
            + str(consultation.zkf_site.ville_site)
            + "</b> </li> </ul>"
        )

        return HttpResponse(html)


"""""
Ordonnances
Ordonnances URLS are in document/views
""" ""

"""
Consignes de Surveillance
"""


class DetailConsigneSurveillance(LoginRequiredMixin, DetailView):
    template_name = "hospitalisation/consigne_surveillance/detail.html"
    model = ConsigneSortie
    context_object_name = "consigne"


class CreateConsigneSurveillance(LoginRequiredMixin, AuditableMixin, CreateView):
    template_name = "hospitalisation/consigne_surveillance/edit.html"
    model = ConsigneSortie
    form_class = CreateConsigneSurveillanceForm
    context_object_name = "consigne"
    success_message = "Consigne de surveillance created"

    def form_valid(
        self,
        form,
    ):
        user = self.request.user
        form.instance.zkf_user = user
        return super(CreateConsigneSurveillance, self).form_valid(form)

    def get_success_url(self):
        return reverse(
            "hospitalisation:detail-consigne-surveillance", args=(self.object.id,)
        )


class UpdateConsigneSurveillance(LoginRequiredMixin, AuditableMixin, UpdateView):
    template_name = "hospitalisation/consigne_surveillance/edit.html"
    model = ConsigneSortie
    form_class = CreateConsigneSurveillanceForm
    context_object_name = "consigne"

    def get_success_url(self):
        return reverse(
            "hospitalisation:detail-consigne-surveillance", args=(self.object.id,)
        )


"""
SUIVI
"""


class AddSuivi(LoginRequiredMixin, View):
    def post(self, request, hospitalisation_pk):
        hos = get_object_or_404(Hospitalisation, id=hospitalisation_pk)
        suivi_entry = request.POST["add_suivi"]

        Suivi.objects.create(
            zkf_hospitalisation=hos,
            zkf_patient=hos.zkf_patient,
            texte_suivi=suivi_entry,
        )

        return HttpResponse("OK")


class DisplaySuivis(LoginRequiredMixin, DetailView):
    def get(self, request, hospitalisation_pk, *args, **kwargs):
        suivi = Suivi.objects.filter(
            zkf_hospitalisation=hospitalisation_pk,
            deleted=False,
        ).order_by("-created_on")
        html = render_to_string(
            "hospitalisation/tabs/suivi/suivis_list_short.html", {"suivi": suivi}
        )
        return HttpResponse(html)


class DeleteSuiviAjax(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        suivi_id = self.request.POST["suivi_id"]
        suivi = Suivi.objects.get(id=suivi_id)
        suivi.deleted = True
        suivi.save()

        return HttpResponse("deleted")


class SelectSuiviAjax(LoginRequiredMixin, View):
    def post(self, request, *args, **kwargs):
        suivi_id = self.request.POST["suivi_id"]
        suivi = Suivi.objects.get(id=suivi_id)
        text = suivi.texte_suivi

        return HttpResponse(text)
