from django.db import models
from django.urls import reverse

from django.contrib.auth.models import User
from accounts.models import Specialite, Service
import uuid
import datetime
from audit.models import Auditable  # to integrate modifications data

# softdelete
from babylone.django_safedelete_master.safedelete.models import (
    SafeDeleteModel,
    SOFT_DELETE_CASCADE,
)


from sort.tag.models import TagEveryWhere
from sort.tag.managers import TaggableManager


class Hospitalisation(Auditable, SafeDeleteModel):
    _safedelete_policy = SOFT_DELETE_CASCADE

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_patient = models.ForeignKey(
        "patient.Patient", on_delete=models.CASCADE, related_name="hospitalisations"
    )

    # linked operations with a hospitalisation -> can be multiple operations to follow with appropriate templates !
    operation_liee = models.ManyToManyField("operation.operation", blank=True)

    zkf_consigne_sortie = models.ForeignKey(
        "hospitalisation.ConsigneSortie",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    consignes_sortie = models.ManyToManyField(
        "hospitalisation.ConsigneSortie",
        db_table="consignes_sortie_hospitalisation",  # Nom court pour PostgreSQL
        blank=True,
        related_name="%(app_label)s_%(class)s_consignes_sortie_liees",
    )

    medecin_referent = models.ManyToManyField(
        User,
        help_text="Choisir un ou plusieurs médecin(s) référent(s)",
    )
    service_hospitalisation = models.ForeignKey(
        Service, on_delete=models.PROTECT
    )  # service d'hospitalisation du patient

    # specialité pour laquelle le patient est hospitalisé dans le service.
    # =>Dans le cas ou un service heberge plusieurs spécialités (vasculaire, thoracique...),
    # Ou dans le cas d'un hebergement d'un patient dans un autre service...
    specialite_referente = models.ForeignKey(Specialite, on_delete=models.PROTECT)

    """Tags"""
    tags = TaggableManager(through=TagEveryWhere, blank=True)

    entree_date = models.DateField(
        auto_now=False,
        auto_now_add=False,
        blank=True,
        null=True,
    )
    # Heure entree hopital -> for convocation patient
    entree_heure = models.TimeField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )

    sortie_date = models.DateField(
        auto_now=False,
        auto_now_add=False,
        blank=True,
        null=True,
    )
    hospitalisation_ambulatoire = models.BooleanField(
        default=False,
    )
    a_programmer = models.BooleanField(default=False)
    annulation_hospitalisation = models.BooleanField(default=False)
    commentaire_annulation_hospitalisation = models.TextField(null=False, blank=True)

    motif_hospitalisation = models.TextField(
        blank=True,
    )
    histoire_maladie = models.TextField(
        blank=True,
    )
    examen_clinique = models.TextField(
        blank=True,
    )

    """Sortie"""
    # Traitement
    traitement_sortie = models.TextField(
        blank=True,
        null=True,
    )

    def traitement_sortie_list(self):
        if self.traitement_sortie:
            return self.traitement_sortie.split("\n")
        else:
            return ""

    """CRH"""
    crh = models.TextField(
        blank=True,
    )
    date_redaction_crh = models.DateField(
        auto_now=False,
        auto_now_add=False,
        blank=True,
        null=True,
    )
    zkf_redacteur = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="crh_redacteur",
    )

    ENVOYE = "envoye"
    VALIDE = "valide"
    ATTENTE_VALIDATION = "attente_validation"
    NON_FAIT = "non_fait"
    NON_STATUE = "non_statue"

    CHOICES_ETAT_CRH = (
        (ENVOYE, "Envoyé"),
        (VALIDE, "Validé"),
        (ATTENTE_VALIDATION, "En attente de validation"),
        (NON_FAIT, "Non fait"),
        (NON_STATUE, "Non statué"),
    )
    statut_crh = models.CharField(
        max_length=50,
        blank=False,
        null=False,
        choices=CHOICES_ETAT_CRH,
        default=NON_FAIT,
    )

    complication_post_op = models.BooleanField(null=True)
    detail_complication_post_op = models.TextField(blank=True)

    # IFAQ

    PROGRAMME = "programme"
    URGENCE = "urgence"
    TRANSFERT = "transfert"

    MODE_ENTREE = (
        (PROGRAMME, "Programmé"),
        (URGENCE, "Urgence"),
        (TRANSFERT, "Transfert"),
    )

    mode_entree = models.CharField(
        max_length=50, null=True, blank=True, choices=MODE_ENTREE, default=PROGRAMME
    )
    origine_transfert = models.CharField(max_length=256, null=True, blank=True)
    transfusion = models.BooleanField(
        default=False,
    )
    test_portage_bmr = models.BooleanField(
        default=False,
    )
    examens_biologie_radiologie = models.BooleanField(
        default=True,
    )
    regles_hygieno_dietetiques = models.BooleanField(
        default=True,
    )
    remise_document_patient = models.BooleanField(
        default=True,
    )
    destination_retour_domicile = models.BooleanField(
        default=True,
    )
    destination_retour_detail = models.CharField(max_length=256, null=True, blank=True)
    pending_exams_report = models.BooleanField(default=False, blank=True)

    def __str__(self):
        return self.motif_hospitalisation

    # sentence to describe duration of the hospitalisation
    def description_instance(self):
        entree = sortie = None
        if self.entree_date:
            entree = datetime.datetime.strftime(self.entree_date, "%d/%m/%Y")
        if self.sortie_date:
            sortie = datetime.datetime.strftime(self.sortie_date, "%d/%m/%Y")
        return "Hospitalisation du " + str(entree) + " au " + str(sortie)

    def link(self):
        return reverse(
            "hospitalisation:edit-crh",
            args=[self.id],
        )

    """Defs"""

    def entree_is_today(self):
        today = datetime.date.today()
        if self.entree_date == today:
            return True
        else:
            return False

    def sortie_is_today(self):
        today = datetime.date.today()
        if self.sortie_date == today:
            return True
        else:
            return False

    # check if hospitalisation is in the future
    def hospitalisation_upcoming(self):
        today = datetime.date.today()
        if self.entree_date:
            if self.entree_date >= today:
                return True
            else:
                return False
        else:
            return False

    def ambulatoire(self):
        today = datetime.date.today()
        if self.entree_date == today and self.sortie_date == today:
            return True
        else:
            return False

    def template_crh(self):
        template = str("hospitalisation/pdf/crh.html")
        return template

    def template(self, type_document=None):
        if type_document == "crh":
            template = str("hospitalisation/pdf/crh.html")
        elif type_document == "convocation_hospitalisation":
            template = str("hospitalisation/pdf/convocation.html")
        else:
            template = None
        return template

    @property
    def count_operations(self):
        return self.operation_set.all().count()

    @property
    def duree_hospitalisation(self):
        if (self.entree_date) and (self.sortie_date):
            duree_hospitalisation = self.sortie_date - self.entree_date
        else:
            duree_hospitalisation = datetime.date.today() - self.entree_date

        return duree_hospitalisation


class Suivi(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_patient = models.ForeignKey(
        "patient.Patient",
        on_delete=models.CASCADE,
    )
    zkf_hospitalisation = models.ForeignKey(
        "hospitalisation.Hospitalisation",
        on_delete=models.CASCADE,
    )
    texte_suivi = models.TextField(blank=False)


class ConsigneSortie(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_user = models.ForeignKey(User, on_delete=models.CASCADE)
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    zkf_service = models.ForeignKey(
        Service, on_delete=models.PROTECT, null=True, blank=True
    )
    zkf_specialite = models.ForeignKey(
        Specialite, on_delete=models.PROTECT, null=True, blank=True
    )
    zkf_protocole = models.ForeignKey(
        "operation.Protocole", on_delete=models.PROTECT, null=True, blank=True
    )
    titre_consigne = models.CharField(
        max_length=200,
        blank=True,
        null=True,
    )
    texte_suivi = models.TextField(blank=False)

    def __str__(self):
        return self.titre_consigne


# model class to store hospitalisation and consignes de sortie/model hospitalisation
class HospitalisationConsigneSortie(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_hospitalisation = models.ForeignKey(Hospitalisation, on_delete=models.CASCADE)
    zkf_consigne_sortie = models.ForeignKey(ConsigneSortie, on_delete=models.CASCADE)
    zkf_user = models.ForeignKey(User, on_delete=models.CASCADE)
