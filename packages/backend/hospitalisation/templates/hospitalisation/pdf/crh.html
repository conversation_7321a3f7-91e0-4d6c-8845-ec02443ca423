{% extends "impression/general.html" %}
<head>
<meta charset="UTF-8">
<style>
	/* https://xhtml2pdf.readthedocs.io/en/latest/format_html.html */
    .paragraph { -pdf-keep-with-next: true; }
	p { margin: 0; -pdf-keep-with-next: true; }
    p.separator { -pdf-keep-with-next: false; font-size: 6pt; }
	
	table.header {
		width: 100%;
		border-collapse: collapse;
	  }
  
	  td {
		vertical-align: middle;
	  }

    </style>
</head>

{% block prescripteur %}
	<div style = "text-align: center;">	
		{{hospitalisation.zkf_redacteur.profile.entete_ordonnance|safe}}<br>
		RPPS: {{hospitalisation.zkf_redacteur.profile.rpps}}
	</div>
{% endblock prescripteur %}


{% block adresse %}
	<div style = "text-align: center">
		<small>
			Service de {{hospitalisation.service_hospitalisation}}<br>
			{{hospitalisation.service_hospitalisation.site_service.nom_site}}<br>
			{{hospitalisation.service_hospitalisation.site_service.adresse_site}}<br>
			{{hospitalisation.service_hospitalisation.site_service.zip_code_site}} {{hospitalisation.service_hospitalisation.site_service.ville_site}}<br>
			{{hospitalisation.service_hospitalisation.site_service.telephone_site}}

		</small>
	</div>
{% endblock adresse %}


{% block title %}

<table class="header">
    <tr>
      <!-- Infos patient à gauche -->
      <td style="width: 25%; border:1px solid black; border-radius: 15px; padding: 5px;">
        {% include 'patient/identification.html' %}
      </td>

      <!-- Cellule centrale pour titre -->
      <td style="width: 40%;" class="title_crh"></td>

      <td style="width: 30%;">
		<!-- CORRESPONDANTS -->
		<div style="text-align: right; font-size: 8pt; font-weight: normal;">
			{% if hospitalisation.zkf_patient.correspondants.all.exists %}
				{% for c in hospitalisation.zkf_patient.correspondants.all %}
					<div style="margin: 3pt 0; line-height: 1.2;">
						{% if c.structure %}
							{{ c.structure }} – {{ c.politesse }}<br>
						{% else %}
							{{ c.politesse }}<br>
						{% endif %}
			
						<small>
							{% for s in c.correspondant.specialite.all %}
							{{ s.spe_medecin }}{% if not forloop.last %}, {% endif %}
							{% endfor %}
				
							{% for v in c.adresse_set.all %}
							{{ v.code_postal }} {{ v.ville|title }}{% if not forloop.last %}, {% endif %}
							{% endfor %}
						</small>
					</div>
				{% endfor %}
	{% endif %}
  </div>
	  </td>
    </tr>
  </table>

  
	<!-- CORRESPONDANTS -->
	{% comment %} <div style="text-align: right; font-size: 8pt; font-weight: normal; margin-top: 5mm;">
		{% if hospitalisation.zkf_patient.correspondants.all.exists %}
		  Double à :<br>
		  {% for c in hospitalisation.zkf_patient.correspondants.all %}
			<div style="margin-left: 5mm; margin-bottom: 2mm;">
			  {% if c.structure %}
				{{ c.structure }} – {{ c.politesse }}<br>
			  {% else %}
				{{ c.politesse }}<br>
			  {% endif %}
	  
			  <small>
				{% for s in c.correspondant.specialite.all %}
				  {{ s.spe_medecin }}{% if not forloop.last %}, {% endif %}
				{% endfor %}
	  
				{% for v in c.adresse_set.all %}
				  {{ v.code_postal }} {{ v.ville|title }}{% if not forloop.last %}, {% endif %}
				{% endfor %}
			  </small>
			</div>
		  {% endfor %}
		{% endif %}
	  </div> {% endcomment %}
	
			{% comment %} <div style="text-align:left; font-size: 60%; margin-left: 0; padding-left: 0;">
					{% if hospitalisation.zkf_patient.correspondants %}
						{% for c in hospitalisation.zkf_patient.correspondants.all %}
								
						{% if c.structure %}
							{{c.politesse}} - {{c.structure}}
						{% else %}

						{{c.politesse}} <br>
						
						<!-- Specialité -->
						<small>
							{% for s in c.correspondant.specialite.all %}
								{{s.spe_medecin}}, 
							{% endfor %}
						{% endif %}	
							<!-- VILLE -->
							{% for v in c.adresse_set.all %}
								{{v.code_postal}} {{v.ville|title}},
							{% endfor %}
							</small>
							<br>
						{% endfor %}
					{% endif %}
				
				</div>
				<br>
				{{hospitalisation.service_hospitalisation.site_service.ville_site}}, le {{hospitalisation.date_redaction_crh|date:"d/m/Y"}} {% endcomment %}


{% endblock title %}

{% block identification %}

<div style="border: 1px solid #000; border-radius: 5px; padding: 8px; text-align: center; margin-bottom: 10mm; font-size: 10pt;">
	<span style="font-size: 14pt; font-weight: bold;">Compte rendu d'Hospitalisation</span><br/>
	<span><b>du {{ hospitalisation.entree_date|date:"d/m/Y" }}</b> au <b>{{ hospitalisation.sortie_date|date:"d/m/Y" }}</b></span><br/>
	<span>{{ hospitalisation.service_hospitalisation.site_service.nom_site }}</span>
  </div>

{% endblock identification %}


{% block content %}

<!-- <div class = "politesse">
	<p style = "text-align:right">{{hospitalisation.service_hospitalisation.site_service.ville_site}}, le {{hospitalisation.date_redaction_crh|date:'l d F Y'}}</p>
	<br>
	<p>Cher confrère,</p>
	<p>	Je vous prie de trouver ci joint le compte rendu d'hospitalisation de {{hospitalisation.zkf_patient.full_name_politesse}}. </p>
	<p>Je suis à votre disposition pour toute information complémentaire nécessaire.</p>
	<p>Avec mes confraternelles salutations, </p>
	<p style = "text-align: right;">Dr. {{hospitalisation.zkf_redacteur.last_name}}</p>
	<br><br>
</div> -->

<!-- CRH ! -->
{{ hospitalisation.crh|safe }}


<!-- Switch to next page -->
<p class="separator">&nbsp;<p>

<p style = "border: 1px solid;border-radius: 5px; padding-top: 15px;
padding-right: 5px;
padding-bottom: 10px;
padding-left: 10px;">
	Mode d'entrée: {{hospitalisation.mode_entree}}
	{% if hospitalisation.origine_transfert != None %}
		 ({{hospitalisation.origine_transfert}})<br>
	{% else %}
		<br>
	{% endif %}

	Transfusion: {{ hospitalisation.transfusion|yesno:"Oui, Non" }}<br>
	Test de portage BMR: {{hospitalisation.test_portage_bmr|yesno:"Oui, Non" }}<br>
	Examens complémentaires/actes techniques: {{hospitalisation.examens_biologie_radiologie|yesno:"Oui, Non" }}<br>
	Examens en attente de résultats: {{hospitalisation.pending_exams_report|yesno:"Oui, Non, Non"}}<br>
	Education aux règles hygieno-diététiques: {{hospitalisation.regles_hygieno_dietetiques|yesno:"Oui, Non" }}<br>
	Remise du document au patient: {{hospitalisation.remise_document_patient|yesno:"Oui, Non" }}<br>
	{% if hospitalisation.destination_retour_domicile == False %}
		Transfert: {{hospitalisation.destination_retour_detail}}
	{% else %}
		Retour à domicile
	{% endif %}

</p>

<br>
<div style = "text-align:right;" >
	Dr.{{hospitalisation.zkf_redacteur.last_name}}
</div>

<div style = "text-align:left;" >
	<br><br><hr>
		<small>
			Numéro d’urgence : 03.20.99.41.11(du lundi au vendredi de 7h à 20h service d’ambulatoire) – 03.20.99.41.06 (la nuit, le week-end et jours fériés service de chir c)
		</small>
	<hr>
</div>


<!-- List of correspondants -->
<div style="text-align:left; font-size: 60%">

	{% if hospitalisation.zkf_patient.correspondants.all.exists %}
	Double à: <br>
		{% for c in hospitalisation.zkf_patient.correspondants.all %}
			
			{{c.politesse}}, 
			
			<!-- Specialité -->
			
			<small>
				{% for s in c.correspondant.specialite.all %}
					{{s.spe_medecin}}, 
				{% endfor %}
				
				<!-- VILLE -->
				{% for a in c.adresse_set.all %}
					{{a.adresse_complete}},
				{% endfor %}
			</small>
			<br>
		{% endfor %}
	{% endif %}

</div>

{% endblock content %}


{% block footer %}
	<div style = "text-align: center">
		<small>
			Service de {{hospitalisation.service_hospitalisation}} - 
			{{hospitalisation.service_hospitalisation.site_service.nom_site}}<br>
			{{hospitalisation.service_hospitalisation.site_service.adresse_site}} - 
			{{hospitalisation.service_hospitalisation.site_service.zip_code_site}} {{hospitalisation.service_hospitalisation.site_service.ville_site}}
		</small>

	</div>
{% endblock footer %}
