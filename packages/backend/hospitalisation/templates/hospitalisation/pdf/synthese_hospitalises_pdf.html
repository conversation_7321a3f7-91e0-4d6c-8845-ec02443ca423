
{% block content %}
<head>
    <title>Tableau A4</title>
    <style>
        @page {
            size: A4;
            margin: 5mm; /* Petite marge pour éviter les problèmes d'impression */
        }
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            width: 100%;
            height: 100%;
        }
        .a4-container {
            width: 200mm; /* Légèrement moins que 210mm pour la marge */
            min-height: 287mm; /* Hauteur A4 moins marges */
            margin: 0 auto;
            padding: 0;
        }
        .full-width-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        .full-width-table th, .full-width-table td {
            border: 1px solid #000;
            padding: 3px; /* Padding réduit */
            text-align: left;
            vertical-align: top; /* Alignement en haut pour éviter l'espace vide */
            height: auto; /* Hauteur automatique */
            min-height: 5mm; /* Hauteur minimale */
            line-height: 1.2; /* Interligne réduit */
        }
        .full-width-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            padding: 4px; /* Légèrement plus pour les headers */
        }
        @media print {
            body {
                width: 210mm;
                height: 297mm;
                margin: 0;
            }
            .a4-container {
                width: 200mm;
                margin: 0;
                padding: 0;
            }
            .full-width-table {
                page-break-inside: avoid; /* Évite de couper le tableau sur plusieurs pages */
            }
        }
    </style>
</head>

<body style="font-family: arial; font-size: 7pt;">

	{% comment %} 
	Genereate a table with 3 rows:
	First: patient identification and hospitalisation details
	Second: operations if applicable
	3rd: follow up notes
	{% endcomment %}


	<div class="a4-container">

		<div style="text-align: center;">
			<img src="{{logo}}" alt="Meiflow" style="width: 100px; height: 50px;">
		</div>
		
        <h2>Suivi des patients hospitalisés le {% now "jS F Y H:i" %}</h2>  
        {% for service, hospitalisation in hospitalisations.items %}
            <h2>{{ service }}</h2>
                <table class="full-width-table">

                    <tbody>
                        {% for h in hospitalisation %}
                            <tr style="height: auto;">

                                <td style="width: 20%;">
                                    {{ h.zkf_patient.full_name_short }}<br>
                                    <b>{{ h.motif_hospitalisation }}</b>
                                    <br>
                                    E: {{ h.entree_date|date:"d/m/Y" }}</a>
                                    {% if h.sortie_date %}
                                        <br>
                                        S:{{ h.sortie_date|date:"d/m/Y" }}
                                    {% endif %}
                                </td>

                                <td style="width: 30%;">
                                    {% if h.operation_set.all %}
                                        {% for o in h.operation_set.all %}
                                        {% if o.operation_date == today %}
                                                <div style="background-color: yellow; padding: 2px;">
                                            {% else %}
                                                <div>
                                            {% endif %}
                                            <font size=1.3>
                                                <li>{{ o.operation_date|date:"d/m/Y" }} <b>{{ o.titre_operation }}</b> </b>
                                            </font>

                                            {% for u in o.operateur_new.all %}
                                                <font size=1.1>Dr.{{ u.nom }}</font>
                                            {% endfor %}

                                        {% endfor %}
                                    {% endif %}
                                </td>

                                <td style="width: 50%;">
                                    {% for s in h.suivi_set.all %}
                                        <div>
                                            {{ s.created_on|date:"d/m/Y H:i" }}<br>
                                            {{ s.texte_suivi|safe }}
                                        </div>
                                        
                                    {% empty %}
                                        <div style="text-align: center;">Aucun suivi disponible</div>
                                    {% endfor %}
                                </td>
                            </tr>

                        {% empty %}
                            <tr>
                                <td colspan="3" style="text-align: center;">Aucune donnée disponible</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endfor %}
		</div>
</body>

{% endblock content %}

