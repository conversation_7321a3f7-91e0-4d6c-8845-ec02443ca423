import re
import unicodedata
from functools import reduce
from django.db.models import Q
from django.contrib.postgres.search import SearchVector, SearchQuery
import operator
from codage.ccam.models import R_acte


def normalize_text(text):
    """
    Normalise le texte en supprimant les accents, la ponctuation
    et en convertissant en minuscules
    """
    if not text:
        return ""

    # Supprimer les accents
    text = unicodedata.normalize("NFD", text)
    text = "".join(char for char in text if unicodedata.category(char) != "Mn")

    # Supprimer la ponctuation et convertir en minuscules
    text = re.sub(r"[^\w\s]", " ", text.lower())

    # Supprimer les espaces multiples
    text = re.sub(r"\s+", " ", text.strip())

    return text


def normalize_query(query_string):
    """Normalise et divise la requête en mots"""
    normalized = normalize_text(query_string)
    return [word.strip() for word in normalized.split() if word and len(word) > 1]


def query_ccam_complex(query_text):
    """
    Recherche dans R_acte sur cod_acte ou nom_long
    - cod_acte: recherche exacte (insensible à la casse)
    - nom_long: tous les mots présents, dans le désordre,
                insensible à la casse, accents et ponctuation
    """
    if not query_text or not query_text.strip():
        return R_acte.objects.none()

    original_query = query_text.strip()
    normalized_words = normalize_query(original_query)

    if not normalized_words:
        return R_acte.objects.none()

    # Option 1: Utiliser PostgreSQL Full-Text Search (recommandé si disponible)
    if hasattr(R_acte.objects, "annotate"):  # Vérification basique pour PostgreSQL
        try:
            # Recherche par cod_acte (insensible à la casse)
            cod_acte_filter = Q(cod_acte__iexact=original_query)

            # Recherche full-text sur nom_long
            search_query = SearchQuery(" & ".join(normalized_words), config="french")
            nom_long_filter = Q(search_vector=search_query)

            # Annoter avec le vecteur de recherche si pas déjà fait
            qs = (
                R_acte.objects.annotate(
                    search_vector=SearchVector("nom_long", config="french")
                )
                .filter(cod_acte_filter | nom_long_filter)
                .order_by("cod_acte", "-dt_modif")
                .distinct("cod_acte")
            )

            return qs

        except Exception:
            # Fallback vers la méthode traditionnelle si erreur
            pass

    # Option 2: Méthode traditionnelle avec regex (compatible tous SGBD)

    # Recherche par cod_acte (insensible à la casse)
    cod_acte_filter = Q(cod_acte__iexact=original_query)

    # Construction des filtres pour nom_long (recherche partielle)
    nom_long_filters = []

    for word in normalized_words:
        # Créer un pattern regex qui ignore accents et ponctuation
        # et permet la recherche partielle (préfixe)
        escaped_word = re.escape(word)

        # Pattern flexible qui accepte les variations d'accents et ponctuation
        pattern = escaped_word
        # Remplacer les lettres par leurs équivalents avec/sans accents
        replacements = {
            "a": "[aàáâãäå]",
            "e": "[eèéêë]",
            "i": "[iìíîï]",
            "o": "[oòóôõö]",
            "u": "[uùúûü]",
            "c": "[cç]",
            "n": "[nñ]",
            "y": "[yÿ]",
        }

        for letter, pattern_letter in replacements.items():
            pattern = pattern.replace(letter, pattern_letter)

        # Pattern pour recherche partielle : le mot peut être au début d'un mot plus long
        # (?:^|\W) = début de chaîne ou non-mot
        # [pattern] = le mot recherché avec variantes d'accents
        # \w* = continuation possible du mot (recherche partielle)
        flexible_pattern = r"(?:^|\W)" + pattern + r"\w*(?=\W|$)"

        nom_long_filters.append(Q(nom_long__iregex=flexible_pattern))

    # Tous les mots doivent être présents
    if nom_long_filters:
        nom_long_filter = reduce(operator.and_, nom_long_filters)
    else:
        nom_long_filter = Q(pk__in=[])  # Aucun résultat

    # Combinaison finale
    final_filter = cod_acte_filter | nom_long_filter

    # Requête finale
    qs = (
        R_acte.objects.filter(final_filter)
        .order_by("cod_acte", "-dt_modif")
        .distinct("cod_acte")
    )

    return qs


def query_ccam(query_text):

    if not query_text or not query_text.strip():
        return R_acte.objects.none()

    original_query = query_text.strip()
    words = normalize_query(original_query)

    if not words:
        return R_acte.objects.none()

    if " " in original_query:
        # Cas 1 : phrase => recherche sur nom_long uniquement
        nom_long_filter = reduce(
            operator.and_, [Q(nom_long__icontains=word) for word in words]
        )
        final_filter = nom_long_filter
    else:
        # Cas 2 : un seul mot => recherche dans cod_acte OU nom_long
        final_filter = Q(cod_acte__icontains=original_query) | Q(
            nom_long__icontains=original_query
        )

    qs = (
        R_acte.objects.filter(final_filter)
        .order_by("cod_acte", "-dt_modif")
        .distinct("cod_acte")
    )

    return qs
