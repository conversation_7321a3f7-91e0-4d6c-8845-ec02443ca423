# from django.test import TestCase
# from consultation.models import Consultation
# from datetime import date
#
#
# class AnimalTestCase(TestCase):
#     def setUp(self):
#         today = date.today()
#         self.consultation = Consultation.objects.create(
#             consultation_date=today, motif_consultation="varices"
#         )
#
#     def test_animals_can_speak(self):
#         """Animals that can speak are correctly identified"""
#         lion = Animal.objects.get(name="lion")
#         cat = Animal.objects.get(name="cat")
#         self.assertEqual(lion.speak(), 'The lion says "roar"')
#         self.assertEqual(cat.speak(), 'The cat says "meow"')
