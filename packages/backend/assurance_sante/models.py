from django.db import models
import uuid
from audit.models import Auditable
from contact.models import <PERSON><PERSON><PERSON>, Email, Telephone


class AssuranceSante(Auditable):
    class Meta:
        ordering = ["nom_mutuelle"]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement_assurance_sante = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    nom_mutuelle = models.CharField(max_length=254, blank=False, null=False)
    adresse_mutuelle = models.ForeignKey(
        Adresse, on_delete=models.PROTECT, blank=True, null=True
    )
    email_mutuelle = models.ForeignKey(
        Email, on_delete=models.PROTECT, blank=True, null=True
    )
    telephone_mutuelle = models.ForeignKey(
        Telephone, on_delete=models.PROTECT, blank=True, null=True
    )
    # attestation_mutuelle = models.File()

    def __str__(self):
        return self.nom_mutuelle
