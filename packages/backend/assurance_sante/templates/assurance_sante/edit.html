{% extends "base_generic.html" %}

{% load staticfiles %}
{% load static %}
{% load widget_tweaks %}

{% block script %}

<script src="{% static "fullcalendar/fullcalendar-3.9.0/moment.min.js" %}"></script>

{% endblock script %}
{% block content %}
{% include "dashboard/dashboard_navbar.html" %}

<style>
/* remove breaks line */
p {
    	margin: 0 0 0 0;
	}
.ck-content { min-height:300px; max-height:500px; }
</style>

<form method="post" class="form">

	{% csrf_token %}
		{% for hidden_field in form.hidden_fields %}
			{{ hidden_field }}
		{% endfor %}
		<br>
		
		<!--Nom de la mutuelle-->
		{{ form.nom_mutuelle.label_tag }}
		{% render_field form.nom_mutuelle type="text" class="form-control mw-100" style="width: 800px;" %}
		{% if field.help_text %}
			<small class="form-text text-muted">{{ form.nom_mutuelle.help_text }}</small>
		{% endif %}


		<button type="submit" class="btn btn-primary" >Submit</button>
		
		
		{% if mutuelle %}
			<a href="{% url 'assurance-sante:detail-assurance-sante' mutuelle.id %}">Cancel</a>
		{% else %}
			<a href="{% url 'dashboard:assurance-sante-list' %}">Cancel</a>
		{% endif %}
		
		<br><br>


</form>

<script>

// CREATE EDITORS
//https://stackoverflow.com/questions/48575534/ckeditor-5-get-editor-instances
const editors = {}; // You can also use new Map() if you use ES6.

function createEditor( elementId ) {
    return ClassicEditor
        .create( document.getElementById( elementId ), {
            toolbar: [ 'heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote' ],
        })
        .then( editor => {
            editors[ elementId ] = editor;
        })
        .catch( error => {
            console.error( error );
        } );
        
}

$(document).ready(function (){
    // In real life, you may also need to take care of the returned promises.
    createEditor( 'id_corps_document' )
    // createEditor( 'modify_ordonnance' )
});
	
//Insert variable in the editor:
function insert_variable (variable){
    
    editor = editors.id_corps_document;
    editor.model.change( writer => {
        const insertPosition = editor.model.document.selection.getFirstPosition();
        writer.insertText( variable, insertPosition );
        });
}
</script>

{% endblock content %}

