# from django.utils import simplejson as json

from audit.views import AuditableMixin
from django.views.generic import (
    CreateView,
    DetailView,
    UpdateView,
)
from django.urls import reverse
from django.contrib.auth.mixins import LoginRequiredMixin

# from .models import AssuranceSante

from .forms import AssuranceSante, CreateAssuranceSanteForm, EditAssuranceSanteForm


class DetailAssuranceSante(LoginRequiredMixin, DetailView):
    template_name = "assurance_sante/detail.html"
    model = AssuranceSante
    context_object_name = "mutuelle"


class CreateAssuranceSante(LoginRequiredMixin, AuditableMixin, CreateView):
    template_name = "assurance_sante/edit.html"
    # model = AssuranceSante
    form_class = CreateAssuranceSanteForm
    context_object_name = "mutuelle"

    def form_valid(
        self,
        form,
    ):
        mutuelle = form.save(commit=False)

        # save environnement !
        user = self.request.user
        mutuelle.zkf_environnement_assurance_sante = user.profile.zkf_environnement_user

        """If the form is valid, save the associated model."""
        self.object = mutuelle.save()
        return super(CreateAssuranceSante, self).form_valid(form)

    def get_success_url(self):
        return reverse("assurance-sante:detail-assurance-sante", args=(self.object.id,))

    def form_invalid(self, form):
        print(form.errors)
        return self.render_to_response(self.get_context_data(form=form))


class UpdateAssuranceSante(LoginRequiredMixin, AuditableMixin, UpdateView):
    template_name = "assurance_sante/edit.html"
    # model = AssuranceSante
    form_class = EditAssuranceSanteForm
    context_object_name = "mutuelle"

    def get_success_url(self):
        return reverse("assurance-sante:detail-assurance-sante", args=(self.object.id,))
