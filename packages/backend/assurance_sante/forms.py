from django import forms
from .models import AssuranceSante


class EditAssuranceSanteForm(forms.ModelForm):
    class Meta:
        model = AssuranceSante
        fields = "__all__"
        exclude = [
            "zkf_patient",
            "zkf_operation",
            "zkf_consultation",
            "zkf_hospitalisation",
            "created_by",
            "modified_by",
            "deleted_by",
            "deleted_on",
            "deleted",
            "titre_document",
        ]

        # labels = {
        # 	# 'portfolio': "Create a portfolio for same group pictures",
        # }


class CreateAssuranceSanteForm(forms.ModelForm):
    class Meta:
        model = AssuranceSante
        fields = "__all__"
        exclude = [
            "zkf_patient",
            "zkf_operation",
            "zkf_consultation",
            "zkf_hospitalisation",
            "zkf_environnement_assurance_sante",
            "created_by",
            "modified_by",
            "deleted_by",
            "deleted_on",
            "deleted",
            "titre_document",
        ]
