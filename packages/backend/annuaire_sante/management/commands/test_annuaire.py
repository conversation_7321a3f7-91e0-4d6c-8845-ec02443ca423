from django.core.management.base import BaseCommand
from annuaire_sante.services import AnnuaireSanteClient


class Command(BaseCommand):
    help = "Test the Annuaire Sante API client"

    def add_arguments(self, parser):
        parser.add_argument("--rpps", type=str, help="RPPS number to search for")
        parser.add_argument(
            "--org",
            type=str,
            help="Organization identifier (SIRET/SIREN/FINESS) to search for",
        )

    def handle(self, *args, **options):
        client = AnnuaireSanteClient()

        if options["rpps"]:
            self.stdout.write("Searching for doctor...")
            doctor = client.get_doctor_info(options["rpps"])
            if doctor:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Found doctor: {doctor.family_name}, {" ".join(doctor.given_names)}'
                    )
                )
                self.stdout.write(f"Active: {doctor.active}")
                self.stdout.write(f"Title: {doctor.title}")
                self.stdout.write(f"CPS: {doctor.cps_number}")
                self.stdout.write(f'Emails: {", ".join(doctor.email_addresses or [])}')

                for activity in doctor.activities or []:
                    self.stdout.write("\nActivity:")
                    self.stdout.write(f"  Organization: {activity.organization.name}")
                    self.stdout.write(f"  SIRET: {activity.organization.siret}")
                    self.stdout.write(f"  SIREN: {activity.organization.siren}")
                    self.stdout.write(f"  FINESS: {activity.organization.finess}")
                    self.stdout.write(f"  Mode: {activity.mode_exercice}")
                    self.stdout.write(f"  Profession: {activity.profession}")
                    self.stdout.write(f"  Specialties: {activity.specialties}")
            else:
                self.stdout.write(self.style.ERROR("Doctor not found"))

        if options["org"]:
            self.stdout.write("Searching for organization...")
            org = client.search_organization(options["org"])
            if org:
                self.stdout.write(self.style.SUCCESS(f"Found organization: {org.name}"))
                self.stdout.write(f"Active: {org.active}")
                self.stdout.write(f"SIRET: {org.siret}")
                self.stdout.write(f"SIREN: {org.siren}")
                self.stdout.write(f"FINESS: {org.finess}")
            else:
                self.stdout.write(self.style.ERROR("Organization not found"))
