from rest_framework import generics
from rest_framework import permissions
from django.http import JsonResponse
import pandas as pd
import logging
from datetime import datetime, date

from rest_framework.views import APIView
from sort.tag.models import FullTag, TagEveryWhere
from api.sort.tag.serializers import (
    FullTagSerializer,
    TagEveryWhereSerializer,
    TagListSerializerField,
)
from operation.models import Operation
from consultation.models import Consultation
from hospitalisation.models import Hospitalisation
import json
from rest_framework.renderers import <PERSON><PERSON><PERSON>ender<PERSON>
from accounts.scripts.filter_user import get_user_secretaire
from accounts.scripts.filter_user import get_operateur, get_user_arc

# import scripts
from statistiques.operation.statistiques_operation import unified_operations_statistics
from statistiques.consultation.statistiques_consultation import (
    unified_consultations_statistics,
)

from django.contrib.contenttypes.prefetch import GenericPrefetch

logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s"
)


class TagsStatistiques(generics.ListAPIView):
    queryset = None
    serializer_class = TagEveryWhereSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # date intervalle:
        start_date = self.request.query_params.get("start_date", None)
        end_date = self.request.query_params.get("end_date", None)

        # operations
        filters = {
            "operation_date__gte": start_date,
            "operation_date__lte": end_date,
        }
        filters_operation = {k: v for k, v in filters.items() if v is not None}

        operateurs_user = get_operateur(self.request.user)
        operations = Operation.objects.filter(
            redacteur__in=operateurs_user, **filters_operation
        )

        filters = {
            "consultation_date__gte": start_date,
            "consultation_date__lte": end_date,
        }
        filters_consultation = {k: v for k, v in filters.items() if v is not None}
        consultations = Consultation.objects.filter(
            redacteur__in=operateurs_user, **filters_consultation
        )

        filters = {
            "hospitalisation_date__gte": start_date,
            "hospitalisation_date__lte": end_date,
        }
        filters_hospitalisation = {k: v for k, v in filters.items() if v is not None}
        hospitalisations = Hospitalisation.objects.filter(
            redacteur__in=operateurs_user, **filters_hospitalisation
        )
        # staffs = Staff.objects.filter(redacteur__in = operateurs_user)

        prefetch = GenericPrefetch(
            "content_object", [operations, consultations, hospitalisations]
        )
        queryset = TagEveryWhere.objects.prefetch_related(prefetch).all()
        queryset_all = TagEveryWhere.objects.all()

        data = self.get_serializer(queryset, many=True).data
        # data = (JSONRenderer().render(queryset_serializer))
        # data = (json.dumps(data.decode('utf-8')))

        # convert data to list of dict
        data_dict = list()
        for tag in data:
            data_dict.append({"tag": tag["zkf_tag"], "model": tag["model"]})
        # convert data do df
        df = pd.DataFrame.from_dict(data_dict)
        # remove NoneType models (objects that has been deleted ! )
        df = df[df.model != "NoneType"]

        # group by model and tag
        # https://stackoverflow.com/questions/71133064/percentage-of-total-with-groupby-for-two-columns
        # https://stackoverflow.com/questions/29836477/pandas-create-new-column-with-count-from-groupby
        new = df.groupby(["model", "tag"])["tag"].count().reset_index(name="tag_count")
        # print (new.groupby(['model'])['tag_count'].sum())
        # out = new/new.groupby(level=0).transform('count') * 100
        # print (out)
        new["pct"] = (
            100 * new["tag_count"] / new.groupby("model")["tag_count"].transform("sum")
        )
        # print (new.sort_values(by=['model', 'tag_count'], ascending=False))
        df_dict_json = new.to_dict(orient="records")
        # print (df_dict_json)
        return JsonResponse(df_dict_json, safe=False)


"""
Class to return list of sejours: consultation, hospitalisation, operation in a time range
Then group by variable
-> return a json with all the dict of the requested variables
for now: only for current user
"""


class SejourStatistiques(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        df = pd.DataFrame()
        user = self.request.user
        # list of dates <=> compare intervals !
        start_date = self.request.query_params.get(
            "start_date", None
        )  # return a list of dates -> to be able to compare different intervals
        end_date = self.request.query_params.get("end_date", None)

        ccam = self.request.query_params.get("ccam", None)
        tags = self.request.query_params.get("tags", None)
        # filters = {
        #     "has_sso": True,
        #     "seat_allowance__gt": 100
        # }

        if len(start_date) > 0 and len(end_date) > 0:
            for i in range(len(start_date)):
                consultations = Consultation.objects.filter(
                    date_consultation__range=[start_date[i], end_date[i]],
                    zkf_redacteur=user,
                )
                df["consultations" + i] = consultations

                hospitalisations = Hospitalisation.objects.filter(
                    entree_date__range=[start_date[i], end_date[i]], zkf_redacteur=user
                )
                df["hospitalisations" + i] = hospitalisations

                operations = Operation.objects.filter(
                    date_operation__range=[start_date[i], end_date[i]],
                    zkf_redacteur=user,
                )
                df["operations" + i] = operations

        return  # JsonResponse(df_dict_json, safe=False)


class DailyStats(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # Récupérer les paramètres
            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")
            date_intervals = request.data.get("date_intervals")  # Nouveau paramètre
            user_id = request.data.get("user_id")

            # Déterminer l'utilisateur
            if user_id:
                from django.contrib.auth.models import User

                try:
                    user = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    user = request.user
            else:
                user = request.user

            # Parser les dates
            start_dt = None
            end_dt = None

            if start_date:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
            if end_date:
                end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()

            # Si aucune date fournie, utiliser aujourd'hui
            if not start_dt and not end_dt:
                start_dt = end_dt = datetime.now().date()

            # Get the operations for date interval
            operations = Operation.objects.filter(
                operation_date__gte=start_dt,
                operation_date__lte=end_dt,
                zkf_redacteur=user,
            )

            # Get the consultations for date interval with patient info
            consultations = Consultation.objects.filter(
                consultation_date__gte=start_dt,
                consultation_date__lte=end_dt,
                zkf_redacteur=user,
            ).select_related("zkf_patient")

            # Appeler les fonctions de statistiques avec les nouveaux paramètres
            operations_stats = unified_operations_statistics(
                operations=operations,
                user=user.id,
                start_date=start_dt,
                end_date=end_dt,
                date_intervals=date_intervals,
            )
            consultations_stats = unified_consultations_statistics(
                consultations=consultations,
                user=user.id,
                start_date=start_dt,
                end_date=end_dt,
                date_intervals=date_intervals,
            )

            data = {
                "success": True,
                "operations": operations_stats,
                "consultations": consultations_stats,
                "metadata": {
                    "start_date": start_dt.strftime("%Y-%m-%d") if start_dt else None,
                    "end_date": end_dt.strftime("%Y-%m-%d") if end_dt else None,
                    "user_id": user.id,
                    "date_intervals": date_intervals,
                },
            }

            return JsonResponse(data, safe=False)

        except Exception as e:
            logging.error(f"Error in DailyStats: {str(e)}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)

    # Maintenir la compatibilité GET pour les anciens appels
    def get(self, request):
        # Rediriger vers POST avec les paramètres de query
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        # Créer un request POST simulé
        post_data = {}
        if start_date:
            post_data["start_date"] = start_date
        if end_date:
            post_data["end_date"] = end_date

        # Copier les données dans request.data
        request._full_data = post_data

        return self.post(request)
