from django.urls import path
from .views import TagsStatistiques, DailyStats
from .query_views import AvailableOptionsView, QueryDataView, ExportDataView
from .intervals_views import IntervalsComparisonView

# api route: /api/statistics

urlpatterns = [
    path("tags-stats/", TagsStatistiques.as_view(), name="tags-statistiques"),
    # daily statistics
    path("daily/", DailyStats.as_view(), name="daily-statistiques"),
    # intervals comparison
    path("intervals/", IntervalsComparisonView.as_view(), name="intervals-comparison"),
    # query and export options
    path("options/", AvailableOptionsView.as_view(), name="available-options"),
    path("query/", QueryDataView.as_view(), name="query-data"),
    path("export/", ExportDataView.as_view(), name="export-data"),
]
