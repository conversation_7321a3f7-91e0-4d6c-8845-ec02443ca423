# serializers.py
from rest_framework import serializers


class PeriodAnalysisSerializer(serializers.Serializer):
    periods = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField())
    )
    grouping = serializers.ChoiceField(choices=["day", "week", "month"], default="day")
    fields_to_analyze = serializers.ListField(
        child=serializers.CharField(), default=["value", "quantity"]
    )
