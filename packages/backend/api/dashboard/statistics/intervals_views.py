from rest_framework import permissions
from django.http import JsonResponse
from rest_framework.views import APIView
import pandas as pd
import logging
from datetime import datetime, timedelta
import numpy as np
from django.contrib.auth.models import User

from operation.models import Operation
from consultation.models import Consultation
from statistiques.operation.statistiques_operation import unified_operations_statistics
from statistiques.consultation.statistiques_consultation import (
    unified_consultations_statistics,
)
from accounts.scripts.filter_user import get_operateur

logger = logging.getLogger(__name__)


class IntervalsComparisonView(APIView):
    """
    Compare plusieurs intervalles de dates avec statistiques détaillées et données pour graphiques
    """

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # Récupérer les paramètres
            date_intervals = request.data.get("date_intervals", [])
            include_operations = request.data.get("include_operations", True)
            include_consultations = request.data.get("include_consultations", True)
            group_by = request.data.get("group_by", "day")  # day, week, month, year
            user_id = request.data.get("user_id")

            if not date_intervals:
                return JsonResponse(
                    {
                        "success": False,
                        "error": "Au moins un intervalle de dates est requis",
                    },
                    status=400,
                )

            # Déterminer l'utilisateur
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    user = request.user
            else:
                user = request.user

            # Générer les statistiques pour chaque intervalle
            intervals_analysis = []
            all_chart_data = []

            for interval in date_intervals:
                start_date = datetime.strptime(interval["start"], "%Y-%m-%d").date()
                end_date = datetime.strptime(interval["end"], "%Y-%m-%d").date()
                label = interval.get("label", f"{start_date} - {end_date}")

                duration_days = (end_date - start_date).days + 1

                interval_result = {
                    "interval_info": {
                        "label": label,
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "duration_days": duration_days,
                    }
                }

                # Statistiques opérations si demandées
                if include_operations:
                    operations = Operation.objects.filter(
                        operation_date__gte=start_date,
                        operation_date__lte=end_date,
                        zkf_redacteur=user,
                    )

                    if operations.exists():
                        ops_stats = unified_operations_statistics(
                            operations=operations,
                            user=user.id,
                            start_date=start_date,
                            end_date=end_date,
                        )
                        interval_result["operations"] = ops_stats

                        # Ajouter les moyennes par groupement pour les opérations
                        if "operations" not in interval_result:
                            interval_result["operations"] = {}

                        total_operations = operations.count()

                        # Calculer la moyenne selon le type de groupement
                        if group_by == "day":
                            avg_per_period = (
                                total_operations / duration_days
                                if duration_days > 0
                                else 0
                            )
                            interval_result["operations"]["average_per_day"] = round(
                                avg_per_period, 2
                            )
                        elif group_by == "week":
                            duration_weeks = duration_days / 7
                            avg_per_period = (
                                total_operations / duration_weeks
                                if duration_weeks > 0
                                else 0
                            )
                            interval_result["operations"]["average_per_week"] = round(
                                avg_per_period, 2
                            )
                        elif group_by == "month":
                            duration_months = (
                                duration_days / 30.44
                            )  # Moyenne approximative
                            avg_per_period = (
                                total_operations / duration_months
                                if duration_months > 0
                                else 0
                            )
                            interval_result["operations"]["average_per_month"] = round(
                                avg_per_period, 2
                            )
                        elif group_by == "year":
                            duration_years = duration_days / 365.25
                            avg_per_period = (
                                total_operations / duration_years
                                if duration_years > 0
                                else 0
                            )
                            interval_result["operations"]["average_per_year"] = round(
                                avg_per_period, 2
                            )

                # Statistiques consultations si demandées
                if include_consultations:
                    consultations = Consultation.objects.filter(
                        consultation_date__gte=start_date,
                        consultation_date__lte=end_date,
                        zkf_redacteur=user,
                    ).select_related("zkf_patient")

                    if consultations.exists():
                        consult_stats = unified_consultations_statistics(
                            consultations=consultations,
                            user=user.id,
                            start_date=start_date,
                            end_date=end_date,
                        )
                        interval_result["consultations"] = consult_stats

                        # Ajouter les moyennes par groupement
                        if "consultations" not in interval_result:
                            interval_result["consultations"] = {}

                        total_consultations = consultations.count()

                        # Calculer la moyenne selon le type de groupement
                        if group_by == "day":
                            avg_per_period = (
                                total_consultations / duration_days
                                if duration_days > 0
                                else 0
                            )
                            interval_result["consultations"]["average_per_day"] = round(
                                avg_per_period, 2
                            )
                        elif group_by == "week":
                            duration_weeks = duration_days / 7
                            avg_per_period = (
                                total_consultations / duration_weeks
                                if duration_weeks > 0
                                else 0
                            )
                            interval_result["consultations"]["average_per_week"] = (
                                round(avg_per_period, 2)
                            )
                        elif group_by == "month":
                            duration_months = (
                                duration_days / 30.44
                            )  # Moyenne approximative
                            avg_per_period = (
                                total_consultations / duration_months
                                if duration_months > 0
                                else 0
                            )
                            interval_result["consultations"]["average_per_month"] = (
                                round(avg_per_period, 2)
                            )
                        elif group_by == "year":
                            duration_years = duration_days / 365.25
                            avg_per_period = (
                                total_consultations / duration_years
                                if duration_years > 0
                                else 0
                            )
                            interval_result["consultations"]["average_per_year"] = (
                                round(avg_per_period, 2)
                            )

                intervals_analysis.append(interval_result)

                # Générer les données pour graphiques
                chart_data = self._generate_chart_data(
                    user,
                    start_date,
                    end_date,
                    group_by,
                    include_operations,
                    include_consultations,
                    label,
                )
                all_chart_data.extend(chart_data)

            # Combiner toutes les données de graphique et les trier
            combined_chart_data = self._combine_chart_data(all_chart_data, group_by)

            return JsonResponse(
                {
                    "success": True,
                    "intervals_analysis": intervals_analysis,
                    "chart_data": combined_chart_data,
                    "comparison_summary": self._generate_comparison_summary(
                        intervals_analysis
                    ),
                    "metadata": {
                        "group_by": group_by,
                        "include_operations": include_operations,
                        "include_consultations": include_consultations,
                        "total_intervals": len(date_intervals),
                    },
                }
            )

        except Exception as e:
            logger.error(f"Error in IntervalsComparisonView: {str(e)}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)

    def _generate_chart_data(
        self,
        user,
        start_date,
        end_date,
        group_by,
        include_operations,
        include_consultations,
        interval_label,
    ):
        """Génère les données pour les graphiques selon la granularité choisie"""
        chart_data = []

        # Créer la liste des périodes selon le groupement
        periods = self._get_periods(start_date, end_date, group_by)

        for period_start, period_end, period_label in periods:
            data_point = {
                "period": period_label,
                "interval": interval_label,
                "total_operations": 0,
                "total_consultations": 0,
                "operation_delay": 0,
                "consultation_delay": 0,
                "occupation_rate": 0,
                "on_time_patients": 0,
                "late_patients": 0,
                "early_patients": 0,
            }

            # Données opérations
            if include_operations:
                operations = Operation.objects.filter(
                    operation_date__gte=period_start,
                    operation_date__lte=period_end,
                    zkf_redacteur=user,
                )

                data_point["total_operations"] = operations.count()

                if operations.exists():
                    # Calculer statistiques détaillées pour cette période
                    ops_stats = unified_operations_statistics(
                        operations=operations,
                        user=user.id,
                        start_date=period_start,
                        end_date=period_end,
                    )

                    if ops_stats and ops_stats.get("vacation_analysis"):
                        vac_analysis = ops_stats["vacation_analysis"]
                        data_point["operation_delay"] = (
                            vac_analysis.get("average_delay", 0) or 0
                        )
                        data_point["occupation_rate"] = (
                            vac_analysis.get("average_occupation_rate", 0) or 0
                        )

            # Données consultations
            if include_consultations:
                consultations = Consultation.objects.filter(
                    consultation_date__gte=period_start,
                    consultation_date__lte=period_end,
                    zkf_redacteur=user,
                ).select_related("zkf_patient")

                data_point["total_consultations"] = consultations.count()

                if consultations.exists():
                    # Calculer statistiques détaillées pour cette période
                    consult_stats = unified_consultations_statistics(
                        consultations=consultations,
                        user=user.id,
                        start_date=period_start,
                        end_date=period_end,
                    )

                    if consult_stats and consult_stats.get("vacation_analysis"):
                        vac_analysis = consult_stats["vacation_analysis"]
                        data_point["consultation_delay"] = (
                            vac_analysis.get("average_consultation_delay", 0) or 0
                        )
                        data_point["on_time_patients"] = (
                            vac_analysis.get("on_time_patients", 0) or 0
                        )
                        data_point["late_patients"] = (
                            vac_analysis.get("late_patients", 0) or 0
                        )
                        data_point["early_patients"] = (
                            vac_analysis.get("early_patients", 0) or 0
                        )

            chart_data.append(data_point)

        return chart_data

    def _get_periods(self, start_date, end_date, group_by):
        """Génère la liste des périodes selon le groupement"""
        periods = []

        if group_by == "day":
            current = start_date
            while current <= end_date:
                periods.append((current, current, current.strftime("%d/%m")))
                current = current + timedelta(days=1)

        elif group_by == "week":
            # Grouper par semaine (lundi à dimanche)
            current = start_date
            while current <= end_date:
                week_start = current - timedelta(days=current.weekday())
                week_end = week_start + timedelta(days=6)
                week_end = min(week_end, end_date)
                week_label = f"S{current.isocalendar()[1]}"
                periods.append((week_start, week_end, week_label))
                current = week_end + timedelta(days=1)

        elif group_by == "month":
            # Grouper par mois
            current = start_date.replace(day=1)
            while current <= end_date:
                # Calculer le dernier jour du mois
                if current.month == 12:
                    next_month = current.replace(year=current.year + 1, month=1)
                else:
                    next_month = current.replace(month=current.month + 1)
                month_end = next_month - timedelta(days=1)
                month_end = min(month_end, end_date)
                month_label = current.strftime("%m/%Y")
                periods.append((current, month_end, month_label))
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1)
                else:
                    current = current.replace(month=current.month + 1)

        elif group_by == "year":
            # Grouper par année
            current = start_date.replace(month=1, day=1)
            while current <= end_date:
                year_end = current.replace(month=12, day=31)
                year_end = min(year_end, end_date)
                year_label = str(current.year)
                periods.append((current, year_end, year_label))
                current = current.replace(year=current.year + 1)

        return periods

    def _combine_chart_data(self, all_chart_data, group_by):
        """Combine et agrège les données de graphique avec groupement intelligent par période temporelle"""
        if not all_chart_data:
            return []

        # Convertir en DataFrame pour faciliter l'agrégation
        df = pd.DataFrame(all_chart_data)

        # Obtenir la liste unique des intervalles dans l'ordre
        unique_intervals = df["interval"].unique()
        interval_to_index = {
            interval: idx + 1 for idx, interval in enumerate(unique_intervals)
        }

        # Créer un dictionnaire pour stocker les données combinées
        combined_data = {}

        # Fonction pour normaliser les périodes selon le groupement
        def normalize_period(period_str, group_by):
            if group_by == "month":
                # Pour les mois, garder seulement le mois (MM/YYYY -> MM)
                if "/" in period_str:
                    month_part = period_str.split("/")[0]
                    return f"Mois {month_part}"
                return period_str
            elif group_by == "week":
                # Pour les semaines, garder seulement le numéro de semaine
                if "W" in period_str:
                    week_part = period_str.split("W")[1]
                    return f"Semaine {week_part}"
                return period_str
            elif group_by == "year":
                # Pour les années, pas de normalisation nécessaire
                return f"Année {period_str}"
            else:
                # Pour les jours, grouper par jour de la semaine ou du mois
                return period_str

        # Grouper les données par période normalisée
        for _, row in df.iterrows():
            original_period = row["period"]
            normalized_period = normalize_period(original_period, group_by)
            interval_label = row.get("interval", "")
            period_idx = interval_to_index.get(interval_label, 1)

            # Initialiser la période normalisée si elle n'existe pas
            if normalized_period not in combined_data:
                combined_data[normalized_period] = {
                    "period": normalized_period,
                    "original_periods": set(),
                }
                # Initialiser toutes les colonnes pour tous les intervalles
                for interval_idx in range(1, len(unique_intervals) + 1):
                    combined_data[normalized_period][
                        f"total_operations_periode_{interval_idx}"
                    ] = 0
                    combined_data[normalized_period][
                        f"total_consultations_periode_{interval_idx}"
                    ] = 0
                    combined_data[normalized_period][
                        f"operation_delay_periode_{interval_idx}"
                    ] = 0
                    combined_data[normalized_period][
                        f"consultation_delay_periode_{interval_idx}"
                    ] = 0
                    combined_data[normalized_period][
                        f"occupation_rate_periode_{interval_idx}"
                    ] = 0
                    combined_data[normalized_period][
                        f"on_time_patients_periode_{interval_idx}"
                    ] = 0
                    combined_data[normalized_period][
                        f"late_patients_periode_{interval_idx}"
                    ] = 0
                    combined_data[normalized_period][
                        f"early_patients_periode_{interval_idx}"
                    ] = 0

            # Ajouter la période originale au set
            combined_data[normalized_period]["original_periods"].add(original_period)

            # Ajouter les données avec des clés spécifiques par période
            combined_data[normalized_period][
                f"total_operations_periode_{period_idx}"
            ] = row.get("total_operations", 0)
            combined_data[normalized_period][
                f"total_consultations_periode_{period_idx}"
            ] = row.get("total_consultations", 0)
            combined_data[normalized_period][
                f"operation_delay_periode_{period_idx}"
            ] = round(row.get("operation_delay", 0), 2)
            combined_data[normalized_period][
                f"consultation_delay_periode_{period_idx}"
            ] = round(row.get("consultation_delay", 0), 2)
            combined_data[normalized_period][
                f"occupation_rate_periode_{period_idx}"
            ] = round(row.get("occupation_rate", 0), 2)
            combined_data[normalized_period][
                f"on_time_patients_periode_{period_idx}"
            ] = row.get("on_time_patients", 0)
            combined_data[normalized_period][f"late_patients_periode_{period_idx}"] = (
                row.get("late_patients", 0)
            )
            combined_data[normalized_period][f"early_patients_periode_{period_idx}"] = (
                row.get("early_patients", 0)
            )

            # Garder aussi les clés originales pour compatibilité (première période seulement)
            if period_idx == 1:
                combined_data[normalized_period]["total_operations"] = row.get(
                    "total_operations", 0
                )
                combined_data[normalized_period]["total_consultations"] = row.get(
                    "total_consultations", 0
                )
                combined_data[normalized_period]["operation_delay"] = round(
                    row.get("operation_delay", 0), 2
                )
                combined_data[normalized_period]["consultation_delay"] = round(
                    row.get("consultation_delay", 0), 2
                )
                combined_data[normalized_period]["occupation_rate"] = round(
                    row.get("occupation_rate", 0), 2
                )
                combined_data[normalized_period]["on_time_patients"] = row.get(
                    "on_time_patients", 0
                )
                combined_data[normalized_period]["late_patients"] = row.get(
                    "late_patients", 0
                )
                combined_data[normalized_period]["early_patients"] = row.get(
                    "early_patients", 0
                )

        # Nettoyer et convertir en liste
        result = []
        for period_data in combined_data.values():
            # Supprimer le set des périodes originales avant la sérialisation
            period_data.pop("original_periods", None)
            result.append(period_data)

        # Trier par période de façon intelligente
        def sort_key(item):
            period = item["period"]
            if "Mois" in period:
                try:
                    month_num = int(period.split("Mois ")[1])
                    return (1, month_num)  # Groupe 1 pour les mois
                except:
                    return (1, 0)
            elif "Semaine" in period:
                try:
                    week_num = int(period.split("Semaine ")[1])
                    return (2, week_num)  # Groupe 2 pour les semaines
                except:
                    return (2, 0)
            elif "Année" in period:
                try:
                    year_num = int(period.split("Année ")[1])
                    return (3, year_num)  # Groupe 3 pour les années
                except:
                    return (3, 0)
            else:
                return (4, period)  # Groupe 4 pour le reste

        return sorted(result, key=sort_key)

    def _generate_comparison_summary(self, intervals_analysis):
        """Génère un résumé comparatif des intervalles"""
        if not intervals_analysis:
            return {}

        summary = {"best_performance": {}, "worst_performance": {}, "trends": {}}

        # Extraire les métriques clés
        metrics = []
        for interval in intervals_analysis:
            metric_data = {
                "label": interval["interval_info"]["label"],
                "total_operations": interval.get("operations", {}).get(
                    "total_operations", 0
                ),
                "total_consultations": interval.get("consultations", {}).get(
                    "total_consultations", 0
                ),
                "operation_delay": interval.get("operations", {})
                .get("vacation_analysis", {})
                .get("average_delay", 0)
                or 0,
                "consultation_delay": interval.get("consultations", {})
                .get("vacation_analysis", {})
                .get("average_consultation_delay", 0)
                or 0,
                "occupation_rate": interval.get("operations", {})
                .get("vacation_analysis", {})
                .get("average_occupation_rate", 0)
                or 0,
            }
            metrics.append(metric_data)

        # Identifier les meilleures et pires performances
        if metrics:
            df_metrics = pd.DataFrame(metrics)

            # Meilleures performances (plus c'est haut, mieux c'est)
            for metric in [
                "total_operations",
                "total_consultations",
                "occupation_rate",
            ]:
                if metric in df_metrics.columns and df_metrics[metric].sum() > 0:
                    best_idx = df_metrics[metric].idxmax()
                    summary["best_performance"][metric] = {
                        "interval": df_metrics.loc[best_idx, "label"],
                        "value": float(df_metrics.loc[best_idx, metric]),
                    }

            # Pour les retards, le meilleur = le plus petit (proche de 0)
            for metric in ["operation_delay", "consultation_delay"]:
                if metric in df_metrics.columns and df_metrics[metric].sum() != 0:
                    best_idx = df_metrics[metric].abs().idxmin()
                    summary["best_performance"][metric] = {
                        "interval": df_metrics.loc[best_idx, "label"],
                        "value": float(df_metrics.loc[best_idx, metric]),
                    }

        return summary


def convert_to_json_serializable(obj):
    """Convert pandas/numpy types to JSON serializable types"""
    if isinstance(obj, dict):
        return {str(k): convert_to_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    elif isinstance(obj, pd.Timestamp):
        return obj.strftime("%Y-%m-%d")
    elif isinstance(obj, (np.integer, np.int64)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64)):
        if np.isnan(obj) or np.isinf(obj):
            return None
        return float(obj)
    elif isinstance(obj, float):
        if obj != obj or obj == float("inf") or obj == float("-inf"):
            return None
        return obj
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif pd.isna(obj):
        return None
    else:
        return obj
