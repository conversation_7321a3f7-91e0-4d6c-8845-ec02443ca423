from rest_framework import permissions
from django.http import JsonResponse, HttpResponse
from rest_framework.views import APIView
from django.db.models import Q
import pandas as pd
import logging
from datetime import datetime
from io import BytesIO
from django.contrib.auth.models import User

from operation.models import Operation, Protocole
from consultation.models import Consultation
from hospitalisation.models import Hospitalisation
from sort.tag.models import FullTag
from accounts.scripts.filter_user import get_operateur

logger = logging.getLogger(__name__)


class AvailableOptionsView(APIView):
    """
    Retourne les options disponibles pour les filtres de recherche
    """

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            operateurs_user = get_operateur(user)

            # Tags disponibles - utiliser l'API des tags comme dans le frontend
            from sort.tag.models import FullTag

            tags = FullTag.objects.values_list("name", flat=True).distinct()

            # Codes CCAM disponibles (depuis les opérations)
            ccam_codes = (
                Operation.objects.filter(zkf_redacteur__in=operateurs_user)
                .values_list("operationdevisccam__acte_ccam", flat=True)
                .distinct()
                .exclude(operationdevisccam__acte_ccam__isnull=True)
            )

            # Protocoles disponibles
            protocols = (
                Protocole.objects.filter(operation__zkf_redacteur__in=operateurs_user)
                .values_list("libelle", flat=True)
                .distinct()
            )

            return JsonResponse(
                {
                    "success": True,
                    "tags": list(tags),
                    "ccam_codes": list(ccam_codes),
                    "protocols": list(protocols),
                }
            )

        except Exception as e:
            logger.error(f"Error in AvailableOptionsView: {str(e)}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)


class QueryDataView(APIView):
    """
    Recherche de données par tags, CCAM et protocoles
    """

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # Récupérer les paramètres
            start_date = request.data.get("start_date")
            end_date = request.data.get("end_date")
            data_type = request.data.get("data_type", "operations")
            tags = request.data.get("tags", [])
            ccam_codes = request.data.get("ccam_codes", [])
            protocols = request.data.get("protocols", [])
            user_id = request.data.get("user_id")

            # Déterminer l'utilisateur
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                except User.DoesNotExist:
                    user = request.user
            else:
                user = request.user

            operateurs_user = get_operateur(user)

            # Parser les dates
            start_dt = (
                datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            )
            end_dt = (
                datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
            )

            if not start_dt or not end_dt:
                return JsonResponse(
                    {"success": False, "error": "Dates de début et de fin requises"},
                    status=400,
                )

            results = []

            if data_type == "operations":
                results = self._query_operations(
                    operateurs_user, start_dt, end_dt, tags, ccam_codes, protocols
                )
            elif data_type == "consultations":
                results = self._query_consultations(
                    operateurs_user, start_dt, end_dt, tags, ccam_codes, protocols
                )
            elif data_type == "hospitalisations":
                results = self._query_hospitalisations(
                    operateurs_user, start_dt, end_dt, tags, ccam_codes, protocols
                )
            elif data_type == "patients":
                results = self._query_patients(
                    operateurs_user, start_dt, end_dt, tags, ccam_codes, protocols
                )

            return JsonResponse(
                {"success": True, "results": results, "count": len(results)}
            )

        except Exception as e:
            logger.error(f"Error in QueryDataView: {str(e)}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)

    def _query_operations(
        self, operateurs_user, start_dt, end_dt, tags, ccam_codes, protocols
    ):
        """Recherche d'opérations"""
        query = Q(
            zkf_redacteur__in=operateurs_user,
            operation_date__gte=start_dt,
            operation_date__lte=end_dt,
        )

        # Filtres par tags
        if tags:
            query &= Q(tags__name__in=tags)

        # Filtres par codes CCAM
        if ccam_codes:
            query &= Q(operationdevisccam__acte_ccam__in=ccam_codes)

        # Filtres par protocoles
        if protocols:
            query &= Q(protocoles__id__in=protocols)

        operations = (
            Operation.objects.filter(query)
            .select_related("zkf_patient", "zkf_redacteur")
            .prefetch_related("operationdevisccam_set", "tags", "protocoles")
            .distinct()
        )

        results = []
        for op in operations:
            # Récupérer les tags
            op_tags = [tag.name for tag in op.tags.all()]

            # Récupérer les codes CCAM
            op_ccam = [
                acte.acte_ccam
                for acte in op.operationdevisccam_set.all()
                if acte.acte_ccam
            ]

            # Récupérer les protocoles
            op_protocols = [
                protocole.libelle
                for protocole in op.protocoles.all()
                if protocole.libelle
            ]

            results.append(
                {
                    "id": str(op.id),
                    "type": "operation",
                    "date": (
                        op.operation_date.isoformat() if op.operation_date else None
                    ),
                    "patient_name": (
                        f"{op.zkf_patient.prenom} {op.zkf_patient.nom}"
                        if op.zkf_patient
                        else None
                    ),
                    "patient_id": str(op.zkf_patient.id) if op.zkf_patient else None,
                    "title": op.titre_operation or "Opération sans titre",
                    "tags": op_tags,
                    "ccam_codes": op_ccam,
                    "protocols": op_protocols,
                    "status": getattr(op, "etat_operation", None),
                }
            )

        return results

    def _query_consultations(
        self, operateurs_user, start_dt, end_dt, tags, ccam_codes=None, protocols=None
    ):
        """Recherche de consultations"""
        query = Q(
            zkf_redacteur__in=operateurs_user,
            consultation_date__gte=start_dt,
            consultation_date__lte=end_dt,
        )

        # Filtres par tags
        if tags:
            query &= Q(tags__name__in=tags)

        consultations = (
            Consultation.objects.filter(query)
            .select_related("zkf_patient", "zkf_redacteur")
            .prefetch_related("tags")
            .distinct()
        )

        results = []
        for consult in consultations:
            # Récupérer les tags
            consult_tags = [tag.name for tag in consult.tags.all()]

            results.append(
                {
                    "id": str(consult.id),
                    "type": "consultation",
                    "date": (
                        consult.consultation_date.isoformat()
                        if consult.consultation_date
                        else None
                    ),
                    "patient_name": (
                        f"{consult.zkf_patient.prenom} {consult.zkf_patient.nom}"
                        if consult.zkf_patient
                        else None
                    ),
                    "patient_id": (
                        str(consult.zkf_patient.id) if consult.zkf_patient else None
                    ),
                    "title": f"Consultation {consult.consultation_type or ''}".strip(),
                    "tags": consult_tags,
                    "ccam_codes": [],  # Les consultations n'ont généralement pas de CCAM
                    "protocols": [],  # Les consultations n'ont généralement pas de protocoles
                    "status": getattr(consult, "etat_consultation", None),
                }
            )

        return results

    def _query_hospitalisations(
        self, operateurs_user, start_dt, end_dt, tags, ccam_codes=None, protocols=None
    ):
        """Recherche d'hospitalisations"""
        query = Q(
            zkf_redacteur__in=operateurs_user,
            entree_date__gte=start_dt,
            entree_date__lte=end_dt,
        )

        # Filtres par tags
        if tags:
            query &= Q(tags__name__in=tags)

        hospitalisations = (
            Hospitalisation.objects.filter(query)
            .select_related("zkf_patient", "zkf_redacteur")
            .prefetch_related("tags")
            .distinct()
        )

        results = []
        for hospit in hospitalisations:
            # Récupérer les tags
            hospit_tags = [tag.name for tag in hospit.tags.all()]

            results.append(
                {
                    "id": str(hospit.id),
                    "type": "hospitalisation",
                    "date": (
                        hospit.entree_date.isoformat() if hospit.entree_date else None
                    ),
                    "patient_name": (
                        f"{hospit.zkf_patient.prenom} {hospit.zkf_patient.nom}"
                        if hospit.zkf_patient
                        else None
                    ),
                    "patient_id": (
                        str(hospit.zkf_patient.id) if hospit.zkf_patient else None
                    ),
                    "title": f"Hospitalisation {hospit.motif_hospitalisation or ''}".strip(),
                    "tags": hospit_tags,
                    "ccam_codes": [],
                    "protocols": [],
                    "status": getattr(hospit, "statut_hospitalisation", None),
                }
            )

        return results

    def _query_patients(
        self,
        operateurs_user,
        start_dt,
        end_dt,
        tags=None,
        ccam_codes=None,
        protocols=None,
    ):
        """Recherche de patients ayant eu des actes dans la période"""
        # Note: tags, ccam_codes, protocols unused for patient queries - patients are found by date range only

        # Récupérer tous les patients ayant eu des opérations, consultations ou hospitalisations
        op_patients = Operation.objects.filter(
            zkf_redacteur__in=operateurs_user,
            operation_date__gte=start_dt,
            operation_date__lte=end_dt,
        ).values_list("zkf_patient", flat=True)

        consult_patients = Consultation.objects.filter(
            zkf_redacteur__in=operateurs_user,
            consultation_date__gte=start_dt,
            consultation_date__lte=end_dt,
        ).values_list("zkf_patient", flat=True)

        hospit_patients = Hospitalisation.objects.filter(
            zkf_redacteur__in=operateurs_user,
            entree_date__gte=start_dt,
            entree_date__lte=end_dt,
        ).values_list("zkf_patient", flat=True)

        all_patient_ids = (
            set(op_patients) | set(consult_patients) | set(hospit_patients)
        )

        from patient.models import Patient

        patients = Patient.objects.filter(id__in=all_patient_ids)

        results = []
        for patient in patients:
            results.append(
                {
                    "id": str(patient.id),
                    "type": "patient",
                    "date": (
                        patient.created_at.date().isoformat()
                        if hasattr(patient, "created_at") and patient.created_at
                        else None
                    ),
                    "patient_name": f"{patient.prenom} {patient.nom}",
                    "patient_id": str(patient.id),
                    "title": f"Patient: {patient.prenom} {patient.nom}",
                    "tags": [],
                    "ccam_codes": [],
                    "protocols": [],
                    "status": "actif",
                }
            )

        return results


class ExportDataView(APIView):
    """
    Export des données en Excel
    """

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # Réutiliser la logique de QueryDataView pour récupérer les données
            query_view = QueryDataView()
            query_response = query_view.post(request)

            if query_response.status_code != 200:
                return query_response

            query_data = query_response.content
            import json

            data = json.loads(query_data)

            if not data.get("success"):
                return query_response

            results = data.get("results", [])

            # Convertir en DataFrame
            if results:
                df = pd.DataFrame(results)

                # Réorganiser les colonnes
                columns_order = [
                    "date",
                    "patient_name",
                    "title",
                    "type",
                    "tags",
                    "ccam_codes",
                    "protocols",
                    "status",
                ]
                df = df.reindex(
                    columns=[col for col in columns_order if col in df.columns]
                )

                # Convertir les listes en chaînes pour Excel
                if "tags" in df.columns:
                    df["tags"] = df["tags"].apply(
                        lambda x: ", ".join(x) if isinstance(x, list) else ""
                    )
                if "ccam_codes" in df.columns:
                    df["ccam_codes"] = df["ccam_codes"].apply(
                        lambda x: ", ".join(x) if isinstance(x, list) else ""
                    )
                if "protocols" in df.columns:
                    df["protocols"] = df["protocols"].apply(
                        lambda x: ", ".join(x) if isinstance(x, list) else ""
                    )
            else:
                # DataFrame vide avec les colonnes appropriées
                df = pd.DataFrame(
                    columns=[
                        "date",
                        "patient_name",
                        "title",
                        "type",
                        "tags",
                        "ccam_codes",
                        "protocols",
                        "status",
                    ]
                )

            # Créer le fichier Excel
            output = BytesIO()
            with pd.ExcelWriter(output, engine="openpyxl") as writer:
                df.to_excel(writer, sheet_name="Données", index=False)

            output.seek(0)

            # Préparer la réponse
            data_type = request.data.get("data_type", "donnees")
            start_date = request.data.get("start_date", "")
            end_date = request.data.get("end_date", "")

            filename = f"export_{data_type}_{start_date}_{end_date}.xlsx"

            response = HttpResponse(
                output.getvalue(),
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            logger.error(f"Error in ExportDataView: {str(e)}")
            return JsonResponse({"success": False, "error": str(e)}, status=500)
