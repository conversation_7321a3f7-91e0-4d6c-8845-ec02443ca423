from django.urls import path

from api.comptabilite.views import (
    OperationDevisFactureCcamList,
    OperationDevisFactureCcamDetail,
    AvoirList,
    AvoirDetail,
    GroupeFacturationList,
    GroupeFacturationDetail,
    RelanceFactureList,
    RelanceFactureDetail,
    DevisList,
    DevisDetail,
    FactureList,
    FactureDetail,
)

# route: api/comptabilite

urlpatterns = [
    path("code", OperationDevisFactureCcamList.as_view(), name="codes-list"),
    path(
        "code/<uuid:pk>/", OperationDevisFactureCcamDetail.as_view(), name="code-detail"
    ),
    path("devis/", DevisList.as_view(), name="devis-list"),
    path("devis/<uuid:pk>/", DevisDetail.as_view(), name="devis-detail"),
    path("facture/", FactureList.as_view(), name="facture-list"),
    path("facture/<uuid:pk>/", FactureDetail.as_view(), name="facture-detail"),
    path("avoir/", AvoirList.as_view(), name="avoir-list"),
    path("avoir/<uuid:pk>/", AvoirDetail.as_view(), name="avoir-detail"),
    path(
        "groupe-facturation/",
        GroupeFacturationList.as_view(),
        name="groupe-facturation-list",
    ),
    path(
        "groupe-facturation/<uuid:pk>/",
        GroupeFacturationDetail.as_view(),
        name="groupe-facturation-detail",
    ),
    path("relance-facture/", RelanceFactureList.as_view(), name="relance-facture-list"),
    path(
        "relance-facture/<uuid:pk>/",
        RelanceFactureDetail.as_view(),
        name="relance-facture-detail",
    ),
]
