from rest_framework import serializers
from operation.models import Devi<PERSON>, OperationDevisCcam
from comptabilite.models import (
    Facture,
    Avoir,
    GroupeFacturation,
    RelanceFacture,
    DevisFavoris,
)
from codage.ccam.models import R_acte


class OperationDevisCcamSerializer(serializers.ModelSerializer):

    class Meta:
        model = OperationDevisCcam
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # The to_representation method is used for all responses (GET, POST, PUT, etc.)
        # We should populate acte_nom_long whenever possible
        if data.get("acte_nom_long") is None and data.get("acte_ccam"):
            try:
                acte = R_acte.objects.get(cod_acte=instance.acte_ccam)
                data["acte_nom_long"] = acte.nom_long
            except R_acte.DoesNotExist:
                # If the CCAM code doesn't exist, leave acte_nom_long as None
                pass
            except Exception as e:
                # Log any other errors but don't break the API
                print(f"Error retrieving acte_nom_long: {str(e)}")
        return data


class DevisSerializer(serializers.ModelSerializer):

    model_app_datas = serializers.SerializerMethodField()

    class Meta:
        model = Devis
        fields = "__all__"

    def get_model_app_datas(self, obj):
        return {
            "model": self.Meta.model.__name__.lower(),
            "app": self.Meta.model._meta.app_label,
        }


class DevisFavorisSerializer(serializers.ModelSerializer):

    class Meta:
        model = DevisFavoris
        fields = "__all__"


class FactureSerializer(serializers.ModelSerializer):

    class Meta:
        model = Facture
        fields = "__all__"


class AvoirSerializer(serializers.ModelSerializer):

    class Meta:
        model = Avoir
        fields = "__all__"


class GroupeFacturationSerializer(serializers.ModelSerializer):

    class Meta:
        model = GroupeFacturation
        fields = "__all__"


class RelanceFactureSerializer(serializers.ModelSerializer):

    class Meta:
        model = RelanceFacture
        fields = "__all__"
