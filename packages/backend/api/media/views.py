from django.forms import ValidationError
from django.http import JsonResponse, FileResponse
from rest_framework import generics
from rest_framework import permissions
from rest_framework.views import APIView
from django.conf import settings

from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView
from operation.models import Operation, CroType, Irradiation, MaterielIrradiation
from consultation.models import Consultation
from operation.models import (
    Operation,
    CroType,
    Irradiation,
    MaterielIrradiation,
    Contraste,
)
from operation.operation_scripts.generate_cro_type import (
    generate_cro_variables,
    # generate_cro_with_variables,
)
from django.shortcuts import get_object_or_404

# script to import all class of a specialite model.
# return exists if there is classes, and the linked class if exists
from operation.operation_scripts.get_operation_data import (
    # get_onetoone_relationship,
    # get_foreign_key_relationship,
    get_foreign_key_relationship_model_operation,
)
from django.template import Context, Template  # render string with arguments
from django.utils.html import format_html
from rest_framework.response import Response
from django.apps import apps
from notification.signals import notification_signal
from api.document.compte_rendu.serializers import CompteRenduSerializer
import os


class MediaServeFile(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, url):
        """Serve a file from the media directory"""
        try:
            file = os.path.join(settings.MEDIA_ROOT, url)

            content_type_dict = {
                "pdf": "application/pdf",
                "doc": "application/msword",
                "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "xls": "application/vnd.ms-excel",
                "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                # Only allow these audio types for serving
                "webm": "audio/webm",
                "ogg": "audio/ogg",
                "mp4": "audio/mp4",
                "x-m4a": "audio/x-m4a",
                "m4a": "audio/mp4",
                "aac": "audio/aac",
                "wav": "audio/wav",
                "mpeg": "audio/mpeg",
                "ppt": "application/vnd.ms-powerpoint",
                "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                "txt": "text/plain",
                "png": "image/png",
                "jpg": "image/jpeg",
                "jpeg": "image/jpeg",
            }

            try:
                f = open(file, mode="rb")
                extension = url.split(".")[-1]
                # Use FileResponse which automatically handles range requests (for audio streaming)
                # and sets Content-Type. 'as_attachment=False' sets Content-Disposition to 'inline'.
                response = FileResponse(
                    f,
                    as_attachment=False,
                    filename=f"Report.{extension}",
                    content_type=content_type_dict.get(extension),
                )
                return response
            except (IOError, FileNotFoundError) as e:
                # Ensure the file is closed if it was opened, then raise error
                if 'f' in locals() and not f.closed:
                    f.close()
                return JsonResponse({"error": "File not found or cannot be read"}, status=404)

        except FileNotFoundError:
            return JsonResponse({"error": "File not found"}, status=404)
