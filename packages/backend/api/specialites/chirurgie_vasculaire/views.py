from rest_framework import generics
from api.specialites.chirurgie_vasculaire.serializers import (
    AtcdChirurgieVasculaireSerializer,
)
from specialites.chirurgie_vasculaire.models import AntecedentsChirurgieVasculaire


class AtcdDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = AntecedentsChirurgieVasculaire.objects.all()
    serializer_class = AtcdChirurgieVasculaireSerializer
    # permission_classes = [permissions.IsAuthenticated]
