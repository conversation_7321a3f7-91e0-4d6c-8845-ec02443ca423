from rest_framework import generics
from rest_framework import permissions
from auditlog.models import LogEntry
from api.auditlog.serializers import AuditLogSerializer
from django.contrib.contenttypes.models import ContentType
from django.apps import apps


class AuditlogList(generics.ListAPIView):
    """
    List all AuditLog entries.
    """

    queryset = LogEntry.objects.all()
    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):
        """
        Restricts the returned AuditLog entries by filtering against
        a query parameter in the URL.
        """
        # Get model name and object ID from the request
        # The `model_name` variable in the provided code is used to retrieve the model class based on
        # the model name provided in the query parameter. It is used to dynamically get the model
        # class using the `apps.get_model()` method by passing the model name in the format
        # 'app_name.model_name'.
        model_name = self.request.query_params.get("model_name", None)
        object_pk = self.request.query_params.get("id", None)
        user = self.request.query_params.get("user", None)

        queryset = LogEntry.objects.all()

        if object_pk is not None and model_name is not None:
            # content_type = ContentType.objects.get(model=model_name.lower())
            model_name = apps.get_model(model_name)  # sous forme 'app_name.model_name'
            content_type = ContentType.objects.get_for_model(model_name)
            queryset = queryset.filter(content_type=content_type, object_pk=object_pk)

        elif user is not None:
            queryset = queryset.filter(actor=user)

        else:
            raise ValueError(
                "Invalid parameters: model_name and object_id or user are required."
            )

        return queryset


class AuditlogDetail(generics.RetrieveAPIView):
    """
    Retrieve a single AuditLog entry.
    """

    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context
