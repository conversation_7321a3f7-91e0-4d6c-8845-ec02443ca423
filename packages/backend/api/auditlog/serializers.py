from auditlog.models import LogEntry
from rest_framework import serializers
from api.settings.serializers import DynamicDepthSerializer
from api.accounts.serializers import UserSerializer
from django.contrib.contenttypes.models import ContentType
from api.patient.serializers import PatientSerializer
from api.operation.serializers import OperationSerializer
from api.consultation.serializers import ConsultationSerializer
from api.hospitalisation.serializers import HospitalisationSerializer

SERIALIZER_MAPPING = {
    "hospitalisation": HospitalisationSerializer,
    "consultation": ConsultationSerializer,
    "patient": PatientSerializer,
    "operation": OperationSerializer,
}


class AuditLogSerializer(DynamicDepthSerializer):
    """
    Serializer for the AuditLog model.
    """

    actor = UserSerializer(many=False, read_only=True)
    object = serializers.SerializerMethodField()

    class Meta:
        model = LogEntry
        exclude = []

    def get_fields(self):
        fields = super().get_fields()
        for field in fields.values():
            field.read_only = True
        return fields

    def get_object(self, obj):
        """
        Get the object associated with the log entry.
        This method retrieves the object associated with the log entry
        and serializes it using the appropriate serializer class.
        """
        # get object by content_type_id and object_pk
        # object = self.content_type_id.model_class()
        content_type = obj.content_type.model_class()
        object_pk = obj.object_pk

        instance = content_type.objects.get(pk=object_pk)

        # Get the serializer
        # serializer_class = SERIALIZER_MAPPING.get(instance.__class__.__name__.lower())

        # if serializer_class:
        #     # Serialize the object
        #     return serializer_class(instance).data
        # else:
        #     return None

        if instance.__class__.__name__.lower() == "operation":
            return f"Operation: {instance.zkf_patient} - {instance.titre_operation} - {instance.operation_date.strftime('%d/%m/%Y')}"
        elif instance.__class__.__name__.lower() == "hospitalisation":
            return f"Hospitalisation: {instance.zkf_patient} - {instance.entree_date.strftime('%d/%m/%Y')} - {instance.motif_hospitalisation}"
        elif instance.__class__.__name__.lower() == "consultation":
            return f"Consultation: {instance.zkf_patient} - {instance.consultation_date.strftime('%d/%m/%Y')} - {instance.motif_consultation}"
        elif instance.__class__.__name__.lower() == "patient":
            return f"Patient: {instance.nom} {instance.prenom} - né le {instance.dob.strftime('%d/%m/%Y')}"
        else:
            return None
