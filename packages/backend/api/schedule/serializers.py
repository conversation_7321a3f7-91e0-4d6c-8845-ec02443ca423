from rest_framework import serializers
from schedule.models import Vacation, <PERSON>rEvent, <PERSON><PERSON><PERSON><PERSON>, UserEventCategory
from accounts.models import Absence, RendezVous
from api.operation.serializers import OperationSerializer


# https://www.linkedin.com/pulse/friday-quick-tip-dynamic-depth-serialization-django-rest-neidinger/
class DynamicDepthSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.Meta.depth = self.context.get("depth", 0)


class VacationSerializer(DynamicDepthSerializer):
    class Meta:
        model = Vacation
        fields = "__all__"


class JourFerieSerializer(DynamicDepthSerializer):
    class Meta:
        model = JourFerie
        fields = "__all__"


class AbsenceSerializer(DynamicDepthSerializer):
    class Meta:
        model = Absence
        fields = "__all__"


class RendezVousSerializer(DynamicDepthSerializer):
    class Meta:
        model = RendezVous
        fields = "__all__"


class AllCalendarItemsSerializer(serializers.Serializer):
    id = serializers.CharField()
    title = serializers.CharField()
    corps = serializers.CharField()
    start = serializers.IntegerField()
    end = serializers.IntegerField()
    type = serializers.CharField()
    zkf_user = serializers.CharField()
    zkf_patient = serializers.DictField(child=serializers.CharField(), required=False)
    # text_color and background_color are optional char fields
    text_color = serializers.CharField(required=False)
    background_color = serializers.CharField(required=False)
    resource_id = serializers.CharField(required=False)
    # operations = serializers.ListField(child=OperationSerializer(), required=False)
    operations = serializers.ListField(allow_empty=True, required=False)
    event_data = serializers.DictField(child=serializers.CharField(), required=False)
    all_day = serializers.BooleanField(default=False)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return representation


class UserEventSerializer(DynamicDepthSerializer):
    class Meta:
        model = UserEvent
        fields = "__all__"


class UserEventCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = UserEventCategory
        fields = "__all__"
