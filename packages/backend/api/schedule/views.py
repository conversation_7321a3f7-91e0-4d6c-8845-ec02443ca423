from datetime import datetime, time, timezone, timedelta
import calendar
from django.conf import settings
import os
from zoneinfo import ZoneInfo
from django.contrib.auth.models import User
from typing import List, Any
from rest_framework import generics
from rest_framework import permissions
from api.schedule.serializers import (
    AbsenceSerializer,
    AllCalendarItemsSerializer,
    RendezVousSerializer,
    VacationSerializer,
    UserEventSerializer,
    UserEventCategorySerializer,
    JourFerieSerializer,
)
from rest_framework.views import APIView
from impression.views import create_html_string
from io import BytesIO
from xhtml2pdf import pisa
import pandas as pd

from api.operation.serializers import OperationSerializer
from api.consultation.serializers import ConsultationSerializer
from schedule.scripts.check_overlap import cancel_vacation_on_leave
from django.forms.models import model_to_dict
from django.http import HttpResponse

# https://github.com/etalab/jours-feries-france/tree/master
from jours_feries_france import JoursFeries

# datetime scripts to find week number in date interval
# https://stackoverflow.com/questions/********/get-the-week-numbers-between-two-dates-with-python
from schedule.scripts.find_week import (
    find_weeks,
    check_semaine_paire_impaire,
    check_interval,
)

from consultation.models import Consultation
from operation.models import Operation
from babylone.django_safedelete_master.safedelete import queryset
from schedule.models import (
    CalendarItem,
    Vacation,
    UserEvent,
    JourFerie,
    UserEventCategory,
)
from accounts.models import Absence, RendezVous
from patient.models import Patient
from django.db.models import Q

from drf_spectacular.utils import extend_schema


class VacationList(generics.ListCreateAPIView):
    queryset = Vacation.objects.all()
    serializer_class = VacationSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        # Get all events
        vacations = self.queryset.filter(deleted=False, zkf_user=self.request.user)
        # filter by user:
        # users = json.loads(request.POST["users"])
        # users = [int(x) for x in users]
        #
        # vacations = queryset.filter(zkf_user__in=users)

        # event_list = []
        #
        # # get all days corresponding of the day of the week in the date interval selected !
        # # https://stackoverflow.com/questions/8153631/js-date-object-to-python-datetime
        # # startDate =
        # # endDate =
        # start = dt.parse(self.request.query_params.get("start", None))
        # end = dt.parse(self.request.query_params.get("end", None))
        #
        # # 1/ get all week numbers from range of dates
        # weeks = find_weeks(start, end)
        #
        # if vacations:
        #     for v in vacations:
        #         # color
        #         color = "grey"
        #         if v.type_vacation == "OPERATION":
        #             color = v.site.operation_schedule_color
        #         elif v.type_vacation == "CONSULTATION":
        #             color = v.site.consultation_schedule_color
        #
        #         # title: N/P + titre operation
        #         title = str(v.type_vacation)
        #
        #         # get all dates:
        #         # 2/ for each week number + year, get the corresponding day, then set the exact date of the event !
        #         # https://stackoverflow.com/questions/38528515/datetime-from-year-and-week-number
        #         for w in weeks:
        #             day = datetime.datetime.strptime(
        #                 str(w.split(",")[0])
        #                 + " "
        #                 + str(w.split(",")[1])
        #                 + " "
        #                 + str(v.jour_semaine),
        #                 "%Y %W %w",
        #             )
        #
        #             # Check if the day is in the start and end period interval (like for vacations...)
        #             # Check if it is une semaine paire ou impaire
        #             if check_semaine_paire_impaire(
        #                 day, v.repetition_semaine
        #             ) and check_interval(day, v.debut_periode, v.fin_periode):
        #                 # ATTENTION TIMEZONES: https://docs.djangoproject.com/fr/3.1/topics/i18n/timezones/
        #                 # https://blog.david-dahan.com/tout-comprendre-des-timezones-dans-django-65b4123838dd
        #                 event_list.append(
        #                     {
        #                         "id": str(v.id),
        #                         "start": datetime.datetime.combine(
        #                             day, v.debut_vacation
        #                         ).strftime("%Y-%m-%d %H:%M:%S"),
        #                         "end": datetime.datetime.combine(
        #                             day, v.fin_vacation
        #                         ).strftime("%Y-%m-%d %H:%M:%S"),
        #                         "title": title,
        #                         # 'all_day': all_day,
        #                         "type": "Vacation",
        #                         "backgroundColor": color,
        #                         # 'overlap': True,
        #                         "rendering": "background",
        #                     }
        #                 )

        # startDate = self.request.query_params.get('start', None)
        # endDate = self.request.query_params.get('end', None)
        # users = self.request.query_params.get('users', None)
        #
        # if startDate and endDate:
        #     try:
        #         # Convert Unix timestamps to timezone-aware datetime objects in UTC
        #         start_datetime = datetime.fromtimestamp(int(startDate), tz=timezone.utc)
        #         end_datetime = datetime.fromtimestamp(int(endDate), tz=timezone.utc)
        #     except ValueError as e:
        #         # Handle parsing errors if the format is incorrect
        #         print(f"Error parsing dates: {e}")
        #         return queryset.none()  # Return an empty queryset
        #
        #     # Filter events that overlap with the given time range
        #     queryset = queryset.filter(
        #         Q(debut_evenement__gte=start_datetime, debut_evenement__lte=end_datetime) |
        #         Q(fin_evenement__gte=start_datetime, fin_evenement__lte=end_datetime) |
        #         Q(debut_evenement__lte=start_datetime, fin_evenement__gte=end_datetime)
        #     )
        #     queryset = queryset.filter(users__in=users).disctinct()

        return vacations

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def perform_create(self, serializer):
        serializer.save(zkf_user=self.request.user)


class VacationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Vacation.objects.all()
    serializer_class = VacationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class UserEventList(generics.ListCreateAPIView):
    queryset = UserEvent.objects.all()
    serializer_class = UserEventSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):
        queryset = UserEvent.objects.all()
        queryset = queryset.filter(
            deleted=False, users__in=[self.request.user]
        ).order_by("-event_start")

        return queryset

    # if startDate and endDate:
    #     try:
    #         # Convert Unix timestamps to timezone-aware datetime objects in UTC
    #         start_datetime = datetime.fromtimestamp(int(startDate), tz=timezone.utc)
    #         end_datetime = datetime.fromtimestamp(int(endDate), tz=timezone.utc)
    #     except ValueError as e:
    #         # Handle parsing errors if the format is incorrect
    #         print(f"Error parsing dates: {e}")
    #         return queryset.none()  # Return an empty queryset

    #     # Filter events that overlap with the given time range
    #     queryset = queryset.filter(
    #         Q(
    #             debut_evenement__gte=start_datetime,
    #             debut_evenement__lte=end_datetime,
    #         )
    #         | Q(fin_evenement__gte=start_datetime, fin_evenement__lte=end_datetime)
    #         | Q(
    #             debut_evenement__lte=start_datetime, fin_evenement__gte=end_datetime
    #         )
    #     )
    #     queryset = queryset.filter(users__in=users).disctinct()

    # return queryset


class UserEventDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = UserEvent.objects.all()
    serializer_class = UserEventSerializer
    permission_classes = [permissions.IsAuthenticated]


class UserEventCategoryList(generics.ListCreateAPIView):
    queryset = UserEventCategory.objects.all()
    serializer_class = UserEventCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None


# class JourFerieList(generics.ListCreateAPIView):
#     queryset = JourFerie.objects.all()
#     serializer_class = JourFerieSerializer
#     # permission_classes = [permissions.IsAuthenticated]

#     def get_queryset(self):
#         queryset = Evenement.objects.all()
#         startDate = self.request.query_params.get("start", None)
#         endDate = self.request.query_params.get("end", None)

#         if startDate and endDate:
#             try:
#                 # Convert Unix timestamps to timezone-aware datetime objects in UTC
#                 start_datetime = datetime.fromtimestamp(int(startDate), tz=timezone.utc)
#                 end_datetime = datetime.fromtimestamp(int(endDate), tz=timezone.utc)
#             except ValueError as e:
#                 # Handle parsing errors if the format is incorrect
#                 print(f"Error parsing dates: {e}")
#                 return queryset.none()  # Return an empty queryset

#             # Filter events that overlap with the given time range
#             queryset = queryset.filter(
#                 Q(debut_ferie__gte=start_datetime, debut_ferie__lte=end_datetime)
#                 | Q(fin_ferie__gte=start_datetime, fin_ferie__lte=end_datetime)
#                 | Q(debut_ferie__lte=start_datetime, fin_ferie__gte=end_datetime)
#             )

#         return queryset


class AbsenceList(generics.ListCreateAPIView):
    queryset = Absence.objects.all()
    serializer_class = AbsenceSerializer
    pagination_class = None

    def get_queryset(self):
        queryset = Absence.objects.all()
        startDate = self.request.query_params.get("start", None)
        endDate = self.request.query_params.get("end", None)
        users = self.request.query_params.get("users", None)

        if startDate and endDate:
            try:
                # Convert Unix timestamps to timezone-aware datetime objects in UTC
                start_datetime = datetime.fromtimestamp(int(startDate), tz=timezone.utc)
                end_datetime = datetime.fromtimestamp(int(endDate), tz=timezone.utc)
            except ValueError as e:
                # Handle parsing errors if the format is incorrect
                print(f"Error parsing dates: {e}")
                return queryset.none()  # Return an empty queryset

            # Filter events that overlap with the given time range
            queryset = queryset.filter(
                Q(
                    date_heure_start__gte=start_datetime,
                    date_heure_start__lte=end_datetime,
                )
                | Q(
                    date_heure_end__gte=start_datetime, date_heure_end__lte=end_datetime
                )
                | Q(
                    date_heure_start__lte=start_datetime,
                    date_heure_end__gte=end_datetime,
                )
            )
            queryset = queryset.filter(zkf_user__in=users)
        else:
            queryset = queryset.filter(zkf_user=self.request.user)
        # print(queryset)
        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class AbsenceDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Absence.objects.all()
    serializer_class = AbsenceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class RendezVousList(generics.ListCreateAPIView):
    queryset = RendezVous.objects.all()
    serializer_class = RendezVousSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = RendezVous.objects.all()
        startDate = self.request.query_params.get("start", None)
        endDate = self.request.query_params.get("end", None)
        users = self.request.query_params.get("users", None)

        if startDate and endDate:
            try:
                # Convert Unix timestamps to timezone-aware datetime objects in UTC
                start_datetime = datetime.fromtimestamp(int(startDate), tz=timezone.utc)
                end_datetime = datetime.fromtimestamp(int(endDate), tz=timezone.utc)
            except ValueError as e:
                # Handle parsing errors if the format is incorrect
                print(f"Error parsing dates: {e}")
                return queryset.none()  # Return an empty queryset

            # Filter events that overlap with the given time range
            queryset = queryset.filter(
                Q(
                    date_heure_debut__gte=start_datetime,
                    date_heure_debut__lte=end_datetime,
                )
                | Q(
                    date_heure_fin__gte=start_datetime, date_heure_fin__lte=end_datetime
                )
                | Q(
                    date_heure_debut__lte=start_datetime,
                    date_heure_fin__gte=end_datetime,
                )
            )
            queryset = queryset.filter(zkf_user__in=users)

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class RendezVousDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = RendezVous.objects.all()
    serializer_class = RendezVousSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class AllCalendarItems(generics.ListAPIView):
    # readonly view for simplified calendar view
    # returns allcalendar items, including vacations, events, public holidays, absences and meetings
    # query params are :
    # - start : unix timestamp
    # - end : unix timestamp
    # - users : user ids separated by commas, json list format
    # gather all items and sort them by date
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = AllCalendarItemsSerializer
    pagination_class = None

    def get_queryset(self):
        # get start and end of the time period
        startDate: Any = self.request.query_params.get("start", None)
        endDate: Any = self.request.query_params.get("end", None)

        # get selectionned users
        users = self.request.query_params.get("users", None)

        # selected users
        if users:
            users = [User.objects.get(id=user_id) for user_id in users.split(",")]

        if startDate and endDate:
            try:
                # Convert Unix timestamps to timezone-aware datetime objects in UTC

                #######
                # ATTENTION: add one day to the start and end date as the week in bigCalendar starts on sunday
                #######

                start_datetime = datetime.fromtimestamp(int(startDate), tz=timezone.utc)
                end_datetime = datetime.fromtimestamp(int(endDate), tz=timezone.utc)

                # get all public holidays for the year. Reverse dict to merge both if year is different
                jourferies = (
                    dict(
                        map(reversed, JoursFeries.for_year(start_datetime.year).items())
                    )
                    if start_datetime.year == end_datetime.year
                    else dict(
                        map(reversed, JoursFeries.for_year(start_datetime.year).items())
                    )
                    | dict(
                        map(reversed, JoursFeries.for_year(end_datetime.year).items())
                    )
                )
                # filter jours feries
                jourferies = {
                    k: v
                    for k, v in jourferies.items()
                    if k >= start_datetime.date() and k <= end_datetime.date()
                }

                vacations = Vacation.objects.filter(
                    deleted=False,
                    zkf_user__in=users,
                )

                user_events = UserEvent.objects.filter(
                    Q(
                        # Case 1: Event starts and ends within range
                        event_start__gte=start_datetime,
                        event_end__lte=end_datetime,
                    )
                    | Q(
                        # Case 2: Event starts within range but ends after
                        event_start__gte=start_datetime,
                        event_start__lte=end_datetime,
                        event_end__gte=end_datetime,
                    )
                    | Q(
                        # Case 3: Event starts before range but ends within
                        event_start__lte=start_datetime,
                        event_end__gte=start_datetime,
                        event_end__lte=end_datetime,
                    )
                    | Q(
                        # Case 4: Event starts before range and ends after (encompasses entire range)
                        event_start__lte=start_datetime,
                        event_end__gte=end_datetime,
                    ),
                    deleted=False,
                    users__in=users,
                ).distinct()

                # for consultations, there is a consultation_date and a consultation_debut_prevu and consultation_fin_prevu
                # so we need to combine them to get the full datetime
                consultations = Consultation.objects.filter(
                    Q(
                        consultation_date__gte=start_datetime,
                        consultation_date__lte=end_datetime,
                    )
                    | Q(
                        consultation_date__lte=start_datetime,
                        consultation_date__gte=end_datetime,
                    ),
                    deleted=False,
                    zkf_redacteur__in=users,
                ).exclude(etat_consultation="annule")

                # same for operations, there is an operation_date and an operation_start and operation_end
                # so we need to combine them to get the full datetime

                # TODO: prefetch related to get the related fields
                operations = Operation.objects.filter(
                    Q(
                        operation_date__gte=start_datetime,
                        operation_date__lte=end_datetime,
                    )
                    | Q(
                        operation_date__lte=start_datetime,
                        operation_date__gte=end_datetime,
                    ),
                    deleted=False,
                    annulation_operation=False,
                    operateur_new__zkf_user__in=users,
                )
            except ValueError as e:
                # Handle parsing errors if the format is incorrect
                # print(f"Error parsing dates: {e}")
                return queryset.none()

        # type of items is AgendaItem
        items: List[CalendarItem] = []
        # then combine, and format the items with the same fields
        # so we can sort them by date
        # fields are: id, title, start, end, type, zkf_user, background_color, text_color, resource_id

        # 1/ get all week numbers from range of dates
        weeks = find_weeks(start_datetime, end_datetime)

        for vacation in vacations:
            color = "grey"
            if vacation.type_vacation == "OPERATION":
                color = vacation.site.operation_schedule_color
            elif vacation.type_vacation == "CONSULTATION":
                color = vacation.site.consultation_schedule_color

            # get all dates:
            # 2/ for each week number + year, get the corresponding day, then set the exact date of the event !
            # https://stackoverflow.com/questions/38528515/datetime-from-year-and-week-number
            for w in weeks:
                day = datetime.strptime(
                    str(w.split(",")[0])
                    + " "
                    + str(w.split(",")[1])
                    + " "
                    + str(vacation.jour_semaine),
                    "%Y %W %w",
                )

                # check if the vacation is during a leave period (absence): if so: return True.
                # event_user_vacations = list()

                # Check if the day is in the start and end period interval (like for vacations...)
                # Check if it is une semaine paire ou impaire
                if check_semaine_paire_impaire(
                    day, vacation.repetition_semaine
                ) and check_interval(day, vacation.debut_periode, vacation.fin_periode):
                    # color = "grey" if True in absences_vacations else color
                    items.append(
                        {
                            "id": vacation.id,
                            "title": str(vacation.type_vacation),
                            "corps": None,
                            # convert to unix timestamp
                            "start": datetime.combine(
                                day, vacation.debut_vacation
                            ).timestamp(),
                            "end": datetime.combine(
                                day, vacation.fin_vacation
                            ).timestamp(),
                            "type": "vacation",
                            "zkf_user": vacation.zkf_user,
                            "background_color": color,
                            "event_data": VacationSerializer(vacation).data,
                            "all_day": False,
                        }
                    )

        for evenement in user_events:
            items.append(
                {
                    "id": evenement.id,
                    "title": evenement.event_title,
                    "corps": evenement.event_note,
                    "start": evenement.event_start.timestamp(),
                    "end": evenement.event_end.timestamp(),
                    "type": "user_event",
                    "zkf_user": evenement.users,
                    "background_color": (
                        evenement.event_category.color
                        if evenement.event_category
                        else "#808080"
                    ),
                    "resource_id": [x.id for x in evenement.users.all()],
                    "event_data": UserEventSerializer(evenement).data,
                    "all_day": False,
                }
            )
        hour_start = datetime.strptime(
            "08:00", "%H:%M"
        ).time()  # or time(10, 12, 30)  #hr/min/sec
        hour_end = datetime.strptime("20:00", "%H:%M").time()

        for date, jourferie in jourferies.items():
            items.append(
                {
                    "id": None,
                    "title": jourferie,
                    "corps": jourferie,
                    "start": datetime.combine(date, hour_start).timestamp(),
                    "end": datetime.combine(date, hour_end).timestamp(),
                    "type": "jourferie",
                    "zkf_user": 2,  # set whatever: it is not used but zkf user is filter as not null in the front
                    "background_color": "#7b7b7b",
                    "event_data": None,
                    "all_day": True,
                    # selectable
                }
            )

        # Exclude foreign key fields (e.g., 'related_model')
        fields_consultation = [
            field.name for field in Consultation._meta.fields if not field.is_relation
        ]
        fields_patient = [
            field.name for field in Patient._meta.fields if not field.is_relation
        ]

        for consultation in consultations:
            if consultation.etat_consultation in ["finie"]:
                text_color = "green"
            elif consultation.etat_consultation in ["non_venu"]:
                text_color = "red"
            else:
                text_color = "black"

            if consultation.premiere_consultation:
                premiere_consultation = "(P)"
            else:
                premiere_consultation = "(S)"
            # if time for the consultation is not set !
            if consultation.consultation_debut_prevu is None:
                consultation.consultation_debut_prevu = datetime.strptime(
                    "00:00", "%H:%M"
                ).time()
                consultation.consultation_fin_prevu = datetime.strptime(
                    "00:15", "%H:%M"
                ).time()
            zkf_patient = model_to_dict(consultation.zkf_patient, fields=fields_patient)
            zkf_patient["id"] = consultation.zkf_patient.id

            items.append(
                {
                    "id": consultation.id,
                    "title": str(consultation.zkf_patient)
                    + " - "
                    + consultation.motif_consultation
                    + " "
                    + premiere_consultation,
                    "corps": None,
                    "start": (
                        datetime.combine(
                            consultation.consultation_date,
                            consultation.consultation_debut_prevu,
                        ).timestamp()
                        if consultation.consultation_debut_prevu
                        else datetime.combine(
                            consultation.consultation_date, time()
                        ).timestamp()
                    ),
                    "end": (
                        datetime.combine(
                            consultation.consultation_date,
                            consultation.consultation_fin_prevu,
                        ).timestamp()
                        if consultation.consultation_fin_prevu
                        else datetime.combine(
                            consultation.consultation_date, time()
                        ).timestamp()
                    ),
                    "type": "Consultation",
                    "zkf_patient": zkf_patient,
                    "zkf_user": consultation.zkf_redacteur,
                    "background_color": consultation.zkf_site.consultation_schedule_color,
                    "text_color": text_color,
                    "event_data": model_to_dict(
                        consultation, fields=fields_consultation
                    ),  # ConsultationSerializer(consultation).data,
                    "resource_id": consultation.zkf_redacteur.id,
                    "all_day": False,
                }
            )

        # Exclude foreign key fields (e.g., 'related_model')
        fields_operation = [
            field.name
            for field in Operation._meta.fields
            if not field.is_relation and field.name != "id"
        ]
        for operation in operations:
            # Get patient data for operation
            operation_patient = model_to_dict(
                operation.zkf_patient, fields=fields_patient
            )
            operation_patient["id"] = operation.zkf_patient.id

            items.append(
                {
                    "id": operation.id,
                    "title": str(operation.zkf_patient)
                    + " - "
                    + operation.titre_operation,
                    "corps": None,
                    # careful, it can be none, so we need to check
                    "start": (
                        operation.operation_start.timestamp()
                        if operation.operation_start
                        else datetime.combine(
                            operation.operation_date, time()
                        ).timestamp()
                    ),
                    # combine date and time,
                    "end": (
                        operation.operation_end.timestamp()
                        if operation.operation_end
                        else datetime.combine(
                            operation.operation_date, time()
                        ).timestamp()
                    ),
                    "type": "Operation",
                    "zkf_patient": operation_patient,  # Use operation specific patient data
                    "zkf_user": (
                        operation.zkf_redacteur if operation.zkf_redacteur else None
                    ),
                    "background_color": (
                        operation.zkf_hospitalisation.service_hospitalisation.site_service.operation_schedule_color
                        if operation.zkf_hospitalisation
                        else None
                    ),
                    "resource_id": (
                        operation.zkf_redacteur.id if operation.zkf_redacteur else None
                    ),
                    "event_data": model_to_dict(operation, fields=fields_operation),
                    "all_day": False,
                }
            )

        # Sort the combined list by date
        items = sorted(items, key=lambda x: x["start"])

        return items


"""
print all events of the current calendar view
"""


class DayEventsPdf(APIView):
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        responses={
            200: {
                "content": {
                    "application/pdf": {
                        "schema": {"type": "string", "format": "binary"}
                    }
                }
            }
        }
    )
    def get(self, request):
        # get start_date and optional end_date in YYYY-MM-DD format
        start_date_str: Any = self.request.query_params.get("start_date", None)
        end_date_str: Any = self.request.query_params.get("end_date", None)
        periodicity: Any = self.request.query_params.get(
            "periodicity", "all"
        )  # am, pm, or all
        # events are consultation, operation, user_event, vacation
        events: Any = self.request.query_params.get("events", None)
        # get selectionned users
        users: Any = self.request.query_params.get("users", None)

        # if no events is given, we get all events
        if not events:
            events = ["consultation", "operation", "user_event", "vacation"]

        # selected users
        if users:
            users = [User.objects.get(id=user_id) for user_id in users.split(",")]

        if start_date_str:
            consultations = Consultation.objects.none()
            operations = Operation.objects.none()

            try:
                # Convert start_date string to datetime
                start_base_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                start_base_date = start_base_date.replace(tzinfo=timezone.utc)

                # If end_date is provided, use it; otherwise use start_date
                if end_date_str:
                    end_base_date = datetime.strptime(end_date_str, "%Y-%m-%d")
                    end_base_date = end_base_date.replace(tzinfo=timezone.utc)
                else:
                    end_base_date = start_base_date

                # Set time ranges based on periodicity
                if periodicity == "am":
                    start_datetime = start_base_date.replace(hour=0, minute=0, second=0)
                    end_datetime = end_base_date.replace(hour=13, minute=0, second=0)
                elif periodicity == "pm":
                    start_datetime = start_base_date.replace(
                        hour=13, minute=0, second=0
                    )
                    end_datetime = end_base_date.replace(hour=23, minute=59, second=59)
                else:  # all day
                    start_datetime = start_base_date.replace(hour=0, minute=0, second=0)
                    end_datetime = end_base_date.replace(hour=23, minute=59, second=59)

                # for consultations, there is a consultation_date and a consultation_debut_prevu and consultation_fin_prevu
                if "consultation" in events:
                    consultations = Consultation.objects.filter(
                        consultation_date__gte=start_base_date,
                        consultation_date__lte=end_base_date,
                        deleted=False,
                        zkf_redacteur__in=users,
                    ).exclude(etat_consultation="annule")

                    # Filter by time of day if periodicity is specified
                    if periodicity in ["am", "pm"]:
                        consultations = consultations.filter(
                            Q(consultation_debut_prevu__gte=start_datetime.time())
                            & Q(consultation_debut_prevu__lt=end_datetime.time())
                        )

                # same for operations
                if "operation" in events:
                    operations = Operation.objects.filter(
                        operation_date__gte=start_base_date,
                        operation_date__lte=end_base_date,
                        deleted=False,
                        annulation_operation=False,
                        operateur_new__zkf_user__in=users,
                    )

                    # Filter by time of day if periodicity is specified
                    if periodicity in ["am", "pm"]:
                        operations = operations.filter(
                            Q(operation_start__gte=start_datetime)
                            & Q(operation_start__lt=end_datetime)
                        )

            except ValueError as e:
                return HttpResponse(
                    f"Invalid date format. Please use YYYY-MM-DD format. Error: {str(e)}",
                    status=400,
                )

            # create a list of items
            items = list()

            for consultation in consultations:
                # if time for the consultation is not set !
                if consultation.consultation_debut_prevu is None:
                    consultation.consultation_debut_prevu = datetime.strptime(
                        "00:00", "%H:%M"
                    ).time()
                    consultation.consultation_fin_prevu = datetime.strptime(
                        "00:15", "%H:%M"
                    ).time()

                items.append(
                    {
                        "id": consultation.id,
                        "patient": consultation.zkf_patient,
                        "title": consultation.motif_consultation,
                        "date": consultation.consultation_date,
                        "date_entree": None,
                        "heure_entree": None,
                        #
                        # Convertir la datetime naïve en timestamp UTC (sans retrait d'heure)
                        # Pourquoi .timestamp() retire une heure parfois ?
                        # Par défaut, .timestamp() considère une datetime naïve comme une datetime en heure locale et applique un décalage lors de la conversion. C'est pourquoi vous perdez une heure si votre machine est configurée en GMT+1.
                        # En utilisant calendar.timegm(), vous évitez ce problème car Python interprète la datetime directement comme UTC.
                        "start": (
                            calendar.timegm(
                                datetime.combine(
                                    consultation.consultation_date,
                                    consultation.consultation_debut_prevu,
                                ).timetuple()
                            )
                            if consultation.consultation_debut_prevu
                            else calendar.timegm(
                                datetime.combine(
                                    consultation.consultation_date, time()
                                ).timetuple()
                            )
                        ),
                        "end": (
                            datetime.combine(
                                consultation.consultation_date,
                                consultation.consultation_fin_prevu,
                            ).timestamp()
                            if consultation.consultation_fin_prevu
                            else datetime.combine(
                                consultation.consultation_date, time()
                            ).timestamp()
                        ),
                        "type": "Consultation",
                        "zkf_user": consultation.zkf_redacteur,
                    }
                )

            for operation in operations:
                # print(operation.operation_start.astimezone(ZoneInfo("Europe/Paris")))
                items.append(
                    {
                        "id": operation.id,
                        "patient": operation.zkf_patient,
                        "title": operation.titre_operation,
                        "date": operation.operation_date,
                        "date_entree": operation.zkf_hospitalisation.entree_date,  # .strftime("%d/%m/%Y") + operation.zkf_hospitalisation.entree_heure.strftime("H:i") if operation.zkf_hospitalisation.entree_date and operation.zkf_hospitalisation.entree_heure else None,
                        "heure_entree": operation.zkf_hospitalisation.entree_heure,
                        "start": (
                            calendar.timegm(
                                operation.operation_start.astimezone(
                                    ZoneInfo("Europe/Paris")
                                ).timetuple()
                            )
                            if operation.operation_start
                            else calendar.timegm(
                                datetime.combine(operation.operation_date, time())
                                .astimezone(ZoneInfo("Europe/Paris"))
                                .timetuple()
                            )
                        ),
                        # combine date and time,
                        "end": (
                            operation.operation_end.timestamp()
                            if operation.operation_end
                            else datetime.combine(
                                operation.operation_date, time()
                            ).timestamp()
                        ),
                        "type": "Operation",
                        "zkf_user": operation.zkf_redacteur,
                    }
                )

        # Sort the combined list by start (or in pandas ?)
        items = sorted(items, key=lambda x: x["start"])

        if not items:
            # If no items found, return empty context
            df = {}
        else:
            # Only create and process DataFrame if we have items
            df = pd.DataFrame(items)
            df["start"] = pd.to_datetime(df["start"], unit="s")
            df["end"] = pd.to_datetime(df["end"], unit="s")
            # https://stackoverflow.com/questions/41998624/how-to-convert-pandas-dataframe-to-nested-dictionary
            # df = df.groupby('date', 'type')[['patient','title', 'start', 'end', 'zkf_user' ]].apply(lambda x: x.set_index('zkf_user').to_dict(orient='records')).to_dict()

            """
            Return a dict with the following structure:
            {Date:
                {User:
                    {Event Type: [Event1, Event2, ...]}
                }
            }

            """

            def convert_to_nested_dict(df):
                nested = {}
                # Iterate over each row
                for _, row in df.iterrows():
                    # Convert the 'end' timestamp to a date string (e.g., "2025-02-20")
                    date_str = row["date"].strftime("%d/%m/%Y")
                    user = row["zkf_user"]
                    event_type = row["type"]

                    # Create an event dictionary – include only the fields you need
                    event = {
                        "id": row["id"],
                        "patient": row["patient"],
                        "title": row["title"],
                        "start": row["start"],
                        "date_entree": row["date_entree"],
                        "heure_entree": row["heure_entree"],
                    }

                    # Initialize nested keys if they don't exist
                    if date_str not in nested:
                        nested[date_str] = {}
                    if user not in nested[date_str]:
                        nested[date_str][user] = {}
                    if event_type not in nested[date_str][user]:
                        nested[date_str][user][event_type] = []

                    # Append the event to the user's list of events
                    nested[date_str][user][event_type].append(event)

                return nested

            df = convert_to_nested_dict(df)

        # Root path for pictures
        # root_path = str(settings.BASE_DIR).split("/")
        # root_path.pop()
        # root_path.append("webapp")
        # root_path = "/".join(root_path)
        # picture = root_path + "/src/assets/img/meiflow-logo.png"

        picture = os.path.join(str(settings.BASE_DIR), "assets/icons/meiflow-logo.png")

        context = {
            "events": df,
            "user": self.request.user,
            "start_program": start_datetime.strftime("%d/%m/%Y"),
            "end_program": end_datetime.strftime("%d/%m/%Y"),
            "logo": picture,
        }

        template = "schedule/pdf/print_current_view.html"
        response = HttpResponse(content_type="application/pdf")
        response["Content-Transfer-Encoding"] = "binary"

        # generate a string (function is imported from impression.views)
        html_string = create_html_string(
            context=context, path=response, template=template
        )

        result = BytesIO()
        pdf = pisa.pisaDocument(BytesIO(html_string.encode("utf-8")), dest=result)

        if not pdf.err:
            return HttpResponse(result.getvalue(), content_type="application/pdf")
        else:
            return HttpResponse("Errors")
