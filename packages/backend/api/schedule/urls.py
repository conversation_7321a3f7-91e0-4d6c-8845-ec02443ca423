from django.urls import path
from api.schedule.views import (
    RendezVousDetail,
    VacationList,
    VacationDetail,
    UserEventList,
    UserEventCategoryList,
    UserEventDetail,
    # JourFerieList,
    AbsenceList,
    AbsenceDetail,
    RendezVousList,
    AllCalendarItems,
    DayEventsPdf,
)

# route = "api/agenda"

urlpatterns = [
    path("items/", AllCalendarItems.as_view(), name="vacations-list"),
    path("vacation/", VacationList.as_view(), name="vacations-list"),
    path("vacation/<uuid:pk>/", VacationDetail.as_view(), name="vacation-detail"),
    # path("event/", EvenementList.as_view(), name="evenements-list"),
    # path("event/<uuid:pk>/", EvenementDetail.as_view(), name="evenement-detail"),
    # path("public-holliday/", JourFerieList.as_view(), name="jourferies-list"),
    path("leave/", AbsenceList.as_view(), name="absences-list"),
    path("leave/<uuid:pk>/", AbsenceDetail.as_view(), name="absence-detail"),
    # path("meeting/", RendezVousList.as_view(), name="rendezvous-list"),
    # path("meeting/<uuid:pk>/", RendezVousDetail.as_view(), name="rendezvous-detail"),
    # path("all/", ScheduleEventsList.as_view(), name="all-events-list"),
    path("user-event/<uuid:pk>/", UserEventDetail.as_view(), name="user-event-detail"),
    path("user-event/", UserEventList.as_view(), name="user-events-list"),
    # user events category list
    path(
        "user-event-category/",
        UserEventCategoryList.as_view(),
        name="user-event-category-list",
    ),
    # day events => generate pdf
    # query params: start_date, end_date, events, users
    path("day-events-pdf/", DayEventsPdf.as_view(), name="day-events-pdf"),
]
