import pytest
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth.models import User
from schedule.models import (
    Absence,
    RendezVous,
    Vacation,
    UserEvent,
    UserEventCategory,
    JourFerie,
)
from operation.models import Operation
from consultation.models import Consultation


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def user(db):
    return User.objects.create_user(username="testuser", password="testpassword")


@pytest.mark.django_db
def test_get_absence_list(api_client, user):
    api_client.force_authenticate(user=user)
    response = api_client.get(reverse("absence-list"))
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_create_absence(api_client, user):
    api_client.force_authenticate(user=user)
    data = {"start_date": "2023-01-01", "end_date": "2023-01-10", "user": user.id}
    response = api_client.post(reverse("absence-list"), data)
    assert response.status_code == status.HTTP_201_CREATED
    assert Absence.objects.count() == 1


@pytest.mark.django_db
def test_get_rendezvous_list(api_client, user):
    api_client.force_authenticate(user=user)
    response = api_client.get(reverse("rendezvous-list"))
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_create_rendezvous(api_client, user):
    api_client.force_authenticate(user=user)
    data = {"date": "2023-01-01", "time": "10:00:00", "user": user.id}
    response = api_client.post(reverse("rendezvous-list"), data)
    assert response.status_code == status.HTTP_201_CREATED
    assert RendezVous.objects.count() == 1


@pytest.mark.django_db
def test_get_vacation_list(api_client, user):
    api_client.force_authenticate(user=user)
    response = api_client.get(reverse("vacation-list"))
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_create_vacation(api_client, user):
    api_client.force_authenticate(user=user)
    data = {"start_date": "2023-01-01", "end_date": "2023-01-10", "user": user.id}
    response = api_client.post(reverse("vacation-list"), data)
    assert response.status_code == status.HTTP_201_CREATED
    assert Vacation.objects.count() == 1


@pytest.mark.django_db
def test_get_userevent_list(api_client, user):
    api_client.force_authenticate(user=user)
    response = api_client.get(reverse("userevent-list"))
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_create_userevent(api_client, user):
    api_client.force_authenticate(user=user)
    data = {"name": "Event 1", "user": user.id}
    response = api_client.post(reverse("userevent-list"), data)
    assert response.status_code == status.HTTP_201_CREATED
    assert UserEvent.objects.count() == 1


@pytest.mark.django_db
def test_get_usereventcategory_list(api_client, user):
    api_client.force_authenticate(user=user)
    response = api_client.get(reverse("usereventcategory-list"))
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_create_usereventcategory(api_client, user):
    api_client.force_authenticate(user=user)
    data = {"name": "Category 1", "user": user.id}
    response = api_client.post(reverse("usereventcategory-list"), data)
    assert response.status_code == status.HTTP_201_CREATED
    assert UserEventCategory.objects.count() == 1


@pytest.mark.django_db
def test_get_jourferie_list(api_client, user):
    api_client.force_authenticate(user=user)
    response = api_client.get(reverse("jourferie-list"))
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_create_jourferie(api_client, user):
    api_client.force_authenticate(user=user)
    data = {"date": "2023-01-01", "name": "New Year"}
    response = api_client.post(reverse("jourferie-list"), data)
    assert response.status_code == status.HTTP_201_CREATED
    assert JourFerie.objects.count() == 1
