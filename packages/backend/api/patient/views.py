from rest_framework import generics
from rest_framework import permissions
from api.patient.serializers import PatientSerializer, CouvertureMaladieSerializer
from api.hospitalisation.serializers import HospitalisationSerializer
from api.consultation.serializers import ConsultationSerializer
from api.schedule.serializers import AllCalendarItemsSerializer
from patient.models import Patient, CouvertureMaladie
from consultation.models import Consultation
from hospitalisation.models import Hospitalisation
from operation.models import Operation
from django.db.models import Q
from datetime import datetime, time
from rest_framework.views import APIView
from rest_framework.response import Response
from django.forms.models import model_to_dict
import requests
from django.conf import settings
import logging
import threading
import asyncio
from asgiref.sync import async_to_sync

# Celery task import
from patient.tasks import (
    perform_ins_lookup_async,
    clear_patient_ins_after_verification,
    reset_patient_ins_status,
)

logger = logging.getLogger(__name__)

# script to search for patients: by DOB, OR name or birth name and first name (separated by a coma !)
from contact.scripts.query_contact import query_patients


# --- Helper function to get INS Auth Token ---
def get_ins_auth_token(struct_type, struct):
    if (
        not settings.INS_IDENTITY_URL
        or not settings.INS_API_USERNAME
        or not settings.INS_API_PASSWORD
    ):
        logger.error("INS Auth: Missing INS API configuration")
        return None
    identity_url = settings.INS_IDENTITY_URL.rstrip("/")
    auth_body = {
        "username": settings.INS_API_USERNAME,
        "password": settings.INS_API_PASSWORD,
        "organization": {"type": struct_type, "value": struct},
    }
    try:
        logger.debug(f"INS Auth: Requesting token for {struct_type}:{struct}")
        auth_resp = requests.post(
            f"{identity_url}/identity/token", json=auth_body, timeout=10
        )
        auth_resp.raise_for_status()
        token = auth_resp.json().get("access_token")
        if not token:
            logger.warning(f"INS Auth: No token received for {struct_type}:{struct}")
            return None
        logger.debug(f"INS Auth: Token obtained for {struct_type}:{struct}")
        return token
    except requests.exceptions.RequestException as e:
        logger.error(
            f"INS Auth: Token request failed for {struct_type}:{struct}: {e}",
            exc_info=True,
        )
        return None


class PatientList(generics.ListCreateAPIView):
    queryset = Patient.objects.all()
    serializer_class = PatientSerializer
    paginate_by = 100
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(
            zkf_environnement_patient=self.request.user.profile.zkf_environnement_user
        )

    # https://www.django-rest-framework.org/api-guide/filtering/
    # also need to define consultation_set to retrieve consultations
    def get_queryset(self):
        user = self.request.user
        queryset = Patient.objects.filter(
            zkf_environnement_patient=user.profile.zkf_environnement_user
        ).order_by("id")

        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class PatientDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Patient.objects.all()
    serializer_class = PatientSerializer

    consultations = ConsultationSerializer(many=True, source="consultation_set")
    hospitalisations = HospitalisationSerializer(
        many=True, source="hospitalisation_set"
    )

    def get_serializer_context(self):
        # get_operations_from_two_years_ago()
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    # def perform_update(self, serializer):
    #     instance = serializer.save()
    #     # update manytomany correspondants
    #     correspondants = self.request.data.get("correspondants", [])
    #     if correspondants:
    #         # Clear existing correspondants and re-add from request data
    #         instance.correspondants.clear()
    #         for correspondant_data in correspondants:
    #             instance.correspondants.add(correspondant_data)

    def retrieve(self, request, *args, **kwargs):
        patient = self.get_object()

        logger.debug(
            f"Retrieved patient: {patient.id}, Current INS Status: {patient.ins_lookup_status}, Checked: {patient.ins_checked}"
        )

        # Check if INS status was NOT_ELIGIBLE_ENV and env might now be eligible
        if patient.ins_lookup_status == Patient.INS_LOOKUP_NOT_ELIGIBLE_ENV:
            env = patient.zkf_environnement_patient
            if env and env.ins_struct_number and env.ins_struct_type:
                logger.info(
                    f"Patient {patient.id} had INS status {Patient.INS_LOOKUP_NOT_ELIGIBLE_ENV}, "
                    f"and environment {env.id} is NOW ELIGIBLE. Resetting INS status to PENDING for re-lookup."
                )
                # This sets status to PENDING and ins_checked to False
                async_to_sync(reset_patient_ins_status)(patient)
                # Refresh patient instance from DB to reflect the reset status immediately
                patient.refresh_from_db(
                    fields=[
                        "ins_lookup_status",
                        "ins_checked",
                        "ins",
                        "ins_oid",
                        "ins_lookup_details",
                    ]
                )

        # Determine if an initial/new INS lookup is needed
        needs_ins_lookup = (
            not patient.ins
            and not patient.ins_checked
            and patient.ins_lookup_status == Patient.INS_LOOKUP_PENDING
        )

        if needs_ins_lookup:
            # Try to atomically set status to IN_PROGRESS to prevent concurrent runs
            updated_rows = Patient.objects.filter(
                pk=patient.id, ins_lookup_status=Patient.INS_LOOKUP_PENDING
            ).update(ins_lookup_status=Patient.INS_LOOKUP_IN_PROGRESS)

            if updated_rows > 0:
                logger.info(
                    f"Patient {patient.id} status PENDING, marked as IN_PROGRESS. Scheduling ASYNC background INS lookup."
                )
                # Update in-memory patient object to reflect the change for the current request context if needed
                patient.ins_lookup_status = Patient.INS_LOOKUP_IN_PROGRESS

                # Run the async task in a separate thread to avoid blocking
                def run_async_task():
                    asyncio.run(perform_ins_lookup_async(patient.id))

                thread = threading.Thread(target=run_async_task, daemon=True)
                thread.start()
            else:
                # This means another concurrent request likely just changed status from PENDING
                logger.info(
                    f"Skipping ASYNC background INS lookup for patient {patient.id}: "
                    f"status no longer PENDING or patient lock failed (current status: {Patient.objects.get(pk=patient.id).ins_lookup_status}). Likely already in progress."
                )
        elif (
            not patient.ins
            and not patient.ins_checked
            and patient.ins_lookup_status == Patient.INS_LOOKUP_IN_PROGRESS
        ):
            logger.info(
                f"Patient {patient.id} INS lookup is already IN_PROGRESS. Not starting a new task."
            )
        elif patient.ins or patient.ins_checked:
            logger.debug(
                f"Skipping ASYNC background INS lookup for patient {patient.id}: already has INS or is checked. "
                f"(INS: {'present' if patient.ins else 'absent'}, Checked: {patient.ins_checked}, Status: {patient.ins_lookup_status})"
            )
        else:  # Other conditions for skipping (e.g. missing patient info, missing env info for lookup)
            logger.debug(
                f"Skipping ASYNC background INS lookup for patient {patient.id}: "
                f"Pre-conditions not fully met for starting a new lookup from PENDING. "
                f"(INS: {'present' if patient.ins else 'absent'}, Checked: {patient.ins_checked}, Status: {patient.ins_lookup_status})"
            )

        serializer = self.get_serializer(patient)
        return Response(serializer.data)


class PatientSearch(generics.ListCreateAPIView):
    queryset = Patient.objects.all()
    serializer_class = PatientSerializer
    # permission_classes = [permissions.IsAuthenticated]

    # https://www.django-rest-framework.org/api-guide/filtering/
    def get_queryset(self):
        """
        Optionally restricts the returned patients by filtering against
        a `query` parameter in the URL.
        """
        queryset = Patient.objects.all()
        # api URL is like /api/patients/?query=Robert
        query = self.request.query_params.get("query", None)

        if query:
            """
            script to search for patient. Filtered by user environnement and linked environnement (=> group by environnement ? TOD0)
            Can we have request parameters in django rest framework?
            """
            queryset = query_patients(self.request, query)

        # Ajouter un ordre explicite pour éviter l'avertissement de pagination
        return queryset.order_by("nom", "prenom")


class PatientConsultations(generics.ListAPIView):
    queryset = Patient.objects.all()
    serializer_class = PatientSerializer
    # permission_classes = [permissions.IsAuthenticated]

    # https://www.django-rest-framework.org/api-guide/filtering/
    # patient uuid is passed as kwarg in the URL as pk
    def get_queryset(self):
        queryset = Patient.objects.all()
        # api URL is like /api/patient/<uuid:pk>/consultation
        patient_uuid = self.kwargs.get("pk", None)

        if patient_uuid:
            # queryset = query_patients(self.request, query)
            queryset = queryset.filter(Q(uuid=patient_uuid))

        return queryset


class PatientEvents(generics.ListAPIView):
    queryset = []
    serializer_class = AllCalendarItemsSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        items = []
        consultations = Consultation.objects.filter(
            zkf_patient=self.kwargs.get("pk", None),
            deleted=False,
        ).exclude(etat_consultation__in=["annule", "non_venu"])

        hospitalisations = Hospitalisation.objects.filter(
            zkf_patient=self.kwargs.get("pk", None), deleted=False
        )
        operations_only = Operation.objects.filter(
            zkf_patient=self.kwargs.get("pk", None), deleted=False
        ).exclude(zkf_hospitalisation__isnull=False)

        # Exclude foreign key fields (e.g., 'related_model')
        fields_consultation = [
            field.name for field in Consultation._meta.fields if not field.is_relation
        ]
        fields_hospitalisation = [
            field.name
            for field in Hospitalisation._meta.fields
            if not field.is_relation
        ]
        fields_operation = [
            field.name for field in Operation._meta.fields if not field.is_relation
        ]

        for consultation in consultations:
            if consultation.consultation_date is None:
                start = end = None
            else:
                start = (
                    datetime.combine(
                        consultation.consultation_date,
                        consultation.consultation_debut_prevu,
                    ).timestamp()
                    if consultation.consultation_debut_prevu
                    else datetime.combine(
                        consultation.consultation_date, time()
                    ).timestamp()
                )

                end = (
                    datetime.combine(
                        consultation.consultation_date,
                        consultation.consultation_fin_prevu,
                    ).timestamp()
                    if consultation.consultation_fin_prevu
                    else datetime.combine(
                        consultation.consultation_date, time()
                    ).timestamp()
                )

            items.append(
                {
                    "id": consultation.id,
                    "title": consultation.motif_consultation,
                    "corps": str(consultation.zkf_site.nom_site)
                    + " "
                    + consultation.courrier_consultation,
                    "start": start,
                    "end": end,
                    "type": "Consultation",
                    "zkf_user": consultation.zkf_redacteur,
                    "background_color": consultation.zkf_site.consultation_schedule_color,
                    "event_data": model_to_dict(
                        consultation, fields=fields_consultation
                    ),
                }
            )

        for hospitalisation in hospitalisations:
            operations = Operation.objects.filter(
                zkf_hospitalisation=hospitalisation, deleted=False
            )
            operation_list = []
            for operation in operations if operations.exists() else []:
                operation_list.append(
                    AllCalendarItemsSerializer(
                        {
                            "id": operation.id,
                            "title": operation.titre_operation,
                            "corps": "<p> Indication:<br>"
                            + operation.indication_operatoire
                            + "</p> <p>"
                            + operation.cro
                            + "</p>",
                            # careful, it can be none, so we need to check
                            "start": (
                                (operation.operation_start).timestamp()
                                if operation.operation_start
                                else (
                                    datetime.combine(
                                        operation.operation_date, time()
                                    ).timestamp()
                                    if operation.operation_date
                                    else None
                                )
                            ),
                            # combine date and time,
                            "end": (
                                (operation.operation_end).timestamp()
                                if operation.operation_end
                                else (
                                    datetime.combine(
                                        operation.operation_date, time()
                                    ).timestamp()
                                    if operation.operation_date
                                    else None
                                )
                            ),
                            "type": "Operation",
                            "zkf_user": operation.zkf_redacteur,
                            "background_color": operation.zkf_hospitalisation.service_hospitalisation.site_service.operation_schedule_color,
                            "event_data": model_to_dict(
                                operation, fields=fields_operation
                            ),
                        }
                    ).data
                )
            items.append(
                {
                    "id": hospitalisation.id,
                    "title": hospitalisation.motif_hospitalisation,
                    "corps": str(hospitalisation.service_hospitalisation.site_service)
                    + " "
                    + hospitalisation.crh,
                    "start": (
                        datetime.combine(
                            hospitalisation.entree_date, hospitalisation.entree_heure
                        ).timestamp()
                        if hospitalisation.entree_date and hospitalisation.entree_heure
                        else (
                            datetime.combine(
                                hospitalisation.entree_date, time()
                            ).timestamp()
                            if hospitalisation.entree_date
                            else None
                        )
                    ),
                    "end": (
                        datetime.combine(
                            hospitalisation.sortie_date, time()
                        ).timestamp()
                        if hospitalisation.sortie_date
                        else None
                    ),
                    "type": "Hospitalisation",
                    "zkf_user": hospitalisation.zkf_redacteur,
                    "background_color": hospitalisation.service_hospitalisation.site_service.operation_schedule_color,
                    "operations": operation_list,  # hospitalisation.operation_set.all(),
                    "event_data": model_to_dict(
                        hospitalisation, fields=fields_hospitalisation
                    ),
                }
            )

        if operations_only.exists():
            for operation in operations_only:
                items.append(
                    {
                        "id": operation.id,
                        "title": operation.titre_operation,
                        "corps": "<p>"
                        + operation.indication_operatoire
                        + "</p> <p>"
                        + operation.cro
                        + "</p>",
                        "start": (
                            operation.operation_start.timestamp()
                            if operation.operation_start
                            else datetime.combine(
                                operation.operation_date, time()
                            ).timestamp()
                        ),
                        "end": (
                            operation.operation_end.timestamp()
                            if operation.operation_end
                            else datetime.combine(
                                operation.operation_date, time()
                            ).timestamp()
                        ),
                        "type": "Operation",
                        "zkf_user": operation.zkf_redacteur,
                        "background_color": operation.zkf_hospitalisation.service_hospitalisation.site_service.operation_schedule_color,
                        "event_data": model_to_dict(operation, fields=fields_operation),
                    }
                )
        # sort by start date
        items = sorted(
            items, key=lambda d: (d["start"] is None, d["start"]), reverse=True
        )
        return items


# list of all Protections sociales (Couverture Maladie)
class CouvertureMaladieList(generics.ListCreateAPIView):
    queryset = CouvertureMaladie.objects.all()
    serializer_class = CouvertureMaladieSerializer
    pagination_class = None
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        return queryset


class PatientModelFields(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        exclude = [
            "id",
            "created_on",
            "created_by",
            "modified_on",
            "modified_by",
            "deleted",
            "deleted_on",
            "deleted_by",
            "zkf_environnement_patient",
            "zkf_assurance_sante",
        ]
        models_fields = list()
        # get all fields of a model + demographic datas !
        fields = [
            x.name
            for x in Patient._meta.get_fields()
            if (
                not x.many_to_many
                and not x.one_to_many
                and not x.one_to_one
                and x.name not in exclude
            )  # and not x.many_to_one) ]
        ]

        fields.append('dob|date:"d/m/Y"')
        # fields.pop('dob')

        models_fields.append(
            {
                "fields": fields,
                "nom": Patient._meta.model_name,
                "model": Patient._meta.model_name.lower(),
                "description": "description " + Patient._meta.model_name.lower(),
            }
        )
        models_fields.append(
            {
                "fields": ["titre_operation", 'operation_date|date:"d/m/Y"'],
                "nom": "Operation",
                "model": "operation",
                "description": "description ",
            }
        )
        models_fields.append(
            {
                "fields": ['now "d/m/Y"'],
                "nom": "date du jour",
                "model": "",
                "description": "date du jour",
            }
        )
        return Response(models_fields)


# Separate View for INS Verification
class PatientVerifyInsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk, *args, **kwargs):
        try:
            # Need select_related here too if accessing environment directly
            patient = Patient.objects.select_related("zkf_environnement_patient").get(
                pk=pk
            )
        except Patient.DoesNotExist:
            return Response({"error": "Patient not found."}, status=404)

        if not settings.INS_IDENTITY_URL or not settings.INS_SEARCH_URL:
            logger.error("INS Verification: Missing INS API configuration")
            return Response({"error": "INS API not available"}, status=503)

        logger.info(f"Starting INS verification for patient {patient.id}")

        if not patient.ins:
            # No need to save status, just inform user
            logger.warning(
                f"INS Verification: Patient {patient.id} has no INS to verify."
            )
            return Response({"error": "Patient has no INS value."}, status=400)

        struct = patient.zkf_environnement_patient.ins_struct_number
        struct_type = patient.zkf_environnement_patient.ins_struct_type

        if not struct or not struct_type:
            logger.warning(
                f"INS Verification: Missing struct info for patient {patient.id}'s environment."
            )
            return Response(
                {"error": "Environment structure information missing."}, status=400
            )

        # Use the synchronous token helper here as we are in a sync view
        token = get_ins_auth_token(struct_type, struct)
        if not token:
            # Don't save status, just return error
            return Response({"error": "Failed to obtain INS auth token."}, status=500)

        headers = {"Authorization": f"Bearer {token}"}
        search_url = settings.INS_SEARCH_URL.rstrip("/")
        verify_url = f"{search_url}/ins/verify"

        temp_oid = "urn:oid:*********.*********"
        identifier_use = "temp" if patient.ins_oid == temp_oid else "official"
        logger.debug(
            f"INS Verification: Using identifier use='{identifier_use}' based on OID {patient.ins_oid}"
        )

        payload = {
            "name": {"family": patient.nom_naissance, "given": patient.prenom},
            "gender": patient.sexe.lower(),
            "birthDate": patient.dob.isoformat(),
            "identifier": [{"use": identifier_use, "value": patient.ins}],
        }
        if patient.birth_place_code:
            payload["birthPlace"] = {"frInseeCode": {"code": patient.birth_place_code}}

        try:
            logger.debug(
                f"INS Verification: Calling verify endpoint for patient {patient.id}"
            )
            # Using sync requests here
            resp = requests.post(verify_url, json=payload, headers=headers, timeout=15)
            resp.raise_for_status()
            verification_result = resp.json()
            logger.info(
                f"INS Verification successful for patient {patient.id}. Result: {verification_result}"
            )

            if not verification_result:
                logger.warning(
                    f"INS Verification returned false for patient {patient.id}. Clearing INS."
                )
                # Use async_to_sync to call the async helper from sync view
                async_to_sync(clear_patient_ins_after_verification)(patient)

            # Return the original true/false result from the API
            return Response(verification_result, status=200)

        except requests.exceptions.Timeout:
            logger.error(f"INS Verification: Timeout for patient {patient.id}")
            # Don't save status, just return error
            return Response({"error": "Verification request timed out."}, status=504)
        except requests.exceptions.HTTPError as e:
            logger.error(
                f"INS Verification: HTTP error for patient {patient.id} - {e.response.status_code}: {e.response.text}"
            )
            try:
                error_detail = e.response.json()
            except ValueError:
                error_detail = e.response.text
            # Don't save status, just return error
            return Response(
                {"error": "Verification failed", "details": error_detail},
                status=e.response.status_code,
            )
        except requests.exceptions.RequestException as e:
            logger.error(
                f"INS Verification: Request failed for patient {patient.id}: {e}",
                exc_info=True,
            )
            # Don't save status, just return error
            return Response({"error": f"Verification request failed: {e}"}, status=500)
        except Exception as e:
            logger.error(
                f"INS Verification: Unexpected error for patient {patient.id}: {e}",
                exc_info=True,
            )
            # Don't save status, just return error
            return Response(
                {"error": "An unexpected error occurred during verification."},
                status=500,
            )


# Separate View for INS Reset
class PatientResetInsView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk, *args, **kwargs):
        try:
            patient = Patient.objects.get(pk=pk)
        except Patient.DoesNotExist:
            return Response({"error": "Patient not found."}, status=404)

        logger.info(f"Resetting INS fields for patient {patient.id} via API endpoint.")
        # Use async_to_sync to call the async helper
        async_to_sync(reset_patient_ins_status)(patient)
        return Response(status=204)  # Return No Content
