# get all operations from 2 years ago with title, acte ccam, date, operateur and irradiation

import datetime
from django.utils import timezone
from operation.models import Operation
import pandas as pd
from django.http import HttpResponse


def get_operations_from_two_years_ago():
    two_years_ago = timezone.now() - datetime.timedelta(days=2 * 365)
    operations = Operation.objects.filter(operation_date__year__gte=two_years_ago.year)

    values = [
        "titre_operation",
        "operation_date",
        "irradiation__pds_unite",
        "irradiation__air_kerma_total",
        "irradiation__air_kerma_unite",
        "irradiation__air_kerma_total",
        "irradiation__pds_scopie",
        "irradiation__air_kerma_scopie",
        "irradiation__duree_scopie_scopie",
        "irradiation__pds_acquisition",
        "irradiation__air_kerma_acquisition",
        "irradiation__duree_scopie_acquisition",
        "irradiation__duree_scopie_total",
        "irradiation__volume_iode",
    ]

    operations = operations.values_list(*values)
    df = pd.DataFrame(operations)
    # print ( df.head() )
    # export in excel
    df.columns = values
    df.to_excel("irradiation.xlsx")

    # return operations
