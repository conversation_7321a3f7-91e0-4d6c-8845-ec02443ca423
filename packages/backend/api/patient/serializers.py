from rest_framework import serializers
from patient.models import Patient, CouvertureMaladie
from api.sort.tag.serializers import TagListSerializerField, TaggitSerializer
import unicodedata
from six import text_type
from patient.tasks import reset_patient_ins_status
from asgiref.sync import async_to_sync
import logging
from api.contact.serializers import CorrespondantPatientSerializer
from contact.models import CorrespondantPatient

logger = logging.getLogger(__name__)


class DynamicDepthSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.Meta.depth = self.context.get("depth", 0)


def strip_accents(text):
    try:
        text = text_type(text)
    except NameError:  # unicode is a default on python 3
        pass

    text = unicodedata.normalize("NFD", text).encode("ascii", "ignore").decode("utf-8")

    return str(text)


def remove_tiret(text):
    text = text.split("-")
    text = " ".join(text)
    return text


def clean_text(text):
    text = strip_accents(text)
    return text


# Serializer for CouvertureMaladie/linked to patient
class CouvertureMaladieSerializer(serializers.ModelSerializer):
    class Meta:
        model = CouvertureMaladie
        fields = "__all__"


class PatientSerializer(TaggitSerializer, DynamicDepthSerializer):
    tags = TagListSerializerField(required=False)
    reset_ins_confirmation = serializers.BooleanField(
        write_only=True,
        required=False,
        default=False,
    )
    model_app_datas = serializers.SerializerMethodField()

    correspondants = CorrespondantPatientSerializer(
        source="correspondantpatient_set",  # référence inverse automatique générée par Django
        many=True,
        read_only=True,  # read_only pour la sérialisation, mais on traite manuellement le champ dans update()
    )

    def create(self, validated_data):
        patient = Patient.objects.filter(
            nom=validated_data["nom"],
            prenom=validated_data["prenom"],
            dob=validated_data["dob"],
        )

        if patient.exists():
            raise serializers.ValidationError(
                {
                    "error": "PATIENT_ALREADY_EXISTS",
                    "patient": PatientSerializer(patient.first()).data,
                },
            )

        # Remove reset_ins_confirmation before creating the patient model instance
        validated_data.pop("reset_ins_confirmation", None)
        # remove contacts
        correspondants_list = self.context.get('request').data.get("correspondants", []) if self.context.get('request') else []
        patient = super().create(validated_data)

        # Create correspondants if provided
        for contact in correspondants_list:
            CorrespondantPatient.objects.create(patient=patient, contact=contact)
        return patient

    def update(self, instance, validated_data):
        reset_ins_confirmation = validated_data.pop("reset_ins_confirmation", False)
        restricted_fields = [
            "nom_naissance",
            "prenom",
            "dob",
            "sexe",
            "birth_place_code",
        ]
        # Check if any restricted field's VALUE has actually changed
        restricted_fields_changed = any(
            key in validated_data and getattr(instance, key) != validated_data[key]
            for key in restricted_fields
        )

        if restricted_fields_changed:
            current_status = instance.ins_lookup_status
            is_success = current_status == Patient.INS_LOOKUP_SUCCESS

            if is_success:
                if not reset_ins_confirmation:
                    raise serializers.ValidationError(
                        {
                            "non_field_errors": [
                                "Modification des informations d'identité (nom de naissance, prénom, date de naissance, sexe, code lieu naissance) impossible car l'INS a été vérifié. "
                                "Veuillez cocher la case de confirmation pour forcer la modification et réinitialiser le statut INS."
                            ]
                        }
                    )
                else:  # INS is SUCCESS and confirmation is given
                    logger.info(
                        f"INS-related fields changed for patient {instance.id} (status: SUCCESS) with confirmation. Resetting INS status."
                    )
                    async_to_sync(reset_patient_ins_status)(instance)
            # For updates, if it's not SUCCESS, any restricted field change should reset it.
            elif not instance.ins or current_status != Patient.INS_LOOKUP_SUCCESS:
                logger.info(
                    f"INS-related fields changed for patient {instance.id} (INS value: {instance.ins}, Status: {current_status}). Resetting INS status automatically as current status is not SUCCESS or INS is missing."
                )
                async_to_sync(reset_patient_ins_status)(instance)

        # duplicate check logic
        if all(
            key in validated_data for key in ("nom", "prenom", "dob")
        ):  # check if keys are in the data
            if (
                validated_data["nom"] != instance.nom
                or validated_data["prenom"] != instance.prenom
                or validated_data["dob"] != instance.dob
            ):
                patient = Patient.objects.filter(
                    nom=validated_data["nom"],
                    prenom=validated_data["prenom"],
                    dob=validated_data["dob"],
                )
                if patient.exists():
                    raise serializers.ValidationError(
                        {
                            "error": "PATIENT_ALREADY_EXISTS",
                            "patient": PatientSerializer(patient.first()).data,
                        },
                    )

        # Mise à jour des correspondants
        correspondants = (
            self.context.get("request").data if self.context.get("request") else {}
        )
        correspondants = correspondants.get("correspondants", [])

        if correspondants:
            try:
                # Extraire les IDs de contacts et préparer les données
                contact_ids = []
                correspondants_data = []

                for item in correspondants:
                    contact_id = None
                    send_report = True  # valeur par défaut
                    referral_source = False  # valeur par défaut

                    # Gérer les différents formats possibles
                    if isinstance(item, dict):
                        # Vos données utilisent la clé 'contact'
                        contact_id = item.get("contact")
                        send_report = item.get("send_report", True)
                        referral_source = item.get("referral_source", False)

                    elif isinstance(item, str) and item:
                        # Si c'est juste une chaîne (ID), l'utiliser directement
                        contact_id = item

                    if contact_id:
                        contact_ids.append(contact_id)
                        correspondants_data.append(
                            {
                                "contact_id": contact_id,
                                "send_report": send_report,
                                "referral_source": referral_source,
                            }
                        )

                if contact_ids:
                    # Supprimer les correspondants qui ne sont plus dans la nouvelle liste
                    deleted_count = instance.correspondantpatient_set.exclude(
                        contact_id__in=contact_ids
                    ).delete()[0]

                    # Créer ou mettre à jour les correspondants
                    for data in correspondants_data:

                        # Vérifier d'abord si l'objet existe
                        existing = CorrespondantPatient.objects.filter(
                            patient=instance, contact_id=data["contact_id"]
                        ).first()

                        if existing:
                            existing.send_report = data["send_report"]
                            existing.referral_source = data["referral_source"]
                            existing.save()
                        else:
                            correspondant_patient = CorrespondantPatient.objects.create(
                                patient=instance,
                                contact_id=data["contact_id"],
                                send_report=data["send_report"],
                                referral_source=data["referral_source"],
                            )
                else:
                    print("ATTENTION: Aucun ID de contact valide trouvé dans la liste")
                    # Optionnel : supprimer tous les correspondants si la liste est vide
                    # instance.correspondantpatient_set.all().delete()

            except Exception as e:
                print(f"Erreur lors de la mise à jour des correspondants: {e}")
                # Optionnel : re-lever l'exception ou la logger proprement
                # raise

        return super().update(instance, validated_data)

    class Meta:
        model = Patient
        exclude = ["zkf_environnement_patient"]
        read_only_fields = ()

    def validate_nom(self, value):
        value = clean_text(value.title())
        return value

    def validate_prenom(self, value):
        value = clean_text(value.title())
        return value

    def validate_nom_naissance(self, value):
        value = clean_text(value.title())
        return value

    def get_model_app_datas(self, obj):
        model_app_datas = {
            "model": self.Meta.model.__name__.lower(),
            "app": self.Meta.model._meta.app_label,
        }
        return model_app_datas

    def to_internal_value(self, data):
        # If birth_place_code is provided as an empty string, convert it to None
        if "birth_place_code" in data and data["birth_place_code"] == "":
            data["birth_place_code"] = None
        return super().to_internal_value(data)
