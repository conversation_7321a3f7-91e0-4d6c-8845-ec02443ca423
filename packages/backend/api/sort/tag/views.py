from rest_framework import generics
from api.sort.tag.serializers import FullTagSerializer
from sort.tag.models import FullTag


class TagList(generics.ListCreateAPIView):
    queryset = FullTag.objects.all()
    serializer_class = FullTagSerializer
    # permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        queryset = FullTag.objects.all()

        if self.request.query_params.get("filter_user"):
            queryset = queryset.filter(user__in=[self.request.user])

        return queryset
