import json

# Third party
from django.utils.translation import gettext_lazy
from rest_framework import serializers
from sort.tag.models import FullTag, TagEveryWhere


class FullTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = FullTag
        fields = "__all__"


class TagList(list):
    """
    This tag list subclass adds pretty printing support to the tag list
    serializer
    """

    def __init__(self, *args, **kwargs):
        pretty_print = kwargs.pop("pretty_print", True)
        super().__init__(*args, **kwargs)
        self.pretty_print = pretty_print

    def __add__(self, rhs):
        return TagList(super().__add__(rhs))

    def __getitem__(self, item):
        result = super().__getitem__(item)
        try:
            return TagList(result)
        except TypeError:
            return result

    def __str__(self):
        if self.pretty_print:
            return json.dumps(self, sort_keys=True, indent=4, separators=(",", ": "))
        else:
            return json.dumps(self)


class TagListSerializerField(serializers.ListField):
    """
    A serializer field that can write out a tag list

    This serializer field has some odd qualities compared to just using a ListField.
    If this field poses problems, we should introduce a new field that is a simpler
    ListField implementation with less features.
    """

    child = serializers.CharField()
    default_error_messages = {
        "not_a_list": gettext_lazy(
            'Expected a list of items but got type "{input_type}".'
        ),
        "invalid_json": gettext_lazy(
            "Invalid json list. A tag list submitted in string"
            " form must be valid json."
        ),
        "not_a_str": gettext_lazy("All list items must be of string type."),
    }
    order_by = None

    def __init__(self, **kwargs):
        pretty_print = kwargs.pop("pretty_print", True)

        style = kwargs.pop("style", {})
        kwargs["style"] = {"base_template": "textarea.html"}
        kwargs["style"].update(style)

        super().__init__(**kwargs)

        self.pretty_print = pretty_print

    def to_internal_value(self, value):
        # note to future maintainers: this field used to not be a ListField
        # and has extra behavior to support string-based input.
        #
        # In the future we should look at removing this feature so we can
        # make this a simple ListField (if feasible)

        if isinstance(value, str):
            if not value:
                value = "[]"
            try:
                value = json.loads(value)
            except ValueError:
                self.fail("invalid_json")

        if not isinstance(value, list):
            self.fail("not_a_list", input_type=type(value).__name__)

        for s in value:
            if not isinstance(s, str):
                self.fail("not_a_str")
            # run_validation is called on the child field (<=> TaggitSerializer)
            self.child.run_validation(s)

        return value

    def to_representation(self, value):
        if not isinstance(value, TagList):
            if not isinstance(value, list):
                if self.order_by:
                    tags = value.all().order_by(*self.order_by)
                else:
                    tags = value.all()
                value = [tag.name for tag in tags]
                # print (f"Value to representation 2: {value}")
            value = TagList(value, pretty_print=self.pretty_print)

        return value


class TaggitSerializer(serializers.Serializer):
    def create(self, validated_data):
        user = self.context["request"].user
        tag_names = validated_data.pop("tags") if "tags" in validated_data else None
        instance = super(TaggitSerializer, self).create(validated_data)
        if tag_names:
            tags = []
            for name in tag_names:
                tag, created = FullTag.objects.get_or_create(
                    name=name, specialite=user.profile.specialite_active
                )
                if created is True:
                    # add user
                    tag.user.add(user)
                    # add mtom specialite
                    tag.specialites.add(user.profile.specialite_active)
                else:
                    # Check if user is already associated with the tag
                    if not tag.user.filter(id=user.id).exists():
                        tag.user.add(user)
                    # Check if specialite is already associated with the tag
                    if not tag.specialites.filter(
                        id=user.profile.specialite_active.id
                    ).exists():
                        tag.specialites.add(user.profile.specialite_active)

                tags.append(tag)

            instance.tags.set(tags)
        return instance

    def update(self, instance, validated_data):
        user = self.context["request"].user
        tag_names = validated_data.pop("tags") if "tags" in validated_data else None
        instance = super(TaggitSerializer, self).update(instance, validated_data)
        if tag_names is not None:
            tags = []
            for name in tag_names:
                tag, created = FullTag.objects.get_or_create(
                    name=name, specialite=user.profile.specialite_active
                )
                if created:
                    # add user
                    tag.user.add(user)
                    # add mtom specialite
                    tag.specialites.add(user.profile.specialite_active)
                else:
                    # Check if user is already associated with the tag
                    if not tag.user.filter(id=user.id).exists():
                        tag.user.add(user)
                    # Check if specialite is already associated with the tag
                    if not tag.specialites.filter(
                        id=user.profile.specialite_active.id
                    ).exists():
                        tag.specialites.add(user.profile.specialite_active)

                tags.append(tag)

            instance.tags.set(tags, clear=True)
        return instance

    def _save_tags(self, tag_object, tags):
        for key in tags.keys():
            tag_values = tags.get(key)
            getattr(tag_object, key).set(tag_values)

        return tag_object

    def _pop_tags(self, validated_data):
        to_be_tagged = {}

        for key in self.fields.keys():
            field = self.fields[key]
            if isinstance(field, TagListSerializerField):
                if key in validated_data:
                    to_be_tagged[key] = validated_data.pop(key)

        return (to_be_tagged, validated_data)


class TagEveryWhereSerializer(serializers.ModelSerializer):

    tag = FullTagSerializer()
    zkf_tag = serializers.UUIDField(source="tag")
    # name = serializers.SerializerMethodField()
    model = serializers.SerializerMethodField()

    class Meta:
        model = TagEveryWhere
        # fields = "__all__"
        exclude = ["id"]

    # def get_name(self, instance):
    #     try:
    #         name ={
    #             "Operation" : "titre_operation",
    #             "Consultation" : "motif_consultation",
    #             "Hospitalisation" : "motif_hospitalisation"
    #         }
    #         print (instance.content_object.name[instance.content_object.__class__.__name__])
    #         return instance.content_object.name[instance.content_object.__class__.__name__]
    #     except:
    #         return None

    def get_model(self, instance):
        try:
            return instance.content_object.__class__.__name__
        except:
            return None
