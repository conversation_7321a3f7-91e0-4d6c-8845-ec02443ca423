from rest_framework import generics
from api.antecedent.serializers import AtcdSerializer
from patient.models import Patient

from django.apps import apps
from django.shortcuts import get_object_or_404
from rest_framework.response import Response
from rest_framework import status
from django.forms.models import model_to_dict  # convert an instance to a dictionary
import datetime


class AtcdDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = None
    serializer_class = AtcdSerializer

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # define context in which atcd are displayed or edited
        self.context_dict = {
            "consultation": "zkf_consultation",
            "hospitalisation": "zkf_hospitalisation",
            "staff": "zkf_staff",
        }

    def get_queryset(self):
        specialite = "_".join(self.kwargs.get("specialite").lower().split(" "))
        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )
        # https://stackoverflow.com/questions/71800312/django-get-models-by-model-name
        model = apps.get_model(app_label=specialite, model_name=model_specialite)

        return model.objects.all()

    def get_object(self):
        """
        Returns the object the view is displaying.

        You may want to override this if you need to provide non-standard
        queryset lookups.  Eg if objects are referenced using multiple
        keyword arguments in the url conf.
        """
        queryset = self.filter_queryset(self.get_queryset())

        # if there is a context passed in the url (consultation, hospitalisation, staff...) => means that atcd are edited or displayed in that context !
        if "context" in self.request.GET:
            zkf_context = self.context_dict[self.request.GET["context"]]
            filter_kwargs = {zkf_context: self.request.GET["id"]}

        # else: atcd are displayed in the patient context (not possible to edit them)
        else:
            filter_kwargs = {"zkf_patient": self.kwargs["patient_id"]}

        try:
            obj = queryset.get(**filter_kwargs)
        except queryset.model.DoesNotExist:
            obj = None

        # May raise a permission denied
        self.check_object_permissions(self.request, obj)

        return obj

    # dynamically set serializer model for AtcdSerializer !
    # https://stackoverflow.com/questions/30831731/create-a-generic-serializer-with-a-dynamic-model-in-meta
    def get_serializer_class(self):
        specialite = "_".join(self.kwargs.get("specialite").lower().split(" "))

        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )
        # https: // stackoverflow.com / questions / 71800312 / django - get - models - by - model - name
        model = apps.get_model(app_label=specialite, model_name=model_specialite)
        AtcdSerializer.Meta.model = model
        return AtcdSerializer

    # group ATCD when request is options
    # override options
    def options(self, request, *args, **kwargs):
        specialite = "_".join(self.kwargs.get("specialite").lower().split(" "))

        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )
        # https: // stackoverflow.com / questions / 71800312 / django - get - models - by - model - name
        model = apps.get_model(app_label=specialite, model_name=model_specialite)

        response = super().options(request, *args, **kwargs)
        # Create a metadata class instance and then call its method
        metadata_class = self.metadata_class
        if metadata_class and callable(metadata_class):
            metadata_instance = metadata_class()
            data = metadata_instance.determine_metadata(request, self)
            if "actions" not in data:
                response = Response({}, status=status.HTTP_200_OK)
                return response
            for field in data["actions"]["PUT"]:
                field_name = model._meta.get_field(field)
                data["actions"]["PUT"][field]["head"] = field_name.db_comment
            response = Response(data, status=status.HTTP_200_OK)

        return response

    # Logic to create and update ATCD when create or update
    def perform_create(self, serializer):
        specialite = "_".join(self.kwargs.get("specialite").lower().split(" "))

        # queryset = SignupRequest.objects.filter(user=self.request.user)
        # if queryset.exists():
        #     raise ValidationError('ATCD exists already.') #TODO: to be raised when ATCD exists already in case of importation !

        """
        # check if there is already antecedents patients crées or check if there is already antecedents hospitalisation/consultation/staff crées
        # => create atcd depending of whats exists !
        # then
            #if antecedents patients are not created: update fiche générale patient !
            #but in any case: update fiche generale patients !!!
        """
        # Load atcd patient if exists !
        object = self.get_object()

        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "antecedents" + "".join(
            x.lower() for x in specialite.split("_")
        )
        patient = Patient.objects.get(id=self.kwargs.get("patient_id"))

        # if atcd fiche patient exists: load datas !
        if (
            hasattr(patient, model_specialite)
            and patient != None
            and object != None
            and not hasattr(object, model_specialite)
        ):
            # Get the attribute safely with a default None value
            obj = getattr(patient, model_specialite, None)
            # Only proceed if obj exists
            if obj is not None:
                obj.zkf_patient = None  # IMPORTANT !!!: Set zkf_patient to None to  be able to duplicate the model instance (otherwise UNIQUE constraint), but don't save it...
                obj.pk = None  # duplicate object

                zkf_context = None
                if "context" in self.request.GET:
                    zkf_context = self.context_dict.get(self.request.GET["context"])
                    if zkf_context:
                        zkf_context = "zkf_" + self.request.GET["context"]

                # Only set attributes if both object and zkf_context are valid
                if zkf_context:
                    setattr(
                        obj, zkf_context, object
                    )  # link it to current object (consultation, hospitalisation, staff)
                    # obj.zkf_hospitalisation = object  # link it to hospitalisation
                    obj.save()

        serializer.save()

    def perform_update(self, serializer):
        specialite = "_".join(self.kwargs.get("specialite").lower().split(" "))
        # for get and seattr, has to be lowercase
        model_specialite_lower = "antecedents" + "".join(
            x.lower() for x in specialite.split("_")
        )
        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite_camel = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        model = apps.get_model(app_label=specialite, model_name=model_specialite_camel)
        object = self.get_object()
        patient = Patient.objects.get(id=self.kwargs.get("patient_id"))
        # update patient atcd !

        # 1/ if fiche patient does not exists:
        if not hasattr(patient, model_specialite_lower):
            # import atcd from current model antecedents !
            obj = getattr(object, model_specialite_lower, None)
            setattr(
                obj, model_specialite_lower, None
            )  # IMPORTANT !!!: Set zkf_patient to None to  be able to duplicate the model instance (otherwise UNIQUE constraint), but don't save it...
            obj.pk = None  # duplicate object
            obj.zkf_patient = self.object.zkf_patient  # link it to patient
            # update date dernière modification
            obj.date_last_update_atcd = datetime.datetime.now()
            obj.save()

        # 2/ IF fiche patient exists:
        else:
            # 1/ get patient antecedents de chirurgie vasculaire
            patient_atcd_chir_vasculaire = model.objects.get(zkf_patient=patient)
            # 2/ get context antecedents de chirurgie vasculaire
            context_atcd_chir_vasculaire = object

            # 2/loop through all fields to check if has changed => update patient !
            for key, value in model_to_dict(context_atcd_chir_vasculaire).items():
                if getattr(patient_atcd_chir_vasculaire, key) != value:
                    # print (getattr(patient_atcd_chir_vasculaire, key), key,  value)
                    if key not in [
                        "id",
                        "zkf_patient",
                        "zkf_hospitalisation",
                        "zkf_consultation",
                        "zkf_staff",
                    ]:
                        setattr(patient_atcd_chir_vasculaire, key, value)

            # update date dernière modification
            patient_atcd_chir_vasculaire.date_last_update_atcd = datetime.datetime.now()
            patient_atcd_chir_vasculaire.save()

            context_atcd_chir_vasculaire.date_last_update_atcd = datetime.datetime.now()
            context_atcd_chir_vasculaire.save()

        serializer.save()
