from rest_framework import serializers


class AtcdSerializer(serializers.ModelSerializer):
    class Meta:
        model = None
        exclude = [
            "id",
            "zkf_patient",
            "zkf_consultation",
            "zkf_hospitalisation",
            "zkf_staff",
            "date_last_update_atcd",
            "modified_by",
            "created_by",
            "modified_on",
            "created_on",
            "deleted_by",
            "deleted_on",
            "deleted",
        ]
