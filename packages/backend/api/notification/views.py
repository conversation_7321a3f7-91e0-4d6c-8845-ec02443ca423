from rest_framework import generics
from rest_framework import permissions
from api.notification.serializers import NotificationSerializer
from notification.models import Notification
from rest_framework.views import APIView
from rest_framework.response import Response


class NotificationList(generics.ListCreateAPIView):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = Notification.objects.filter(zkf_recipient=self.request.user)

        filter = self.request.query_params.get("filter", None)
        # transfrom strings to list of strings
        if filter is not None:
            filter = filter.split(",")
            filter = [f.strip() for f in filter]

            queryset = queryset.filter(nf_type__in=filter)

        # sort by created and flag
        queryset = queryset.order_by("-flagged", "-created_on")

        return queryset


class ReadNotifications(APIView):

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """
        Delete all notifications read for the user
        #"""

        # # DELETE Read Notifications
        delete_read_notifications = self.request.data.get("delete", False)
        ids = self.request.data.get("ids", None)

        if ids is not None:

            queryset = Notification.objects.filter(
                id__in=ids,
                recipients__in=[self.request.user],
                zkf_environnement=self.request.user.profile.zkf_environnement_user,
            )

            if delete_read_notifications == True:
                # delete all readed notifications
                queryset = queryset.filter(flagged=False)
                queryset.delete()

            else:
                # change status notifications to read
                queryset.update(read=True)

        else:
            raise ValueError("ids is required")

        return Response({"status": "success"}, status=201)


class NotificationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    # add ability to mark notification as read by PATCH request
    def perform_update(self, serializer):
        notification = serializer.save()
        return notification

        # Prevoir de pouvoir reporter la notification (à quelques jours: par defaut 3 jours par exemple ! )

    # add ability to delete notification by DELETE request
    def perform_destroy(self, instance):

        all_notifications = self.request.query_params.get("all", "false").lower()

        if all_notifications == "true":
            notifications = Notification.objects.filter(
                recipients__in=self.get_object().recipients.all(),
                group_id=self.get_object().group_id,
                zkf_environnement=self.get_object().zkf_environnement,
            )
            notifications.delete()

        else:
            # delete notification
            notification: Notification = self.get_object()
            notification.delete()
