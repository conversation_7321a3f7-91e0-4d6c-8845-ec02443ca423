from django.urls import path

from api.protocole.views import (
    ProtocoleList,
    ProtocoleDetail,
    FormFieldList,
    FormFieldDetail,
    FormTemplateList,
    FormTemplateDetail,
    AnswerFormFieldList,
    AnswerFormFieldDetail,
    FormList,
    FormDetail,
)


urlpatterns = [
    # protocoles
    path("", ProtocoleList.as_view(), name="protocole-list"),
    path("<uuid:pk>/", ProtocoleDetail.as_view(), name="protocole-detail"),
    # formfields
    path("formfield/", FormFieldList.as_view(), name="formfield-list"),
    path("formfield/<uuid:pk>/", FormFieldDetail.as_view(), name="formfield-detail"),
    # formtemplates
    path("formtemplate/", FormTemplateList.as_view(), name="formtemplate-list"),
    path(
        "formtemplates/<uuid:pk>/",
        FormTemplateDetail.as_view(),
        name="formtemplate-detail",
    ),
    # answerformfields
    path(
        "answerformfield/", AnswerFormFieldList.as_view(), name="answerformfield-list"
    ),
    path(
        "answerformfield/<uuid:pk>/",
        AnswerFormFieldDetail.as_view(),
        name="answerformfield-detail",
    ),
    # forms
    path("form/", FormList.as_view(), name="form-list"),
    path("form/<uuid:pk>/", FormDetail.as_view(), name="form-detail"),
]
