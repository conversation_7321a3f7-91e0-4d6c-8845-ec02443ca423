from rest_framework import generics
from rest_framework import permissions
from api.protocole.serializers import (
    ProtocoleSerializer,
    FormFieldSerializer,
    FormTemplateSerializer,
    AnswerFormFieldSerializer,
    FormSerializer,
)
from django.db.models import Q
from operation.models import Protocole
from protocole.models import <PERSON>Field, <PERSON>T<PERSON>plate, AnswerFormField, Form


class ProtocoleList(generics.ListCreateAPIView):
    queryset = Protocole.objects.all()
    serializer_class = ProtocoleSerializer
    pagination_class = None

    def get_queryset(self):
        return Protocole.objects.filter(
            Q(
                zkf_environnement_protocole=self.request.user.profile.zkf_environnement_user
            )
            | Q(protocole_users__in=[self.request.user])
        ).distinct()

    def perform_create(self, serializer):
        serializer.save(zkf_environnement_protocole=self.request.user)


class ProtocoleDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Protocole.objects.all()
    serializer_class = ProtocoleSerializer


class FormFieldList(generics.ListCreateAPIView):
    queryset = FormField.objects.all()
    serializer_class = FormFieldSerializer

    def get_queryset(self):
        return FormField.objects.all()


class FormFieldDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = FormField.objects.all()
    serializer_class = FormFieldSerializer
    permission_classes = [permissions.IsAuthenticated]


class FormTemplateList(generics.ListCreateAPIView):
    queryset = FormTemplate.objects.all()
    serializer_class = FormTemplateSerializer
    # permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return FormTemplate.objects.all()


class FormTemplateDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = FormTemplate.objects.all()
    serializer_class = FormTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]


class AnswerFormFieldList(generics.ListCreateAPIView):
    queryset = AnswerFormField.objects.all()
    serializer_class = AnswerFormFieldSerializer
    # permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return AnswerFormField.objects.all()


class AnswerFormFieldDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = AnswerFormField.objects.all()
    serializer_class = AnswerFormFieldSerializer
    permission_classes = [permissions.IsAuthenticated]


class FormList(generics.ListCreateAPIView):
    queryset = Form.objects.all()
    serializer_class = FormSerializer
    # permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Form.objects.all()


class FormDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Form.objects.all()
    serializer_class = FormSerializer
    permission_classes = [permissions.IsAuthenticated]
