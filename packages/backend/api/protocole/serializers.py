from rest_framework import serializers
from protocole.models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>plate, AnswerFormField, Form
from operation.models import Protocole

# from api.accounts.serializers import UserSerializer


class ProtocoleSerializer(serializers.ModelSerializer):
    # protocole_users = UserSerializer(many=True)

    class Meta:
        model = Protocole
        fields = "__all__"


class FormFieldSerializer(serializers.ModelSerializer):
    zkf_protocole = ProtocoleSerializer()

    class Meta:
        model = FormField
        fields = "__all__"


class FormTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = FormTemplate
        fields = "__all__"


class AnswerFormFieldSerializer(serializers.ModelSerializer):
    class Meta:
        model = AnswerFormField
        fields = "__all__"


class FormSerializer(serializers.ModelSerializer):
    class Meta:
        model = Form
        fields = "__all__"
