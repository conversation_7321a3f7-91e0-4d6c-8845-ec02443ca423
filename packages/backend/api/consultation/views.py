from rest_framework import generics
from rest_framework import permissions
from rest_framework.response import Response
from django.http import HttpResponse
from rest_framework.views import APIView
from api.consultation.serializers import ConsultationSerializer
from consultation.models import Consultation
import datetime
import html
from django.contrib.auth.models import User

from api.consultation.serializers import (
    ConsultationSerializer,
    ConsultationTypeSerializer,
)
from django.http import JsonResponse

from consultation.models import Consultation, ConsultationType
from hospitalisation.models import Hospitalisation
from patient.models import Patient
from operation.models import Operation
from accounts.models import Site

from rest_framework.views import APIView
from rest_framework.response import Response
from django.template import Context, Template
from consultation.scripts.pick_consultation import new_consultation_assistant
from consultation.scripts.premiere_consultation import check_premiere_consultation
from patient.patient_scripts.etat import update_etat_patient

# Import manquant pour filters
from rest_framework import generics, permissions, filters
import datetime
from django.utils import timezone


class ConsultationList(generics.ListCreateAPIView):
    queryset = Consultation.objects.all().order_by(
        "-consultation_date"
    )  # Default ordering by date (newest first)
    serializer_class = ConsultationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()

        patient_id = self.request.query_params.get("patient", None)
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        operation = self.request.query_params.get("operation", None)
        filter_consultation = self.request.query_params.get("filter_consultation", None)
        date = self.request.query_params.get("date", None)

        if patient_id is not None:
            queryset = queryset.filter(zkf_patient=patient_id).exclude(
                etat_consultation__in=["annule", "non_venu"]
            )
        else:
            raise ValueError("Missing required parameters: patient")

        if filter_consultation is not None and date is not None:

            date = datetime.datetime.strptime(date, "%Y-%m-%d").date()
            filter_consultation = int(filter_consultation)

            if filter_consultation > 0:
                queryset = (
                    queryset.filter(consultation_date__gte=date)
                    .order_by("-consultation_date")
                    .exclude(etat_consultation__in=["annule", "non_venu"])[
                        :filter_consultation
                    ]
                )
            else:
                filter_consultation = abs(filter_consultation)
                queryset = (
                    queryset.filter(consultation_date__lte=date)
                    .order_by("-consultation_date")
                    .exclude(etat_consultation__in=["annule", "non_venu"])[
                        :filter_consultation
                    ]
                )
            return queryset

        if patient_id is not None and hospitalisation is None and operation is None:

            return queryset
        # # return only last 3 consultations
        if hospitalisation is not None:
            hospitalisation = Hospitalisation.objects.get(id=hospitalisation)
            queryset = queryset.filter(zkf_patient=hospitalisation.zkf_patient).exclude(
                etat_consultation__in=["annule", "non_venu"]
            )
            # get only upcomping consultations
            date_reference = (
                hospitalisation.entree_date
                if hospitalisation.entree_date
                else datetime.date.today()
            )
            queryset = queryset.filter(consultation_date__lte=date_reference).order_by(
                "-consultation_date"
            )[:3]
            return queryset

        if operation is not None:
            operation = Operation.objects.get(id=operation)
            queryset = queryset.filter(zkf_patient=operation.zkf_patient).exclude(
                etat_consultation__in=["annule", "non_venu"]
            )
            # get only last 3 consultations
            queryset = queryset.filter(
                consultation_date__lte=operation.operation_date
            ).order_by("-consultation_date")[:3]
            return queryset

        return queryset.order_by("-consultation_date")

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def perform_create(self, serializer):

        instance = serializer.save()
        patient = instance.zkf_patient

        # update patient date etat
        update_etat_patient(
            patient, instance.consultation_date
        )  # , "creation consultation"

        # update praticien referent
        if instance.zkf_redacteur:
            patient.praticien_referent.add(instance.zkf_redacteur)

        # check if first consultation
        check_premiere_consultation(patient, instance)
        instance.save()


class RecentConsultationList(generics.ListAPIView):
    """
    Vue pour lister les consultations récentes, tous patients confondus.
    Supporte le tri (par défaut par date de consultation décroissante) et la pagination.
    """

    serializer_class = ConsultationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = [
        "consultation_date",
        "created_on",
        "modified_on",
    ]  # Champs autorisés pour le tri via l'URL
    ordering = ["-consultation_date"]  # Tri par défaut

    def get_queryset(self):
        # Commence par toutes les consultations et exclut certains états
        queryset = Consultation.objects.exclude(
            etat_consultation__in=["annule", "non_venu"]
        )

        # Filtrer pour n'inclure que les consultations passées ou d'aujourd'hui
        today = timezone.now().date()
        queryset = queryset.filter(consultation_date__lte=today)

        # Aucun filtrage spécifique par patient ici.
        # Le OrderingFilter (via le paramètre 'ordering' de l'URL ou celui par défaut de la classe)
        # et la pagination de DRF (via le paramètre 'limit' de l'URL) feront le reste.

        # Optionnel: Filtrer pour ne montrer que les consultations des X derniers jours/mois
        # from django.utils import timezone
        # from datetime import timedelta
        # queryset = queryset.filter(consultation_date__gte=timezone.now() - timedelta(days=60)) # Ex: 60 derniers jours

        return queryset


class SuggestedConsultationList(APIView):

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        patient = self.request.query_params.get("patient", None)
        date = self.request.query_params.get("date", None)  # today
        duration = self.request.query_params.get("duration", None)
        site = self.request.query_params.get("site", None)
        lieu = self.request.query_params.get("lieu", None)
        hospitalisation = self.request.query_params.get("hospitalisation", None)

        if patient is not None and date is not None and duration is not None:
            patient = Patient.objects.get(id=patient)
            date = datetime.datetime.strptime(date, "%Y-%m-%d").date()
            duration = int(duration)

            # get last consultation less than the date !
            consultation = (
                Consultation.objects.filter(
                    zkf_patient=patient, deleted=False, consultation_date__lte=date
                )
                .order_by("-consultation_debut_prevu")
                .first()
            )
            # get current hospitalisation
            hospitalisation = (
                Hospitalisation.objects.get(id=hospitalisation)
                if hospitalisation is not None
                else None
            )
            # user
            if consultation is not None and consultation.zkf_redacteur is not None:
                user = consultation.zkf_redacteur
            elif (
                hospitalisation is not None
                and hospitalisation.zkf_redacteur is not None
            ):
                user = hospitalisation.zkf_redacteur
            else:
                user = self.request.user

            # get site and lieu: get last site and lieu if not provided
            if consultation:
                site = (
                    consultation.zkf_site if site is None else Site.objects.get(id=site)
                )
                lieu = consultation.zkf_lieu if lieu is None else lieu
            # if no consultation ever done: if the user has one site, use it, else use the first site
            else:
                site = user.profile.site.first()

            suggested_consultations = new_consultation_assistant(
                patient, duration, date, user, site, lieu, hospitalisation
            )

        if not suggested_consultations:
            return JsonResponse(
                {
                    "message": "No available consultation slots for the patient.",
                    "results": [],
                },
                status=200,
            )
        if site is None:
            return JsonResponse(
                {
                    "message": "Please provide a site for the consultation.",
                    "results": [],
                },
                status=200,
            )

        return JsonResponse(
            {"message": "OK", "results": suggested_consultations},
            safe=False,
            status=200,
        )


class ConsultationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Consultation.objects.all()
    serializer_class = ConsultationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def perform_update(self, serializer):
        instance = serializer.save()
        patient = instance.zkf_patient
        # update patient date etat
        update_etat_patient(patient, instance.consultation_date)

        # check if first consultation
        check_premiere_consultation(patient, instance)
        instance.save()


class ConsultationTypeList(generics.ListCreateAPIView):
    queryset = ConsultationType.objects.all()
    serializer_class = ConsultationTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = self.request.query_params.get("depth", 0)
        return context

    def get_queryset(self):
        queryset = self.queryset
        queryset = queryset.filter(zkf_user=self.request.user)

        return queryset

    def perform_create(self, serializer):
        # create ordonnance template
        # if duplicate_user is not in request data

        if not ("duplicate_user" in self.request.data):
            serializer.save(
                zkf_user=self.request.user,
                zkf_environnement=self.request.user.profile.zkf_environnement_user,
            )

        # duplicate ordonnance template for selected users
        if "duplicate_user" in self.request.data:
            users = self.request.data["duplicate_user"]
            if not users:
                users = [self.request.data["zkf_user"]]
            # duplicate ordonnance template for selected users
            for user in users:
                user = User.objects.get(id=user)
                new_instance = ConsultationTypeSerializer(data=self.request.data)
                if new_instance.is_valid():
                    new_instance.save(
                        zkf_user=user,
                        zkf_environnement=user.profile.zkf_environnement_user,
                    )
                else:
                    print(new_instance.errors)


class ConsultationTypeDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = ConsultationType.objects.all()
    serializer_class = ConsultationTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = self.request.query_params.get("depth", 0)
        return context


class GenerateConsultationType(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        consultation_id = request.query_params.get("consultation", None)
        consultation_type_id = request.query_params.get("consultation_type", None)
        corps = None

        if consultation_id is not None and consultation_type_id is not None:
            courrier_type_id = ConsultationType.objects.get(id=consultation_type_id)
            consultation = Consultation.objects.get(id=consultation_id)

            context = {
                "patient": consultation.zkf_patient,
                "consultation": consultation,
                "corps": consultation.courrier_consultation,
            }
            # html.unescape: convert html entities to characters
            ######generate corps_document with variables
            t = Template(courrier_type_id.corps)
            c = Context(context)
            # corps =  html2text.html2text(t.render(c))
            corps = html.unescape(t.render(c))

            # bas de page
            t = Template(courrier_type_id.bas_de_page)
            c = Context(context)
            bas_de_page = html.unescape(t.render(c))

            # Check if template contains {{corps}} variable
            template_has_corps_variable = "{{corps}}" in courrier_type_id.corps or "{{ corps }}" in courrier_type_id.corps
            
            courrier_type_generated = {
                "corps": corps,
                "bas_de_page": bas_de_page,
                "should_replace_all": template_has_corps_variable,
            }

            # add consultation type to manytomany
            consultation.consultations_type.add(courrier_type_id)

            # TODO: when front done: modify back to delete if statement => renvoie toujorus un dictionnaire !
            text_generated = courrier_type_generated
            return JsonResponse(text_generated)

        # TODO: when front done: modify back to delete if statement => renvoie toujorus un dictionnaire !

        else:
            raise ValueError("Missing required parameters: consultation, courrier_type")

        return Response(text_generated)
