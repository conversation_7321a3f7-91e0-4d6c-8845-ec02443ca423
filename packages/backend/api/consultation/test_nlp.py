import requests
import os
from mistralai import Mistral
from django.conf import settings
# import response

# Remplacez 'votre_clé_d_api' par votre clé d'API réelle
api_key = os.getenv('MISTRAL_API_KEY', 'votre_clé_d_api')
api_url = 'https://api.mistral.ai/v1/models/mistral-medium-latest/completions'
MODEL_NAME = [settings.MISTRAL_MODEL, "mistral-large-latest", "magistral-small-latest"]

# Liste des paramètres à tester
parameters = [
    {
        'temperature': 0.7,
        'top_k': 50,
        'top_p': 0.9,
        'frequency_penalty': 0.2,
        'presence_penalty': 0.1
    },
    {
        'temperature': 0.3,
        'top_k': 50,
        'top_p': 0.9,
        'frequency_penalty': 0.2,
        'presence_penalty': 0.1
    },
    {
        'temperature': 0.1,
        'top_k': 50,
        'top_p': 0.9,
        'frequency_penalty': 0.2,
        'presence_penalty': 0.1
    },

]

transcription = ("chers confrères, chers juristes, avec plaisir que je vois M. Cordouan dans le cadre du suivi d'une tournication bilatérale opérée en 2020 et 2024. Point nouveau paragraphe, le patient décrit un périmètre de marche relativement large, bien qu'il existe des lésions, en particulier fémorales, superficielles, droites, multi-étagées, mais de toute évidence sans retentissement clinique majeur. Point nouveau paragraphe, net effort également sur l'arrêt de l'intoxication tabagique, avec passage à la cigarette électronique, dont je félicite bien sûr M. Cordouan, et renouvelle mes encouragements à l'activité physique régulièrement, 45 minutes trois fois par semaine, idéalement quotidiennement. Point nouveau paragraphe, poursuite du traitement médical, montant associant bien intégration plaquettaire et marche régulière. Je reverrai M. Cordouan dans un an, et bien sûr plus tôt, en cas de souci lors du suivi. Sensible à votre confiance, sur les compétences de station, correcteur, présence du patient. Merci beaucoup Martin.")

# Le prompt à utiliser pour les tests
prompt = (
            "Tu es un(e) secrétaire médical(e) expérimenté(e), spécialiste de la rédaction de comptes-rendus médicaux professionnels et lisibles.\n"
            "Voici la transcription brute d’une consultation, générée par reconnaissance vocale. Elle peut contenir :\n"
            "- des erreurs de transcription,\n"
            "- des mots inadaptés ou incorrects,\n"
            "- des phrases mal formulées ou floues.\n\n"
            "Ta tâche est de Transformer ce texte en un courrier médical formel et structuré clair et fidèle au contenu clinique, SANS JAMAIS RIEN INVENTER.\n\n"
            "Instructions STRICTES :\n"
            "- Structurer le texte en paragraphes clairs et concis.\n"
            "- Utiliser un langage médical précis et approprié.\n"
            "- Corrige les erreurs de transcription et reformule les phrases pour une clarté et une fluidité maximales.\n"
            "- Utilise impérativement les termes médicaux appropriés et précis.\n"
            "- Améliore la syntaxe, la grammaire et la lisibilité générale du texte.\n"
            "- **Structure le compte-rendu en paragraphes clairs et distincts lorsque cela améliore la lisibilité. Utilise des sauts de ligne pour séparer les idées ou sections distinctes.**\n"
            "- **NE PAS commencer la réponse par des formules introductives telles que 'Compte rendu médical', 'Transcription corrigée :', le nom du patient, ou toute autre salutation ou titre. Commence DIRECTEMENT par le contenu clinique corrigé.**\n"
            "- **Il est ABSOLUMENT IMPÉRATIF de ne RIEN inventer ni ajouter d'informations qui ne sont pas explicitement présentes dans la transcription fournie. Si un détail n'est pas dans le texte source, il ne doit PAS apparaître dans ta réponse.**\n"
            "- Ta réponse doit contenir UNIQUEMENT le texte médical corrigé. Aucun commentaire, aucune note, aucune explication supplémentaire.\n\n"
            "- La réponse DOIT RESPECTER le STYLE et les FORMULATIONS du texte initial.\n"
            "Texte à corriger :\n"
            f"{transcription}\n"
        )
client = Mistral(api_key=api_key)
for model in MODEL_NAME:
    for params in parameters:
        data = {
            'prompt': prompt,
            **params
        }

        messages = [{"role":"user","content": prompt}]
        chat_response = client.chat.complete(
            model=model, 
            messages=messages,
            temperature=0.15
        )

        # if chat_response.status_code == 200:
        print(f"Model: {model}")
        print(f"Parameters: {params}")
        print("Response:")
        print(chat_response.json())
        # else:
        #     # print(f"Request failed with status code {chat_response.status_code}")
        #     print("Response:")
        #     print(chat_response.text)