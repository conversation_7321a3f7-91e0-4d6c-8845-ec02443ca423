from accounts.models import Lieu, Site
from rest_framework import serializers
from consultation.models import Consultation, ConsultationType
from api.operation.serializers import OperationSerializer
from patient.models import Patient
from operation.models import Operation

from api.sort.tag.serializers import TagListSerializerField, TaggitSerializer

# https://www.linkedin.com/pulse/friday-quick-tip-dynamic-depth-serialization-django-rest-neidinger/


class DynamicDepthSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.Meta.depth = self.context.get("depth", 0)


class ConsultationSerializer(TaggitSerializer, DynamicDepthSerializer):

    tags = TagListSerializerField(required=False)
    model_app_datas = serializers.SerializerMethodField()

    class Meta:
        model = Consultation
        fields = "__all__"

    def get_model_app_datas(self, obj):
        return {
            "model": self.Meta.model.__name__.lower(),
            "app": self.Meta.model._meta.app_label,
        }


class ConsultationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConsultationType
        fields = "__all__"
        read_only_fields = ["zkf_environnement", "zkf_user"]
