from django.urls import path

from api.consultation.views import (
    ConsultationList,
    ConsultationDetail,
    ConsultationTypeList,
    ConsultationTypeDetail,
    GenerateConsultationType,
    SuggestedConsultationList,
    RecentConsultationList,  # Import RecentConsultationList
)

# route = "api/consultation"

urlpatterns = [
    path("", ConsultationList.as_view(), name="consultations-list"),
    path(
        "<uuid:pk>",
        ConsultationDetail.as_view(),
        name="consultation-detail",
    ),
    path(
        "consultation-type/",
        ConsultationTypeList.as_view(),
        name="consultation-types-list",
    ),
    path(
        "consultation-type/<uuid:pk>",
        ConsultationTypeDetail.as_view(),
        name="consultation-type-detail",
    ),
    path(
        "generate-consultation-type",
        GenerateConsultationType.as_view(),
        name="generate-consultation-type",
    ),
    # url to retrieve a list of consultations given a patient, user, start date and duration
    path(
        "suggested-consultations",
        SuggestedConsultationList.as_view(),
        name="suggested-consultations",
    ),
    path(
        "recent-consultations/",
        RecentConsultationList.as_view(),
        name="recent-consultation-list",
    ),
]
