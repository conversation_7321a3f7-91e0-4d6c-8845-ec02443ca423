from impression.models import GestionnaireImpression
from django.http import HttpResponse
import django.apps
from django.contrib.contenttypes.models import ContentType
from django.conf import settings

# create PDF
# from weasyprint import HTML, CSS #convert pdf
from xhtml2pdf import pisa

import PyPDF2

# create temporary folder
import tempfile  # create temporary files
import shutil  # for file operations


def combine_strings(request, ids: list[str], recto_verso: bool) -> HttpResponse:
    pdfs = GestionnaireImpression.objects.filter(id__in=ids).order_by("zkf_patient")
    # load all documents from the waiting list
    # convert queryset as list of dict
    pdfs = pdfs.values()

    # 2/ sort the dict
    order = {
        "crh": 0,
        "cro": 1,
        "devis": 2,
        "ordonnance": 3,
        "doc_libre": 4,
        "convocation_consultation": 5,
        "courrier_consultation": 6,
        "convocation_operation": 7,
        "facture": 8,
        "avoir": 9,
    }
    # get all applications=> add sort !
    sort = dict()
    i = 0
    models = django.apps.apps.get_models()
    for model in models:
        sort[model._meta.model_name] = i
        i += 1
    # https://stackoverflow.com/questions/21773866/how-to-sort-a-dictionary-based-on-a-list-in-python

    # https://stackoverflow.com/questions/50297639/python-sorting-list-of-dictionary-by-custom-order
    # set all type_document as lowercase to avoid case sensitive, in ids as well
    for p in pdfs:
        p["type_document"] = p["type_document"].lower()

    pdfs_dict = sorted(pdfs, key=lambda x: order[x["type_document"]])

    # initiate pdf merger
    final_pdf = PyPDF2.PdfMerger(strict=True)

    # create a temporary folder
    with tempfile.TemporaryDirectory() as tmpdirname:
        i = 0
        for p in pdfs_dict:
            # load html string
            html_string = p["string_of_html"]

            """Generate PDF with xhtml2"""
            # if gestionnaire string is a pdf: result file is the pdf: Copy pdf in the temp directory
            # get object related (contenttype and object_id)
            ct = ContentType.objects.get_for_id(p["content_type_id"])
            object = ct.get_object_for_this_type(id=p["object_id"])
            object_fields = object._meta.model._meta.get_fields()
            object_fields = [field.name for field in object_fields]

            if "document_libre_file" in object_fields and str(
                getattr(object, "document_libre_file", None)
            ).endswith(".pdf"):
                media_root = settings.MEDIA_ROOT
                temp_file = shutil.copyfile(
                    media_root + "/" + object.document_libre_file.name,
                    (tmpdirname + "/" + str(i) + "test.pdf"),
                )

            else:
                # open output file for writing (truncated binary)
                try:
                    resultFile = open(tmpdirname + "/" + str(i) + "test.pdf", "w+b")
                except Exception as e:
                    print(e)
                    error_msg = f"Error opening file: {str(e)}".encode("utf-8")
                    return HttpResponse(
                        error_msg, status=500, content_type="text/plain"
                    )

                # convert HTML to PDF  http://www.ordinateur.cc/Logiciel/Portable-Document-Format/154234.html
                # https://programtalk.com/python-examples/xhtml2pdf.pisa.CreatePDF/
                # https://xhtml2pdf.readthedocs.io/en/latest/usage.html
                try:
                    pisaStatus = pisa.CreatePDF(
                        html_string,
                        dest=resultFile,  # the HTML to convert
                    )  # file handle to receive result
                except Exception as e:
                    print(e)
                    error_msg = f"Error creating PDF: {str(e)}".encode("utf-8")
                    return HttpResponse(
                        error_msg, status=500, content_type="text/plain"
                    )

                # close output file
                resultFile.close()  # close output file

            # Print recto-verso => Add blank page if count is odd
            # count pages in the pdf
            file = open(tmpdirname + "/" + str(i) + "test.pdf", "rb")
            readpdf = PyPDF2.PdfReader(file)
            writer = PyPDF2.PdfWriter()
            totalpages = len(readpdf.pages)

            # https://gist.github.com/dalgu90/9f62df70ac3462960c745cf673d3910c
            # https://stackoverflow.com/questions/61711572/pypdf2-append-a-blank-page-for-odd-numbered-pdf-document-gives-blank-pages

            if totalpages % 2 == 1 and recto_verso == True:
                # Get MediaBox properties safely
                media_box = readpdf.pages[0].mediabox
                w, h = float(media_box.width), float(media_box.height)

                writer.append_pages_from_reader(readpdf)
                writer.add_blank_page(w, h)
                out_file = tmpdirname + "/" + str(i) + "test_even.pdf"
                # print (out_file)

                with open(out_file, "wb") as fd:
                    writer.write(fd)

                final_pdf.append(tmpdirname + "/" + str(i) + "test_even.pdf")

            else:
                final_pdf.append(tmpdirname + "/" + str(i) + "test.pdf")

            i += 1

        response = HttpResponse(content_type="application/pdf")
        response["Content-Transfer-Encoding"] = "binary"

        # generate pdf in response
        try:
            # Create a BytesIO buffer
            from io import BytesIO

            pdf_buffer = BytesIO()
            final_pdf.write(pdf_buffer)
            pdf_buffer.seek(0)

            # Write the buffer contents to the HttpResponse
            response.write(pdf_buffer.getvalue())
        except Exception as e:
            print(e)
            error_msg = f"Error writing PDF: {str(e)}".encode("utf-8")
            return HttpResponse(error_msg, status=500, content_type="text/plain")

        return response
