from rest_framework import generics
from rest_framework import permissions
from impression.views import create_html_string as html_string_creator
from impression.views import print_manager_list as manager_list_creator
from rest_framework.views import APIView
from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView
from api.mixins.drf_multiple_model.pagination import MultipleModelLimitOffsetPagination
from django.views.generic import View
from django.http import HttpResponse
from rest_framework.response import Response
from rest_framework import status
from django.apps import apps
from django.conf import settings
import copy  # copy dict To copy the mutable types like dictionaries, use copy / deepcopy of the copy module. https://stackoverflow.com/questions/2465921/how-to-copy-a-dictionary-and-only-edit-the-copy
import shutil
import os
from xhtml2pdf import pisa
from django.db.models import Q

# Merge PDF
from PyPDF2 import PdfMerger
import PyPDF2

# create temporary folder
import tempfile
import datetime

from api.impression.serializers import GestionnaireImpressionSerializer
from impression.models import GestionnaireImpression
from hospitalisation.models import Hospitalisation
from operation.models import Operation, Devis, Facture
from consultation.models import Consultation
from document.models.ordonnance import Ordonnance
from document.models.libre import DocumentLibre
from api.accounts.serializers import UserSerializer

from api.hospitalisation.serializers import HospitalisationSerializer
from api.consultation.serializers import ConsultationSerializer

from django.urls import reverse
from api.impression.scripts.combine_strings import combine_strings
from babylone.authentication import KeycloakAuthentication
from comptabilite.scripts.context import context_devis

# Import PatientSerializer properly
from api.patient.serializers import PatientSerializer


# ajout de documents au gestionnaire d'impression en attente d'être imprimés
class GestionnaireImpressionList(generics.ListCreateAPIView):
    queryset = None
    serializer_class = GestionnaireImpressionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self, *args, **kwargs):
        # now = (datetime.datetime.now())
        queryset = manager_list_creator(self.request)
        # print (now - datetime.datetime.now())
        return queryset

    def create(self, request, *args, **kwargs):
        # check if request data is a dict or a list of dict
        datas = self.request.data
        if not isinstance(datas, list):
            datas = [datas]
        user = self.request.user

        for data in datas:
            type_document = titre_document = custom_context = template = None

            if data["type_document"] == "crh":
                object = Hospitalisation.objects.get(id=data["uuid"])
                if hasattr(object, "entree_date") and object.entree_date:
                    titre_document = f"CRH {object.entree_date.year}"
                else:
                    titre_document = "CRH (sans date)"
                patient = object.zkf_patient
                template = Hospitalisation.template_crh(object)
                url_document = reverse(
                    "hospitalisation:detail-hospitalisation", args=(object.id,)
                )
                type_document = "crh"
                custom_context = {
                    "hospitalisation": object,
                    "user": object.zkf_redacteur if object.zkf_redacteur else user,
                    "patient": patient,
                }

            elif data["type_document"] == "cro":
                object = Operation.objects.get(id=data["uuid"])
                titre_document = "CRO: " + str(object)
                patient = object.zkf_patient
                template = Operation.template_cro(object)
                url_document = reverse("operation:detail-operation", args=(object.id,))
                type_document = "cro"
                custom_context = {
                    "operation": object,
                    "user": object.zkf_redacteur if object.zkf_redacteur else user,
                    "patient": patient,
                }

            elif data["type_document"] == "devis":
                object = Devis.objects.get(id=data["uuid"])
                titre_document = "Devis " + str(object.date_devis.strftime("%d/%m/%Y"))
                patient = object.zkf_patient
                template = Devis.template_devis(object)
                url_document = "api/comptabilite/devis/" + str(object.id)
                type_document = "devis"
                devis_context = context_devis(object)
                # Ensure devis_context is a dictionary before updating
                if isinstance(devis_context, dict):
                    custom_context = devis_context
                    custom_context.update(
                        {
                            "user": (
                                object.zkf_operation.zkf_redacteur
                                if object.zkf_operation
                                and object.zkf_operation.zkf_redacteur
                                else user
                            ),
                            "patient": patient,
                        }
                    )
                else:
                    # If it's not a dict, create a new context dictionary
                    custom_context = {"user": user, "patient": patient}

            elif data["type_document"] == "facture":
                object = Facture.objects.get(id=data["uuid"])
                type_document = "facture"
                titre_document = "Facture " + str(object.date_facture)
                patient = object.zkf_patient
                template = Facture.template_c(object)
                url_document = reverse("facture:detail-facture", args=(object.id,))
                custom_context = {"facture": object, "user": user, "patient": patient}

            elif data["type_document"] == "ordonnance":
                object = Ordonnance.objects.get(id=data["uuid"])
                titre_document = "Ordonnance " + str(object)
                patient = object.zkf_patient
                template = Ordonnance.template_ordonnance(object)
                url_document = reverse("ordonnance:edit-ordonnance", args=(object.id,))
                type_document = "ordonnance"
                custom_context = {
                    "ordonnance": object,
                    "user": object.redacteur if object.redacteur else user,
                    "patient": patient,
                }

            elif data["type_document"] == "document_libre":
                object = DocumentLibre.objects.get(id=data["uuid"])

                url_document = "api/document/libre/" + str(
                    object.id
                )  # reverse("document-libre:detail-document", args=(object.id,))
                type_document = "doc_libre"
                titre_document = "Document  " + str(object.titre_document)
                patient = object.zkf_patient
                template = DocumentLibre.template_document(object)
                custom_context = {"document": object, "user": user, "patient": patient}

            elif data["type_document"] == "convocation_operation":
                object = Operation.objects.get(id=data["uuid"])

                type_document = "convocation_operation"
                titre_document = "Convocation operation "
                patient = object.zkf_patient
                template = object.template("convocation_operation")
                url_document = (
                    reverse("operation:detail-operation", args=(object.id,)),
                )
                custom_context = {
                    "operation": object,
                    "user": object.zkf_redacteur if object.zkf_redacteur else user,
                    "patient": patient,
                }

            elif data["type_document"] == "convocation_consultation":
                object = Consultation.objects.get(id=data["uuid"])

                type_document = "convocation_consultation"
                if object.consultation_date and hasattr(
                    object.consultation_date, "year"
                ):
                    titre_document = (
                        f"Convocation consultation {object.consultation_date.year}"
                    )
                else:
                    titre_document = "Convocation consultation (sans date)"
                patient = object.zkf_patient
                template = object.template(type_document="convocation_consultation")
                url_document = reverse(
                    "consultation:detail-consultation", args=(object.id,)
                )
                custom_context = {
                    "consultation": object,
                    "user": object.zkf_redacteur if object.zkf_redacteur else user,
                    "patient": patient,
                }

            elif data["type_document"] == "courrier_consultation":
                object = Consultation.objects.get(id=data["uuid"])
                type_document = "courrier_consultation"
                if object.consultation_date and hasattr(
                    object.consultation_date, "year"
                ):
                    titre_document = (
                        f"Courrier consultation {object.consultation_date.year}"
                    )
                else:
                    titre_document = "Courrier consultation (sans date)"
                patient = object.zkf_patient
                template = object.template(type_document="courrier_consultation")
                url_document = reverse(
                    "consultation:detail-consultation", args=(object.id,)
                )
                custom_context = {
                    "consultation": object,
                    "user": object.zkf_redacteur if object.zkf_redacteur else user,
                    "patient": patient,
                }
            # if template is a string, convert it to a list
            if isinstance(template, str):
                template = [template]
            i = 1
            for t in template:
                # Déterminer le titre du document AVANT d'incrémenter le compteur
                doc_title = (
                    titre_document + " " + str(i)
                    if len(template) > 1
                    else titre_document
                )

                custom_data = {
                    "string_of_html": html_string_creator(
                        context=custom_context, template=t
                    ),
                    "zkf_user": user,
                    "zkf_patient": patient,
                    "titre_document": doc_title,
                    "url_document": url_document,
                    "zkf_id": object.id,
                    "type_document": type_document,
                    "created_by": user,
                    "modified_by": user,
                    "zkf_environnement": self.request.user.profile.zkf_environnement_user,
                }

                # Incrémenter le compteur après avoir défini le titre
                i += 1

                # check if the document has already be added in the waiting list
                obj = GestionnaireImpression.objects.filter(
                    zkf_id=object.id,
                    zkf_patient=patient,
                    zkf_environnement=self.request.user.profile.zkf_environnement_user,
                    titre_document=doc_title,
                )

                if obj.exists():
                    obj.update(**custom_data)
                    for o in obj:
                        o.content_object = object
                        o.save()
                    obj = obj.first()
                else:
                    obj = GestionnaireImpression.objects.create(**custom_data)
                    obj.content_object = object
                    obj.save()

                serializer = GestionnaireImpressionSerializer(obj)
                headers = self.get_success_headers(serializer.data)

            return Response(
                serializer.data, status=status.HTTP_201_CREATED, headers=headers
            )
            # return Response(status=status.HTTP_201_CREATED)

    # add route to delete document from print manager
    def delete(self, request, *args, **kwargs):
        # id of doc in print manager is passed as pk
        # body should be a list of ids, no property only a list of ids
        if isinstance(request.data, list):
            ids = request.data
            GestionnaireImpression.objects.filter(id__in=ids).delete()
        else:
            GestionnaireImpression.objects.all().delete()

        return Response(status=status.HTTP_204_NO_CONTENT)


class GestionnaireImpressionDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = GestionnaireImpression.objects.all()
    serializer_class = GestionnaireImpressionSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def delete(self, request, *args, **kwargs):
        # print(request.data)
        if isinstance(request.data, list):
            # Check if UUID is in the data dict and get it safely
            uuids = []
            for item in request.data:
                if isinstance(item, dict) and "uuid" in item:
                    uuids.append(item["uuid"])

            # Only proceed if we have valid UUIDs
            for id in uuids:
                self.destroy(request, id)
            return Response(status=status.HTTP_204_NO_CONTENT)

        return self.destroy(request, *args, **kwargs)


# imprime tous les documents selectionnés du gestionnaire d'impression !
class GestionnaireImpressionPrint(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        return HttpResponse(b"GET Print selected print manager")

    def post(self, request):
        # json list of uuids => fk to generated docs
        # return content type must be application/pdf
        ids = request.data
        try:
            ids = request.data.get("ids", None)
        except:
            pass
        try:
            recto_verso = request.data.get("recto_verso", None)
        except:
            pass
        result = combine_strings(request, ids, recto_verso)
        # Since result is already an HttpResponse, return it directly
        return result


# Class to print selected documents OUTSIDE print manager
class CombineSelectedPdf(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request, format=None):
        return HttpResponse(b"GET")

    def post(self, request, *args, **kwargs):
        # initiate pdf merger
        final_pdf = PdfMerger(strict=True)

        # request.data is a list of dict with id and model_app_datas
        # check if devis is in the list of docs to be printed
        # add template for multiples devis (one for patient, one for mutuelle)
        # Duplicate entry and add a manual template !!!
        # kind of a dirty hack, but works !
        # copy datas (use deepcopy to copy the mutable types like dictionaries)
        datas = request.data.copy()
        # for item in request.data:
        #     if item["type_document"] == "devis":
        #         item2 = copy.deepcopy(item)
        #         item2["model_app_datas"][
        #             "template"
        #         ] = "operation/pdf/devis_mutuelle.html"
        #         datas.append(item2)

        # create a temporary folder
        with tempfile.TemporaryDirectory() as tmpdirname:
            i = 0

            # create a list of dict with strings of html and url of the document
            if datas:  # check if data is empty
                for data in datas:
                    # get document model:
                    app = apps.get_model(
                        data["model_app_datas"]["app"],
                        data["model_app_datas"]["model"].upper(),
                    )
                    object = app.objects.get(id=data["id"])

                    # check if zkf_redacteur field is in object
                    if hasattr(object, "zkf_redacteur"):
                        user = object.zkf_redacteur
                    else:
                        user = self.request.user

                    if "template" in data["model_app_datas"]:
                        template = data["model_app_datas"]["template"]
                    elif "type_document" in data:
                        template = object.template(type_document=data["type_document"])
                    # Context
                    if data["type_document"] == "document_libre":
                        data["type_document"] = "document"
                    if data["type_document"] in [
                        "courrier_consultation",
                        "convocation_consultation",
                    ]:
                        data["type_document"] = "consultation"
                    if data["type_document"] in ["convocation_operation", "cro"]:
                        data["type_document"] = "operation"
                    if data["type_document"] in ["crh"]:
                        data["type_document"] = "hospitalisation"

                    context = {
                        data["type_document"]: object,
                        "user": user,
                        "patient": object.zkf_patient,
                    }

                    if data["type_document"] == "devis":
                        devis_context = context_devis(object)
                        # Ensure devis_context is a dictionary before updating
                        if isinstance(devis_context, dict):
                            context.update(devis_context)

                    """Generate PDF with xhtml2"""
                    # if document is not a string but is a pdf: result file is the pdf: Copy pdf in the temp directory
                    object_fields = object._meta.model._meta.get_fields()
                    object_fields = [field.name for field in object_fields]

                    if "document_libre_file" in object_fields and str(
                        getattr(object, "document_libre_file", None)
                    ).endswith(".pdf"):
                        media_root = settings.MEDIA_ROOT
                        temp_file = shutil.copyfile(
                            media_root + "/" + str(object.document_libre_file),
                            (tmpdirname + "/" + str(i) + "test.pdf"),
                        )

                        # Add to final_pdf
                        with open(tmpdirname + "/" + str(i) + "test.pdf", "rb") as file:
                            readpdf = PyPDF2.PdfReader(file)
                            totalpages = len(readpdf.pages)

                            if totalpages % 2 == 1:
                                # Get MediaBox properties safely
                                media_box = readpdf.pages[0].mediabox
                                w, h = float(media_box.width), float(media_box.height)

                                writer = PyPDF2.PdfWriter()
                                writer.append_pages_from_reader(readpdf)
                                writer.add_blank_page(w, h)
                                out_file = tmpdirname + "/" + str(i) + "test_even.pdf"

                                with open(out_file, "wb") as fd:
                                    writer.write(fd)

                                final_pdf.append(out_file)
                            else:
                                final_pdf.append(tmpdirname + "/" + str(i) + "test.pdf")

                    else:
                        # Ensure template is a list
                        if isinstance(template, str):
                            template = [template]

                        # Process each template as a separate document
                        for t_idx, t in enumerate(template):
                            # Generate unique filename for this template
                            pdf_filename = (
                                tmpdirname + "/" + str(i) + "_" + str(t_idx) + ".pdf"
                            )

                            # Create PDF for this template
                            with open(pdf_filename, "w+b") as pdf_file:
                                html_string = html_string_creator(
                                    context=context, template=t
                                )
                                pisa.CreatePDF(html_string, dest=pdf_file)

                            # Check if it has odd number of pages and add blank page if needed
                            with open(pdf_filename, "rb") as file:
                                readpdf = PyPDF2.PdfReader(file)
                                totalpages = len(readpdf.pages)

                                if totalpages % 2 == 1:
                                    # Get MediaBox properties safely
                                    media_box = readpdf.pages[0].mediabox
                                    w, h = float(media_box.width), float(
                                        media_box.height
                                    )

                                    writer = PyPDF2.PdfWriter()
                                    writer.append_pages_from_reader(readpdf)
                                    writer.add_blank_page(w, h)
                                    out_file = pdf_filename.replace(".pdf", "_even.pdf")

                                    with open(out_file, "wb") as fd:
                                        writer.write(fd)

                                    # Add to final merger
                                    final_pdf.append(out_file)
                                else:
                                    # Add to final merger
                                    final_pdf.append(pdf_filename)

                        # For backward compatibility, copy the first template's PDF to the standard filename
                        # This ensures other parts of the code that expect this file will still work
                        if len(template) > 0:
                            first_pdf = tmpdirname + "/" + str(i) + "_0.pdf"
                            if os.path.exists(first_pdf):
                                shutil.copy(
                                    first_pdf, tmpdirname + "/" + str(i) + "test.pdf"
                                )

                    i += 1

                # generate pdf in response
                response = HttpResponse(content_type="application/pdf")
                response["Content-Transfer-Encoding"] = "binary"
                try:
                    # Create a BytesIO buffer
                    from io import BytesIO

                    pdf_buffer = BytesIO()
                    final_pdf.write(pdf_buffer)
                    pdf_buffer.seek(0)

                    # Write the buffer contents to the HttpResponse
                    response.write(pdf_buffer.getvalue())
                except Exception as e:
                    print(e)
                    # Convert the error message to bytes
                    error_message = f"Error writing PDF: {str(e)}".encode("utf-8")
                    return HttpResponse(
                        error_message, status=500, content_type="text/plain"
                    )

                return response

            else:
                return HttpResponse(b"No data", status=400)


class LimitPagination(MultipleModelLimitOffsetPagination):
    default_limit = 10


class CompteRenduConsultationManager(generics.ListCreateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = LimitPagination

    def get_serializer_class(self):
        class ExtendedConsultationSerializer(ConsultationSerializer):
            zkf_patient = PatientSerializer()
            zkf_redacteur = UserSerializer()

            class Meta(ConsultationSerializer.Meta):
                depth = 1

        return ExtendedConsultationSerializer

    def get_queryset(self, *args, **kwargs):
        users = self.request.query_params.get("users", None)

        if users is None:
            # check if user is in users_lies => get compte rendu of current user
            if self.request.user in self.request.user.profile.praticiens_lies.all():
                users = [self.request.user]
            # else: display all compte rendus of users lies
            else:
                users = self.request.user.profile.praticiens_lies.all()

        consultations = (
            Consultation.objects.select_related("zkf_patient")
            .filter(
                # zkf_redacteur__profile__zkf_environnement_user__in=[
                #     self.request.user.profile.zkf_environnement_user
                # ],
                zkf_redacteur__in=users,
                deleted=False,
                statut_courrier_consultation__in=[
                    "valide",
                    "attente_validation",
                    "non_fait",
                    "transcrit",
                ],
            )
            .filter(
                Q(consultation_date__lt=datetime.date.today())
                | Q(
                    consultation_date=datetime.date.today(),
                    consultation_debut_prevu__lte=datetime.datetime.now(),
                )
            )
            .exclude(Q(etat_consultation="non_venu") | Q(etat_consultation="annule"))
            .order_by("-consultation_date", "consultation_debut_prevu")
        )

        status = self.request.query_params.get("status", None)

        if status:

            allowed_statuses = ["attente_validation", "valide", "non_fait", "transcrit"]

            if status in allowed_statuses:
                consultations = consultations.filter(
                    statut_courrier_consultation=status
                )

        return consultations


class CompteRenduHospitalisationManager(generics.ListCreateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = LimitPagination

    def get_serializer_class(self):
        class ExtendedHospitalisationSerializer(HospitalisationSerializer):
            zkf_patient = PatientSerializer()
            zkf_redacteur = UserSerializer()

            class Meta(HospitalisationSerializer.Meta):
                depth = 1

        return ExtendedHospitalisationSerializer

    def get_queryset(self, *args, **kwargs):
        users = self.request.query_params.get("users", None)

        if users is None:
            # check if user is in users_lies => get compte rendu of current user
            if self.request.user in self.request.user.profile.praticiens_lies.all():
                users = [self.request.user]
            # else: display all compte rendus of users lies
            else:
                users = self.request.user.profile.praticiens_lies.all()

        crh = (
            Hospitalisation.objects.select_related("zkf_patient")
            .filter(
                # zkf_redacteur__profile__zkf_environnement_user__in=[
                #     self.request.user.profile.zkf_environnement_user
                # ],
                zkf_redacteur__in=users,
                deleted=False,
                sortie_date__lte=datetime.datetime.now(),  # filter only crh that are already done
            )
            .exclude(statut_crh__in=["envoye", "non_statue"])
            .order_by("-entree_date")
        )

        status = self.request.query_params.get("status", None)
        if status:
            # Define the list of allowed statuses for filtering CRH
            allowed_crh_statuses = [
                "attente_validation",
                "valide",
                "non_fait",
                "transcrit",
            ]

            if status in allowed_crh_statuses:
                crh = crh.filter(statut_crh=status)
            # Optional: Handle unexpected status values if necessary

        return crh
