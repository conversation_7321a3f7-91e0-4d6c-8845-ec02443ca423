from rest_framework import serializers
from contact.models import (
    Contact,
    Correspondant,
    Email,
    Telephone,
    Adresse,
    Ville,
    FormulePolitesse,
    UserComment,
    ContactCategorie,
    CorrespondantPatient,
)


# https://www.linkedin.com/pulse/friday-quick-tip-dynamic-depth-serialization-django-rest-neidinger/
class DynamicDepthSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.Meta.depth = self.context.get("depth", 0)


class ContactCategorieSerializer(DynamicDepthSerializer):

    class Meta:
        model = ContactCategorie
        fields = "__all__"
        read_only_fields = [
            "created_on",
            "modified_on",
            "created_by",
            "modified_by",
        ]


class EmailSerializer(DynamicDepthSerializer):
    class Meta:
        model = Email
        fields = "__all__"


class FormulePolitesseSerializer(DynamicDepthSerializer):
    class Meta:
        model = FormulePolitesse
        fields = "__all__"


class TelephoneSerializer(DynamicDepthSerializer):
    class Meta:
        model = Telephone
        fields = "__all__"


class AdresseSerializer(DynamicDepthSerializer):
    class Meta:
        model = Adresse
        fields = "__all__"


class VilleSerializer(DynamicDepthSerializer):
    class Meta:
        model = Ville
        fields = "__all__"


class UserCommentSerializer(DynamicDepthSerializer):
    class Meta:
        model = UserComment
        fields = "__all__"


class CorrespondantSerializer(DynamicDepthSerializer):

    specialite_string = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Correspondant
        # fields = "__all__"
        exclude = ["formule_politesse_debut", "formule_politesse_fin", "zkf_contact"]
        read_only_fields = [
            "created_on",
            "modified_on",
            "created_by",
            "modified_by",
        ]

    def get_specialite_string(self, obj):

        return [spe.spe for spe in obj.specialite.all()]


class ContactSerializer(DynamicDepthSerializer):

    class Meta:
        model = Contact
        fields = "__all__"
        read_only_fields = [
            "zkf_environnement_contact",
            "zkf_contactcategorie",
        ]

    def validate_nom(self, value):
        value = value.title()
        return value

    def validate_prenom(self, value):
        value = value.title()
        return value

    def create(self, validated_data):
        contact = Contact.objects.filter(
            nom=validated_data["nom"],
            prenom=validated_data["prenom"],
        )
        if contact.exists():
            raise serializers.ValidationError(
                {
                    "error": "PATIENT_ALREADY_EXISTS",
                    "patient": ContactSerializer(contact.first()).data,
                },
            )

        return super().create(validated_data)

    def update(self, instance, validated_data):

        if all(
            key in validated_data for key in ("nom", "prenom")
        ):  # check if keys are in the data
            if (
                validated_data["nom"] != instance.nom
                or validated_data["prenom"] != instance.prenom
            ):
                contact = Contact.objects.filter(
                    nom=validated_data["nom"],
                    prenom=validated_data["prenom"],
                )
                if contact.exists():
                    raise serializers.ValidationError(
                        {
                            "error": "PATIENT_ALREADY_EXISTS",
                            "patient": ContactSerializer(contact.first()).data,
                        },
                    )
                return super().update(instance, validated_data)
            else:
                return super().update(instance, validated_data)
        else:
            instance = super().update(instance, validated_data)
            return instance


class CorrespondantPatientSerializer(serializers.ModelSerializer):
    # get contact details - serialized
    contact = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = CorrespondantPatient
        fields = ["contact", "send_report", "referral_source"]

    def get_contact(self, obj):
        if obj.contact:
            return ContactSerializer(obj.contact, context=self.context).data
        return None
