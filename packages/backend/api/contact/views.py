from rest_framework import generics
from rest_framework import permissions
from django.shortcuts import get_object_or_404
from rest_framework.exceptions import ValidationError

# models
from patient.models import Patient

from api.contact.serializers import (
    ContactSerializer,
    CorrespondantSerializer,
    EmailSerializer,
    TelephoneSerializer,
    AdresseSerializer,
    VilleSerializer,
    CorrespondantPatientSerializer,
)
from contact.models import (
    Contact,
    Email,
    Telephone,
    Adresse,
    Ville,
    ContactCategorie,
    Correspondant,
    CorrespondantPatient,
)

# script to search contact/correspondant
from contact.scripts.query_contact import query_contact


class ContactList(generics.ListCreateAPIView):
    queryset = Contact.objects.all()
    serializer_class = ContactSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):

        if self.request.query_params.get("search") is not None:
            search = self.request.query_params.get("search")
            queryset = query_contact(self.request, search)
            return queryset

        elif self.request.query_params.get("patient") is not None:
            patient = self.request.query_params.get("patient")
            contacts = CorrespondantPatient.objects.filter(patient=patient)

            return contacts

        else:
            return Contact.objects.filter(
                zkf_environnement_contact=self.request.user.profile.zkf_environnement_user
            )

    def perform_create(self, serializer):
        # define "correspondant" categorie as default and the only choice !
        zkf_contactcategorie = ContactCategorie.objects.get(
            id="f052c00e-22ac-47b6-abeb-358cabcfb608"
        )

        email = self.request.data.get("email", None)
        telephone = self.request.data.get("telephone", None)
        adresse = self.request.data.get("adresse", None)

        # create contact:
        # add the environnement user and the contact categorie to the validated data
        instance = serializer.save(
            zkf_environnement_contact=self.request.user.profile.zkf_environnement_user,
            zkf_contactcategorie=zkf_contactcategorie,
        )

        email_serializer = EmailSerializer(data=email, many=True)
        telephone_serializer = TelephoneSerializer(data=telephone, many=True)
        adresse_serializer = AdresseSerializer(data=adresse, many=True)
        # correspondant_serializer = CorrespondantSerializer(data=correspondant)

        if email_serializer.is_valid():
            email_serializer.save(zkf_contact=instance)
        else:
            raise ValidationError(email_serializer.errors)

        if telephone_serializer.is_valid():
            telephone_serializer.save(zkf_contact=instance)
        else:
            raise ValidationError(telephone_serializer.errors)

        if adresse_serializer.is_valid():
            adresse_serializer.save(zkf_contact=instance)
        else:
            raise ValidationError(adresse_serializer.errors)

        # if correspondant_serializer.is_valid():
        #     correspondant_serializer.save(zkf_contact=instance)
        # else:
        #     raise ValidationError(correspondant_serializer.errors)


class ContactDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Contact.objects.all()
    serializer_class = ContactSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def perform_update(self, serializer):
        # Save the contact instance first
        instance = serializer.save()

        # Check if email, telephone or adresse data was included in the PATCH request
        email = self.request.data.get("email", None)
        telephone = self.request.data.get("telephone", None)
        adresse = self.request.data.get("adresse", None)

        # Process email data if provided
        if email is not None:
            email_serializer = EmailSerializer(data=email, many=True)
            if email_serializer.is_valid():
                # Remove existing email records for this contact
                Email.objects.filter(zkf_contact=instance).delete()
                # Create new email records
                email_serializer.save(zkf_contact=instance)
            else:
                raise ValidationError(email_serializer.errors)

        # Process telephone data if provided
        if telephone is not None:
            telephone_serializer = TelephoneSerializer(data=telephone, many=True)
            if telephone_serializer.is_valid():
                # Remove existing telephone records for this contact
                Telephone.objects.filter(zkf_contact=instance).delete()
                # Create new telephone records
                telephone_serializer.save(zkf_contact=instance)
            else:
                raise ValidationError(telephone_serializer.errors)

        # Process adresse data if provided
        if adresse is not None:
            adresse_serializer = AdresseSerializer(data=adresse, many=True)
            if adresse_serializer.is_valid():
                # Remove existing adresse records for this contact
                Adresse.objects.filter(zkf_contact=instance).delete()
                # Create new adresse records
                adresse_serializer.save(zkf_contact=instance)
            else:
                raise ValidationError(adresse_serializer.errors)


class EmailList(generics.ListCreateAPIView):
    queryset = Email.objects.all()
    serializer_class = EmailSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):
        patient = self.request.query_params.get("patient", None)
        contact = self.request.query_params.get("contact", None)

        if patient is not None:
            return Email.objects.filter(
                zkf_patient__id=patient,
            )
        elif contact is not None:
            return Email.objects.filter(
                zkf_contact__id=contact,
            )
        else:
            return Email.objects.none()

    def perform_create(self, serializer):
        patient = self.request.query_params.get("patient", None)
        contact = self.request.query_params.get("contact", None)
        if patient is not None:
            patient = get_object_or_404(Patient, id=patient)
            serializer.save(zkf_patient=patient)
        elif contact is not None:
            contact = get_object_or_404(Contact, id=contact)
            serializer.save(zkf_contact=contact)

        else:
            raise ValidationError("Error : You must provide a contact or a patient")


class EmailDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Email.objects.all()
    serializer_class = EmailSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class TelephoneList(generics.ListCreateAPIView):
    queryset = Telephone.objects.all()
    serializer_class = TelephoneSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):
        patient = self.request.query_params.get("patient", None)
        contact = self.request.query_params.get("contact", None)

        if patient is not None:
            return Telephone.objects.filter(
                zkf_patient__id=patient,
            )
        elif contact is not None:
            return Telephone.objects.filter(
                zkf_contact__id=contact,
            )
        else:
            return Telephone.objects.none()

    def perform_create(self, serializer):
        patient = self.request.query_params.get("patient", None)
        contact = self.request.query_params.get("contact", None)
        if patient is not None:
            patient = get_object_or_404(Patient, id=patient)
            serializer.save(zkf_patient=patient)
        elif contact is not None:
            contact = get_object_or_404(Contact, id=contact)
            serializer.save(zkf_contact=contact)

        else:
            raise ValidationError("Error : You must provide a contact or a patient")


class TelephoneDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Telephone.objects.all()
    serializer_class = TelephoneSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class CorrespondantList(generics.ListCreateAPIView):
    queryset = Contact.objects.all()
    serializer_class = CorrespondantSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):

        if self.request.query_params.get("search") is not None:
            search = self.request.query_params.get("search")
            queryset = query_contact(self.request, search)
            return queryset

        elif self.request.query_params.get("patient") is not None:
            patient = self.request.query_params.get("patient")
            patient = get_object_or_404(Patient, id=patient)
            contacts = patient.correspondants.all()
            return contacts

        else:
            return Contact.objects.filter(
                zkf_contact__zkf_environnement_contact=self.request.user.profile.zkf_environnement_user
            )


class CorrespondantDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Correspondant.objects.all()
    serializer_class = CorrespondantSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class AdresseList(generics.ListCreateAPIView):
    queryset = Adresse.objects.all()
    serializer_class = AdresseSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):
        patient = self.request.query_params.get("patient", None)
        contact = self.request.query_params.get("contact", None)

        if patient is not None:
            return self.queryset.filter(
                zkf_patient__id=patient,
            )
        elif contact is not None:
            return self.queryset.filter(
                zkf_contact__id=contact,
            )
        else:
            return Adresse.objects.none()

    def perform_create(self, serializer):
        patient = self.request.query_params.get("patient", None)
        contact = self.request.query_params.get("contact", None)
        if patient is not None:
            patient = get_object_or_404(Patient, id=patient)
            serializer.save(zkf_patient=patient)
        elif contact is not None:
            contact = get_object_or_404(Contact, id=contact)
            serializer.save(zkf_contact=contact)

        else:
            raise ValidationError("Error : You must provide a contact or a patient")


class AdresseDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Adresse.objects.all()
    serializer_class = AdresseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class VilleList(generics.ListCreateAPIView):
    queryset = Ville.objects.all()
    serializer_class = VilleSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):
        queryset = self.queryset

        if self.request.query_params.get("zip_code") is not None:
            zip_code = self.request.query_params.get("zip_code")
            queryset = queryset.filter(code_postal__contains=zip_code)
            return queryset

        return Ville.objects.all()


class CorrespondantPatientList(generics.ListAPIView):
    queryset = CorrespondantPatient.objects.all()
    serializer_class = CorrespondantPatientSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self):
        patient_id = self.request.query_params.get("patient", None)

        if patient_id is not None:
            return CorrespondantPatient.objects.filter(patient_id=patient_id)

        return CorrespondantPatient.objects.none()
