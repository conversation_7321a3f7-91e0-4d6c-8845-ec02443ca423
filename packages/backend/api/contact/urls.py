from django.urls import path

from api.contact.views import (
    ContactList,
    ContactDetail,
    EmailList,
    EmailDetail,
    TelephoneList,
    TelephoneDetail,
    CorrespondantList,
    CorrespondantDetail,
    AdresseList,
    <PERSON><PERSON>eDetail,
    VilleList,
    CorrespondantPatientList,
)

# route api: api/contact/

urlpatterns = [
    path("", ContactList.as_view(), name="contacts-list"),
    path("<uuid:pk>", ContactDetail.as_view(), name="contact-detail"),
    path("correspondant", CorrespondantList.as_view(), name="correspondant-list"),
    path(
        "correspondant/<uuid:pk>",
        CorrespondantDetail.as_view(),
        name="correspondant-detail",
    ),
    path("<uuid:pk>", ContactDetail.as_view(), name="contact-detail"),
    path("email", EmailList.as_view(), name="email-list"),
    path("email/<uuid:pk>", EmailDetail.as_view(), name="email-detail"),
    path("telephone", TelephoneList.as_view(), name="telephone-list"),
    path("telephone/<uuid:pk>", TelephoneDetail.as_view(), name="telephone-detail"),
    path("adresse", AdresseList.as_view(), name="adresse-list"),
    path("adresse/<uuid:pk>", AdresseDetail.as_view(), name="adresse-detail"),
    path("ville", VilleList.as_view(), name="ville-list"),
    path(
        "correspondant-patient",
        CorrespondantPatientList.as_view(),
        name="correspondant-patient-list",
    ),
]
