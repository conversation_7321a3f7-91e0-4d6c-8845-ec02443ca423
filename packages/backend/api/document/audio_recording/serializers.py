from rest_framework import serializers
from document.models.audio_recording import AudioRecording
from api.patient.serializers import PatientSerializer
from api.consultation.serializers import ConsultationSerializer
from patient.models import Patient
from consultation.models import Consultation

from django.contrib.auth import get_user_model


class AudioRecordingSerializer(serializers.ModelSerializer):
    # Nested serializer for patient details (read-only in response)
    patient = PatientSerializer(read_only=True)
    # Nested serializer for consultation details (read-only in response)
    consultation = ConsultationSerializer(read_only=True)
    # Accept patient and consultation by ID when creating/updating
    patient_id = serializers.PrimaryKeyRelatedField(
        queryset=Patient.objects.all(),
        source="patient",
        write_only=True,
        required=False,
    )
    consultation_id = serializers.PrimaryKeyRelatedField(
        queryset=Consultation.objects.all(),
        source="consultation",
        write_only=True,
        required=False,
    )
    # Accept author by user ID when creating/updating
    auteur_id = serializers.PrimaryKeyRelatedField(
        queryset=get_user_model().objects.all(),
        source="auteur",
        write_only=True,
        required=False,
    )

    # This serializer allows you to POST patient_id and consultation_id (UUIDs) when creating an AudioRecording.
    # In response, you get full nested patient and consultation details.
    class Meta:
        model = AudioRecording
        fields = [
            "id",
            "created_at",
            "auteur",
            "auteur_id",  # Accept ID in write
            "document",
            "consultation",  # Nested details in response
            "consultation_id",  # Accept ID in write
            "hospitalisation",
            "patient",  # Nested details in response
            "patient_id",  # Accept ID in write
            "file",
            "title",
            "transcription",
            "transcription_corrected",
            "status",  # Validation status of the audio recording
        ]
        read_only_fields = ["id", "created_at"]
