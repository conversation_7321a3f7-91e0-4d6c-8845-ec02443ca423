from rest_framework import viewsets
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from document.models.audio_recording import AudioRecording
from impl.services.CorrectionTextService import CorrectionTextService
from api.document.audio_recording.serializers import AudioRecordingSerializer

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q


class AudioRecordingViewSet(viewsets.ModelViewSet):
    serializer_class = AudioRecordingSerializer
    parser_classes = [MultiPartParser, FormParser]

    def get_queryset(self):
        """
        Optionally restricts the returned audio recordings to a given patient
        and/or consultation, by filtering against a `patient` and `consultation`
        query parameter in the URL.
        """
        queryset = AudioRecording.objects.all().distinct()
        # Récupérer les valeurs des paramètres avec les noms envoyés par le client
        patient_uuid = self.request.query_params.get("patient", None)
        consultation_uuid = self.request.query_params.get("consultation", None)
        hospitalisation_uuid = self.request.query_params.get("hospitalisation", None)

        filters = Q()

        if patient_uuid:
            filters &= Q(patient__id=patient_uuid)

        if consultation_uuid:
            filters &= Q(consultation__id=consultation_uuid)

        if hospitalisation_uuid:
            filters &= Q(hospitalisation__id=hospitalisation_uuid)

        # Appliquer les filtres. order_by est optionnel mais recommandé.
        return queryset.filter(filters).order_by("-created_at")

    @action(detail=True, methods=["post"])
    def correct(self, request, pk=None):
        """
        Corrects the medical transcription using Mistral LLM and saves the result.
        """
        instance = self.get_object()
        if not instance.transcription:
            return Response(
                {"error": "No transcription found."}, status=status.HTTP_400_BAD_REQUEST
            )
        service = CorrectionTextService()
        corrected = service.correct_medical_transcription(instance.transcription)
        instance.transcription_corrected = corrected
        instance.save(update_fields=["transcription_corrected"])
        return Response({"transcription_corrected": corrected})
