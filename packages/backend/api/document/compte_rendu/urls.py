from django.urls import path
from api.document.compte_rendu.views import (
    CompteRenduDetail,
    CheckDuplicateCompteRendu,
    CompteRenduList,
)

# api route=: api/compte-rendu/?patient=uuid

urlpatterns = [
    path("", CompteRenduList.as_view(), name="compte-rendu-list"),
    path(
        "<uuid:pk>",
        CompteRenduDetail.as_view(),
        name="compte-rendu-detail",
    ),
    # check for doublon
    path(
        "duplicate", CheckDuplicateCompteRendu.as_view(), name="compte-rendu-duplicate"
    ),
]
