import uuid
from pathlib import Path
from rest_framework import generics
from rest_framework import permissions
from api.document.compte_rendu.serializers import CompteRenduSerializer
from document.models.compte_rendu_correspondant import CompteRenduCorrespondant
from contact.models import Correspondant
from rest_framework import generics, permissions
from .serializers import CompteRenduSerializer
from rest_framework.parsers import (
    <PERSON><PERSON>art<PERSON>ars<PERSON>,
    FormParser,
    JSONParser,
)
from models.document import DocumentMetadata
from module import module
from patient.patient_scripts.etat import update_etat_patient
from notification.signals import notification_signal
import datetime
from contact.models import Contact
from django.core.exceptions import ValidationError
from rest_framework.views import APIView
from rest_framework.response import Response
from notification.scripts_notifications.situation import (
    notification_upload_compte_rendu_correspondant,
    notification_convocation_patient_compterendu,
)


class CompteRenduList(generics.ListCreateAPIView):
    queryset = CompteRenduCorrespondant.objects.all()
    serializer_class = CompteRenduSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>)
    # Enable pagination with default settings
    # This will use the REST_FRAMEWORK.DEFAULT_PAGINATION_CLASS from settings.py
    # or the default PageNumberPagination with page_size=10

    def perform_create(self, serializer: CompteRenduSerializer) -> None:
        # Get zkf_auteur from the request data and remove extra whitespace.
        auteur_id_raw = self.request.data.get("zkf_auteur", "").strip()
        if auteur_id_raw:
            try:
                # Validate that the provided author ID is a valid UUID.
                auteur_uuid = uuid.UUID(auteur_id_raw)
                # Retrieve the contact corresponding to the UUID.
                auteur = Contact.objects.get(id=auteur_uuid)
            except ValueError:
                raise ValidationError(
                    {"zkf_auteur": "Invalid UUID format for contact id."}
                )
            except Contact.DoesNotExist:
                raise ValidationError({"zkf_auteur": "Contact not found."})
        else:
            # If no author is provided, set zkf_contact to None.
            auteur = None

        # Save the document using the provided (or null) contact.
        instance = serializer.save(
            zkf_contact=auteur,
            zkf_environnement=self.request.user.profile.zkf_environnement_user,
        )

        # Run NLP on the document text to extract its metadata.
        file_path = instance.examen_file.path
        text = module().text_extraction_service().extract_text(Path(file_path))
        metadata = module().nlp_service().extract_document_metadata(text)
        update_instance_with_metadata(instance, metadata)

        # Update the patient's state based on the document's dates.
        date_examen = [instance.date_examen, instance.date_entree, instance.date_sortie]
        date_examen = [d for d in date_examen if isinstance(d, datetime.date)]

        if date_examen and instance.zkf_patient:
            max_date = max(date_examen)
            update_etat_patient(instance.zkf_patient, max_date, "compte rendu")

        # Send a notification if requested.
        # notification = self.request.data.get("notification", None)
        # if notification == "true":
        # if (
        #     instance.zkf_patient
        #     and len(list(instance.zkf_patient.praticien_referent.all())) > 0
        # ):
        # extra = {
        #     "patient": str(intance.zkf_patient),
        #     "apercu": "/media/" + str(instance.examen_file),
        #     "format": "pdf",
        # }
        # notification compte rendu uploaded
        if instance.status == True and instance.zkf_patient:
            notification_upload_compte_rendu_correspondant(
                sender=self.request.user,
                compte_rendu_correspondant=instance,
                recipients=list(instance.zkf_patient.praticien_referent.all()),
                label="WARNING" if instance.should_be_contacted else "INFO",
            )

        if instance.should_be_contacted == True:
            notification_convocation_patient_compterendu(
                sender=self.request.user,
                compte_rendu_correspondant=instance,
                recipients=list(instance.zkf_patient.praticien_referent.all()),
                level="WARNING",
            )

    def get_queryset(self):
        patient_param = self.request.query_params.get("patient", None)
        status = self.request.query_params.get("status", None)
        sort_by = self.request.query_params.get("sort_by", None)

        if status == "not_validated":
            queryset = CompteRenduCorrespondant.objects.filter(
                status=False,
                zkf_environnement=self.request.user.profile.zkf_environnement_user,
            ).order_by("status", "-created_on")
        elif patient_param:
            patient_param = patient_param.strip()
            try:
                valid_uuid = uuid.UUID(patient_param)
                queryset = CompteRenduCorrespondant.objects.filter(
                    zkf_patient__id=valid_uuid
                )
            except ValueError:
                # Return an empty queryset if the patient parameter is invalid.
                queryset = CompteRenduCorrespondant.objects.none()
        else:
            # No patient parameter provided: return documents not attached to a patient.
            queryset = CompteRenduCorrespondant.objects.filter(zkf_patient__isnull=True)

        # Appliquer un tri spécifique selon le contexte
        if sort_by == "created_on":
            # Pour la page /documents, trier par date de création (du plus récent au plus ancien)
            queryset = queryset.order_by("-created_on")
        elif sort_by == "date_examen":
            # Pour la page patient/id_patient/document, trier par date d'examen
            queryset = queryset.order_by("-date_examen")
        # Si aucun tri spécifique n'est demandé, on utilise l'ordre par défaut du modèle

        return queryset


def update_instance_with_metadata(
    instance: CompteRenduCorrespondant, metadata: DocumentMetadata
) -> None:
    """
    Update a CompteRenduCorrespondant instance with data extracted by NLP.
    """
    # print(type(metadata.patient))
    if instance.zkf_patient is None and metadata.patient:
        # Vérifier que metadata.patient est une instance valide de Patient
        from patient.models import Patient

        if isinstance(metadata.patient, Patient) and metadata.patient.id:
            instance.zkf_patient = metadata.patient
        else:
            # Ne pas assigner si ce n'est pas une instance valide
            print(f"Warning: Invalid patient data received: {metadata.patient}")

    if metadata.type:
        instance.type_compte_rendu = metadata.type

    if metadata.specialties:
        instance.specialite.set(metadata.specialties)

    # Assign author to zkf_contact if available
    if metadata.auteur:
        # Check if the author is a Contact object or ContactTraits
        if hasattr(metadata.auteur, "id") and metadata.auteur.id:
            # It's a Contact object with a valid ID
            instance.zkf_contact = metadata.auteur
        elif hasattr(metadata.auteur, "last_name") and metadata.auteur.last_name:
            # It's ContactTraits, try to find a matching Contact
            from contact.models import Contact
            from django.db.models import Q

            # Try to find a matching contact
            contacts = Contact.objects.filter(
                Q(nom__iexact=metadata.auteur.last_name)
                & Q(prenom__iexact=metadata.auteur.first_name)
            )
            if contacts.exists():
                instance.zkf_contact = contacts.first()
            else:
                print(
                    f"Warning: No matching contact found for author: {metadata.auteur.last_name} {metadata.auteur.first_name}"
                )
                # Ajouter l'information comme attribut temporaire (ne sera pas sauvegardé en base)
                instance._contact_not_found = f"Non trouvé dans la base : {metadata.auteur.last_name} {metadata.auteur.first_name}"

    # Update the document date (e.g., date_examen) if provided.
    if metadata.document_date:
        try:
            # If the document_date is a string (e.g., "2025-03-17"), convert it to a date.
            from datetime import datetime

            instance.date_examen = datetime.strptime(
                metadata.document_date, "%Y-%m-%d"
            ).date()
        except ValueError:
            pass

    if metadata.patient_should_be_contacted is not None:
        instance.should_be_contacted = metadata.patient_should_be_contacted

    instance.status = False
    instance.save()


class CompteRenduDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = CompteRenduCorrespondant.objects.all()
    serializer_class = CompteRenduSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def perform_update(self, serializer: CompteRenduSerializer) -> None:
        # auteur = Correspondant.objects.get(zkf_contact=self.request.data["zkf_auteur"])
        instance = serializer.save()

        # Update the patient's state based on the document's dates.
        date_examen = [instance.date_examen, instance.date_entree, instance.date_sortie]
        date_examen = [d for d in date_examen if isinstance(d, datetime.date)]

        if date_examen:
            max_date = max(date_examen)
            update_etat_patient(instance.zkf_patient, max_date)

        # update document status from False to True
        if instance.status == True and serializer.validated_data.get("status", False):
            # Send a notification if requested.
            notification_upload_compte_rendu_correspondant(
                sender=self.request.user,
                compte_rendu_correspondant=instance,
                recipients=list(instance.zkf_patient.praticien_referent.all()),
                level="WARNING" if instance.should_be_contacted else "INFO",
            )

        # if should_be_contacted changed from False to True, send a notification
        if (
            serializer.validated_data.get("should_be_contacted", False)
            and instance.should_be_contacted == True
        ):
            notification_convocation_patient_compterendu(
                sender=self.request.user,
                compte_rendu_correspondant=instance,
                recipients=list(instance.zkf_patient.praticien_referent.all()),
                level="WARNING",
            )


class CheckDuplicateCompteRendu(APIView):

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        Check for duplicate CompteRenduCorrespondant entries based on the provided zkf_patient, auteur and date_examen.
        """
        zkf_patient = request.data.get("zkf_patient")
        date_examen = request.data.get("date_examen")
        zkf_auteur = request.data.get("zkf_auteur")

        if not zkf_patient or not date_examen and not zkf_auteur:
            return Response(
                {"error": "zkf_patient, zkf_auteur and date_examen are required."},
                status=400,
            )

        try:
            compte_rendu = CompteRenduCorrespondant.objects.filter(
                zkf_patient=zkf_patient, date_examen=date_examen
            )
            return Response(
                {
                    "exists": True,
                    "compte_rendus": CompteRenduSerializer(
                        compte_rendu, many=True
                    ).data,
                }
            )
        except CompteRenduCorrespondant.DoesNotExist:
            return Response({"exists": False})
