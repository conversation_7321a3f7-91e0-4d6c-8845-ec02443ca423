from rest_framework import serializers
from document.models.compte_rendu_correspondant import CompteRenduCorrespondant
from api.audit.serializers import AuditableSerializer
from api.accounts.serializers import SpecialiteSerializer


class CompteRenduSerializer(AuditableSerializer, serializers.ModelSerializer):

    # Ajouter des champs pour les noms des patients et des auteurs
    patient_nom = serializers.SerializerMethodField()
    auteur_nom = serializers.SerializerMethodField()
    contact_not_found = serializers.SerializerMethodField()
    should_be_contacted = serializers.BooleanField(
        required=False
    )  # Allow modification, make it optional
    specialites = SpecialiteSerializer(source="specialite", many=True, read_only=True)

    class Meta:
        model = CompteRenduCorrespondant
        fields = "__all__"

    def get_patient_nom(self, obj):
        if obj.zkf_patient:
            # Retourner le nom complet du patient (nom + prénom)
            return (
                f"{obj.zkf_patient.nom} {obj.zkf_patient.prenom}"
                if obj.zkf_patient.nom
                else ""
            )
        return ""

    def get_auteur_nom(self, obj):
        if obj.zkf_contact:
            # Retourner le nom complet de l'auteur (nom + prénom)
            return (
                f"{obj.zkf_contact.prenom} {obj.zkf_contact.nom}"
                if obj.zkf_contact.nom
                else ""
            )
        # Si l'attribut _contact_not_found existe, le retourner
        if hasattr(obj, "_contact_not_found"):
            return obj._contact_not_found
        return ""

    def get_contact_not_found(self, obj):
        # Retourner l'information sur le contact non trouvé si disponible
        if hasattr(obj, "_contact_not_found"):
            return obj._contact_not_found
        return ""

    def save(self, **kwargs) -> CompteRenduCorrespondant:
        return super().save(**kwargs)
