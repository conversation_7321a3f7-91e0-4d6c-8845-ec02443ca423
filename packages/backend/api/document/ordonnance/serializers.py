from rest_framework import serializers
from document.models.ordonnance import (
    Ordonnance,
    OrdonnanceTemplate,
    CategorieOrdonnance,
)


class OrdonnanceSerializer(serializers.ModelSerializer):
    model_app_datas = serializers.SerializerMethodField()

    class Meta:
        model = Ordonnance
        fields = "__all__"  # ('id', 'nom', 'prenom', ...)

    def get_model_app_datas(self, obj):
        return {
            "model": self.Meta.model.__name__.lower(),
            "app": self.Meta.model._meta.app_label,
            # "template": obj.template_ordonnance(),
        }


class OrdonnanceTemplateSerializer(serializers.ModelSerializer):
    suggested = serializers.SerializerMethodField()

    class Meta:
        model = OrdonnanceTemplate
        # fields = "__all__"
        exclude = [
            "ordonnance_template_hospitalisation",
            "ordonnance_template_consultation",
        ]

    # add prediction au template de l'ordonnance if sent in objects (custom display)
    def get_suggested(self, obj):
        suggested = obj.suggested if hasattr(obj, "suggested") else False
        return suggested


class CategorieOrdonnanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = CategorieOrdonnance
        fields = "__all__"
