from rest_framework import generics
from rest_framework import permissions
from api.document.ordonnance.serializers import (
    OrdonnanceSerializer,
    OrdonnanceTemplateSerializer,
    CategorieOrdonnanceSerializer,
)
from document.models.ordonnance import (
    Ordonnance,
    OrdonnanceTemplate,
    CategorieOrdonnance,
)
from patient.models import Patient
from hospitalisation.models import Hospitalisation
from consultation.models import Consultation
from impression.models import GestionnaireImpression
from django.core.exceptions import ValidationError

# ML models
from babylone.core_scripts.machine_learning.multi_label_text_classification import (
    pickle_file_path,
)
import pickle
from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User


class OrdonnanceList(generics.ListCreateAPIView):
    queryset = Ordonnance.objects.all()
    serializer_class = OrdonnanceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # queryset
        ordonnances = self.queryset

        # get situations/variables in which you need to retrieve ordonnances
        patient = self.request.query_params.get("patient", None)
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        consultation = self.request.query_params.get("consultation", None)
        operation = self.request.query_params.get("operation", None)

        if patient is not None:
            ordonnances = ordonnances.filter(zkf_patient=patient)
        elif hospitalisation is not None:
            ordonnances = ordonnances.filter(zkf_hospitalisation=hospitalisation)
        elif consultation is not None:
            ordonnances = ordonnances.filter(zkf_consultation=consultation)
        elif operation is not None:
            ordonnances = ordonnances.filter(zkf_operation=operation)
        elif (
            patient is None
            and hospitalisation is None
            and consultation is None
            and operation is None
        ):
            raise ValidationError("you must provide a context for ordonnances")

        # sorte ordonnances by date
        ordonnances = ordonnances.order_by("-date_ordonnance")
        return ordonnances

    def perform_create(self, serializer):
        instance = serializer.save()

        # get situations/variables in which you need to retrieve ordonnances
        patient = self.request.query_params.get("patient", None)
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        consultation = self.request.query_params.get("consultation", None)
        operation = self.request.query_params.get("operation", None)

        # if patient is not None:
        #     instance.ordonnances.filter(zkf_patient=patient)
        if hospitalisation is not None:
            instance.ordonnance_template_hospitalisation.add(hospitalisation)
        elif consultation is not None:
            instance.ordonnance_template_consultation.add(consultation)
        # elif operation is not None:
        #     ordonnances = ordonnances.filter(zkf_operation=operation)


class OrdonnanceDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Ordonnance.objects.all()
    serializer_class = OrdonnanceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_destroy(self, instance):
        # delete from print manager if exists
        # fetch ordonnance from print manager
        ordonnance = GestionnaireImpression.objects.filter(zkf_id=instance.id)
        if ordonnance.exists():
            ordonnance = ordonnance.first()
            # delete ordonnance from print manager
            ordonnance.delete()
        # delete ordonnance
        return super().perform_destroy(instance)


class OrdonnanceTemplateList(generics.ListCreateAPIView):
    queryset = OrdonnanceTemplate.objects.all()
    serializer_class = OrdonnanceTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        # get situations/variables in which you need to retrieve ordonnances
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        consultation = self.request.query_params.get("consultation", None)
        predictions = []

        # model for ml predictions
        model = option = text = None
        hospitalisation_object = consultation_object = None
        if hospitalisation is not None:
            hospitalisation_object = get_object_or_404(
                Hospitalisation, id=hospitalisation
            )
            model = "hospitalisation"
            option = "ordonnances_hospitalisation"
            filename = "ordonnances_hospitalisation_clf.pkl"
            text = (
                hospitalisation_object.motif_hospitalisation
                + hospitalisation_object.histoire_maladie
            )
        elif consultation is not None:
            consultation_object = get_object_or_404(Consultation, id=consultation)
            model = "consultation"
            option = "ordonnances_consultation"
            filename = "ordonnances_consultation_clf.pkl"
            text = consultation_object.motif_consultation

        # Get trained model for ordonnances
        predictions = OrdonnanceTemplate.objects.none()
        if model is not None and option is not None and text is not None:
            try:
                path = pickle_file_path(
                    model=model,
                    option=option,
                    user=self.request.user,
                    filename=filename,
                )
                pickled_clf, pickled_multilabel_binarizer = pickle.load(
                    open(path, "rb")
                )
                q = text
                # q_pred = pickled_clf.predict([q])
                proba = pickled_clf.predict_proba([q])
                # print ("proba", proba)
                t = 0.2  # threshold value
                y_pred_threshold = (proba >= t).astype(int)
                # print ("new", y_pred_new)
                # print (pickled_multilabel_binarizer.classes_)
                prediction = pickled_multilabel_binarizer.inverse_transform(
                    y_pred_threshold
                )
                predictions = [
                    OrdonnanceTemplate.objects.get(id=str(x)) for x in prediction[0]
                ]

            except Exception as e:
                print(e)
                pass

        # get all ordonnances type for current user
        ordonnances_type = self.queryset
        if consultation_object is not None:
            ordonnances_type = (
                ordonnances_type.filter(zkf_user=consultation_object.zkf_redacteur)
                if consultation_object.zkf_redacteur
                else ordonnances_type.filter(zkf_user=self.request.user)
            )
        elif hospitalisation_object is not None:
            ordonnances_type = (
                ordonnances_type.filter(zkf_user=hospitalisation_object.zkf_redacteur)
                if hospitalisation_object.zkf_redacteur
                else ordonnances_type.filter(zkf_user=self.request.user)
            )
        else:
            ordonnances_type = ordonnances_type.filter(zkf_user=self.request.user)

        for ordonnance in ordonnances_type:
            ordonnance.suggested = False
            if ordonnance in predictions:
                ordonnance.suggested = True

        return ordonnances_type

    def perform_create(self, serializer):
        # TO BE DELETED WHEN EVERYTHING FIXED
        # create ordonnance template
        # if duplicate_user is not in request data
        if (
            not "duplicate_user" in self.request.data
            and not self.request.data["duplicate_user"]
        ):
            instance = serializer.save(zkf_user=self.request.user)

        # SEND DUPLICATE USERS
        # duplicate ordonnance template for selected users
        if "duplicate_user" in self.request.data:

            users = self.request.data["duplicate_user"]
            if not users:
                users = [self.request.data["zkf_user"]]
            # duplicate ordonnance template for selected users
            for user in users:
                user = User.objects.get(id=user)
                # self.request.data["zkf_user"] = user
                new_instance = OrdonnanceTemplateSerializer(data=self.request.data)
                if new_instance.is_valid():
                    new_instance.save(zkf_user=user)
                else:
                    print(new_instance.errors)


class OrdonnanceTemplateDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = OrdonnanceTemplate.objects.all()
    serializer_class = OrdonnanceTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]


class CategorieOrdonnanceList(generics.ListCreateAPIView):
    queryset = CategorieOrdonnance.objects.all()
    serializer_class = CategorieOrdonnanceSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None
