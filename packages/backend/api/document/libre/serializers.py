from rest_framework import serializers
from api.audit.serializers import AuditableSerializer
from document.models.libre import DocumentLibreTemplate, DocumentLibre


class DocumentLibreTemplateSerializer(AuditableSerializer, serializers.ModelSerializer):

    # add extra fields to the json response
    # https://stackoverflow.com/questions/73612304/add-extra-property-to-this-json-response-returned-by-django-rest-framework
    suggested = serializers.SerializerMethodField()

    class Meta:
        model = DocumentLibreTemplate
        # fields = "__all__"
        pagination_class = False
        exclude = [
            "document_libre_template_consultation",
            "document_libre_template_hospitalisation",
            "document_libre_template_operation",
        ]

    # add extra field: is the document libre template suggested ?
    # <PERSON><PERSON><PERSON>
    def get_suggested(self, obj):
        # print ("obj.suggested", obj.id)

        suggested = obj.suggested if hasattr(obj, "suggested") else False
        return suggested


class DocumentLibreSerializer(AuditableSerializer, serializers.ModelSerializer):

    model_app_datas = serializers.SerializerMethodField()

    class Meta:
        model = DocumentLibre
        fields = "__all__"
        pagination_class = False

    def get_model_app_datas(self, obj):
        return {
            "model": self.Meta.model.__name__.lower(),
            "app": self.Meta.model._meta.app_label,
            "template": obj.template_document(),
        }
