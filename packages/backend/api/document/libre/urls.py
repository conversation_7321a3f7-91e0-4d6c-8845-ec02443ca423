from django.urls import path
from api.document.libre.views import (
    DocumentLibreTemplateList,
    DocumentLibreTemplateDetail,
    DocumentLibreList,
    DocumentLibreDetail,
)

# api route=: api/document/libre

urlpatterns = [
    path("", DocumentLibreList.as_view(), name="document-libre-list"),
    path(
        "<uuid:pk>",
        DocumentLibreDetail.as_view(),
        name="document-libre-detail",
    ),
    path(
        "template",
        DocumentLibreTemplateList.as_view(),
        name="document-libre-template-list",
    ),
    path(
        "template/<uuid:pk>",
        DocumentLibreTemplateDetail.as_view(),
        name="document-libre-template-detail",
    ),
]
