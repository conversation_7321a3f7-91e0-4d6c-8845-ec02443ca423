from rest_framework import generics
from rest_framework import permissions
from rest_framework.response import Response

# models
from document.models.libre import DocumentLibreTemplate, DocumentLibre
from django.contrib.auth.models import User

# serializers
from api.document.libre.serializers import (
    DocumentLibreTemplateSerializer,
    DocumentLibreSerializer,
)
import re
import binascii

# shortcuts/get object
from django.shortcuts import get_object_or_404

from django.template import Context, Template  # render string with arguments

import base64
import uuid
from django.core.files.base import ContentFile


# Fonction pour convertir une chaîne base64 en fichier
# On utilise ContentFile pour créer un fichier à partir de la chaîne base64
def base64_to_file(data, filename=None):
    """
    Convert base64 PDF data to a PDF file.

    Args:
        base64_data (str): Base64 string with optional header (e.g., "data:application/pdf;base64,...")
        output_path (str, optional): Where to save the PDF. Defaults to a random filename.

    Returns:
        str: Path to the saved PDF file
    """

    try:
        # Vérification si data est un dict
        if isinstance(data, dict):
            data = data.get("content", data)

        # On sépare le header de la data (ex: 'data:application/pdf;base64,...')
        if ";base64," in data:
            header, data = data.split(";base64,")
        else:
            header = ""

        # Nettoyage : suppression des sauts de ligne, espaces, etc.
        data = data.replace("\n", "").replace("\r", "").strip()
        # Remove ALL whitespace characters (including spaces, tabs, etc.)
        data = re.sub(r"\s+", "", data)
        data = re.sub(r"[^A-Za-z0-9+/=]", "", data)

        missing_padding = len(data) % 4
        if missing_padding != 0:
            data += "=" * (4 - missing_padding)

        # Détection de l'extension selon le header
        ext = header.split("/")[-1] if "/" in header else "bin"

        # Génération d'un nom de fichier si besoin
        if not filename:
            filename = f"{uuid.uuid4()}.{ext}"

        # Décodage de la base64
        decoded_file = base64.b64decode(data, validate=True)

    except binascii.Error as e:
        raise ValueError(f"Invalid base64 data: {str(e)}. Length: {len(data)}")
    except Exception as e:
        raise ValueError(f"Failed to decode base64 data: {str(e)}")
    return ContentFile(decoded_file, name=filename)


class DocumentLibreTemplateList(generics.ListCreateAPIView):
    queryset = DocumentLibreTemplate.objects.all()
    serializer_class = DocumentLibreTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        user = (
            [self.request.user]
            if self.request.user in self.request.user.profile.praticiens_lies.all()
            else self.request.user.profile.praticiens_lies.all()
        )
        queryset = self.queryset.filter(
            zkf_environnement=self.request.user.profile.zkf_environnement_user,
            zkf_user__in=user,
        ).order_by("titre_document")
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        # get documents suggested for the user <=> has to be a queryset of DocumentLibreTemplate
        queryset_suggested = queryset[:2]
        for doc in queryset:
            if doc in queryset_suggested:
                doc.suggested = True
                doc.save()

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)

        return Response(serializer.data)

    def perform_create(self, serializer):
        user = self.request.query_params.get("user", None)
        current_user = self.request.user
        environment = None

        # Safely get environment from user profile
        if hasattr(current_user, "profile") and current_user.profile is not None:
            environment = current_user.profile.zkf_environnement_user

        if user is not None:
            user = get_object_or_404(User, pk=user)
            serializer.validated_data["zkf_user"] = user
            serializer.validated_data["zkf_environnement"] = (
                self.request.user.profile.zkf_environnement_user
            )

        file = None
        # check if file in request.data:
        # 1. is not empty
        # 2. is base64
        if (
            "file" in self.request.data
            and self.request.data["file"] != ""
            and self.request.data["file"] is not None
        ):
            # convert base64 to file
            file = base64_to_file(
                self.request.data["file"],
            )

        if not ("duplicate_user" in self.request.data):
            instance = serializer.save(
                zkf_user=self.request.user,
                zkf_environnement=self.request.user.profile.zkf_environnement_user,
            )
            if file is not None:
                # save file to instance
                instance.document_type_file.save(file.name, file)
                # save file to instance
                instance.save()

        # duplicate doc libre template for selected users
        if "duplicate_user" in self.request.data:
            # convert self.request.data which is a Querydict to dict
            datas = dict(self.request.data)
            users = datas["duplicate_user"]
            if not users:
                users = [datas["zkf_user"]]
            # remove duplicate_user from datas
            del datas["duplicate_user"]

            # duplicate ordonnance template for selected users
            for user in users:

                user = get_object_or_404(User, id=user)

                new_instance = DocumentLibreTemplateSerializer(data=datas)
                if new_instance.is_valid():

                    # Create with safe environment handling for duplicates too
                    dup_save_kwargs = {}
                    environment = self.request.user.profile.zkf_environnement_user
                    if environment:
                        dup_save_kwargs["zkf_environnement"] = environment
                    else:
                        raise ValueError("passing environment")
                    dup_save_kwargs["zkf_user"] = user
                    new_instance.save(**dup_save_kwargs)

                    # save file to instance
                    if file is not None:
                        new_instance.instance.document_type_file.save(file.name, file)

                else:
                    raise ValueError(new_instance.errors)
                    # print(new_instance.errors)
        return


class DocumentLibreTemplateDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = DocumentLibreTemplate.objects.all()
    serializer_class = DocumentLibreTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]


class DocumentLibreList(generics.ListCreateAPIView):
    queryset = DocumentLibre.objects.all()
    serializer_class = DocumentLibreSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        # Safely get operation parameter
        operation_id = None
        if hasattr(self.request, "query_params"):
            operation_id = self.request.query_params.get("operation", None)

        return DocumentLibre.objects.filter(zkf_operation=operation_id)

    def perform_create(self, serializer):
        # check if template exists => return boolean value
        doc_template_exists = (
            "zkf_document_template" in serializer.validated_data
            and serializer.validated_data["zkf_document_template"] is not None
        )

        # get document template id=> generate document with appropriate variables, save many to many fields
        if doc_template_exists == True:
            document_template = serializer.validated_data["zkf_document_template"]
            if (
                "zkf_hospitalisation" in serializer.validated_data
                and serializer.validated_data["zkf_hospitalisation"] is not None
            ):
                document_template.document_libre_template_hospitalisation.add(
                    serializer.validated_data["zkf_hospitalisation"]
                )
            elif (
                "zkf_operation" in serializer.validated_data
                and serializer.validated_data["zkf_operation"] is not None
            ):
                document_template.document_libre_template_operation.add(
                    serializer.validated_data["zkf_operation"]
                )
            elif (
                "zkf_consultation" in serializer.validated_data
                and serializer.validated_data["zkf_consultation"] is not None
            ):
                document_template.document_libre_template_consultation.add(
                    serializer.validated_data["zkf_consultation"]
                )

            # Generate corps_document with variables
            context = {
                "user": self.request.user,
                "hospitalisation": (
                    serializer.validated_data["zkf_hospitalisation"]
                    if "zkf_hospitalisation" in serializer.validated_data
                    else None
                ),
                "operation": (
                    serializer.validated_data["zkf_operation"]
                    if "zkf_operation" in serializer.validated_data
                    else None
                ),
                "consultation": (
                    serializer.validated_data["zkf_consultation"]
                    if "zkf_consultation" in serializer.validated_data
                    else None
                ),
                "patient": serializer.validated_data["zkf_patient"],
            }
            t = Template(document_template.corps_document)
            c = Context(context)
            serializer.validated_data["corps_document"] = t.render(c)

            # check if pdf is associated with the template
            if document_template.document_type_file:
                serializer.validated_data["document_libre_file"] = (
                    document_template.document_type_file
                )

        serializer.save()

        return


class DocumentLibreDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = DocumentLibre.objects.all()
    serializer_class = DocumentLibreSerializer
    permission_classes = [permissions.IsAuthenticated]
