from rest_framework import permissions
from consultation.models import Consultation, ConsultationType
from operation.models import Operation
from rest_framework.views import APIView
from rest_framework.response import Response
from django.apps import apps
from patient.models import Patient


# List all fields of Operation Model
class ModelFields(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request, *args, **kwargs):

        exclude = [
            "id",
            "created_on",
            "created_by",
            "modified_on",
            "modified_by",
            "deleted",
            "deleted_on",
            "deleted_by",
            "id",
            "zkf_environnement_patient",
            "zkf_assurance_sante",
            "dob",
        ]

        operation_fields = [
            f.name
            for f in Operation._meta.get_fields()
            if (
                not f.many_to_many
                and not f.one_to_many
                and not f.one_to_one
                and not f.many_to_one
            )  # not n.one_to_many or n.one_to_one) ]
        ]

        spe = self.request.user.profile.specialite_active.spe
        user_spe = "_".join(spe.split(" ")).lower()

        all_models = apps.all_models[user_spe]
        # all_models is an OrderedDict -> convert as list
        # https://stackoverflow.com/questions/10058140/accessing-items-in-an-collections-ordereddict-by-index
        # models = [(x[1].__name__, x[0]) for x in list(all_models.items())]
        models = [x[1].__name__ for x in list(all_models.items())]

        # create a dict with list of fields for each model
        models_fields = list()
        structured_fields = [
            {"label": e, "value": "{{operation." + e + "}}"}
            for e in operation_fields
            if e not in exclude
        ]
        models_fields.append(
            {
                "fields": structured_fields,
                "nom": "Operation",
                "model": "operation",
                "descriptiont": "Tous les champs d'une operation",
                "name_filter": "operation_full",
            }
        )

        # all models of user specialite principale
        for model_name in models:
            model = apps.get_model(user_spe + "." + model_name)  # e.g "accounts.User"

            fields = [
                x.name
                for x in model._meta.get_fields()
                if (
                    not x.many_to_many and not x.one_to_many and not x.one_to_one
                )  # and not x.many_to_one) ]
            ]
            # exclude fields
            fields = structured_fields = [
                {"label": e, "value": "{{" + model_name + "." + e + "}}"}
                for e in fields
                if e not in exclude
            ]
            models_fields.append(
                {
                    "fields": fields,
                    "nom": model._meta.verbose_name,
                    "model": model_name.lower(),
                    "description": "description " + model._meta.verbose_name,
                    "name_filter": str(self.request.user.profile.specialite_active.spe),
                }
            )

        # get all fields of patient model + demographic datas !
        patient_fields = [
            x.name
            for x in Patient._meta.get_fields()
            if (
                not x.many_to_many
                and not x.one_to_many
                and not x.one_to_one
                and x.name not in exclude
            )  # and not x.many_to_one) ]
        ]
        # remove dates and structure them ! (dob, date_etat...)

        # patient_fields.pop('dob')
        fields = structured_fields = [
            {"label": e, "value": "{{patient." + e + "}}"}
            for e in patient_fields
            if e not in exclude
        ]
        fields.append(
            {"label": "date de naissance", "value": "{{patient.dob|date:'d/m/Y'}}"}
        )

        models_fields.append(
            {
                "fields": fields,
                "nom": Patient._meta.model_name,
                "model": Patient._meta.model_name.lower(),
                "description": "description " + Patient._meta.model_name.lower(),
                "name_filter": "patient",
            }
        )
        models_fields.append(
            {
                "fields": [
                    {
                        "label": "titre de l'operation",
                        "value": "{{operation.titre_operation}}",
                    },
                    {
                        "label": "date de l'operation",
                        "value": "{{operation.operation_date|date:'d/m/Y'}}",
                    },
                ],
                "nom": "Operation",
                "model": "operation",
                "description": "operation short ",
                "name_filter": "operation_short",
            }
        )
        models_fields.append(
            {
                "fields": [
                    {"label": "date du jour", "value": '{% now "d/m/Y" %}'},
                    {
                        "label": "Corps du text de la consultation",
                        "value": "{{consultation.courrier_consultation}}",
                    },
                ],
                "nom": "Horodatage",
                "model": "",
                "description": "date du jour",
                "name_filter": "general",
            }
        )

        # text body of consultation/CRH...
        models_fields.append(
            {
                "fields": [
                    {"label": "corps du texte", "value": "{{corps}}"},
                ],
                "nom": "Texte déjà rédigé",
                "model": "",
                "description": "texte déjà rédigé",
                "name_filter": "general",
            }
        )

        return Response(models_fields)
