from django.urls import path

from api.hospitalisation.views import (
    HospitalisationList,
    HospitalisationDetail,
    HospitalisationFormParameters,
    PatientService,
    SuiviHospitalisationList,
    SuiviHospitalisationDetail,
    ConsigneSortieList,
    ConsigneSortieDetail,
    GenerateCrh,
    GenerateConsigneSortie,
)

# app_name = "hospitalisation" => api/hospitalisation/

urlpatterns = [
    path("", HospitalisationList.as_view(), name="hospitalisations-list"),
    path(
        "<uuid:pk>",
        HospitalisationDetail.as_view(),
        name="hospitalisation-detail",
    ),
    path(
        "<uuid:zkf_patient>/create",
        HospitalisationFormParameters.as_view(),
        name="hospitalisation-form-parameters",
    ),
    path("service", PatientService.as_view(), name="hospitalisations-service"),
    path(
        "<uuid:pk>/suivi",
        SuiviHospitalisationList.as_view(),
        name="suivi-list",
    ),
    path(
        "<uuid:pk>/suivi/<uuid:suivi_pk>",
        SuiviHospitalisationDetail.as_view(),
        name="suivi-detail",
    ),
    # Consignes de sortie
    path(
        "consigne-sortie/", ConsigneSortieList.as_view(), name="consignes-sortie-list"
    ),
    path(
        "consigne-sortie/<uuid:pk>",
        ConsigneSortieDetail.as_view(),
        name="consigne-sortie-detail",
    ),
    # generer CRH
    path(
        "<uuid:pk>/generate-crh",
        GenerateCrh.as_view(),
        name="generate-crh",
    ),
    # Generate consigne de sortie
    path(
        "generate-consigne-sortie",
        GenerateConsigneSortie.as_view(),
        name="generate-consigne-sortie",
    ),
]
