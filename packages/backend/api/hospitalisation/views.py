from rest_framework import generics
from rest_framework.views import APIView
from rest_framework.response import Response
from django.http import HttpResponse
from django.conf import settings
from django.db.models import Prefetch
from contact.models import Contact
import os
from patient.patient_scripts.etat import update_etat_patient
from django.core.exceptions import ValidationError
from accounts.models import Intervenant, Profile, Preferences_user
from rest_framework import permissions
from impression.views import create_html_string
from api.hospitalisation.serializers import (
    HospitalisationSerializer,
    SuiviSerializer,
    ConsignesSortieSerializer,
)
from hospitalisation.models import (
    Hospitalisation,
    Suivi,
    ConsigneSortie,
    HospitalisationConsigneSortie,
)
from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView
from api.accounts.serializers import (
    ServiceSerializer,
    SpecialiteSerializer,
    UserSerializer,
)
from api.operation.serializers import OperationSerializer
from babylone.authentication import KeycloakAuthentication

# generate consignes de surveillance
from django.template import Context, Template

# script to generate a crh
from hospitalisation.scripts.generate_crh import generate_crh
from operation.models import Operation
from django.contrib.auth.models import User
from accounts.models import Service, Specialite
from patient.models import Patient
from django.db.models import Q  # making complex queries
import datetime
from hospitalisation.scripts.filtering import retrieve_current_hospitalisation

from io import BytesIO
from xhtml2pdf import pisa


class HospitalisationList(generics.ListCreateAPIView):
    queryset = Hospitalisation.objects.all()
    serializer_class = HospitalisationSerializer
    # permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [KeycloakAuthentication]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self, *args, **kwargs):
        user = self.request.user
        patient = self.request.query_params.get("patient", None)
        # filter by only_active if in url
        only_active = self.request.query_params.get("only_active", None)
        if only_active is not None:
            hospitalisations = Hospitalisation.objects.filter(
                zkf_patient=patient, deleted=False, sortie_date__isnull=True
            ).order_by("-entree_date")
        else:
            hospitalisations = Hospitalisation.objects.filter(
                zkf_patient=patient, deleted=False
            ).order_by("-entree_date")

        return hospitalisations

    def perform_create(self, serializer):
        serializer.save()


class HospitalisationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Hospitalisation.objects.all()
    serializer_class = HospitalisationSerializer
    # permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [KeycloakAuthentication]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def perform_update(self, serializer):

        # convert current hospitalisation en "a programmer":
        if (
            serializer.validated_data.get("a_programmer") is True
            and self.get_object().a_programmer is False
        ):
            serializer.validated_data["entree_date"] = None
            serializer.validated_data["sortie_date"] = None

            # set all operation date to None
            operations = Operation.objects.filter(zkf_hospitalisation=self.get_object())
            for operation in operations:
                operation.operation_date = None
                operation.save()
        serializer.save()

        # update etat patient:
        if serializer.validated_data.get("sortie_date", None):
            # if sortie_date is set, update etat patient
            update_etat_patient(
                self.get_object().zkf_patient, serializer.validated_data["sortie_date"]
            )


# get parameters of an hospitalisation
class HospitalisationFormParameters(ObjectMultipleModelAPIView):
    querylist = []
    # permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [KeycloakAuthentication]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        patient_id = self.kwargs.get("zkf_patient", None)
        querylist = None
        service_hospitalisation = Service.objects.all()
        specialite_referente = Specialite.objects.all()
        redacteur = User.objects.all()
        operation_liee = Operation.objects.filter(
            zkf_patient=Patient.objects.get(id=patient_id)
        )
        medecin_referent = User.objects.all()

        # generate a list of queryset with operation, and related tables
        querylist = [
            {
                "queryset": service_hospitalisation,
                "serializer_class": ServiceSerializer,
                "label": "Service",
            },
            {
                "queryset": specialite_referente,
                "serializer_class": SpecialiteSerializer,
                "label": "Specialite",
            },
            {
                "queryset": redacteur,
                "serializer_class": UserSerializer,
                "label": "Redacteur",
            },
            {
                "queryset": operation_liee,
                "serializer_class": OperationSerializer,
                "label": "Operation Liee",
            },
            {
                "queryset": medecin_referent,
                "serializer_class": UserSerializer,
                "label": "medecin referent",
            },
        ]

        return querylist


class PatientService(generics.ListAPIView):
    queryset = Hospitalisation.objects.all()
    serializer_class = HospitalisationSerializer
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self, *args, **kwargs):

        user = self.request.user
        today = datetime.date.today()

        # Get user profile specialties and sites once to avoid repeated lookups
        user_specialties = user.profile.specialite_profile.all().values_list(
            "spe", flat=True
        )
        user_sites = user.profile.site.all().values_list("id", flat=True)

        # hospitalisations = Hospitalisation.objects.filter(
        #     Q(entree_date__lte=datetime.date.today()),
        #     Q(sortie_date__isnull=True) | Q(sortie_date__gte=datetime.date.today()),
        #     Q(deleted=False),
        #     # get all patients that are in the same specialite of the user
        #     Q(
        #         service_hospitalisation__specialite_service__spe__in=user.profile.specialite_profile.all().values(
        #             "spe"
        #         )
        #     ),
        # )  # .order_by("service_hospitalisation")

        # # Filter hospitalisations by site <=> Display patients/hospitlaisations filtered by site the user has access to !
        # hospitalisations = hospitalisations.filter(
        #     service_hospitalisation__site_service__in=user.profile.site.all().values(
        #         "id"
        #     )
        # )

        # Base queryset with essential filters
        queryset = (
            Hospitalisation.objects.filter(
                entree_date__lte=today,
                deleted=False,
            )
            .filter(Q(sortie_date__isnull=True) | Q(sortie_date__gte=today))
            .filter(
                service_hospitalisation__specialite_service__spe__in=user_specialties,
                service_hospitalisation__site_service__in=user_sites,
            )
        )

        # Add prefetch_related for operations with filtering
        queryset = queryset.prefetch_related(
            Prefetch(
                "operation_set",
                queryset=Operation.objects.filter(deleted=False).select_related(
                    "zkf_redacteur"
                ),
            )
        )

        # Add select_related for frequently accessed foreign keys
        queryset = queryset.select_related("service_hospitalisation")

        return queryset

    # for generating pdf
    def group_by_service(self, queryset):
        from itertools import groupby
        from operator import attrgetter

        # Trier d'abord par service
        sorted_hosp = sorted(
            queryset, key=attrgetter("service_hospitalisation.nom_service")
        )

        # Grouper par service
        grouped = {
            service: list(hospitals)
            for service, hospitals in groupby(
                sorted_hosp, key=attrgetter("service_hospitalisation.nom_service")
            )
        }
        return grouped

    def get(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        # if query params: pdf = True => return a pdf with previous queryset !
        if self.request.query_params.get("pdf", None) == "true":
            # Apply filtering based on parameters
            filter_type = self.request.query_params.get("filter", None)
            search_query = self.request.query_params.get("search", None)
            sort_field = self.request.query_params.get("sort_field", None)
            sort_direction = self.request.query_params.get("sort_direction", None)

            # Filter by user (doctor) if specified
            if filter_type == "user":
                queryset = self.get_queryset().filter(
                    medecin_referent__in=[self.request.user]
                )

            # Apply search filtering if provided
            if search_query:
                queryset = queryset.filter(
                    Q(zkf_patient__nom__icontains=search_query)
                    | Q(zkf_patient__prenom__icontains=search_query)
                )

            # Apply sorting if provided
            if sort_field:
                # Define mapping between frontend sort fields and model fields
                sort_field_mapping = {
                    "name": "zkf_patient__nom",
                    "entryDate": "entree_date",
                    "exitDate": "sortie_date",
                    "birthDate": "zkf_patient__dob",
                    "doctor": "medecin_referent__last_name",
                    "gender": "zkf_patient__sexe",
                }

                db_sort_field = sort_field_mapping.get(sort_field)
                if db_sort_field:
                    # Apply sort direction
                    if sort_direction == "desc":
                        db_sort_field = f"-{db_sort_field}"
                    queryset = queryset.order_by(db_sort_field)

            context = {}
            context["hospitalisations"] = self.group_by_service(queryset)
            context["user"] = self.request.user
            context["logo"] = os.path.join(
                str(settings.BASE_DIR), "assets/icons/meiflow-logo.png"
            )
            context["today"] = datetime.date.today()

            response = HttpResponse(content_type="application/pdf")
            response["Content-Transfer-Encoding"] = "binary"

            # generate a string (function is imported from impression.views)
            html_string = create_html_string(
                context=context,
                path=response,
                template="hospitalisation/pdf/synthese_hospitalises_pdf.html",
            )

            result = BytesIO()
            pdf = pisa.pisaDocument(BytesIO(html_string.encode("utf-8")), dest=result)

            if not pdf.err:
                return HttpResponse(result.getvalue(), content_type="application/pdf")
            else:
                return HttpResponse("Errors")
                # response = HttpResponse(file, content_type="application/pdf")
                # filename = "hospitalisation_summary.pdf"
                # response["Content-Transfer-Encoding"] = "binary"
                # return response
        else:
            return super().get(request, *args, **kwargs)


class SuiviHospitalisationList(generics.ListCreateAPIView):
    queryset = Suivi.objects.all()
    serializer_class = SuiviSerializer
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self, *args, **kwargs):

        suivis = Suivi.objects.filter(zkf_hospitalisation=self.kwargs.get("pk", None))

        return suivis

    def perform_create(self, serializer):

        hospitalisation = Hospitalisation.objects.get(id=self.kwargs.get("pk", None))

        serializer.save(
            created_by=self.request.user,
            zkf_hospitalisation=hospitalisation,
            zkf_patient=hospitalisation.zkf_patient,
        )


class SuiviHospitalisationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Suivi.objects.all()
    serializer_class = SuiviSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self, *args, **kwargs):
        suivi = Suivi.objects.get(id=self.kwargs.get("suivi_pk", None))

        return suivi

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class ConsigneSortieList(generics.ListCreateAPIView):
    queryset = ConsigneSortie.objects.all()
    serializer_class = ConsignesSortieSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def perform_create(self, serializer):
        # create ordonnance template
        # if duplicate_user is not in request data
        if not ("duplicate_user" in self.request.data):
            serializer.save(
                zkf_user=self.request.user,
                zkf_environnement=self.request.user.profile.zkf_environnement_user,
            )

        # duplicate ordonnance template for selected users
        if "duplicate_user" in self.request.data:
            users = self.request.data["duplicate_user"]
            if not users:
                users = [self.request.data["zkf_user"]]
            # duplicate ordonnance template for selected users
            for user in users:
                user = User.objects.get(id=user)
                new_instance = ConsignesSortieSerializer(data=self.request.data)
                if new_instance.is_valid():
                    new_instance.save(
                        zkf_user=user,
                        zkf_environnement=self.request.user.profile.zkf_environnement_user,
                    )
                else:
                    print(new_instance.errors)

    def get_queryset(self, *args, **kwargs):
        consignes = ConsigneSortie.objects.filter(zkf_user=self.request.user)

        suggested_list = ["0c901671-5e3f-4e44-a36e-44f0278630f4"]
        suggested_list = ConsigneSortie.objects.filter(id__in=suggested_list)
        for consigne in consignes:
            consigne.suggested = False
            if consigne in suggested_list:
                consigne.suggested = True
        return consignes


class ConsigneSortieDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = ConsigneSortie.objects.all()
    serializer_class = ConsignesSortieSerializer
    permission_classes = [permissions.IsAuthenticated]


class GenerateCrh(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, pk):
        # 1/ get specialite and related onetoone table
        hospitalisation = Hospitalisation.objects.get(id=pk)
        specialite = hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))
        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_specialite = "Antecedents" + "".join(
            x.capitalize() for x in specialite.split("_")
        )

        # 2/ check if there is antecedents filled in !
        antecedents = None
        if hasattr(hospitalisation, str(model_specialite).lower()):
            # 3/ load antecedents
            obj = getattr(hospitalisation, str(model_specialite).lower())
            antecedents = obj

        crh = generate_crh(hospitalisation, antecedents, request)

        return HttpResponse(crh)


class GenerateConsigneSortie(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        hospitalisation = Hospitalisation.objects.get(
            id=self.request.query_params.get("hospitalisation", None)
        )
        consigne_sortie = ConsigneSortie.objects.get(
            id=self.request.query_params.get("consigne_sortie", None)
        )

        # generate corps_document with variables
        context = {
            "patient": hospitalisation.zkf_patient,
            "hospitalisation": hospitalisation,
            "operations": Operation.objects.filter(zkf_hospitalisation=hospitalisation),
        }
        t = Template(consigne_sortie.texte_suivi)
        c = Context(context)
        corps = t.render(c)

        # add consigne de sortie to manytomany DB for machine learning training !
        HospitalisationConsigneSortie.objects.create(
            zkf_hospitalisation=hospitalisation,
            zkf_consigne_sortie=consigne_sortie,
            zkf_user=self.request.user,
        )
        # add consigne de sortie to manytomany DB for machine learning training !
        hospitalisation.consignes_sortie.add(consigne_sortie)

        return HttpResponse(corps)
