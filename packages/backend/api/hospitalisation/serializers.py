from rest_framework import serializers
from patient.models import Patient
from django.core import serializers as core_serializers
from hospitalisation.models import Hospitalisation, <PERSON><PERSON><PERSON>, ConsigneSortie
from api.accounts.serializers import (
    ServiceSerializer,
)
from operation.models import Operation
from api.sort.tag.serializers import TagList<PERSON>erial<PERSON><PERSON>ield, TaggitSerializer
from api.settings.serializers import DynamicDepthSerializer


class ConsignesSortieSerializer(serializers.ModelSerializer):

    suggested = serializers.SerializerMethodField()

    class Meta:
        model = ConsigneSortie
        fields = "__all__"
        read_only_fields = ["zkf_environnement", "zkf_user"]

    def get_suggested(self, obj):
        suggested = obj.suggested if hasattr(obj, "suggested") else False
        return suggested


class HospitalisationSerializer(TaggitSerializer, DynamicDepthSerializer):
    tags = TagListSerializerField(required=False)

    operations = serializers.SerializerMethodField()
    model_app_datas = serializers.SerializerMethodField()

    class Meta:
        model = Hospitalisation
        fields = "__all__"  # ('id', 'nom', 'prenom', ...)
        depth = 2

    def get_model_app_datas(self, obj):
        model_app_datas = {
            "model": self.Meta.model.__name__.lower(),
            "app": self.Meta.model._meta.app_label,
        }
        return model_app_datas

    def get_operations(self, obj):
        # operations = Operation.objects.filter(zkf_hospitalisation=obj, deleted=False)
        hospitalisation = obj
        operations = hospitalisation.operation_set.filter(deleted=False)
        operation_list = list()
        for operation in operations:
            operation_dict = {
                "id": operation.id,
                "operation_date": operation.operation_date,
                "operation_start": operation.operation_start,
                "zkf_redacteur_id": (
                    operation.zkf_redacteur.id if operation.zkf_redacteur else None
                ),
                "zkf_redacteur_last_name": (
                    operation.zkf_redacteur.last_name
                    if operation.zkf_redacteur
                    else None
                ),
                "titre_operation": operation.titre_operation,
            }
            operation_list.append(operation_dict)

        return operation_list

    # https://stackoverflow.com/questions/64048863/how-to-display-field-value-instead-of-id-for-foreign-key-in-django-rest-framewor
    def to_representation(self, instance, *args, **kwargs):

        # print (instance)
        rep = super().to_representation(instance)
        rep["service_hospitalisation"] = ServiceSerializer(
            instance.service_hospitalisation
        ).data
        # .filter(deleted=False, service_hospitalisaiton = filter_service_hospitalisation(request.user))
        # rep['specialite_referente'] = SpecialiteSerializer(instance.specialite_referente).data
        # rep['redacteur'] = UserSerializer(instance.redacteur).data
        # rep['operation_liee'] = OperationSerializer(instance.operation_liee).data
        return rep

    def create(self, validated_data):
        # get nested datas
        if "operations" in validated_data:
            operation = validated_data.pop("operations")
            hospitalisation = Hospitalisation.objects.create(**validated_data)
            # save new nested datas
            for o in operation:
                Operation.objects.create(zkf_hospitalisation=hospitalisation, **o)
        else:
            return super().create(validated_data)
        return hospitalisation


class SuiviSerializer(DynamicDepthSerializer):
    class Meta:
        model = Suivi
        fields = "__all__"
