from api.intervenant.serializers import (
    IntervenantSerializer,
)
from api.accounts.serializers import UserSerializer
from rest_framework import permissions
from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView

from accounts.scripts.filter_intervenant import (
    get_intervenant_operateur,
    get_intervenant_anesthesiste,
    get_intervenant_aide_operatoire,
)

from accounts.scripts.filter_user import get_operateur


# get intervenants for an event
class IntervenantEvent(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        operation = self.request.query_params.get("operation", None)
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        consultation = self.request.query_params.get("consultation", None)

        querylist = None
        anethesiste = get_intervenant_anesthesiste(
            self.request.user, operation, hospitalisation, consultation
        )
        operateur = get_intervenant_operateur(
            self.request.user, operation, hospitalisation, consultation
        )
        redacteur = get_operateur(
            self.request.user, operation, hospitalisation, consultation
        )
        aide_operatoire = get_intervenant_aide_operatoire(
            self.request.user, operation, hospitalisation, consultation
        )

        # generate a list of queryset with operation, and related tables
        querylist = [
            {
                "queryset": anethesiste,
                "serializer_class": IntervenantSerializer,
                "label": "Anesthesiste",
            },
            {
                "queryset": operateur,
                "serializer_class": IntervenantSerializer,
                "label": "Operateur",
            },
            {
                "queryset": redacteur,
                "serializer_class": UserSerializer,
                "label": "Redacteur",
            },
            {
                "queryset": aide_operatoire,
                "serializer_class": IntervenantSerializer,
                "label": "Aide Operatoire",
            },
        ]

        return querylist
