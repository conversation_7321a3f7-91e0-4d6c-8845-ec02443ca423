from rest_framework import generics
from rest_framework import permissions

from codage.ccam.models import (
    R_acte,
    R_tb_03,
    R_tb_23,
    R_activite,
    R_acte_ivite,
    R_acte_ivite_phase,
    R_pu_base,
    R_activite_modificateur,
    R_associations,
    R_tb_02,
    R_note_acte,
    R_menu,
    Ngap,
)
from api.codage.ccam.serializers import (
    ActeSerializer,
    R_tb_03Serializer,
    R_tb_23Serializer,
    R_acte_iviteSerializer,
    R_activiteSerializer,
    R_acte_ivite_phaseSerializer,
    R_pu_baseSerializer,
    R_activite_modificateurSerializer,
    R_associationsSerializer,
    R_tb_02Serializer,
    R_note_acteSerializer,
    R_menuSerializer,
    NgapSerializer,
)

from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView

# from django.db.models import Q  # making complex queries

from codage.ccam.ccam_scripts.query_ccam import query_ccam  # search in ccam
from codage.ccam.ccam_scripts.acte_functions import get_note
from module import module


class ActeList(generics.ListAPIView):
    queryset = R_acte.objects.all()
    serializer_class = ActeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = R_acte.objects.all()

        if self.request.query_params.get("menu", None) is not None:
            cod_menu = self.request.query_params.get("menu")
            queryset = queryset.filter(menu_cod=cod_menu)
            return queryset
        else:
            return queryset


class ActeSearch(generics.ListCreateAPIView):
    queryset = R_acte.objects.all()
    serializer_class = ActeSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        queryset = None
        # api URL is like /api/actes/?query=Robert
        query = self.request.query_params.get("query", None)

        # context for which the search is performed !
        # zkf_operation = self.request.query_params.get("zkf_operation", None)
        # zkf_devis = self.request.query_params.get("zkf_devis", None)
        # zkf_facture = self.request.query_params.get("zkf_facture", None)

        if query:
            queryset = query_ccam(query)
        print("queryset", queryset)

        return queryset


"""
Route pour les modificateurs de l'acte:
activite (1=chir, 4=anesth...): definie dans les preferences user: par defaut pref user ! (user.profile.activite_ccam)
phase
grille => definie dans les preferences user: par defaut pref user ! user.profile.conventionnement)
modificateur
"""


class ActeModificateurs(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        querylist = super().get_querylist()

        # send via query parameters acte
        aa_code = self.request.query_params.get("aa_code", None)
        activite = self.request.query_params.get(
            "activite", self.request.user.profile.activite_ccam.cod_activ
        )
        # TODO phase
        # phase = R_phase.objects.all()
        # phase = [
        #     {
        #         "cod_phase": "0",
        #         "libelle": "phase par defaut",
        #     },
        #     {
        #         "cod_phase": "1",
        #         "libelle": "phase 1",
        #     },
        #     {
        #         "cod_phase": "2",
        #         "libelle": "phase 2",
        #     },
        #     {
        #         "cod_phase":"3",
        #         "libelle":"phase 3",
        #     }
        # ]
        # la table R_tb_23 (=grille) est la table regroupant les numéros de grille tarifaire (optam, non conventionné...)
        grille = R_tb_23.objects.all()

        # check if there is a query_parameters
        # otherwise return None as the database is very large !
        modificateurs = R_activite_modificateur.objects.none()
        if aa_code and activite:
            acte_ivite = R_acte_ivite.objects.filter(
                acte_cod=aa_code, activ_cod=activite
            )
            if acte_ivite.exists():
                acte_ivite = acte_ivite.latest("dt_modif")
                modificateurs = R_activite_modificateur.objects.filter(
                    aa_code=acte_ivite.cod_aa
                )
                # get group last modified !
                last_update = modificateurs.latest("aadt_modif")
                modificateurs = modificateurs.filter(aadt_modif=last_update.aadt_modif)

        activite_list = R_activite.objects.all()

        querylist = [
            {
                "queryset": activite_list,
                "serializer_class": R_activiteSerializer,
                "label": "activite",
            },
            # {
            #     "queryset": phase,
            #     "serializer_class": PhaseSerializer,
            # },
            {
                "queryset": grille,
                "serializer_class": R_tb_23Serializer,
                "label": "grille",
            },
            {
                "queryset": modificateurs,
                "serializer_class": R_activite_modificateurSerializer,
            },
        ]

        return querylist


class ActeDetail(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        # # get options
        acte = self.kwargs.get("acte", None)
        activite = self.request.query_params.get(
            "activite", self.request.user.profile.activite_ccam.cod_activ
        )
        phase = self.request.query_params.get("phase", 0)
        grille = self.request.query_params.get(
            "grille", self.request.user.profile.conventionnement.cod_grille
        )
        # modificateur = self.request.query_params.get("modificateur", None)

        actes = R_acte.objects.filter(cod_acte=acte)

        # convert queryset to object for acte details and scripts. but context has to be a queryset
        acte = actes.first()

        # =>has to return a queryset
        # # get acte details (acte activite, acte activite phase, prix_unitaire )
        # acte details needs an object has a parameter !
        acte_details = (
            module().acte_service().get_acte_details(acte, activite, phase, grille)
        )

        for a in actes:
            if acte_details.prix and len(acte_details.prix) > 0:
                a.pu_base = acte_details.prix[0].pu_base
            else:
                a.pu_base = None

            # # get note of the acte
        acte_details.note = get_note(acte)

        # # get associations of the acte
        if len(acte_details.acte_ivite) > 0:
            associations_query = (
                module()
                .acte_service()
                .get_associations(acte_details.acte_ivite[0].cod_aa)
            )
            # add pu_base to each association
            for a in associations_query:
                details = (
                    module().acte_service().get_acte_details(a, activite, phase, grille)
                )
                # Set default None value for pu_base
                a.pu_base = None
                # Only try to access prix if details.prix exists and has values
                if hasattr(details, "prix") and details.prix and len(details.prix) > 0:
                    a.pu_base = details.prix[0].pu_base

            acte_details.associations = associations_query

        # get incompatibilites
        # context["incompatibilites"] = get_acte_incompatibilites(acte)

        # get actes voisins
        # context["voisins"] = get_acte_voisins(acte, activite, phase, grille)

        querylist = [
            {
                "queryset": actes,
                "serializer_class": ActeSerializer,
                "label": "acte",
            },
            {
                "queryset": acte_details.prix,
                "serializer_class": R_pu_baseSerializer,
                "label": "prix",
            },
            {
                "queryset": acte_details.acte_ivite,
                "serializer_class": R_acte_iviteSerializer,
                "label": "acte_ivite",
            },
            {
                "queryset": acte_details.acte_ivite_phase,
                "serializer_class": R_acte_ivite_phaseSerializer,
                "label": "acte_ivite_phase",
            },
            {
                "queryset": acte_details.associations,
                "serializer_class": ActeSerializer,
                "label": "associations",
            },
            {
                "queryset": acte_details.note,
                "serializer_class": R_note_acteSerializer,
                "label": "note",
            },
            {
                "queryset": None,  # context["incompatibilites"],
                "serializer_class": ActeSerializer,
                "label": "incompatibilites",
            },
            {
                "queryset": None,  # context["voisins"],
                "serializer_class": ActeSerializer,
                "label": "actes_voisins",
            },
        ]
        return querylist


class ActesSuggested(generics.ListAPIView):
    queryset = R_acte.objects.all()
    serializer_class = ActeSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        # api URL is like /api/actes/?operation=uuid
        # operation_uuid = self.request.query_params.get("operation", None)
        # operation = get_object_or_404(Operation, id=operation_uuid)

        # get actes proposes
        # actes_proposes = script_actes_proposes(operation=operation)
        actes_proposes = R_acte.objects.filter(cod_acte__in=["EEAF006", "EEPF002"])

        return actes_proposes


"""
tb_03
Règles Tarifaires
Le coef asso. prévu doit être rattaché à une grille Tarifaire.
"""


class R_tb_03List(generics.ListCreateAPIView):
    queryset = R_tb_03.objects.all()
    serializer_class = R_tb_03Serializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_tb_03.objects.all()


class R_tb_03Detail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_tb_03.objects.all()
    serializer_class = R_tb_03Serializer
    permission_classes = [permissions.IsAuthenticated]


"""
R_tb_23
Grille Tarifaire
Libellé et détail des Grilles Tarifaire
"""


class R_tb_23List(generics.ListCreateAPIView):
    queryset = R_tb_23.objects.all()
    serializer_class = R_tb_23Serializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_tb_23.objects.all()


class R_tb_23Detail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_tb_23.objects.all()
    serializer_class = R_tb_23Serializer
    permission_classes = [permissions.IsAuthenticated]


class R_activiteList(generics.ListCreateAPIView):
    queryset = R_activite.objects.all()
    serializer_class = R_activiteSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        return R_activite.objects.all()


class R_activiteDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_activite.objects.all()
    serializer_class = R_activiteSerializer
    permission_classes = [permissions.IsAuthenticated]


class R_acte_iviteList(generics.ListCreateAPIView):
    queryset = R_acte_ivite.objects.all()
    serializer_class = R_acte_iviteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_acte_ivite.objects.all()


class R_acte_iviteDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_acte_ivite.objects.all()
    serializer_class = R_acte_iviteSerializer
    permission_classes = [permissions.IsAuthenticated]


class R_acte_ivite_phaseList(generics.ListCreateAPIView):
    queryset = R_acte_ivite_phase.objects.all()
    serializer_class = R_acte_ivite_phaseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_acte_ivite_phase.objects.all()


class R_acte_ivite_phaseDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_acte_ivite.objects.all()
    serializer_class = R_acte_iviteSerializer
    permission_classes = [permissions.IsAuthenticated]


class R_pu_baseList(generics.ListCreateAPIView):
    queryset = R_pu_base.objects.all()
    serializer_class = R_pu_baseSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_pu_base.objects.all()


class R_pu_baseDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_pu_base.objects.all()
    serializer_class = R_pu_baseSerializer
    permission_classes = [permissions.IsAuthenticated]


class R_activite_modificateurList(generics.ListCreateAPIView):
    queryset = R_activite_modificateur.objects.all()
    serializer_class = R_activite_modificateurSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_activite_modificateur.objects.all()


class R_activite_modificateurDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_activite_modificateur.objects.all()
    serializer_class = R_activite_modificateurSerializer
    permission_classes = [permissions.IsAuthenticated]


class R_associationsList(generics.ListCreateAPIView):
    queryset = R_associations.objects.all()
    serializer_class = R_associationsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_associations.objects.all()


class R_associationsDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_associations.objects.all()
    serializer_class = R_associationsSerializer
    permission_classes = [permissions.IsAuthenticated]


class R_tb_02List(generics.ListCreateAPIView):
    queryset = R_tb_02.objects.all()
    serializer_class = R_tb_02Serializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_tb_02.objects.all()


class R_tb_02Detail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_tb_02.objects.all()
    serializer_class = R_tb_02Serializer
    permission_classes = [permissions.IsAuthenticated]


class R_note_acteList(generics.ListCreateAPIView):
    queryset = R_note_acte.objects.all()
    serializer_class = R_note_acteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return R_note_acte.objects.all()


class R_note_acteDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = R_note_acte.objects.all()
    serializer_class = R_note_acteSerializer
    permission_classes = [permissions.IsAuthenticated]


class R_menuList(generics.ListAPIView):
    queryset = R_menu.objects.all()
    serializer_class = R_menuSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        cod_menu = self.request.query_params.get("cod_menu", 1)
        queryset = R_menu.objects.filter(cod_pere=cod_menu).order_by("rang")

        if not queryset.exists():
            # if no sub_menu, return empty queryset
            return R_menu.objects.none()

        return queryset


class R_menuDetail(generics.RetrieveAPIView):
    queryset = R_menu.objects.all()
    serializer_class = R_menuSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = "cod_menu"
    lookup_url_kwarg = "menu_id"


class NgapList(generics.ListCreateAPIView):
    queryset = Ngap.objects.all()
    serializer_class = NgapSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Ngap.objects.all()


class NgapDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Ngap.objects.all()
    serializer_class = NgapSerializer
    permission_classes = [permissions.IsAuthenticated]
