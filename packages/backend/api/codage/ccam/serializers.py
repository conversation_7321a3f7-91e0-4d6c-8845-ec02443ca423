from rest_framework import serializers
from codage.ccam.models import (
    R_acte,
    R_tb_03,
    R_tb_23,
    R_activite,
    R_acte_ivite,
    R_acte_ivite_phase,
    R_pu_base,
    R_phase,
    R_activite_modificateur,
    R_associations,
    R_tb_02,
    R_note_acte,
    R_menu,
    R_tb_11,
    R_incompatibilites,
    Ngap,
)


class PhaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_phase
        fields = "__all__"


class ActiviteSerializer(serializers.ModelSerializer):

    class Meta:
        model = R_activite
        fields = "__all__"


class ActeSerializer(serializers.ModelSerializer):
    # add extra fields to the json response
    # https://stackoverflow.com/questions/73612304/add-extra-property-to-this-json-response-returned-by-django-rest-framework
    pu_base = serializers.SerializerMethodField()

    class Meta:
        model = R_acte
        fields = "__all__"
        read_only_fields = ["pu_base"]

    # add pu_base de l'acte if added to the object, else None !
    def get_pu_base(self, obj):
        pu_base = obj.pu_base if hasattr(obj, "pu_base") else 0
        return pu_base


class R_tb_03Serializer(serializers.ModelSerializer):
    class Meta:
        model = R_tb_03
        fields = "__all__"


class R_tb_23Serializer(serializers.ModelSerializer):
    class Meta:
        model = R_tb_23
        fields = "__all__"


class R_activiteSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_activite
        fields = "__all__"


class R_acte_iviteSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_acte_ivite
        fields = "__all__"


class R_acte_ivite_phaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_acte_ivite_phase
        fields = "__all__"


class R_pu_baseSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_pu_base
        fields = "__all__"


class R_activite_modificateurSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_activite_modificateur
        fields = "__all__"


class R_associationsSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_associations
        fields = "__all__"


class R_tb_02Serializer(serializers.ModelSerializer):
    class Meta:
        model = R_tb_02
        fields = "__all__"


class R_note_acteSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_note_acte
        fields = "__all__"


class R_menuSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_menu
        fields = "__all__"


class R_tb_11Serializer(serializers.ModelSerializer):
    class Meta:
        model = R_tb_11
        fields = "__all__"


class R_incompatibilitesSerializer(serializers.ModelSerializer):
    class Meta:
        model = R_incompatibilites
        fields = "__all__"


class NgapSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ngap
        fields = "__all__"
