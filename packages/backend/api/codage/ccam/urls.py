from django.urls import path

from api.codage.ccam.views import (
    ActeList,
    ActesSuggested,
    ActeSearch,
    ActeDetail,
    ActeModificateurs,
    R_tb_23List,
    R_activiteList,
    R_activite_modificateurList,
    R_menuList,
    R_menuDetail,
)

# api api/ccam/

urlpatterns = [
    path("acte", ActeList.as_view(), name="acte-list"),
    path("suggested-actes", ActesSuggested.as_view(), name="acte-suggested"),
    path("acte/<str:acte>", ActeDetail.as_view(), name="acte-detail"),
    # modificateur d'acte
    path("modificateur", ActeModificateurs.as_view(), name="modificateur"),
    # search acte
    path("search", ActeSearch.as_view(), name="search"),
    # R_TB23: conventionnement
    path("r_tb_23", R_tb_23List.as_view(), name="r_tb_23"),
    # R_activite: activite_ccam
    path("r_activite", R_activiteList.as_view(), name="r_activite"),
    # Liste des modificateurs possibles
    path(
        "modificateur-list",
        R_activite_modificateurList.as_view(),
        name="modificateurs-list",
    ),
    # Menu: liste des titres
    path("menu", R_menuList.as_view(), name="menu-list"),
    # Menu: detail d'un titre
    path("menu/<str:menu_id>", R_menuDetail.as_view(), name="menu-detail"),
]
