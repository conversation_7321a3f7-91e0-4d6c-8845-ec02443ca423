from django.urls import path

from api.accounts.views import (
    IntervenantList,
    IntervenantsEvent,
    IntervenantDetail,
    Myself,
    UserDetail,
    UserList,
    UserSites,
    UserSitesLieux,
    UserLinkedUsers,
    # UserEvents,
    PreferencesUser,
    ProfileUser,
    UsersLiesUser,
    SpecialitesList,
    SpecialiteDetail,
    ProfileUser,
    ServiceList,
    ServiceDetail,
    EnteteDocumentList,
    EnteteDocumentDetail,
    NotificationChoicesView,
    GroupList,
)

# name = "api/user"

urlpatterns = [
    path("", UserList.as_view(), name="users-list"),
    path("groups", GroupList.as_view(), name="groups"),
    path("<int:pk>/", UserDetail.as_view(), name="user-detail"),
    path("site", UserSites.as_view(), name="user-sites"),
    path("profile/<int:pk>", ProfileUser.as_view(), name="profile-user"),
    path(
        "preferences-user/<int:pk>", PreferencesUser.as_view(), name="preferences-user"
    ),
    # get intervenant linked to a user
    path(
        "<int:user_id>/intervenant",
        IntervenantDetail.as_view(),
        name="intervenant-by-user",
    ),
    # get intervenant by id
    path(
        "intervenant/<uuid:pk>", IntervenantDetail.as_view(), name="intervenant-by-id"
    ),
    path(
        "notification-choices/",
        NotificationChoicesView.as_view(),
        name="notification-choices",
    ),
    path("site/<uuid:site_id>/lieu", UserSitesLieux.as_view(), name="user-sites-lieux"),
    # get linked users of the user
    path("linked-users", UserLinkedUsers.as_view(), name="user-linked-users"),
    # path("user-events", UserEvents.as_view(), name="user-events"),
    # get intervenants for an event
    path("intervenant", IntervenantList.as_view(), name="intervenant-list"),
    path("intervenants-event", IntervenantsEvent.as_view(), name="intervenants-event"),
    # get and update preferences User
    path(
        "preferences-user/<uuid:pk>", PreferencesUser.as_view(), name="preferences-user"
    ),
    # get and update profile User
    path("profile-user/<uuid:pk>", ProfileUser.as_view(), name="profile-user"),
    # get all users lies, directs and indirects !
    path("users-lies-all", UsersLiesUser.as_view(), name="users-lies-all"),
    path("specialites", SpecialitesList.as_view(), name="specialites"),
    path("specialites/<uuid:pk>", SpecialiteDetail.as_view(), name="specialite-detail"),
    # get services of a user
    path("service", ServiceList.as_view(), name="services-user"),
    # service detail
    path("service/<uuid:pk>", ServiceDetail.as_view(), name="service-detail"),
    # Entete document
    path("entete-document", EnteteDocumentList.as_view(), name="entete-document"),
    path(
        "entete-document/<uuid:pk>",
        EnteteDocumentDetail.as_view(),
        name="entete-document",
    ),
    path("me", Myself.as_view(), name="myself"),
]
