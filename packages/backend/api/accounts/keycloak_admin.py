from keycloak import Keyc<PERSON>akAdmin, KeycloakOpenIDConnection
from django.conf import settings


def get_keycloak_admin():
    # Connexion en tant qu'administrateur
    # keycloak_admin = KeycloakAdmin(
    #     server_url=settings.KEYCLOAK_SERVER_URL,
    #     realm_name=settings.K<PERSON><PERSON><PERSON>OAK_REALM_NAME,
    #     client_id=settings.KEYCLOAK_ADMIN_CLIENT_ID,  # Généralement "admin-cli"
    #     # client_secret_key=settings.KEYCLOAK_ADMIN_CLIENT_SECRET,
    #     username=settings.KEYCLOAK_ADMIN_USERNAME,
    #     password=settings.KEYCLOAK_ADMIN_PASSWORD,
    #     verify=True
    # )
    # return keycloak_admin

    keycloak_connection = KeycloakOpenIDConnection(
        server_url=settings.KEYCLOAK_SERVER_URL,
        # authent via client: no need for user credentials
        # username=settings.KEYCLOAK_USERNAME,
        # password=settings.KEY<PERSON>OAK_PASSWORD,
        realm_name=settings.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALM_NAME,
        user_realm_name=settings.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALM_NAME,
        client_id=settings.KEYCLOAK_CLIENT_ID,
        client_secret_key=settings.KEYCLOAK_CLIENT_SECRET_KEY,
        verify=True,
    )

    keycloak_admin = KeycloakAdmin(connection=keycloak_connection)

    return keycloak_admin
