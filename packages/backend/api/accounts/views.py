from requests import Response
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework import generics
from django.shortcuts import get_object_or_404
from rest_framework.exceptions import NotFound
from django.conf import settings
from contact.models import Contact
from api.accounts.serializers import (
    GroupSerializer,
    UserSerializer,
    UserSitesLieuxSerializer,
    UserSitesSerializer,
    IntervenantSerializer,
    PreferenceUserSerialyzer,
    SpecialiteSerializer,
    ProfileUserSerialyzer,
    GroupSerializer,
    ServiceSerializer,
    EnteteDocumentSerializer,
)
from django.contrib.auth.models import User, Group
from accounts.models import (
    Lieu,
    Profile,
    Site,
    Preferences_user,
    Intervenant,
    Specialite,
    RendezVous,
    Service,
    EnteteDocument,
)
from django.db.models import Q
from operation.models import Operation
from consultation.models import Consultation
from rest_framework import permissions
from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView
from api.operation.serializers import OperationSerializer
from api.consultation.serializers import ConsultationSerializer
from api.accounts.serializers import RendezVousSerializer
from datetime import datetime
from hospitalisation.models import Hospitalisation
from rest_framework.pagination import PageNumberPagination
from .keycloak_admin import get_keycloak_admin
from django.core.exceptions import ValidationError

from accounts.scripts.filter_intervenant import (
    get_intervenant_operateur,
    get_intervenant_anesthesiste,
    get_intervenant_aide_operatoire,
)
from accounts.scripts.filter_user import get_user_secretaire
from accounts.scripts.filter_user import get_operateur, get_user_arc
from accounts.models import NotificationEvents, NotificationChannels


# get all the choices for the notification events and channels
class NotificationChoicesView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        return JsonResponse(
            {
                "events": NotificationEvents.CHOICES,
                "channels": NotificationChannels.CHOICES,
            }
        )


class LargeResultsSetPagination(PageNumberPagination):
    page_size = 1000
    page_size_query_param = "page_size"
    max_page_size = 10000


# get list of users
class UserList(generics.ListCreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = LargeResultsSetPagination

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 1))
        return context

    # filter by environnement
    def get_queryset(self):
        user = self.request.user

        if user.is_authenticated:
            try:
                profile = user.profile
                user_env = profile.zkf_environnement_user
                queryset = self.queryset.filter(
                    profile__zkf_environnement_user=user_env
                )
                return queryset
            except Profile.DoesNotExist:
                return User.objects.none()
        return User.objects.none()

    def perform_create(self, serializer):
        username = serializer.validated_data["username"]
        first_name = serializer.validated_data["first_name"]
        last_name = serializer.validated_data["last_name"]
        email = serializer.validated_data["email"]

        # check if user already exists
        # if serializer.validated_data["first_name"] is not None and serializer.validated_data["last_name"] is not None and serializer.validated_data["username"] is not None:
        #     user = User.objects.filter(first_name = serializer.validated_data["first_name"], last_name = serializer.validated_data["last_name"])
        #     username = User.objects.filter(username = serializer.validated_data["username"])
        #     if user.exists():
        #         return Response({"error": "User already exists"}, status=400)
        #     elif username.exists():
        #         return Response({"error": "Username already exists"}, status=400)
        #     else:
        #         serializer.save()
        from api.accounts.keycloak_admin import get_keycloak_admin

        # créer un user django
        # Créer un utilisateur
        email = serializer.validated_data["email"]
        username = serializer.validated_data["username"]
        first_name = serializer.validated_data["first_name"]
        last_name = serializer.validated_data["last_name"]

        # Check if user already exists: username or email or first_name and last_name
        if User.objects.filter(
            Q(username=username)
            | Q(email=email)
            | Q(first_name=first_name, last_name=last_name)
        ).exists():
            # User already exists, handle accordingly
            raise ValidationError(
                "Username or email or first_name and last_name already exists"
            )

        try:
            # 1/ Create django User
            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
            )
            user.is_staff = False  # Peut accéder à l'admin
            user.is_superuser = False  # Pas superadmin
            user.set_unusable_password()  # Empêche l’auth locale, sécurisé
            user.save()

            # 1.1/ Create profile
            profile = Profile.objects.create(
                user=user,
                zkf_environnement_user=self.request.user.profile.zkf_environnement_user,
            )
            # 1.2/ Create preferences
            preferences = Preferences_user.objects.create(
                user=user,
            )

            # 2/ Create keycloak user
            keycloak_admin = get_keycloak_admin()
            # send password, or activate user when password is set => depends if user is created from keycloak or directly in the front
            new_user = keycloak_admin.create_user(
                {
                    "email": email,
                    "username": username,
                    "enabled": True,
                    "firstName": "ryan",
                    "lastName": "ryan",
                    "attributes": {"locale": ["fr"]},
                    "enabled": True,
                    "emailVerified": False,
                },
                exist_ok=False,
            )

            profile.keycloak_id = new_user
            profile.save()

            user_id = new_user
            # Send verification email and update password
            keycloak_admin.send_verify_email(user_id=user_id)
            keycloak_admin.send_update_account(
                user_id=user_id, payload=["UPDATE_PASSWORD"]
            )

            # 3/ create intervenant
            intervenant = Intervenant.objects.create(
                zkf_user=user,
                zkf_environnement=self.request.user.profile.zkf_environnement_user,
                nom=last_name,
                prenom=first_name,
                # titre, specialite, service, site...
            )

            # 4/ link to contact if applicable
            contact = Contact.objects.filter(
                nom=last_name,
                prenom=first_name,
            )
            if contact.exists() and contact.count() == 1:
                contact = contact.first()
                # link to contact
                intervenant.zkf_contact = contact
                intervenant.save()
        except Exception as e:
            # Gérer l'erreur d'envoi de l'email
            print(f"Error in sending verify email: {e}")


class UserDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def perform_update(self, serializer):
        # get the user from the request
        user = self.get_object()
        # get the data from the serializer
        data = serializer.validated_data
        # check if the user is superuser or staff
        if user.is_superuser or user.is_staff:
            # if superuser or staff, update the user
            serializer.save()
        else:
            # if not, update only the fields that are allowed
            serializer.save(
                # username=data.get("username", user.username),
                email=data.get("email", user.email),
                first_name=data.get("first_name", user.first_name),
                last_name=data.get("last_name", user.last_name),
            )


class UserLinkedUsers(generics.ListAPIView):
    permission_classes = [permissions.IsAuthenticated]
    queryset = User.objects.all()
    serializer_class = UserSerializer
    pagination_class = None

    def get_queryset(self):
        return self.request.user.profile.praticiens_lies.all()


class PreferencesUser(generics.RetrieveUpdateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    queryset = Preferences_user.objects.all()
    serializer_class = PreferenceUserSerialyzer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        from contact.scripts.annuaire_sante import (
            update_local_annuaire,
            search_duplicate,
        )

        # search_duplicate(self.request)
        # update_local_annuaire(self.request)
        return context


class ProfileUser(generics.RetrieveUpdateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    queryset = Profile.objects.all()
    serializer_class = ProfileUserSerialyzer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class GroupList(generics.ListAPIView):
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None


# get all events for a user (today !) => filter by day is to be done !
# class UserEvents(ObjectMultipleModelAPIView):
#     querylist = []
#     permission_classes = [permissions.IsAuthenticated]
#     pagination_class = None

#     def get_querylist(self, *args, **kwargs):
#         # super().get_querylist()
#         from babylone.core_scripts.machine_learning.multi_label_text_classification import (
#             create_df_consultation,
#         )

#         create_df_consultation(user=self.request.user)
#         # create_df_consultation()
#         querylist = None
#         date = datetime.today()
#         users = (
#             self.request.user.preferences_user.current_praticiens_lies_selectionnes.all()
#         )
#         consultations = (
#             Consultation.objects.filter(
#                 zkf_redacteur__in=users, consultation_date=date, deleted=False
#             )
#             .exclude(etat_consultation="annule")
#             .order_by("consultation_debut_prevu")
#         )

#         operations = Operation.objects.filter(
#             operateur_new__zkf_user__in=users, operation_date=date, deleted=False
#         )
#         rdv = RendezVous.objects.filter(
#             zkf_user__in=users, date_heure_debut__date=date, deleted=False
#         )
#         querylist = [
#             # operations
#             {
#                 "queryset": operations,
#                 "serializer_class": OperationSerializer,
#             },
#             # consultations
#             {
#                 "queryset": consultations,
#                 "serializer_class": ConsultationSerializer,
#             },
#             # rdv
#             {
#                 "queryset": rdv,
#                 "serializer_class": RendezVousSerializer,
#             },
#         ]

#         return querylist


class UserSites(generics.ListAPIView):
    serializer_class = UserSitesSerializer
    pagination_class = None
    # permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            try:
                profile = Profile.objects.get(user=user)
                user_env = profile.zkf_environnement_user
                queryset = Site.objects.filter(zkf_environnement_site=user_env)
                return queryset
            except Profile.DoesNotExist:
                return Site.objects.none()
        return Site.objects.none()

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(user=user)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class UserSitesLieux(generics.ListAPIView):
    serializer_class = UserSitesLieuxSerializer
    # get site_id from url, and get linked Lieux from this site
    # return site: Site, lieux: Lieu
    serializer_class = UserSitesLieuxSerializer
    pagination_class = None

    def get_queryset(self):
        site = Site.objects.get(id=self.kwargs["site_id"])
        # get lieux from site
        lieux = Lieu.objects.filter(zkf_site=site.id)
        return lieux

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class IntervenantList(generics.ListAPIView):
    queryset = Intervenant.objects.all()
    serializer_class = IntervenantSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    # filter by environnement
    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            try:
                profile = user.profile
                user_env = profile.zkf_environnement_user
                queryset = self.queryset.filter(zkf_environnement=user_env)
                return queryset
            except profile.DoesNotExist:
                return Intervenant.objects.none()
        return Intervenant.objects.none()

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 1))
        return context

    # def perform_create(self, serializer):


class IntervenantDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Intervenant.objects.all()
    serializer_class = IntervenantSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_object(self):
        # check depending of the url if we query by intervenant id or user id !
        view_name = self.request.resolver_match.view_name

        if view_name == "intervenant-by-id":
            return super().get_object()
        elif view_name == "intervenant-by-user":
            try:
                intervenant = Intervenant.objects.get(
                    zkf_user=User.objects.get(id=self.kwargs["user_id"]),
                    zkf_environnement=self.request.user.profile.zkf_environnement_user,
                )
            except:
                intervenant = None
            return intervenant
        else:
            raise NotFound("Route non reconnue.")


# get intervenants for an event
class IntervenantsEvent(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        querylist = None

        # get query params: operation, consultation or hospitalisation
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        operation = self.request.query_params.get("operation", None)
        consultation = self.request.query_params.get("consultation", None)
        redacteur_event = None
        anethesiste = Intervenant.objects.none()
        # get intervenants then filter in script
        intervenants = Intervenant.objects.filter(
            zkf_environnement=self.request.user.profile.zkf_environnement_user,
            deleted=False,
            is_active=True,
        )

        # get redacteur for each event:
        if hospitalisation is not None:
            hospitalisation = Hospitalisation.objects.get(id=hospitalisation)
            redacteur_event = get_operateur(
                hospitalisation.zkf_redacteur
                if hospitalisation.zkf_redacteur
                else self.request.user
            )
        if operation is not None:
            operation = Operation.objects.get(id=operation)
            redacteur_event = get_operateur(
                operation.zkf_redacteur
                if operation.zkf_redacteur
                else self.request.user
            )
            anethesiste = get_intervenant_anesthesiste(
                self.request.user, intervenants=intervenants, operation=operation
            )
        if consultation is not None:
            consultation = Consultation.objects.get(id=consultation)
            redacteur_event = get_operateur(
                consultation.zkf_redacteur
                if consultation.zkf_redacteur
                else self.request.user
            )

        operateurs = get_intervenant_operateur(
            self.request.user, intervenants=intervenants
        )
        # set defaut operateur
        for operateur in operateurs:
            if operateur.zkf_user == self.request.user:
                operateur.default = True
            elif operateur.zkf_user == redacteur_event:
                operateur.default = True
            else:
                operateur.default = False

        redacteurs = get_operateur(self.request.user)
        # set default if user in redacteur
        for r in redacteurs:
            if r == self.request.user:
                r.default = True
            elif r == redacteur_event:
                r.default = True
            else:
                r.default = False

        aide_operatoire = get_intervenant_aide_operatoire(
            self.request.user,
            intervenants=intervenants,
            operation=operation,
        )
        secretaires = get_user_secretaire(self.request.user)
        # generate a list of queryset with operation, and related tables
        querylist = [
            {
                "queryset": anethesiste,
                "serializer_class": IntervenantSerializer,
                "label": "Anesthesiste",
            },
            {
                "queryset": operateurs,
                "serializer_class": IntervenantSerializer,
                "label": "Operateur",
            },
            {
                "queryset": redacteurs,
                "serializer_class": UserSerializer,
                "label": "Redacteur",
            },
            {
                "queryset": aide_operatoire,
                "serializer_class": IntervenantSerializer,
                "label": "Aide Operatoire",
            },
            {
                "queryset": secretaires,
                "serializer_class": UserSerializer,
                "label": "Secretaire",
            },
        ]

        return querylist


# get intervenants for an event
class UsersLiesUser(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        querylist = None
        # get intervenants then filter in script
        # intervenants = Intervenant.objects.filter(
        #     zkf_environnement=self.request.user.profile.zkf_environnement_user,
        #     deleted=False,
        #     is_active=True,
        # )

        # aide_operatoire = get_intervenant_aide_operatoire(self.request.user, intervenants = intervenants)
        secretaires = get_user_secretaire(self.request.user)
        arc = get_user_arc(self.request.user)

        # generate a list of queryset with operation, and related tables
        querylist = [
            {
                "queryset": secretaires,
                "serializer_class": UserSerializer,
                "label": "Secretaire",
            },
            {
                "queryset": arc,
                "serializer_class": UserSerializer,
                "label": "Arc",
            },
        ]

        return querylist


# list of specialites
class SpecialitesList(generics.ListAPIView):
    queryset = Specialite.objects.all()
    serializer_class = SpecialiteSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        return Specialite.objects.all()  # .order_by("spe")

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class SpecialiteDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Specialite.objects.all()
    serializer_class = SpecialiteSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class Groups(generics.ListAPIView):
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return User.objects.filter(groups__name=self.kwargs["group_name"])

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class ServiceList(generics.ListAPIView):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):

        parameters = self.request.query_params.get("all", None)
        if parameters is not None:
            return Service.objects.all()  # .order_by("spe")
        # # add service hospitalisation depending of specialite of the user !!!
        # service = Service.objects.filter(
        #     specialite_service__in=user.profile.specialite_profile.all(),
        #     site_service__zkf_environnement_site=user.profile.zkf_environnement_user,
        # )
        # Service d'hospitalisation
        service = Service.objects.filter(
            nom_service__in=self.request.user.profile.service.all().values(
                "nom_service"
            )
        )
        return service

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class ServiceDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class EnteteDocumentList(generics.ListCreateAPIView):
    queryset = EnteteDocument.objects.all()
    serializer_class = EnteteDocumentSerializer
    pagination_class = None

    def get_queryset(self):
        queryset = EnteteDocument.objects.all().filter(
            zkf_environnement=self.request.user.profile.zkf_environnement_user,
            deleted=False,
        )
        return queryset

    def perform_create(self, serializer):
        serializer.save(
            zkf_environnement=self.request.user.profile.zkf_environnement_user
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class EnteteDocumentDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = EnteteDocument.objects.all()
    serializer_class = EnteteDocumentSerializer
    # permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class Myself(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_object(self):
        return self.request.user
