from requests import Response
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework import generics
from django.conf import settings
from api.accounts.serializers import (
    GroupSerializer,
    UserSerializer,
    UserSitesLieuxSerializer,
    UserSitesSerializer,
    IntervenantSerializer,
    PreferenceUserSerialyzer,
    SpecialiteSerializer,
    ProfileUserSerialyzer,
    GroupSerializer,
    ServiceSerializer,
    EnteteDocumentSerializer,
)
from django.contrib.auth.models import User, Group
from accounts.models import (
    Lieu,
    Profile,
    Site,
    Preferences_user,
    Intervenant,
    Specialite,
    RendezVous,
    Service,
    EnteteDocument,
)
from django.db.models import Q
from operation.models import Operation
from consultation.models import Consultation
from rest_framework import permissions
from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView
from api.operation.serializers import OperationSerializer
from api.consultation.serializers import ConsultationSerializer
from api.accounts.serializers import RendezVousSerializer
from datetime import datetime
from hospitalisation.models import Hospitalisation
from rest_framework.pagination import PageNumberPagination

from accounts.scripts.filter_intervenant import (
    get_intervenant_operateur,
    get_intervenant_anesthesiste,
    get_intervenant_aide_operatoire,
)
from accounts.scripts.filter_user import get_user_secretaire
from accounts.scripts.filter_user import get_operateur, get_user_arc
from accounts.models import NotificationEvents, NotificationChannels


# get all the choices for the notification events and channels
class NotificationChoicesView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        return JsonResponse(
            {
                "events": NotificationEvents.CHOICES,
                "channels": NotificationChannels.CHOICES,
            }
        )


class LargeResultsSetPagination(PageNumberPagination):
    page_size = 1000
    page_size_query_param = "page_size"
    max_page_size = 10000


# get list of users
class UserList(generics.ListCreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = LargeResultsSetPagination

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 1))
        return context

    # filter by environnement
    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            try:
                profile = user.profile
                user_env = profile.zkf_environnement_user
                queryset = self.queryset.filter(
                    profile__zkf_environnement_user=user_env
                )
                return queryset
            except Profile.DoesNotExist:
                return User.objects.none()
        return User.objects.none()

    def perform_create(self, serializer):
        username = serializer.validated_data["username"]
        first_name = serializer.validated_data["first_name"]
        last_name = serializer.validated_data["last_name"]
        email = serializer.validated_data["email"]

        # check if user already exists
        if (
            serializer.validated_data["first_name"] is not None
            and serializer.validated_data["last_name"] is not None
            and serializer.validated_data["username"] is not None
        ):
            user = User.objects.filter(
                first_name=serializer.validated_data["first_name"],
                last_name=serializer.validated_data["last_name"],
            )
            username = User.objects.filter(
                username=serializer.validated_data["username"]
            )
            if user.exists():
                return Response({"error": "User already exists"}, status=400)
            elif username.exists():
                return Response({"error": "Username already exists"}, status=400)
            else:
                serializer.save()
        # create keycloack user and send validation emails
        keycloak_admin = settings.keycloak_admin
        # Add user and raise exception if username already exists
        # exist_ok currently defaults to True for backwards compatibility reasons
        new_user = keycloak_admin.create_user(
            {
                "email": email,
                "username": username,
                "enabled": True,
                "firstName": first_name,
                "lastName": last_name,
                "attributes": {"locale": ["fr"]},
            },
            exist_ok=False,
        )
        # Send verify email
        response = keycloak_admin.send_verify_email(user_id=new_user["id"])

        response = keycloak_admin.send_update_account(
            user_id=new_user["id"], payload=["UPDATE_PASSWORD"]
        )


class UserDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class UserLinkedUsers(generics.ListAPIView):
    permission_classes = [permissions.IsAuthenticated]
    queryset = User.objects.all()
    serializer_class = UserSerializer
    pagination_class = None

    def get_queryset(self):
        return self.request.user.profile.praticiens_lies.all()


class PreferencesUser(generics.RetrieveUpdateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    queryset = Preferences_user.objects.all()
    serializer_class = PreferenceUserSerialyzer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        from contact.scripts.annuaire_sante import (
            update_local_annuaire,
            search_duplicate,
        )

        # search_duplicate(self.request)
        # update_local_annuaire(self.request)
        return context


class ProfileUser(generics.RetrieveUpdateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    queryset = Profile.objects.all()
    serializer_class = ProfileUserSerialyzer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class GroupList(generics.ListAPIView):
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None


# get all events for a user (today !) => filter by day is to be done !
# class UserEvents(ObjectMultipleModelAPIView):
#     querylist = []
#     permission_classes = [permissions.IsAuthenticated]
#     pagination_class = None

#     def get_querylist(self, *args, **kwargs):
#         # super().get_querylist()
#         from babylone.core_scripts.machine_learning.multi_label_text_classification import (
#             create_df_consultation,
#         )

#         create_df_consultation(user=self.request.user)
#         # create_df_consultation()
#         querylist = None
#         date = datetime.today()
#         users = (
#             self.request.user.preferences_user.current_praticiens_lies_selectionnes.all()
#         )
#         consultations = (
#             Consultation.objects.filter(
#                 zkf_redacteur__in=users, consultation_date=date, deleted=False
#             )
#             .exclude(etat_consultation="annule")
#             .order_by("consultation_debut_prevu")
#         )

#         operations = Operation.objects.filter(
#             operateur_new__zkf_user__in=users, operation_date=date, deleted=False
#         )
#         rdv = RendezVous.objects.filter(
#             zkf_user__in=users, date_heure_debut__date=date, deleted=False
#         )
#         querylist = [
#             # operations
#             {
#                 "queryset": operations,
#                 "serializer_class": OperationSerializer,
#             },
#             # consultations
#             {
#                 "queryset": consultations,
#                 "serializer_class": ConsultationSerializer,
#             },
#             # rdv
#             {
#                 "queryset": rdv,
#                 "serializer_class": RendezVousSerializer,
#             },
#         ]

#         return querylist


class UserSites(generics.ListAPIView):
    serializer_class = UserSitesSerializer
    pagination_class = None
    # permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            try:
                profile = Profile.objects.get(user=user)
                user_env = profile.zkf_environnement_user
                queryset = Site.objects.filter(zkf_environnement_site=user_env)
                return queryset
            except Profile.DoesNotExist:
                return Site.objects.none()
        return Site.objects.none()

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(user=user)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class UserSitesLieux(generics.ListAPIView):
    serializer_class = UserSitesLieuxSerializer
    # get site_id from url, and get linked Lieux from this site
    # return site: Site, lieux: Lieu
    serializer_class = UserSitesLieuxSerializer
    pagination_class = None

    def get_queryset(self):
        site = Site.objects.get(id=self.kwargs["site_id"])
        # get lieux from site
        lieux = Lieu.objects.filter(zkf_site=site.id)
        return lieux

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class IntervenantList(generics.ListAPIView):
    queryset = Intervenant.objects.all()
    serializer_class = IntervenantSerializer
    permission_classes = [permissions.IsAuthenticated]

    # filter by environnement
    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            try:
                profile = user.profile
                user_env = profile.zkf_environnement_user
                queryset = self.queryset.filter(zkf_environnement=user_env)
                return queryset
            except profile.DoesNotExist:
                return Intervenant.objects.none()
        return Intervenant.objects.none()

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 1))
        return context

    # def perform_create(self, serializer):


# get intervenants for an event
class IntervenantsEvent(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        querylist = None

        # get query params: operation, consultation or hospitalisation
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        operation = self.request.query_params.get("operation", None)
        consultation = self.request.query_params.get("consultation", None)
        redacteur_event = None
        anethesiste = Intervenant.objects.none()
        # get intervenants then filter in script
        intervenants = Intervenant.objects.filter(
            zkf_environnement=self.request.user.profile.zkf_environnement_user,
            deleted=False,
            is_active=True,
        )

        # get redacteur for each event:
        if hospitalisation is not None:
            hospitalisation = Hospitalisation.objects.get(id=hospitalisation)
            redacteur_event = get_operateur(
                hospitalisation.zkf_redacteur
                if hospitalisation.zkf_redacteur
                else self.request.user
            )
        if operation is not None:
            operation = Operation.objects.get(id=operation)
            redacteur_event = get_operateur(
                operation.zkf_redacteur
                if operation.zkf_redacteur
                else self.request.user
            )
            anethesiste = get_intervenant_anesthesiste(
                self.request.user, intervenants=intervenants, operation=operation
            )
        if consultation is not None:
            consultation = Consultation.objects.get(id=consultation)
            redacteur_event = get_operateur(
                consultation.zkf_redacteur
                if consultation.zkf_redacteur
                else self.request.user
            )

        operateurs = get_intervenant_operateur(
            self.request.user, intervenants=intervenants
        )
        # set defaut operateur
        for operateur in operateurs:
            if operateur.zkf_user == self.request.user:
                operateur.default = True
            elif operateur.zkf_user == redacteur_event:
                operateur.default = True
            else:
                operateur.default = False

        redacteurs = get_operateur(self.request.user)
        # set default if user in redacteur
        for r in redacteurs:
            if r == self.request.user:
                r.default = True
            elif r == redacteur_event:
                r.default = True
            else:
                r.default = False

        aide_operatoire = get_intervenant_aide_operatoire(
            self.request.user,
            intervenants=intervenants,
            operation=operation,
        )
        secretaires = get_user_secretaire(self.request.user)
        # generate a list of queryset with operation, and related tables
        querylist = [
            {
                "queryset": anethesiste,
                "serializer_class": IntervenantSerializer,
                "label": "Anesthesiste",
            },
            {
                "queryset": operateurs,
                "serializer_class": IntervenantSerializer,
                "label": "Operateur",
            },
            {
                "queryset": redacteurs,
                "serializer_class": UserSerializer,
                "label": "Redacteur",
            },
            {
                "queryset": aide_operatoire,
                "serializer_class": IntervenantSerializer,
                "label": "Aide Operatoire",
            },
            {
                "queryset": secretaires,
                "serializer_class": UserSerializer,
                "label": "Secretaire",
            },
        ]

        return querylist


# get intervenants for an event
class UsersLiesUser(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        querylist = None
        # get intervenants then filter in script
        # intervenants = Intervenant.objects.filter(
        #     zkf_environnement=self.request.user.profile.zkf_environnement_user,
        #     deleted=False,
        #     is_active=True,
        # )

        # aide_operatoire = get_intervenant_aide_operatoire(self.request.user, intervenants = intervenants)
        secretaires = get_user_secretaire(self.request.user)
        arc = get_user_arc(self.request.user)

        # generate a list of queryset with operation, and related tables
        querylist = [
            {
                "queryset": secretaires,
                "serializer_class": UserSerializer,
                "label": "Secretaire",
            },
            {
                "queryset": arc,
                "serializer_class": UserSerializer,
                "label": "Arc",
            },
        ]

        return querylist


# list of specialites
class SpecialitesList(generics.ListAPIView):
    queryset = Specialite.objects.all()
    serializer_class = SpecialiteSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):
        return Specialite.objects.all()  # .order_by("spe")

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class SpecialiteDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Specialite.objects.all()
    serializer_class = SpecialiteSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class Groups(generics.ListAPIView):
    queryset = Group.objects.all()
    serializer_class = GroupSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return User.objects.filter(groups__name=self.kwargs["group_name"])

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class ServiceList(generics.ListAPIView):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_queryset(self):

        parameters = self.request.query_params.get("all", None)
        if parameters is not None:
            return Service.objects.all()  # .order_by("spe")
        # # add service hospitalisation depending of specialite of the user !!!
        # service = Service.objects.filter(
        #     specialite_service__in=user.profile.specialite_profile.all(),
        #     site_service__zkf_environnement_site=user.profile.zkf_environnement_user,
        # )
        # Service d'hospitalisation
        service = Service.objects.filter(
            nom_service__in=self.request.user.profile.service.all().values(
                "nom_service"
            )
        )
        return service

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class ServiceDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class EnteteDocumentList(generics.ListCreateAPIView):
    queryset = EnteteDocument.objects.all()
    serializer_class = EnteteDocumentSerializer
    pagination_class = None

    def get_queryset(self):
        queryset = EnteteDocument.objects.all().filter(
            zkf_environnement=self.request.user.profile.zkf_environnement_user,
            deleted=False,
        )
        return queryset

    def perform_create(self, serializer):
        serializer.save(
            zkf_environnement=self.request.user.profile.zkf_environnement_user
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class EnteteDocumentDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = EnteteDocument.objects.all()
    serializer_class = EnteteDocumentSerializer
    # permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class Myself(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_object(self):
        return self.request.user
