from rest_framework import serializers
from django.contrib.auth.models import User, Group
from accounts.models import (
    Lieu,
    Profile,
    Preferences_user,
    Site,
    Specialite,
    Service,
    RendezVous,
    Intervenant,
    EnteteDocument,
)


# https://www.linkedin.com/pulse/friday-quick-tip-dynamic-depth-serialization-django-rest-neidinger/
class DynamicDepthSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.Meta.depth = self.context.get("depth", 0)


class ProfileUserSerialyzer(DynamicDepthSerializer):

    class Meta:
        model = Profile
        exclude = ["keycloak_id"]


class UserSerializer(DynamicDepthSerializer):

    profile_id = serializers.SerializerMethodField()
    preferences_id = serializers.SerializerMethodField()
    default = serializers.SerializerMethodField()
    annuaire_sante_verification = serializers.SerializerMethodField()
    profile = ProfileUserSerialyzer(read_only=True)
    specialite_principale = serializers.SerializerMethodField()
    intervenant = serializers.SerializerMethodField()

    class Meta:
        model = User
        # fields = "__all__"
        exclude = ["password"]

    def get_profile_id(self, obj):
        return obj.profile.id if hasattr(obj, "profile") else None

    def get_preferences_id(self, obj):
        return obj.preferences_user.id if hasattr(obj, "preferences_user") else None

    def get_default(self, obj):
        return obj.default if hasattr(obj, "default") else None

    def get_specialite_principale(self, obj):
        if hasattr(obj, "profile") and obj.profile.specialite_active:
            return obj.profile.specialite_active.spe
        return None

    def get_annuaire_sante_verification(self, obj):
        from accounts.fields import AnnuaireSanteVerificationField

        field = AnnuaireSanteVerificationField()
        return field.get_attribute(obj)

    def get_intervenant(self, obj):
        try:
            intervenant = Intervenant.objects.get(
                zkf_user=obj, zkf_environnement=obj.profile.zkf_environnement_user
            )
        except Intervenant.DoesNotExist:
            intervenant = None
        if intervenant is not None:
            # return the intervenant object serialized
            return IntervenantSerializer(intervenant, context=self.context).data
        return None


class PreferenceUserSerialyzer(DynamicDepthSerializer):
    current_praticiens_lies_selectionnes = UserSerializer(many=True)

    class Meta:
        model = Preferences_user
        fields = "__all__"


class UserGroupSerializer(DynamicDepthSerializer):
    class Meta:
        model = Group
        fields = "__all__"


class SpecialiteSerializer(DynamicDepthSerializer):
    class Meta:
        model = Specialite
        fields = "__all__"


class ServiceSerializer(DynamicDepthSerializer):
    class Meta:
        model = Service
        fields = "__all__"
        read_only_fields = ["zkf_environnement"]


class SiteSerializer(DynamicDepthSerializer):
    class Meta:
        model = Site
        fields = "__all__"


class LieuSerializer(DynamicDepthSerializer):
    class Meta:
        model = Lieu
        fields = "__all__"


class UserSitesSerializer(DynamicDepthSerializer):
    class Meta:
        model = Site
        fields = "__all__"


class UserSitesLieuxSerializer(DynamicDepthSerializer):
    class Meta:
        model = Lieu
        fields = "__all__"
        # exclude = ['specialite_profile', 'service', 'site', ]


class RendezVousSerializer(DynamicDepthSerializer):
    class Meta:
        model = RendezVous
        fields = "__all__"
        # exclude = ['specialite_profile', 'service', 'site', ]


class IntervenantSerializer(DynamicDepthSerializer):

    default = serializers.SerializerMethodField()

    class Meta:
        model = Intervenant
        fields = "__all__"

    def get_default(self, obj):
        return obj.default if hasattr(obj, "default") else None


class GroupSerializer(DynamicDepthSerializer):
    class Meta:
        model = Group
        fields = "__all__"


class EnteteDocumentSerializer(DynamicDepthSerializer):
    class Meta:
        model = EnteteDocument
        fields = "__all__"
