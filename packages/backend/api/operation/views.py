from django.forms import ValidationError
from django.http import JsonResponse
from rest_framework import generics
from rest_framework import permissions
from api.operation.serializers import (
    OperationSerializer,
    CroTypeSerializer,
    getGenericSerializer,
    IrradiationSerializer,
    MatrielIrradiationSerializer,
    OperationComplicationsSerializer,
    ContrasteSerializer,
)
from rest_framework.views import APIView
from django.contrib.auth.models import User

from api.mixins.drf_multiple_model.views import ObjectMultipleModelAPIView
from operation.models import Operation, CroType, Irradiation, MaterielIrradiation
from consultation.models import Consultation
from operation.models import (
    Operation,
    CroType,
    Irradiation,
    MaterielIrradiation,
    Contraste,
    Protocole,
)
from operation.operation_scripts.generate_cro_type import (
    generate_cro_variables,
    # generate_cro_with_variables,
)
from django.shortcuts import get_object_or_404
from notification.scripts_notifications.situation import (
    notification_operation_create,
    notification_add_patient_protocole,
)
from patient.patient_scripts.etat import update_etat_patient

# script to import all class of a specialite model.
# return exists if there is classes, and the linked class if exists
from operation.operation_scripts.get_operation_data import (
    # get_onetoone_relationship,
    # get_foreign_key_relationship,
    get_foreign_key_relationship_model_operation,
)
from django.template import Context, Template  # render string with arguments
from django.utils.html import format_html
from rest_framework.response import Response
from django.apps import apps
from notification.signals import notification_signal

# from babylone.authentication import KeycloakAuthentication

from rest_framework.viewsets import ModelViewSet
from auditlog.context import set_actor
from collections import Counter


class OperationList(generics.ListCreateAPIView):
    queryset = Operation.objects.all()
    serializer_class = OperationSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self, *args, **kwargs):
        queryset = super().get_queryset()
        patient = self.request.query_params.get("patient", None)
        hospitalisation = self.request.query_params.get("hospitalisation", None)
        consultation = self.request.query_params.get("consultation", None)
        date = self.request.query_params.get("date", None)

        if len(self.request.query_params) == 0:
            raise ValidationError("you must provide query parameters")

        if (
            date is not None
            and patient is None
            and hospitalisation is None
            and consultation is None
        ):
            date = datetime.strptime(date, "%Y-%m-%d").date()
            queryset = queryset.filter(
                operation_date=date,
                deleted=False,
                zkf_environnement=self.request.user.profile.zkf_environnement_user,
            )

        elif patient is not None and hospitalisation is None:
            queryset = queryset.filter(zkf_patient=patient, deleted=False)
        elif consultation is not None:
            consultation = get_object_or_404(Consultation, id=consultation)
            queryset = consultation.operation_liee.all()
        elif hospitalisation is not None and patient is None:
            queryset = queryset.filter(
                zkf_hospitalisation=hospitalisation, deleted=False
            )
        elif hospitalisation is None and patient is None:
            raise ValidationError(
                "you must provide either a patient or hospitalisation"
            )
        elif hospitalisation is not None and patient is not None:
            raise ValidationError("you must choose either a patient or hospitalisation")

        return queryset.order_by("-operation_date")

    def perform_create(self, serializer):
        instance = serializer.save(
            zkf_environnement=self.request.user.profile.zkf_environnement_user
        )

        # send notification to SECRETAIRE when operation crée
        # get secretaire...->personne liée autre que soit !
        users_praticiens = self.request.user.accounts_profile_praticiens_lies.all()
        # filter user by praticiens_lies and group => validate for secretaires !
        users = User.objects.all().filter(
            profile__in=users_praticiens, profile__role__in=["SEC"]
        )
        # check if users have the site authorization..., exclude current user
        users = users.exclude(username=self.request.user)
        # convert users as a list
        users = list(users)
        # send notification to each user
        notification_operation_create(
            sender=self.request.user,
            operation=instance,
            recipients=users,
        )

        # check if operation is linked to a protocole:
        if instance.protocoles.exists():
            # send notification to each user
            for protocole in instance.protocoles.all():
                recipients = (
                    protocole.protocole_users.all()
                    if protocole.protocole_users.exists()
                    else []
                )
                notification_add_patient_protocole(
                    sender=self.request.user,
                    patient=instance.zkf_patient,
                    protocole=protocole,
                    recipients=list(protocole.protocole_users.all()),
                )

        update_etat_patient(
            instance.zkf_patient, instance.operation_date
        )  # , "creation consultation")

        # update praticien referent
        if instance.zkf_redacteur:
            instance.zkf_patient.praticien_referent.add(instance.zkf_redacteur)


class OperationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Operation.objects.all()
    serializer_class = OperationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    # def get_object(self):
    #     obj = super().get_object()
    #     # check if the user has access to the object
    #     # send notification to each user
    #     users = self.request.user.profile.praticiens_lies.all()
    #     notification_operation_create(
    #         sender = self.request.user,
    #         operation = obj,
    #         recipients = list(users),#[User.objects.get(id=2)],
    #     )
    #     return obj

    def perform_update(self, serializer):
        instance = serializer.instance
        current_m2m = set(instance.protocoles.all().values_list("id", flat=True))

        # Save the instance
        super().perform_update(serializer)

        # send notification if patient added in protocole
        # Refresh from DB
        instance.refresh_from_db()

        updated_m2m = set(instance.protocoles.all().values_list("id", flat=True))

        new_relationships = updated_m2m - current_m2m

        if new_relationships:
            # send notification to each user
            for protocole in new_relationships:
                protocole = Protocole.objects.get(id=protocole)
                recipients = (
                    protocole.protocole_users.all()
                    if protocole.protocole_users.exists()
                    else []
                )
                notification_add_patient_protocole(
                    sender=self.request.user,
                    patient=instance.zkf_patient,
                    protocole=protocole,
                    recipients=list(protocole.protocole_users.all()),
                )
        update_etat_patient(
            instance.zkf_patient, instance.operation_date
        )  # , "creation operation"

        # update praticien referent
        if instance.zkf_redacteur:
            instance.zkf_patient.praticien_referent.add(instance.zkf_redacteur)


class OperationFieldsList(APIView):
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get(self, request, *args, **kwargs):
        field = self.request.query_params.get("field", None)
        if field is not None:
            fields = set(
                list(
                    Operation.objects.filter(
                        zkf_redacteur=self.request.user
                    ).values_list(field, flat=True)
                )
            )

        return Response(fields)


class CroTypeList(generics.ListCreateAPIView):
    queryset = CroType.objects.all()
    serializer_class = CroTypeSerializer
    pagination_class = None
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self, *args, **kwargs):
        queryset = CroType.objects.all().filter(zkf_user=self.request.user)
        suggested = CroType.objects.filter(id="a3e11874-64dd-4a53-be81-4745dd349f93")
        for object in queryset:
            if object in suggested:
                object.suggested = True
            else:
                object.suggested = False

        return queryset

    def perform_create(self, serializer):
        # create ordonnance template
        # if duplicate_user is not in request data
        if not (
            "duplicate_user" in self.request.data
            and self.request.data["duplicate_user"]
        ):
            instance = serializer.save(
                zkf_user=self.request.user,
            )

        # duplicate ordonnance template for selected users
        if "duplicate_user" in self.request.data:
            users = self.request.data["duplicate_user"]
            if not users:
                users = [self.request.data["zkf_user"]]
            # duplicate ordonnance template for selected users
            # duplicate ordonnance template for selected users
            for user in users:
                user = User.objects.get(id=user)
                new_instance = CroTypeSerializer(data=self.request.data)
                if new_instance.is_valid():
                    new_instance.save(zkf_user=user)
                else:
                    print(new_instance.errors)


class CroTypeDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = CroType.objects.all()
    serializer_class = CroTypeSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class PeriOperativeDetail(ObjectMultipleModelAPIView):
    querylist = []
    permission_classes = [
        permissions.IsAuthenticated,
    ]
    pagination_class = None

    def get_querylist(self, *args, **kwargs):
        # super().get_querylist()
        querylist = None
        # get all classes linked to the operation
        operation = get_object_or_404(
            Operation, id=self.kwargs.get("operation_pk", None)
        )
        specialite = operation.zkf_hospitalisation.specialite_referente.spe
        classes = get_foreign_key_relationship_model_operation(
            user=self.request.user, obj=operation
        )

        model_name = [name for name, model in classes["onetomany_model_class"].items()]
        # print ("model_name", model_name)
        # get all models objects from model_name
        app = "_".join(specialite.split(" ")).lower()
        models = [apps.get_model(app + "." + m) for m in model_name]

        # generate a list of queryset with operation, and related tables
        querylist = [
            {
                "queryset": Operation.objects.filter(id=operation.id),
                "serializer_class": OperationSerializer,
            }
        ]

        for model in models:
            serializer = getGenericSerializer(model)
            try:
                query = model.objects.filter(zkf_operation=operation.id)
                if query.exists():
                    query_dict = {"queryset": query, "serializer_class": serializer}
                    querylist.append(query_dict)

            except Exception as e:
                print(e)
                pass
        # print (querylist)

        return querylist


class GenerateCro(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request, *args, **kwargs):

        context = {}

        operation = get_object_or_404(
            Operation, id=self.kwargs.get("operation_pk", None)
        )
        cro_type_object = get_object_or_404(
            CroType, id=self.kwargs.get("cro_type_pk", None)
        )

        # generate cro type with variables feeded !
        # 1/ generate variables of the template (script !)
        context_operation = generate_cro_variables(operation)

        # 2/ generate a "template" from the cro_type
        template_corps = Template(cro_type_object.cro_type)
        template_indication = Template(cro_type_object.cro_type_indication_operatoire)

        # 3/ generate cro filled with variables
        c = Context(context_operation)  # generate context from dict
        cro_type = template_corps.render(c)
        cro_type = format_html(cro_type)
        context["cro_type"] = cro_type

        indication_operation = template_indication.render(c)
        indication_operation = format_html(indication_operation)
        context["indication_operation_type"] = (
            indication_operation if str(indication_operation) != "None" else ""
        )

        # 4/update manytomanyFields
        operation.cro_types.add(cro_type_object)
        operation.zkf_cro_type = cro_type_object
        operation.save()

        return Response(context)


class OperationIrradiationList(generics.ListCreateAPIView):
    queryset = Irradiation.objects.all()
    serializer_class = IrradiationSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context

    def get_queryset(self, *args, **kwargs):
        queryset = super().get_queryset()
        operation = self.request.query_params.get("operation", None)
        if operation is not None:
            queryset = queryset.filter(zkf_operation=operation)
        return queryset

    # def perform_create(self, serializer):
    #     operation = self.request.query_params.get("operation", None)
    #     if operation is not None:
    #         operation = get_object_or_404(Operation, id=operation)
    #         serializer.save(zkf_operation=operation)
    #     else:
    #         raise ValidationError("You must provide an operation id")


class OperationIrradiationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Irradiation.objects.all()
    serializer_class = IrradiationSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class MaterielIrradiationList(generics.ListCreateAPIView):
    queryset = MaterielIrradiation.objects.all()
    serializer_class = MatrielIrradiationSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class MaterielIrradiationDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = MaterielIrradiation.objects.all()
    serializer_class = MatrielIrradiationSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class ContrasteList(generics.ListCreateAPIView):
    queryset = Contraste.objects.all()
    serializer_class = ContrasteSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]
    pagination_class = None

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


class ContrasteDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = Contraste.objects.all()
    serializer_class = ContrasteSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["depth"] = int(self.request.query_params.get("depth", 0))
        return context


# get list des types d'anesthesie selon les operations de chaque user
class AnesthesieTypeList(generics.ListAPIView):
    permission_classes = [
        permissions.IsAuthenticated,
    ]
    serializer_class = OperationSerializer
    pagination_class = None

    def get(self, *args, **kwargs):
        user = self.request.user
        operations = Operation.objects.filter(redacteur=user)
        anesthesie_types = operations.values("anesthesie").distinct()
        queryset = list(set(val for dic in anesthesie_types for val in dic.values()))

        return JsonResponse(queryset, safe=False)


# List all fields of Operation Model
class OperationModelFields(APIView):
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get(self, request, *args, **kwargs):

        exclude = [
            "id",
            "created_on",
            "created_by",
            "modified_on",
            "modified_by",
            "deleted",
            "deleted_on",
            "deleted_by",
        ]
        operation_fields = [
            f.name
            for f in Operation._meta.get_fields()
            if (
                not f.many_to_many
                and not f.one_to_many
                and not f.one_to_one
                and not f.many_to_one
            )  # not n.one_to_many or n.one_to_one) ]
        ]

        spe = self.request.user.profile.specialite_active.spe
        user_spe = "_".join(spe.split(" ")).lower()

        all_models = apps.all_models[user_spe]
        # all_models is an OrderedDict -> convert as list
        # https://stackoverflow.com/questions/********/accessing-items-in-an-collections-ordereddict-by-index
        # models = [(x[1].__name__, x[0]) for x in list(all_models.items())]
        models = [x[1].__name__ for x in list(all_models.items())]

        # create a dict with list of fields for each model
        models_fields = list()
        models_fields.append(
            {
                "fields": [e for e in operation_fields if e not in exclude],
                "nom": "Operation",
                "model": "operation",
                "descriptiont": "description operation",
            }
        )

        for model_name in models:
            model = apps.get_model(user_spe + "." + model_name)  # e.g "accounts.User"

            fields = [
                x.name
                for x in model._meta.get_fields()
                if (
                    not x.many_to_many and not x.one_to_many and not x.one_to_one
                )  # and not x.many_to_one) ]
            ]
            # exclude fields
            fields = [e for e in fields if e not in exclude]
            models_fields.append(
                {
                    "fields": fields,
                    "nom": model_name,
                    "model": model_name.lower(),
                    "description": "description " + model_name,
                }
            )

        return Response(models_fields)


# List all fields of templates model of a specialite
class OperationTemplateModelFields(APIView):
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get(self, request, *args, **kwargs):
        # get user specialite principale
        spe = self.request.user.profile.specialite_active.spe
        user_spe = "_".join(spe.split(" ")).lower()

        all_models = apps.all_models[user_spe]
        # all_models is an OrderedDict -> convert as list
        # https://stackoverflow.com/questions/********/accessing-items-in-an-collections-ordereddict-by-index
        # models = [(x[1].__name__, x[0]) for x in list(all_models.items())]
        models = [x[1].__name__ for x in list(all_models.items())]

        # create a dict with list of fields for each model
        models_fields = dict()
        for model_name in models:
            model = apps.get_model(user_spe + "." + model_name)  # e.g "accounts.User"

            fields = [
                x.name
                for x in model._meta.get_fields()
                if (
                    not x.many_to_many and not x.one_to_many and not x.one_to_one
                )  # and not x.many_to_one) ]
            ]
            models_fields[model_name] = fields

        return Response(models_fields)


class ComplicationsPostOperatoireList(generics.ListAPIView):
    permission_classes = [
        permissions.IsAuthenticated,
    ]
    serializer_class = OperationComplicationsSerializer
    querysert = None
    pagination_class = None

    # dynamically set serializer model for AtcdSerializer !
    # https://stackoverflow.com/questions/********/create-a-generic-serializer-with-a-dynamic-model-in-meta
    def get_serializer_class(self):
        operation = self.request.query_params.get("operation", None)
        # if operation is not None:
        operation = get_object_or_404(Operation, id=operation)

        specialite = operation.zkf_hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))
        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_complications = "ComplicationPostOperatoire" + "".join(
            x.capitalize() for x in specialite.split("_")
        )
        model = apps.get_model(specialite, model_complications)
        OperationComplicationsSerializer.Meta.model = model

        return OperationComplicationsSerializer

    def get_queryset(self):
        operation = self.request.query_params.get("operation", None)
        if operation is not None:
            operation = get_object_or_404(Operation, id=operation)

            specialite = operation.zkf_hospitalisation.specialite_referente.spe
            specialite = "_".join(specialite.lower().split(" "))
            # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
            model_complications = "ComplicationPostOperatoire" + "".join(
                x.capitalize() for x in specialite.split("_")
            )
            # model = apps.get_model(specialite + "." + model_complications)
            model_name = apps.get_model(specialite, model_complications)

            # check if there is already a one to one relationship created
            # else create a one to one relationship:
            if hasattr(operation, model_complications.lower()) is False:
                # if not create one to one table (spe of hospitalisation, get complications table)
                obj = model_name(zkf_operation=operation)
                obj.save()
                obj = model_name.objects.filter(zkf_operation=operation)
            else:
                obj = model_name.objects.filter(
                    zkf_operation=operation
                )  # getattr(operation, model_complications.lower())

            return obj

        return Operation.objects.none()

    def perform_create(self, serializer):
        operation = self.request.query_params.get("operation", None)
        if operation is not None:
            operation = get_object_or_404(Operation, id=operation)
            serializer.save(zkf_operation=operation)
        else:
            raise ValidationError("You must provide an operation id")


class ComplicationsPostOperatoireDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = None
    serializer_class = OperationComplicationsSerializer
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    # dynamically set serializer model
    # https://stackoverflow.com/questions/********/create-a-generic-serializer-with-a-dynamic-model-in-meta
    def get_serializer_class(self):
        operation = self.request.query_params.get("operation", None)
        # if operation is not None:
        operation = get_object_or_404(Operation, id=operation)

        specialite = operation.zkf_hospitalisation.specialite_referente.spe
        specialite = "_".join(specialite.lower().split(" "))
        # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
        model_complications = "ComplicationPostOperatoire" + "".join(
            x.capitalize() for x in specialite.split("_")
        )
        model = apps.get_model(specialite, model_complications)
        OperationComplicationsSerializer.Meta.model = model

        return OperationComplicationsSerializer

    def get_object(self):
        operation = self.request.query_params.get("operation", None)
        if operation is not None:
            operation = get_object_or_404(Operation, id=operation)

            specialite = operation.zkf_hospitalisation.specialite_referente.spe
            specialite = "_".join(specialite.lower().split(" "))
            # Convert name of the specialite to the model -> naming consistency is mandatory !!!!
            model_complications = "ComplicationPostOperatoire" + "".join(
                x.capitalize() for x in specialite.split("_")
            )
            # model = apps.get_model(specialite + "." + model_complications)

            obj = getattr(operation, model_complications.lower())

            return obj

        raise ValidationError("You must provide an operation id")


class ListValuesFieldOperation(APIView):
    permission_classes = [
        permissions.IsAuthenticated,
    ]

    def get(self, request, *args, **kwargs):
        field = self.request.query_params.get("field", None)
        if field is None:
            raise ValidationError("You must provide a field name")

        # get all values of the field
        values = Operation.objects.filter(zkf_redacteur=self.request.user).values_list(
            field, flat=True
        )
        # Exclude empty values
        values = [v for v in values if v is not None and v != ""]
        # The goal is to return a list of tuples with unique values and number of occurrences of each value
        values = Counter(values)
        # Convert Counter to a list of tuples
        if isinstance(values, Counter):
            # Convert Counter to a list of tuples
            values = [(k, v) for k, v in values.items()]
        # If you want to return only the unique values, you can convert it to a set
        values = set(values)
        # If you want to return a list of values, you can convert it to a list
        if not values:
            raise ValidationError(f"No values found for field '{field}'")

        if isinstance(values, set):
            # Convert set to a list
            values = list(values)

        return Response(values)
