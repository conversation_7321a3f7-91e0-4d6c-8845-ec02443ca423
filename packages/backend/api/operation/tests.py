from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from operation.models import Operation
from consultation.models import Consultation
from django.contrib.auth.models import User
from operation.operation_models import CroType, Irradiation, MaterielIrradiation


class OperationListTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)
        self.operation = Operation.objects.create(
            zkf_patient="patient1", zkf_hospitalisation="hospitalisation1"
        )
        self.consultation = Consultation.objects.create(id=1)

    def test_get_queryset_with_patient(self):
        response = self.client.get(reverse("operation-list"), {"patient": "patient1"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["zkf_patient"], "patient1")

    def test_get_queryset_with_consultation(self):
        self.consultation.operation_liee.add(self.operation)
        response = self.client.get(reverse("operation-list"), {"consultation": 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["zkf_patient"], "patient1")

    def test_get_queryset_with_hospitalisation(self):
        response = self.client.get(
            reverse("operation-list"), {"hospitalisation": "hospitalisation1"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["zkf_hospitalisation"], "hospitalisation1")

    def test_get_queryset_without_patient_or_hospitalisation(self):
        response = self.client.get(reverse("operation-list"))
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_queryset_with_both_patient_and_hospitalisation(self):
        response = self.client.get(
            reverse("operation-list"),
            {"patient": "patient1", "hospitalisation": "hospitalisation1"},
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_perform_create(self):
        data = {"zkf_patient": "patient2", "zkf_hospitalisation": "hospitalisation2"}
        response = self.client.post(reverse("operation-list"), data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Operation.objects.count(), 2)
        self.assertEqual(
            Operation.objects.get(id=response.data["id"]).zkf_patient, "patient2"
        )


class OperationDetailTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)
        self.operation = Operation.objects.create(
            zkf_patient="patient1", zkf_hospitalisation="hospitalisation1"
        )

    def test_get_operation_detail(self):
        response = self.client.get(
            reverse("operation-detail", args=[self.operation.id])
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["zkf_patient"], "patient1")

    def test_update_operation_detail(self):
        data = {"zkf_patient": "patient2"}
        response = self.client.put(
            reverse("operation-detail", args=[self.operation.id]), data
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.operation.refresh_from_db()
        self.assertEqual(self.operation.zkf_patient, "patient2")

    def test_delete_operation_detail(self):
        response = self.client.delete(
            reverse("operation-detail", args=[self.operation.id])
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Operation.objects.filter(id=self.operation.id).exists())


class GenerateCroTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.operation = Operation.objects.create(
            zkf_patient="patient1", zkf_hospitalisation="hospitalisation1"
        )
        self.cro_type = CroType.objects.create(cro_type="{{ operation.zkf_patient }}")

    def test_generate_cro(self):
        response = self.client.get(
            reverse("generate-cro", args=[self.operation.id, self.cro_type.id])
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("cro_type", response.data)
        self.assertEqual(response.data["cro_type"], "patient1")


class OperationIrradiationTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)
        self.irradiation = Irradiation.objects.create(name="irradiation1")

    def test_get_irradiation_list(self):
        response = self.client.get(reverse("irradiation-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["name"], "irradiation1")

    def test_create_irradiation(self):
        data = {"name": "irradiation2"}
        response = self.client.post(reverse("irradiation-list"), data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Irradiation.objects.count(), 2)
        self.assertEqual(
            Irradiation.objects.get(id=response.data["id"]).name, "irradiation2"
        )


class MaterielIrradiationTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)
        self.materiel_irradiation = MaterielIrradiation.objects.create(name="materiel1")

    def test_get_materiel_irradiation_list(self):
        response = self.client.get(reverse("materielirradiation-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]["name"], "materiel1")

    def test_create_materiel_irradiation(self):
        data = {"name": "materiel2"}
        response = self.client.post(reverse("materielirradiation-list"), data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(MaterielIrradiation.objects.count(), 2)
        self.assertEqual(
            MaterielIrradiation.objects.get(id=response.data["id"]).name, "materiel2"
        )


class AnesthesieTypeListTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)
        self.operation = Operation.objects.create(
            zkf_patient="patient1",
            zkf_hospitalisation="hospitalisation1",
            redacteur=self.user,
            anesthesie="type1",
        )

    def test_get_anesthesie_type_list(self):
        response = self.client.get(reverse("anesthesie-type-list"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0], "type1")


class OperationModelFieldsTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)

    def test_get_operation_model_fields(self):
        response = self.client.get(reverse("operation-model-fields"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Operation", [model["nom"] for model in response.data])


class OperationTemplateModelFieldsTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)

    def test_get_operation_template_model_fields(self):
        response = self.client.get(reverse("operation-template-model-fields"))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, dict)


class ComplicationsPostOperatoireTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username="testuser", password="testpassword"
        )
        self.client.force_authenticate(user=self.user)
        self.operation = Operation.objects.create(
            zkf_patient="patient1", zkf_hospitalisation="hospitalisation1"
        )

    def test_get_complications_post_operatoire_list(self):
        response = self.client.get(
            reverse("complications-post-operatoire-list"),
            {"operation": self.operation.id},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_complications_post_operatoire_detail(self):
        response = self.client.get(
            reverse("complications-post-operatoire-detail"),
            {"operation": self.operation.id},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
