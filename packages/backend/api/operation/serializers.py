from rest_framework import serializers
from operation.models import (
    Operation,
    CroType,
    Irradiation,
    MaterielIrradiation,
    Contraste,
)
from operation.models import Protocole
import datetime

# from api.operation.scripts.filter_intervenant import get_intervenant_operateurs
from api.sort.tag.serializers import TagList<PERSON>erial<PERSON>Field, TaggitSerializer


# https://www.linkedin.com/pulse/friday-quick-tip-dynamic-depth-serialization-django-rest-neidinger/
class DynamicDepthSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.Meta.depth = self.context.get("depth", 0)


class OperationSerializer(TaggitSerializer, DynamicDepthSerializer):
    tags = TagListSerializerField(required=False)
    model_app_datas = serializers.SerializerMethodField()
    # protocoles is manytomany with through
    protocoles = serializers.PrimaryKeyRelatedField(
        queryset=Protocole.objects.all(), many=True, required=False
    )

    # override create and update as protocole is a manytomany with through (by default read only field in DRF)

    def create(self, validated_data):

        protocoles = validated_data.pop("protocoles", [])

        instance = super().create(validated_data)
        for protocole in protocoles:
            instance.protocoles.add(protocole)

        return instance

    def update(self, instance, validated_data):

        protocoles_update = (
            validated_data.pop("protocoles") if "protocoles" in validated_data else []
        )

        instance = super().update(instance, validated_data)

        protocoles_all = instance.protocoles.all()
        # add new protocoles
        for protocole in protocoles_update:
            if protocole not in protocoles_all:
                instance.protocoles.add(protocole)
        # remove protocoles
        for protocole in protocoles_all:
            if protocole not in protocoles_update:
                instance.protocoles.remove(protocole)

        # #check heure d'entrée hospitalisation et heure du bloc
        # if instance.zkf_hospitalisation:
        #     if datetime.datetime.combine(
        #         instance.zkf_hospitalisation.entree_heure,
        #         instance.zkf_hospitalisation.entree_date
        #         ) > instance.operation_start:
        #         raise serializers.ValidationError(
        #             {"error": "ENTREE_HOSPITALISATION_AFTER_OPERATION_START"}
        #         )

        return instance

    class Meta:
        model = Operation
        # fields = "__all__"
        exclude = ["zkf_environnement"]

    def get_model_app_datas(self, obj):
        return {
            "model": self.Meta.model.__name__.lower(),
            "app": self.Meta.model._meta.app_label,
        }


class CroTypeSerializer(DynamicDepthSerializer):

    suggested = serializers.SerializerMethodField()

    class Meta:
        model = CroType
        fields = "__all__"  # ('id', 'nom', 'prenom', ...)
        # exclude = ["zkf_cro_type", "zkf_cro_types"]

    def get_suggested(self, obj):
        suggested = obj.suggested if hasattr(obj, "suggested") else False
        return suggested


def getGenericSerializer(model_arg):
    class GenericSerializer(serializers.ModelSerializer):
        class Meta:
            model = model_arg
            fields = "__all__"

    return GenericSerializer


"""Irradiation"""


class IrradiationSerializer(DynamicDepthSerializer):

    class Meta:
        model = Irradiation
        fields = "__all__"


class MatrielIrradiationSerializer(DynamicDepthSerializer):

    # zkf_site: manytomany vers le site ou le materiel est utilisé
    site = serializers.UUIDField(required=False)

    # ManyToManyField vers spé
    specialite = serializers.UUIDField(required=False)

    class Meta:
        model = MaterielIrradiation
        fields = "__all__"


class ContrasteSerializer(DynamicDepthSerializer):

    class Meta:
        model = Contraste
        fields = "__all__"


class OperationComplicationsSerializer(serializers.ModelSerializer):

    class Meta:

        model = None
        exclude = [
            "modified_by",
            "created_by",
            "modified_on",
            "created_on",
            "deleted_by",
            "deleted_on",
            "deleted",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # get model from Meta
        model = getattr(self.Meta, "model")
