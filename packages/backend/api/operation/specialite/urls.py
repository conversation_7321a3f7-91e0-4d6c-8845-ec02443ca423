from django.urls import path
from api.operation.specialite.views import (
    OperationTemplateListView,
    OperationTemplateDetailView,
    OperationModelsListView,
)

# app_name = "operation-templates"

urlpatterns = [
    # get operation list of specific operative models by specialite -> send spécialité in url or get it from request.user !
    path("", OperationModelsListView.as_view(), name="operation-models-list"),
    path(
        "<str:specialite>",
        OperationModelsListView.as_view(),
        name="operation-models-list",
    ),
    # get operation templates by specialite
    path(
        "<str:specialite>/<str:template_name>",
        OperationTemplateListView.as_view(),
        name="operation-template-list",
    ),
    # get operation template details
    path(
        "<str:specialite>/<str:template_name>/<uuid:pk>",
        OperationTemplateDetailView.as_view(),
        name="operation-template-detail",
    ),
]
