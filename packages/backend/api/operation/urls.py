from django.urls import path

from api.operation.views import (
    OperationList,
    OperationDetail,
    CroTypeList,
    CroTypeDetail,
    GenerateCro,
    PeriOperativeDetail,
    OperationIrradiationList,
    OperationIrradiationDetail,
    MaterielIrradiationList,
    MaterielIrradiationDetail,
    AnesthesieTypeList,
    OperationModelFields,
    ComplicationsPostOperatoireList,
    ComplicationsPostOperatoireDetail,
    OperationFieldsList,
    ContrasteList,
    ContrasteDetail,
    ListValuesFieldOperation,
)

# route: /api/operation/

urlpatterns = [
    path("", OperationList.as_view(), name="operations-list"),
    path(
        "<uuid:pk>",
        OperationDetail.as_view(),
        name="operation-detail",
    ),
    # pre, per et post op datas
    path("<uuid:operation_pk>/peri-op", PeriOperativeDetail.as_view(), name="peri-op"),
    # get intervenants hospitalisation_operation_consultation
    # get list of CRO type for current user !
    path("cro-type", CroTypeList.as_view(), name="cro-type-list"),
    path(
        "cro-type/<uuid:pk>",
        CroTypeDetail.as_view(),
        name="cro-type-detail",
    ),
    # generate CRO from CROtype
    path(
        "<uuid:operation_pk>/<uuid:cro_type_pk>/generate-cro",
        GenerateCro.as_view(),
        name="generate-cro",
    ),
    path("list-fields", OperationFieldsList.as_view(), name="operation-fields-list"),
    # irradiation
    path("irradiation", OperationIrradiationList.as_view(), name="irradiation-list"),
    path(
        "irradiation/<uuid:pk>",
        OperationIrradiationDetail.as_view(),
        name="irradiation-detail",
    ),
    # appareil de scopie
    path(
        "appareil-scopie",
        MaterielIrradiationList.as_view(),
        name="appareil-scopie-list",
    ),
    path(
        "appareil-scopie/<uuid:pk>",
        MaterielIrradiationDetail.as_view(),
        name="appareil-scopie-detail",
    ),
    # get liste de type d'anesthesie selon les operations de chaque user
    path("anesthesie-type", AnesthesieTypeList.as_view(), name="anesthesie-type-list"),
    # get complications post opératoires
    path(
        "complications-post-op",
        ComplicationsPostOperatoireList.as_view(),
        name="complications-post-op",
    ),
    path(
        "complications-post-op/<uuid:pk>",
        ComplicationsPostOperatoireDetail.as_view(),
        name="complications-post-op-detail",
    ),
    # get contrast list
    path("contrast", ContrasteList.as_view(), name="contrast-list"),
    path("contrast/<uuid:pk>", ContrasteDetail.as_view(), name="contrast-detail"),
    # get list of values for a field, filtered by operateur
    path(
        "operation-model-fields",
        ListValuesFieldOperation.as_view(),
        name="operation-model-fields",
    ),
]
