# Generated by Django 5.1.7 on 2025-06-01 19:24

from django.db import migrations


def transfer_correspondants(apps, schema_editor):
    Patient = apps.get_model("patient", "Patient")
    CorrespondantPatient = apps.get_model("contact", "CorrespondantPatient")

    for patient in Patient.objects.all():
        correspondants = patient.correspondants.all()
        for contact in correspondants:
            CorrespondantPatient.objects.get_or_create(
                patient=patient,
                contact=contact,
                defaults={
                    "send_report": True,  # Valeur par défaut, à adapter si nécessaire
                    "referral_source": False,  # Valeur par défaut, à adapter si nécessaire
                },
            )


class Migration(migrations.Migration):

    dependencies = [
        ("contact", "0006_correspondantpatient"),
    ]

    operations = [
        migrations.RunPython(transfer_correspondants),
    ]
