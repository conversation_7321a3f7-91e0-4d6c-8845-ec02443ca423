from django.db import models
from audit.models import Auditable
from accounts.models import Specialite
from django.contrib.auth.models import User

# from patient.models import Patient

import uuid

"""""
Contacts:
Correspondants, ambulanciers, infirmier/es de ville...
->All people involved in patient healthcare (it is not a personnal contact database...)

*****supertype/subtype db schema*****
https://stackoverflow.com/questions/7844460/foreign-key-to-multiple-tables

So for emails, telephone...Instead of having multiple foreign keys, 
a new table is created with the foreign key of the corresponding record (email, tel...)
And another field populates the "category" for which the record is related to (ambulance, correspondant, infirmier de ville...)

""" ""


# list all types of contacts (patients, correspondants, paramedical...)
class ContactCategorie(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    contact_categorie = models.CharField(max_length=256)

    def __str__(self):
        return self.contact_categorie


class Contact(Auditable):
    # https://stackoverflow.com/questions/3052975/django-models-avoid-duplicates
    class Meta:
        unique_together = ["nom", "prenom", "structure", "zkf_environnement_contact"]

    MALE = "MALE"
    FEMALE = "FEMALE"

    SEXE = (
        (MALE, "Homme"),
        (FEMALE, "Femme"),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # Define which categorie the contact belongs to ! Can be set by default in the form, for patients-correspondants-ambulances-infirmières... for instance !...
    zkf_contactcategorie = models.ForeignKey(
        ContactCategorie, on_delete=models.PROTECT, blank=False, null=False
    )
    # environnement
    zkf_environnement_contact = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )

    nom = models.CharField(max_length=254, null=True, blank=True)
    prenom = models.CharField(max_length=254, null=True, blank=True)
    # rpps: 11 chiffres
    rpps = models.PositiveBigIntegerField(null=True, blank=True)
    sexe = models.CharField(
        max_length=100,
        choices=SEXE,
        null=True,
        blank=True,
    )
    structure = models.CharField(max_length=254, null=True, blank=True)
    DR = "Docteur"
    PR = "Professeur"
    MME = "Madame"
    MR = "Monsieur"

    TITRE = (
        (DR, "Docteur"),
        (PR, "Professeur"),
        (MME, "Madame"),
        (MR, "Monsieur"),
    )

    specialite = models.ManyToManyField(Specialite, blank=True)
    titre = models.CharField(max_length=50, choices=TITRE, default=DR)
    en_activite = models.BooleanField(default=True)
    comment = models.TextField(null=True, blank=True)

    # link to annuaire sante
    created_from_annuaire_sante = models.BooleanField(default=False)
    annuaire_sante_verification = models.DateTimeField(null=True, blank=True)
    annuaire_sante_last_attempt = models.DateTimeField(null=True, blank=True)
    id_annuaire_sante = models.CharField(max_length=254, null=True, blank=True)

    def __str__(self):
        return "{0} {1}".format(str(self.nom), str(self.prenom))

    def full_name(self):
        return "{0} {1}".format(self.nom, self.prenom)

    def politesse(self):
        politesse = ""
        if self.sexe == "MALE":
            politesse = "Monsieur"
        elif self.sexe == "FEMALE":
            politesse = "Madame"
        return "{0} le Docteur {1} {2}".format(politesse, self.prenom, self.nom)


# details for correspondant
class Correspondant(Auditable):
    DR = "Docteur"
    PR = "Professeur"

    TITRE = (
        (DR, "Docteur"),
        (PR, "Professeur"),
    )
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_contact = models.OneToOneField(Contact, on_delete=models.CASCADE)
    specialite = models.ManyToManyField(Specialite, blank=True)
    titre = models.CharField(max_length=50, choices=TITRE, default=DR)
    en_activite = models.BooleanField(default=True)
    formule_politesse_debut = models.ForeignKey(
        "FormulePolitesse",
        on_delete=models.CASCADE,
        related_name="%(app_label)s_%(class)s_politesse_debut_related",
        null=True,
        blank=True,
    )
    formule_politesse_fin = models.ForeignKey(
        "FormulePolitesse",
        on_delete=models.CASCADE,
        related_name="%(app_label)s_%(class)s_politesse_fin_related",
        null=True,
        blank=True,
    )

    def politesse_correspondant(self):
        politesse = ""
        if self.zkf_contact.sexe == "MALE":
            politesse = "Monsieur"
        elif self.zkf_contact.sexe == "FEMALE":
            politesse = "Madame"
        return "{0} le {1} {2} {3}".format(
            politesse, self.titre, self.zkf_contact.prenom, self.zkf_contact.nom
        )

    @property
    def specialite_correspondant(self):
        specialite = []
        for s in self.specialite.all():
            specialite.append(s.spe_medecin)

        return ",".join(specialite)


class CorrespondantPatient(Auditable):
    patient = models.ForeignKey("patient.Patient", on_delete=models.CASCADE)
    contact = models.ForeignKey("contact.Contact", on_delete=models.CASCADE)
    send_report = models.BooleanField(
        default=True, help_text="Envoyer les rapports/courriers à ce contact"
    )
    referral_source = models.BooleanField(
        default=False,
        help_text="Contact adresseur",
    )

    class Meta:
        unique_together = ("patient", "contact")

    def __str__(self):
        return f"{self.contact} ↔ {self.patient}"


# All emails
class Email(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # foreign key to the contact
    zkf_contact = models.ForeignKey(
        Contact, on_delete=models.PROTECT, null=True, blank=True
    )
    zkf_patient = models.ForeignKey(
        "patient.Patient", on_delete=models.PROTECT, null=True, blank=True
    )
    email = models.EmailField(
        max_length=254, null=False, blank=False, help_text="Entrer l'email"
    )
    email_mssante_boolean = models.BooleanField(
        default=False, help_text="Email messagerie sécurisée ?"
    )
    editable = models.BooleanField(default=True)
    categorie = models.CharField(max_length=254, null=True, blank=True)

    preferred = models.BooleanField(
        default=False, help_text="Mode de communication préféré ?"
    )

    def __str__(self):
        return self.email

    def email_all(self):
        emails = self.zkf_contact.email_set.all()
        array = []
        for e in emails:
            array.append(e.email)
        return ",".join(array)


class Telephone(Auditable):
    PROFESSIONNEL = "PROFESSIONNEL"
    PORTABLE = "PORTABLE"
    DOMICILE = "DOMICILE"
    AUTRE = "AUTRE"
    FAX = "FAX"

    TELEPHONE = (
        (PROFESSIONNEL, "Professionnel"),
        (PORTABLE, "Portable"),
        (DOMICILE, "Domicile"),
        (AUTRE, "Autre"),
        (FAX, "Fax"),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # foreign key to the contact
    zkf_contact = models.ForeignKey(
        Contact, on_delete=models.CASCADE, null=True, blank=True
    )
    zkf_patient = models.ForeignKey(
        "patient.Patient", on_delete=models.CASCADE, null=True, blank=True
    )
    numero = models.CharField(max_length=20, null=False, blank=False)
    categorie = models.CharField(
        max_length=254,
        null=True,
        blank=True,
        choices=TELEPHONE,
    )
    editable = models.BooleanField(default=True)
    preferred = models.BooleanField(
        default=False, help_text="Mode de communication préféré ?"
    )

    def __str__(self):
        return self.numero

    def numero_format(self):
        numero = self.numero
        try:
            # numero = "{}{} {}{} {}{} {}{} {}{} {}{} {}{}".format(*numero)
            numero = " ".join(numero[i : i + 2] for i in range(0, len(numero), 2))

        except Exception as e:
            numero = self.numero
        return numero


class Adresse(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # foreign key to the contact
    zkf_contact = models.ForeignKey(
        Contact, on_delete=models.CASCADE, null=True, blank=True
    )
    zkf_patient = models.ForeignKey(
        "patient.Patient", on_delete=models.CASCADE, null=True, blank=True
    )
    titre = models.CharField(max_length=254, null=True, blank=True)
    structure = models.CharField(max_length=256, null=True, blank=True)
    numero_rue = models.CharField(
        max_length=12, null=True, blank=True
    )  # charfield because of bis and ter...
    rue = models.CharField(max_length=254, null=True, blank=True)
    complement_adresse = models.CharField(max_length=254, null=True, blank=True)
    code_postal = models.PositiveIntegerField(null=False, blank=False)
    ville = models.CharField(max_length=254, null=False, blank=False)
    editable = models.BooleanField(default=True)

    preferred = models.BooleanField(
        default=False, help_text="Mode de communication préféré ?"
    )

    def __str__(self):
        return str(self.numero_rue) + str(self.rue) + str(self.ville)

    def adresse_complete(self):
        structure = ""
        if self.structure:
            structure = str(self.structure)

        numero_rue = ""
        if self.numero_rue:
            numero_rue = str(self.numero_rue)

        rue = ""
        if self.rue:
            rue = self.rue

        complement_adresse = ""
        if self.complement_adresse:
            complement_adresse = self.complement_adresse

        return str(
            structure
            + " "
            + numero_rue
            + ", "
            + rue
            + " "
            + complement_adresse
            + " "
            + str(self.code_postal)
            + " "
            + self.ville.upper()
        )


# https://maps.google.com/?q=23.22,88.32&z=8
class Ville(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom_ville = models.CharField(max_length=254, null=False, blank=False)
    code_postal = models.PositiveIntegerField(null=False, blank=False)
    coordonnees_gps = models.CharField(max_length=40, null=True)

    def __str__(self):
        return self.nom_ville


class FormulePolitesse(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    debut_ou_fin = models.CharField(
        max_length=6, null=False, blank=False
    )  # set if it is formule de politesse au début ou à la fin
    texte = models.CharField(max_length=254, null=False, blank=False)

    def __str__(self):
        return self.texte


"""""
Add private commentary for users
""" ""


class UserComment(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_user = models.ForeignKey(User, on_delete=models.PROTECT, blank=True, null=True)
    zkf_contact = models.ForeignKey(Contact, on_delete=models.PROTECT)
    commentaire = models.TextField(blank=True, null=True)


"""
Annuaire => import base CNAM
https://annuaire.sante.fr/web/site-pro/extractions-publiques
https://www.data.gouv.fr/fr/datasets/annuaire-sante-de-la-cnam/
"""


class CodeProfessionnelSante(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    champs_application = models.CharField(max_length=254, null=False, blank=False)
    code = models.CharField(max_length=10, null=False, blank=False)
    libelle = models.CharField(max_length=254, null=False, blank=False)

    def __str__(self):
        return self.libelle


"""1 : Ambulance /Véhicule sanitaire léger
2 : Anatomo-Cyto-Pathologiste
3 : Anesthésiste réanimateur
4 : Cancérologue radiothérapeute
5 : Cancérologue médical
6 : Cardiologue
7 : Chirurgien général
8 : Chirurgien infantile
9 : Chirurgien maxillo-facial
10 : Chirurgien maxillo-facial et stomatologiste
11 : Chirurgien oral
12 : Chirurgien orthopédiste et traumatologue
13 : Chirurgien plasticien
14 : Chirurgien thoracique et cardio-vasculaire
15 : Chirurgien urologue
16 : Chirurgien vasculaire
17 : Chirurgien viscéral
18 : Chirurgien-dentiste
19 : Chirurgien-dentiste spécialiste en orthopédie dento-faciale
20 : Chirurgiens-dentistes spécialiste en chirurgie orale
21 : Chirurgiens-dentistes spécialiste en médecine bucco-dentaire
22 : Dermatologue et vénérologue
23 : Endocrinologue-diabétologue
24 : Fournisseur de matériel médical et para-médical
25 : Fournisseur de matériel médical et para-médical
26 : Fournisseur de matériel médical et para-médical
27 : Fournisseur de matériel médical et para-médical
28 : Fournisseur de matériel médical et para-médical
29 : Fournisseur de matériel médical et para-médical
30 : Fournisseur de matériel médical et para-médical
31 : Fournisseur de matériel médical et para-médical
32 : Fournisseur de matériel médical et para-médical
33 : Gastro-entérologue et hépatologue
34 : Gériatre
35 : Gynécologue médical
36 : Gynécologue médical et obstétricien
37 : Gynécologue obstétricien
38 : Hématologue
39 : Infirmier
40 : Laboratoire
41 : Laboratoire
42 : Laboratoire d'anatomo-pathologie
43 : Masseur-kinésithérapeute
44 : Médecin biologiste
45 : Médecin généraliste
46 : Médecin généraliste
47 : Médecin généraliste
48 : Médecin généticien
49 : Médecin spécialiste en médecine nucléaire
50 : Médecin spécialiste en santé publique et médecine sociale
51 : Néphrologue
52 : Neurochirurgien
53 : Neurologue
54 : Neuropsychiatre
55 : Obstétricien
56 : Ophtalmologiste
57 : Orthophoniste
58 : Orthoptiste
59 : Oto-Rhino-Laryngologue (ORL) et chirurgien cervico-facial
60 : Pédiatre
61 : Pédicure-podologue
62 : Pharmacien
63 : Pharmacien
64 : Pneumologue
65 : Psychiatre
66 : Psychiatre de l'enfant et de l'adolescent
67 : Radiologue
68 : Radiothérapeute
69 : Réanimateur médical
70 : Rhumatologue
71 : Sage-femme
72 : Spécialiste en médecine interne
73 : Spécialiste en médecine physique et de réadaptation
74 : Stomatologiste
"""

"""Annuaire:
PS_LibreAcces_Personne_activite


Type d'identifiant PP
Identifiant PP
Identification nationale PP
Code civilité d'exercice
Libellé civilité d'exercice
Code civilité
Libellé civilité
Nom d'exercice
Prénom d'exercice
Code profession
Libellé profession
Code catégorie professionnelle
Libellé catégorie professionnelle
Code type savoir-faire
Libellé type savoir-faire
Code savoir-faire
Libellé savoir-faire
Code mode exercice
Libellé mode exercice
Numéro SIRET site
Numéro SIREN site
Numéro FINESS site
Numéro FINESS établissement juridique
Identifiant technique de la structure
Raison sociale site
Enseigne commerciale site
Complément destinataire (coord. structure)
Complément point géographique (coord. structure)
Numéro Voie (coord. structure)
Indice répétition voie (coord. structure)
Code type de voie (coord. structure)
Libellé type de voie (coord. structure)
Libellé Voie (coord. structure)
Mention distribution (coord. structure)
Bureau cedex (coord. structure)
Code postal (coord. structure)
Code commune (coord. structure)
Libellé commune (coord. structure)
Code pays (coord. structure)
Libellé pays (coord. structure)
Téléphone (coord. structure)
Téléphone 2 (coord. structure)
Télécopie (coord. structure)
Adresse e-mail (coord. structure)
Code Département (structure)
Libellé Département (structure)
Ancien identifiant de la structure
Autorité d'enregistrement
Code secteur d'activité
Libellé secteur d'activité
Code section tableau pharmaciens
Libellé section tableau pharmaciens
Code rôle
Libellé rôle
Code genre activité
Libellé genre activité
"""
