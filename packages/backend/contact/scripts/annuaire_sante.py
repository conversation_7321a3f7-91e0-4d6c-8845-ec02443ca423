from typing import Optional, Dict, Any, List
from django.db.models import Count
import logging
import requests
import urllib3
from django.conf import settings
from contact.models import Contact, Email, <PERSON>resse, Telephone
import re
import datetime
from django.contrib.auth.models import User

from annuaire_sante.services import AnnuaireSanteClient
from annuaire_sante.services import DoctorInfo

logger = logging.getLogger(__name__)


# https://www.calazan.com/adding-basic-search-to-your-django-site/
def get_doctor(
    request, family_name: str = None, given_names: str = None, rpps: str = None
) -> Optional[DoctorInfo]:
    """
    Get doctor info from the Annuaire Sante API.
    """
    client = AnnuaireSanteClient()
    # logger.warning(f"Searching for doctor: {family_name} {given_names} {rpps}")
    if rpps is not None:
        doctor = client.get_doctor_info(rpps)
        if doctor:
            return doctor

    if family_name is not None and given_names is not None:
        doctors = client.search_practicioner(family_name, given_names)
        if doctors:
            return doctors

    return None


def update_local_annuaire(request, contacts: List[Dict[str, Any]] = None):
    """
    Update the local database with the latest data from the Annuaire Sante API.
    """
    # first delete duplicates !
    search_duplicate(request)

    # then update doctors
    # check if list of contacts has been sent => update only those contacts ! Avoid updating contacts to often !
    if contacts is None:
        contacts = Contact.objects.filter(
            en_activite=True,
            zkf_environnement_contact=request.user.profile.zkf_environnement_user.id,
        )

    for contact in contacts:
        practicioner = get_doctor(request, contact.nom, contact.prenom, contact.rpps)
        if practicioner:
            contact.nom = practicioner.family_name
            contact.prenom = " ".join(practicioner.given_names)
            contact.rpps = practicioner.rpps

            emails_contact = contact.email_set.all()
            # Check if emails exists and is not None before iterating
            if practicioner.emails is not None:
                for e in practicioner.emails:
                    # add email => loop through existing emails !
                    for email_contact in emails_contact:
                        if email_contact.email in practicioner.emails:
                            pass
                        else:
                            Email.objects.create(
                                email_mssante=e,
                                contact=contact,
                                editable=False,
                                email_mssante_boolean=True,
                            )
                    # create new emails if no email for practitioner !
                    if e not in [email.email for email in emails_contact]:
                        Email.objects.create(
                            email_mssante=e,
                            contact=contact,
                            editable=False,
                            email_mssante_boolean=True,
                        )

            adresses_contact = contact.adresse_set.all()
            # Check if addresses exists and is not None before iterating
            if practicioner.addresses is not None:
                for a in practicioner.addresses:
                    # add adresse => loop through existing adresses !
                    for adresse_contact in adresses_contact:
                        if adresse_contact.adresse_mssante in practicioner.addresses:
                            pass
                        else:
                            Adresse.objects.create(
                                adresse_mssante=a, contact=contact, editable=False
                            )
                    # create new adresses if no adresse for practitioner !
                    if a not in [
                        adresse.adresse_mssante for adresse in adresses_contact
                    ]:
                        Adresse.objects.create(
                            adresse_mssante=a, contact=contact, editable=False
                        )

            # contact.save()
            logger.warning(
                f"Doctor updated: {contact.nom} {contact.prenom} {contact.rpps}"
            )
        else:
            logger.warning(
                f"Doctor not found: {contact.nom} {contact.prenom} {contact.rpps}"
            )


def search_duplicate(request):
    contacts = Contact.objects.filter(
        en_activite=True,
        zkf_environnement_contact=request.user.profile.zkf_environnement_user.id,
    )
    dups = (
        contacts.values("nom", "prenom")
        .annotate(count=Count("id"))
        .values("nom", "prenom")
        .order_by()
        .filter(count__gt=1)
    )
    # logger.warning(f"Found {dups.count()} duplicates")
    for dup in dups:
        # logger.warning(f"Duplicate found: {dup['nom']} {dup['prenom']}")
        contacts = contacts.filter(nom=dup["nom"], prenom=dup["prenom"])
        for contact in contacts:
            # get number of linked patients
            patients = contact.patient_set.all()
            # logger.warning(f"Linked patients:{contact} {patients.count()}")
            if patients.count() <= 1:
                contact.delete()
