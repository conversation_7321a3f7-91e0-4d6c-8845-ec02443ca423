from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.contrib.postgres.fields import Array<PERSON>ield

from audit.models import Auditable  # to integrate modifications data

from codage.ccam.models import R_acte
from comptabilite.models import Facture

import uuid
import datetime


# tags
from sort.tag.models import TagEveryWhere
from sort.tag.managers import TaggableManager

# from taggit.models import GenericUUIDTaggedItemBase, TaggedItemBase, TagBase, GenericTaggedItemBase

from babylone.django_safedelete_master.safedelete.models import (
    SafeDeleteModel,
    SOFT_DELETE_CASCADE,
)

from codage.ccam.ccam_scripts.acte_functions import calculatePrixCcam, get_modificateurs


class Protocole(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement_protocole = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    protocole_users = models.ManyToManyField(User)
    libelle = models.CharField(max_length=256, blank=False, null=False)
    description = models.CharField(max_length=256, blank=False, null=False)
    frequence_rappel = models.CharField(
        max_length=256,
        blank=True,
        null=True,
        help_text="en mois, séparés par un point virgule",
    )
    temporalite_rappel = models.CharField(
        max_length=256, blank=False, null=False, help_text="jour, semaine ou mois"
    )
    default_fields = models.TextField(blank=True, null=True)
    protocole_summary = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.libelle


class Operation(Auditable, SafeDeleteModel):
    class Meta:
        ordering = ["operation_date"]

    _safedelete_policy = SOFT_DELETE_CASCADE

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # link operation with cro type
    zkf_cro_type = models.ForeignKey(
        "operation.CroType", on_delete=models.PROTECT, blank=True, null=True
    )
    cro_types = models.ManyToManyField(
        "operation.CroType",
        related_name="%(app_label)s_%(class)s_cro_types",
        blank=True,
    )
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement",
        on_delete=models.PROTECT,
    )
    zkf_site = models.ForeignKey(
        "accounts.Site", on_delete=models.PROTECT, blank=True, null=True
    )

    zkf_patient = models.ForeignKey("patient.Patient", on_delete=models.CASCADE)

    # zkf_hospitalisation is normally mandatory. But to be able to add an operation without hospitalisation, it is set to null
    zkf_hospitalisation = models.ForeignKey(
        "hospitalisation.Hospitalisation",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    # Link consultation where the operation has been created !
    zkf_consultation_pre_op = models.ForeignKey(
        "consultation.Consultation",
        on_delete=models.CASCADE,
        related_name="consultation_pre_operatoire",
        null=True,
        blank=True,
    )
    # tags
    tags = TaggableManager(through=TagEveryWhere, blank=True)

    # protocoles
    protocoles = models.ManyToManyField(
        Protocole,
        through="ProtocoleNote",
        blank=True,
    )

    operation_date = models.DateField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )

    # old fields: to be deleted !
    operateurs_old = models.ManyToManyField(
        User,
        # limit_choices_to={"profile__role": "MED"},
        blank=True,  # operateurs: uniquement medecins#
    )
    aide_operatoire_old = models.ManyToManyField(
        User,
        # limit_choices_to={"profile__user__groups__name": "infirmier"},  # operateurs: uniquement infirmier#
        related_name="aide_operatoire",
        blank=True,
    )
    anesthesiste_old = models.ForeignKey(
        User,
        related_name="anesthesiste",
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )

    # New field: intervenants
    operateur_new = models.ManyToManyField(
        "accounts.Intervenant", related_name="chirurgien_operation", blank=True
    )
    anesthesiste_new = models.ManyToManyField(
        "accounts.Intervenant", related_name="anesthesiste_operation", blank=True
    )
    aides_operatoire = models.ManyToManyField(
        "accounts.Intervenant", related_name="aides_operatoire", blank=True
    )

    operation_start = models.DateTimeField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )
    operation_end = models.DateTimeField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )
    a_programmer = models.BooleanField(
        default=False
    )  # if checked, start and end are set to null
    annulation_operation = models.BooleanField(default=False)  # operation annulee ?
    commentaire_programmation_operation = models.TextField(null=False, blank=True)
    consentement_pre_operatoire_recupere = models.BooleanField(
        null=False, default=False
    )

    URGENT = "urgence"
    PROGRAMMEE = "programme"

    URGENCE = (
        (URGENT, "Urgence"),
        (PROGRAMMEE, "Programmé"),
    )
    urgence = models.CharField(
        max_length=50, blank=True, choices=URGENCE, default=PROGRAMMEE
    )

    commentaire_operation = models.TextField(
        null=False, blank=True
    )  # general commentary about operation

    titre_operation = models.CharField(max_length=256)
    indication_operatoire = models.TextField(blank=True)

    GENERALE = "generale"
    LOCO_REGIONALE = "loco-regionale"
    LOCALE = "locale"

    ANESTH = (
        (GENERALE, "Generale"),
        (LOCO_REGIONALE, "ALR"),
        (LOCALE, "Locale"),
    )
    anesthesie = models.CharField(
        max_length=50,
        blank=True,  # choices=ANESTH, default=GENERALE
    )
    cro = models.TextField(blank=True)
    zkf_redacteur = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name="cro_redacteur",
    )

    # Irradiation device used
    materiel_irradiation = models.ForeignKey(
        "operation.MaterielIrradiation", on_delete=models.PROTECT, blank=True, null=True
    )

    complication = models.BooleanField(null=True)

    def __str__(self):
        if self.operation_date:
            return (
                self.titre_operation
                + " "
                + str(self.operation_date.strftime("%d/%m/%Y"))
            )
        else:
            return self.titre_operation + "pas de date d'operation"

    def template_cro(self):
        template = str("operation/pdf/cro.html")
        return template

    def template(self, type_document=None):
        if type_document == "cro":
            template = str("operation/pdf/cro.html")
        elif type_document == "convocation_operation":
            template = str("operation/pdf/convocation_operation.html")
        return template

    def convocation_operation_template(self):
        template = str("operation/pdf/convocation_operation.html")
        return template

    # duration between consultation pre op and surgery:
    def consultation_surgery_duration(self):
        if self.operation_date and self.zkf_consultation_pre_op:
            if self.zkf_consultation_pre_op.consultation_date:
                duree = round(
                    (
                        (
                            self.zkf_consultation_pre_op.consultation_date
                            - self.operation_date
                        ).days
                        / 30
                    ),
                    2,
                )
                return "Temps depuis la consultation de programmation: {} jours".format(
                    duree
                )
        else:
            return ""

    @property
    def duree_intervention(self):
        duree_intervention = None
        if (self.operation_start) and (self.operation_end):
            duree_intervention = self.operation_end - self.operation_start
            duree_intervention = duree_intervention.total_seconds() / 60
            # duree_intervention = duree_intervention.strftime("%H heures, %M minutes")
            # s = duree_intervention.total_seconds()
            # duree_intervention = '{:02}:{:02}'.format(s // 3600, s % 3600 // 60,)

        return duree_intervention

    @property  # months
    def duree_suivi(self):
        if self.operation_date and self.zkf_patient.date_etat:
            return (self.zkf_patient.date_etat - self.operation_date).days / 30
        else:
            return None

    def link(self):
        return reverse(
            "operation:detail-operation",
            args=[self.id],
        )

    @property
    def paclitaxel_quantite_operation(self):
        all_stents = self.operationstent_set.all()
        if all_stents:
            dose_paclitaxel = 0
            for s in all_stents:
                dose_paclitaxel += s.stent.paclitaxel_dose
        else:
            dose_paclitaxel = "absence de paclitaxel"
        return dose_paclitaxel

    @property
    def longueur_stent(self):
        all_stents = self.operationstent_set.all()
        if all_stents:
            longueur_totale = 0
            for s in all_stents:
                longueur_totale += s.stent.longueur
        else:
            longueur_totale = "pas de stent"

        return longueur_totale


class ProtocoleNote(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    protocole = models.ForeignKey(Protocole, on_delete=models.CASCADE)
    operation = models.ForeignKey(Operation, on_delete=models.CASCADE)
    note = models.TextField(blank=True, null=True)


class CroType(Auditable):
    class Meta:
        ordering = ["cro_type_titre"]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    zkf_user = models.ForeignKey(User, on_delete=models.PROTECT)
    cro_type_titre_descriptif = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text="Titre court pour selection dans la liste",
    )
    cro_type_titre = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text="Titre du CRO type à inserer dans une operation",
    )
    cro_type_indication_operatoire = models.TextField(
        blank=True,
        null=True,
        help_text="Indication opératoire du CRO type à inserer dans une operation",
    )
    cro_type = models.TextField(blank=True, null=True)
    nom_modele_specialite = models.CharField(max_length=256, blank=True, null=True)

    def __str__(self):
        if self.cro_type_titre is not None:
            return self.cro_type_titre
        elif self.cro_type_titre_descriptif is not None:
            return self.cro_type_titre_descriptif
        else:
            return "absence de titre"


class Devis(Auditable, SafeDeleteModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_operation = models.ForeignKey(
        Operation, on_delete=models.CASCADE, blank=True, null=True
    )
    zkf_patient = models.ForeignKey("patient.Patient", on_delete=models.CASCADE)
    zkf_createur = models.ForeignKey(
        User, on_delete=models.PROTECT, blank=True, null=True
    )
    zkf_devisfavoris = models.ForeignKey(
        "comptabilite.DevisFavoris", null=True, blank=True, on_delete=models.CASCADE
    )

    date_devis = models.DateField(blank=True, auto_now_add=False, auto_now=False)
    depassement_honoraire = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2, default=0
    )

    def __str__(self):
        if self.date_devis:
            date = "{:%d/%m/%Y}".format(self.date_devis)
        else:
            date = "pas de date"
        return str(date)

    # template to generate pdf
    def template_devis(self):
        template = [
            str("operation/pdf/devis.html"),
            str("operation/pdf/devis_mutuelle.html"),
        ]
        return template

    def template(self, type_document=None):
        if type_document == "devis":
            template = [
                str("operation/pdf/devis.html"),
                str("operation/pdf/devis_mutuelle.html"),
            ]
        else:
            template = None
        return template


# actes with all associated details: same for devis or operation final codes
class OperationDevisCcam(Auditable, SafeDeleteModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_devis = models.ForeignKey(
        "operation.Devis", on_delete=models.CASCADE, blank=True, null=True
    )
    zkf_operation = models.ForeignKey(
        Operation, on_delete=models.CASCADE, blank=True, null=True
    )
    zkf_facture = models.ForeignKey(
        Facture, on_delete=models.CASCADE, blank=True, null=True
    )
    zkf_devis_favoris = models.ForeignKey(
        "comptabilite.DevisFavoris", null=True, blank=True, on_delete=models.CASCADE
    )
    # linked acte (like YYYY)
    zkf_acte = models.ForeignKey(
        "operation.OperationDevisCcam", on_delete=models.CASCADE, blank=True, null=True
    )
    acte_ccam = models.CharField(max_length=15)
    acte_nom_long = models.TextField(blank=True, null=True)
    acte_principal_secondaire_supplementaire = models.IntegerField(
        blank=True, null=True
    )
    acte_activite = models.PositiveSmallIntegerField(blank=True, null=True)
    acte_phase = models.PositiveSmallIntegerField(blank=True, null=True)
    acte_grille = models.PositiveSmallIntegerField(blank=True, null=True)
    acte_modificateur_urgence = models.CharField(blank=True, null=True, max_length=2)
    acte_modificateur_chir = models.CharField(blank=True, null=True, max_length=12)
    acte_modificateur = models.CharField(blank=True, null=True, max_length=12)
    acte_association = models.PositiveSmallIntegerField(blank=True, null=True)
    acte_supplementaire = models.PositiveSmallIntegerField(
        blank=True, null=True, default=0
    )
    prix_base = models.DecimalField(
        max_digits=7, decimal_places=2, null=True, blank=True
    )
    prix_modificateurs = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )
    depassement_honoraire_acte = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2, default=0
    )

    def prix_total_acte(self):
        return self.prix_modificateurs + self.depassement_honoraire_acte

    def depassement_honoraire(self):
        if self.zkf_devis:
            if self.zkf_devis.depassement_honoraire:
                devis_codes_ccam = OperationDevisCcam.objects.filter(
                    zkf_devis=self.zkf_devis.id,
                )
                prix_total_actes = calculatePrixCcam().somme_acte(devis_codes_ccam)
                pourcentage = self.zkf_devis.depassement_honoraire / prix_total_actes
                return round(self.prix_modificateurs * pourcentage, 2)
        else:
            return "0"

    def depassement_honoraire_pourcentage(self):
        if self.zkf_devis:
            # if self.zkf_devis.depassement_honoraire:
            #     devis_codes_ccam = OperationDevisCcam.objects.filter(zkf_devis=self.zkf_devis.id, )
            #     prix_total_actes = calculatePrixCcam().somme_acte(devis_codes_ccam)
            #     pourcentage = 100*(self.depassement_honoraire_acte / prix_total_actes)
            #     return pourcentage
            return 100 * (self.depassement_honoraire_acte / (self.prix_modificateurs))

        if self.zkf_facture:
            # print (self.zkf_facture.montant)
            # if self.zkf_facture.montant:
            # facture_codes_ccam = OperationDevisCcam.objects.filter(zkf_facture = self.zkf_facture.id, )
            # prix_total_actes = calculatePrixCcam().somme_acte(facture_codes_ccam)
            # pourcentage = 100*(self.depassement_honoraire_acte/prix_total_actes)
            # return pourcentage
            return 100 * (self.depassement_honoraire_acte / self.prix_modificateurs)
        else:
            return "0"

    def get_modificateurs(self):
        acte = R_acte.objects.get(cod_acte=self.acte_ccam)
        modificateurs = get_modificateurs(acte, self.acte_activite, self.acte_grille)
        # print (modificateurs)
        return modificateurs  # dict with allowed modificateurs for the acte !

    def somme_acte_depassement(self):
        if self.depassement_honoraire_acte:
            return self.prix_modificateurs + self.depassement_honoraire_acte
        else:
            return self.prix_modificateurs

    def __str__(self):
        return self.acte_ccam


# total de l'operation: actes CCAM + dépassements d'honoraires
class ComptaOperation(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_operation = models.OneToOneField(Operation, on_delete=models.PROTECT)
    zkf_devis = models.ForeignKey(
        "operation.Devis", on_delete=models.PROTECT, blank=True, null=True
    )
    total_ccam = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )
    depassement_honoraire = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2, default=0
    )
    total_operation = models.DecimalField(
        blank=True, null=True, max_digits=7, decimal_places=2
    )


Gycm2 = "Gycm2"
mGycm2 = "mGycm2"

PDS = (
    (Gycm2, "Gycm2"),
    (mGycm2, "mGycm2"),
)


class MaterielIrradiation(
    Auditable
):  # list all irradiations materiels available (scopie, salle hybrid...)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement = models.ForeignKey(
        "accounts.Environnement",
        on_delete=models.PROTECT,
        default="c0363b53-af07-4705-9c07-88699feaa250",
    )
    zkf_site = models.ManyToManyField("accounts.Site", blank=True)
    zkf_specialite = models.ManyToManyField("accounts.Specialite", blank=True)
    nom_appareil_radio = models.CharField(max_length=256)
    marque_appareil_radio = models.CharField(max_length=256)
    date_premiere_utilisation = models.DateField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )
    en_utilisation = models.BooleanField()
    pds_unite = models.CharField(max_length=30, blank=True, null=True, choices=PDS)
    air_kerma_unite = models.CharField(max_length=30, blank=True, null=True)

    def __str__(self):
        nom = self.nom_appareil_radio + " " + self.marque_appareil_radio
        return nom


class Contraste(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    service = models.ManyToManyField("accounts.Service")
    denomination = models.CharField(max_length=60)
    composition = models.CharField(max_length=254, blank=True, null=True)
    teneur_iode = models.CharField(max_length=254, blank=True, null=True)

    def __str__(self):
        return self.denomination


class Irradiation(Auditable, SafeDeleteModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_operation = models.OneToOneField(
        Operation, on_delete=models.CASCADE, null=True, blank=True
    )
    pds_total = models.FloatField(blank=True, null=True)
    pds_unite = models.CharField(max_length=30, blank=True, null=True)
    air_kerma_total = models.FloatField(blank=True, null=True)
    air_kerma_unite = models.CharField(max_length=30, blank=True, null=True)
    duree_scopie_total = models.TimeField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )
    produit_contraste = models.ForeignKey(
        Contraste, on_delete=models.CASCADE, blank=True, null=True
    )
    volume_iode = models.FloatField(blank=True, null=True)
    # detail scopie
    pds_scopie = models.FloatField(blank=True, null=True)
    air_kerma_scopie = models.FloatField(blank=True, null=True)
    duree_scopie_scopie = models.TimeField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )

    pds_acquisition = models.FloatField(blank=True, null=True)
    air_kerma_acquisition = models.FloatField(blank=True, null=True)
    duree_scopie_acquisition = models.TimeField(
        auto_now=False, auto_now_add=False, blank=True, null=True
    )

    @property
    def duree_scopie_min(self):
        duree_scopie = None
        x = self.duree_scopie_total
        if x:
            x = datetime.timedelta(hours=x.hour, minutes=x.minute, seconds=x.second)
            duree_scopie = x.total_seconds() / 60

        return duree_scopie

    @property
    def return_gycm2(self):
        if self.pds_unite == "Gycm2":
            return self.pds
        elif self.pds_unite == "mGycm2":
            return self.pds / 1000
