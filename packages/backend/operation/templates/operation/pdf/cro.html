
{% extends "impression/general.html" %}
{% load static %}

{% block prescripteur %}
	<small>	
		{{operation.zkf_redacteur.profile.entete_ordonnance|safe}}<br>
		RPPS: {{operation.zkf_redacteur.profile.rpps}}
	</small>
{% endblock prescripteur %}

{% block adresse %}
		<br>
			<div style = "text-align: center">
				<small>
					{{operation.zkf_hospitalisation.service_hospitalisation.site_service.nom_site}}<br>
					{{operation.zkf_hospitalisation.service_hospitalisation}}<br>
					{{operation.zkf_hospitalisation.service_hospitalisation.site_service.adresse_site}}<br>
					{{operation.zkf_hospitalisation.service_hospitalisation.site_service.zip_code_site}} 
					{{operation.zkf_hospitalisation.service_hospitalisation.site_service.ville_site}}<br>
					{{operation.zkf_hospitalisation.service_hospitalisation.site_service.telephone_site}}
				</small>
			</div>
{% endblock adresse %}


{% block title %}
Compte rendu opératoire
{% endblock title%}


{% block identification%}
{{operation.zkf_patient.full_name}}
{% endblock identification %}

{% block content %}

<style>
.right {
  position: absolute;
  right: 0px;
  width: 300px;
  border: 3px solid #73AD21;
  padding: 10px;
}

td {
	vertical-align: top;
}
	
th, td {
  text-align: left;
}

</style>

<!-- Identification du patient -->
<p>{{ patient.fullname }}</p>
<br>

<table>
	
	<tr>
		<td>
			
			{% if operation.operateur_new %}

					{% if operation.operateur_new.count > 1 %}
						<h6>Operateurs</h6>
					{% else %}
						<h6>Operateur</h6>
					{% endif %}

					<p>
						{% for o in operation.operateur_new.all %}
							{{o.nom_politesse}}
						{% endfor %}
					</p>

			{% endif %}
		</td>
	
		<td>			
			{% if operation.anesthesiste_new %}
				<h6>Anesthésiste</h6>
				<p>
					{% for a in operation.anesthesiste_new.all %}
						{{a.nom_politesse}}
					{% endfor %}
				</p>
				<br>
			{% endif %}
		</td>

		<td>

			{% if operation.aides_operatoire.count > 0 %}

				{% if operation.aides_operatoire.count > 1 %}
					<h6>Aides operatoires</h6>
				{% else %}
					<h6>Aide operatoire</h6>
				{% endif %}
				
				{% for a in operation.aides_operatoire.all %}
					{{a.nom_politesse}}<br>
				{% endfor %}

			{% endif %}

		</td>

</tr>
	
</table> 

<br>

<!-- date operation --> 
<p>Date de l'opération: {{ operation.operation_date|date:"d/m/Y" }} </p>
<br>
<!-- indication opératoire -->
<u>Indication opératoire</u>
<p>{{ operation.indication_operatoire|safe }}</p>

<!--Titre operation-->
<div style = "border: 2px solid; border-radius: 5px; text-align: center;  padding-top: 15px;
padding-right: 5px;
padding-bottom: 10px;
padding-left: 5px;">
	<p >{{ operation.titre_operation|safe }}</p>
</div>
<br>
<!-- CRO -->

<p>{{ operation.cro|safe }}</p>
<br>

<table>

	<tr>
		<!-- scopie -->	
		{% if operation.materiel_irradiation %}
			<td> 		
				<h5>Scopie</h5>
						<small>
							Procédure réalisée sous amplificateur de brillance: {{ operation.materiel_irradiation.marque_appareil_radio }} {{ operation.materiel_irradiation.nom_appareil_radio }}<br>
							Date de première utilisation: {{ operation.materiel_irradiation.date_premiere_utilisation|date:"d/m/Y" }}<br>
						<br>
						<p>
							{% if operation.irradiation.pds_total %}
								PDS: {{operation.irradiation.pds_total}} {{operation.irradiation.pds_unite}} <br>
							{% endif %}
							{% if operation.irradiation.air_kerma_total %}
								AK: {{operation.irradiation.air_kerma_total}} {{operation.irradiation.air_kerma_unite}}<br>
							{% endif %}
							{% if operation.irradiation.duree_scopie_total %}
								Durée de scopie: {{operation.irradiation.duree_scopie_total|time:"H:i:s"}}<br>
							{% endif %}
							{% if operation.irradiation.produit_contraste %}
								Produit de contraste utilisé: {{operation.irradiation.produit_contraste}}<br>
							{% endif %}
							{% if operation.irradiation.volume_iode %}
								Quantité de produit de contraste: {{operation.irradiation.volume_iode}}cc
							{% endif %}	
						</p>
					</small>
			</td>
		{% endif %}
		<td> 
			<!-- Materiel -->
			{% if materiel %}
				<h5>Matériel implanté:</h5>
				{% for s in materiel %}
					{% if s.medical_device.affichage_cro is True %}
						<small>{{s.medical_device.descriptif_ref}}</small><br>
					{% endif %}
				{% endfor %}
			{% endif %}

			<!-- Duree de l'intervention -->
			{% if operation.duree_intervention %}
				<small>Durée de l'intervention: {{operation.duree_intervention}} minutes</small><br>
			{% endif %}
		</td>
	</tr>

</table>



<div style = "text-align:right;">
	Dr.{{operation.zkf_redacteur.last_name|title}}
</div>

{% endblock content %}
