from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from celery.schedules import crontab

# set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "babylone.settings")

app = Celery("babylone")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django app configs.
app.autodiscover_tasks(["document"])  # ou ['document', 'autre_app']
# app.autodiscover_tasks()

# Define the beat schedule
app.conf.beat_schedule = {
    "cleanup-inactive-audio-files-daily": {
        "task": "document.tasks.cleanup_inactive_audio_files_task",
        "schedule": crontab(hour=0, minute=0),  # Run daily at midnight
        # crontab(minute='*'),
    },
    "check-audio-for-validated-consultations-daily": {
        "task": "document.tasks.check_audio_for_validated_consultations_task",
        "schedule": crontab(hour=1, minute=0),  # Run daily at 1 AM
        # crontab(minute='*/2'),
    },
}
app.conf.timezone = "UTC"  # It's good practice to set a timezone for beat


@app.task(bind=True)
def debug_task(self):
    print("Request: {0!r}".format(self.request))
