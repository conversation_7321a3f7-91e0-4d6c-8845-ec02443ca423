# response = HttpResponse(content_type='application/pdf')
# response['Content-Transfer-Encoding'] = 'binary'

# # generate a string (function is imported from impression.views)
# html_string = create_html_string(context = context, path = response, template = Hospitalisation.template_crh())

# result = BytesIO()
# pdf = pisa.pisaDocument(BytesIO(html_string.encode("utf-8")), dest=result)

# if not pdf.err:
#     return HttpResponse(result.getvalue(), content_type='application/pdf')
# else:
#     return HttpResponse('Errors')
