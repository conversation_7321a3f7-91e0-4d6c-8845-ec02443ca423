from django.contrib.staticfiles.urls import staticfiles_urlpatterns

"""babylone URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path
from django.conf.urls import include
from django.urls import re_path as url

from django.conf import settings
from django.conf.urls.static import static

from .views import CustomAuthToken, HomePage, DaySummaryHomePage


app_name = "core"  # define an app name for redirections in templates

urlpatterns = [
    # """All url apis are defined here""",
    # agenda
    url("api/agenda/", include("api.schedule.urls")),
    url("api/antecedent/", include("api.antecedent.urls")),
    # auditlog
    url("api/auditlog/", include("api.auditlog.urls")),
    # Codage
    url("api/ccam/", include("api.codage.ccam.urls")),
    # Compta
    url("api/comptabilite/", include("api.comptabilite.urls")),
    # comptes rendus
    url("api/compte-rendu/", include("api.document.compte_rendu.urls")),
    # Contacts/Correspondants/email/telephone/adresse patient
    url("api/contact/", include("api.contact.urls")),
    url("api/consultation/", include("api.consultation.urls")),
    url("api/hospitalisation/", include("api.hospitalisation.urls")),
    url("api/document-libre/", include("api.document.libre.urls")),
    url("api/intervenant/", include("api.intervenant.urls")),
    # get operation templates by specialite
    url("api/operation-templates/", include("api.operation.specialite.urls")),
    # medias: serve static files
    url("api/media/", include("api.media.urls")),
    # gestionnaire d'impression
    url("api/print-manager/", include("api.impression.urls")),
    url("api/statistics/", include("api.dashboard.statistics.urls")),
    # Protocoles
    url("api/protocole/", include("api.protocole.urls")),
    # notifications
    url("api/notification/", include("api.notification.urls")),
    # ordonnances
    url("api/ordonnance/", include("api.document.ordonnance.urls")),
    url("api/operation/", include("api.operation.urls")),
    # gestionnaire d'impression
    url("api/print-manager/", include("api.impression.urls")),
    # Protocoles
    url("api/protocole/", include("api.protocole.urls")),
    url("api/patient/", include("api.patient.urls")),
    # settings general
    url("api/settings/", include("api.settings.urls")),
    # tags
    url("api/tag/", include("api.sort.tag.urls")),
    # path("api-token-auth/", views.obtain_auth_token),
    path("api/token-auth/", CustomAuthToken.as_view()),
    url("api/user/", include("api.accounts.urls")),
    # """old urls""",
    path("day-summary", DaySummaryHomePage.as_view(), name="day-summary"),
    url("admin/", admin.site.urls),
    # url for accounts
    url("accounts/", include("accounts.urls")),
    path("", HomePage.as_view(), name="home"),
    url("patient/", include("patient.urls")),
    url("operation/", include("operation.urls.operation")),
    # staff
    url("staff/", include("staff.urls")),
    # devis
    url("devis/", include("operation.urls.devis")),
    # materiel
    url("materiel/", include("materiel.urls")),
    url("hospitalisation/", include("hospitalisation.urls")),
    url("consultation/", include("consultation.urls")),
    # ATCD
    url(
        "antecedents-chirurgie-vasculaire/",
        include("specialites.chirurgie_vasculaire.urls"),
    ),
    url("angiologie/", include("specialites.angiologie.urls")),
    url("infectiologie/", include("specialites.infectiologie.urls")),
    url("anesthesie/", include("specialites.anesthesie.urls")),
    url("schedule/", include("schedule.urls")),
    # url('ordonnance/', include('ordonnance.urls',)),
    url("ccam/", include("codage.ccam.urls")),
    # url('cim/', include('cim.urls',)),
    # #documents
    url("ordonnance/", include("document.urls.ordonnance")),
    url("photo/", include("document.urls.photo")),
    url("document-libre/", include("document.urls.libre")),
    url("consultation/document/", include("document.urls.consultation")),
    url("impression/", include("impression.urls")),
    url("examen-complementaire/", include("document.urls.examen_complementaire")),
    url(
        "compte-rendu-correspondant/",
        include("document.urls.compte_rendu_correspondant"),
    ),
    url("audio-recording/", include("document.urls.audio_recording")),
    # Dashboard
    url("dashboard/", include("accounts.dashboard.urls")),
    url("admindashboard/", include("accounts.admindashboard.urls")),
    # Contacts
    url("correspondant/", include("contact.urls.correspondant")),
    url("ville/", include("contact.urls.villes")),
    # #Comptabilite
    url("comptabilite/", include("comptabilite.urls")),
    # #Assurance Santé
    url("assurance-sante/", include("assurance_sante.urls")),
    # Protocoles
    url("protocole/", include("protocole.urls")),
    # notifications
    # desactivé par Martin
    # url(r'^notifications/', include('notify.urls', 'notifications')),
    # url(r'^notifications-views/', include('notifications.urls')),
    # rappels
    url(r"^rappel/", include("rappel.urls", "rappel")),
]
# urlpatterns += [path("silk/", include("silk.urls", namespace="silk"))]
# Serving the media files in development mode
# if settings.DEBUG:
#     urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
# else:
#     # urlpatterns += staticfiles_urlpatterns()+ static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
#     urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
