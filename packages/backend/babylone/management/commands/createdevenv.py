from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.db import connection
from django.core.management import call_command
from accounts.models import (
    Environnement,
    Site,
    Profile,
    Specialite,
    Service,
    Lieu,
    Secretariat,
)
from patient.models import <PERSON><PERSON>, Dossier
from datetime import date


class Command(BaseCommand):
    help = "Wipes the database, creates a superuser (admin/admin) and a regular user (mrouer/mrouer), then runs migrations and populates with dev data."

    def handle(self, *args, **options):
        User = get_user_model()

        self.stdout.write(
            self.style.WARNING(
                "This command will WIPE your current database and set up a new development environment."
            )
        )
        confirmation = input("Are you sure you want to continue? (yes/no): ")

        if confirmation.lower() != "yes":
            self.stdout.write(self.style.ERROR("Operation cancelled by the user."))
            return

        # Wipe the database
        self.stdout.write(self.style.WARNING("Wiping the database..."))
        try:
            with connection.cursor() as cursor:
                cursor.execute("DROP SCHEMA public CASCADE;")
                cursor.execute("CREATE SCHEMA public;")
            self.stdout.write(self.style.SUCCESS("Database wiped successfully."))
        except Exception as e:
            raise CommandError(f"Error wiping database: {e}")

        # Run migrations
        self.stdout.write(self.style.WARNING("Running migrations..."))
        try:
            call_command("migrate", interactive=False)
            self.stdout.write(self.style.SUCCESS("Migrations applied successfully."))
        except Exception as e:
            raise CommandError(f"Error running migrations: {e}")

        # Create superuser
        self.stdout.write(self.style.WARNING("Creating superuser admin..."))
        User.objects.create_superuser("admin", "<EMAIL>", "admin")
        self.stdout.write(self.style.SUCCESS("Superuser admin created."))

        # Create regular user
        self.stdout.write(self.style.WARNING("Creating user mrouer..."))
        mrouer_user = User.objects.create_user("mrouer", "<EMAIL>", "mrouer")
        mrouer_user.is_staff = True
        mrouer_user.first_name = "Martin"
        mrouer_user.last_name = "Rouer"
        mrouer_user.is_superuser = True
        mrouer_user.save()
        self.stdout.write(
            self.style.SUCCESS("User mrouer created and set as staff and superuser.")
        )

        # Create Environnement
        self.stdout.write(self.style.WARNING("Creating development environnement..."))
        dev_environnement = Environnement.objects.create(
            nom_environnement="Dev Environnement",
            ins_struct_number="10B0166708",
            ins_struct_type="FINESS",
        )
        self.stdout.write(self.style.SUCCESS("Development environnement created."))

        # Create Site
        self.stdout.write(self.style.WARNING("Creating development site..."))
        dev_site = Site.objects.create(
            nom_site="Dev Site",
            zkf_environnement_site=dev_environnement,
            adresse_site="123 Dev Street",
            zip_code_site=75001,
            ville_site="Deville",
        )
        self.stdout.write(self.style.SUCCESS("Development site created."))

        # Create Specialite
        self.stdout.write(self.style.WARNING("Creating default specialite..."))
        dev_specialite = Specialite.objects.create(
            spe="Chirurgie vasculaire",
            type_specialite="MEDICAL",
            spe_medecin="Chirurgien Vasculaire",
            data_app="chirurgie_vasculaire",
        )
        self.stdout.write(self.style.SUCCESS("Default specialite created."))

        # Create Service
        self.stdout.write(self.style.WARNING("Creating default service..."))
        dev_service = Service.objects.create(
            nom_service="General Medical Service",
            zkf_environnement=dev_environnement,
            site_service=dev_site,
        )
        dev_service.specialite_service.add(dev_specialite)
        self.stdout.write(
            self.style.SUCCESS("Default service created and linked to specialite.")
        )

        # Create Lieu
        self.stdout.write(self.style.WARNING("Creating default lieu..."))
        Lieu.objects.create(
            nom_lieu="Consultation Room 1",
            zkf_environnement=dev_environnement,
            zkf_site=dev_site,
        )
        self.stdout.write(self.style.SUCCESS("Default lieu created."))

        # Create Secretariat
        self.stdout.write(self.style.WARNING("Creating default secretariat..."))
        dev_secretariat = Secretariat.objects.create(
            nom_secretariat="Main Secretariat",
            zkf_environnement=dev_environnement,
        )
        self.stdout.write(self.style.SUCCESS("Default secretariat created."))

        if mrouer_user:
            self.stdout.write(
                self.style.WARNING(
                    f"Linking entities to {mrouer_user.username}'s profile..."
                )
            )
            try:
                mrouer_profile, _ = Profile.objects.get_or_create(user=mrouer_user)
                mrouer_profile.zkf_environnement_user = dev_environnement
                mrouer_profile.site.add(dev_site)
                mrouer_profile.specialite_profile.add(dev_specialite)
                mrouer_profile.specialite_active = dev_specialite
                mrouer_profile.service.add(dev_service)
                mrouer_profile.secretariat.add(dev_secretariat)
                mrouer_profile.save()
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully linked entities to {mrouer_user.username}'s profile."
                    )
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Could not link entities to {mrouer_user.username}'s profile: {e}"
                    )
                )

        # Create Patients and link them
        self.stdout.write(self.style.WARNING("Preparing patient data..."))
        initial_patients_data = [
            {
                "nom": "Dupont",
                "prenom": "Jean",
                "dob": date(1980, 1, 15),
                "sexe": "MALE",
                "nom_naissance": "Dupont",  # numero_dossier_suffix will be assigned below
            },
            {
                "nom": "Durand",
                "prenom": "Marie",
                "dob": date(1992, 5, 20),
                "sexe": "FEMALE",
                "nom_naissance": "Durand",
            },
            {
                "nom": "Martin",
                "prenom": "Lucas",
                "dob": date(1975, 11, 3),
                "sexe": "MALE",
                "nom_naissance": "Martin",
            },
            {
                "nom": "Bernard",
                "prenom": "Chloé",
                "dob": date(2001, 7, 28),
                "sexe": "FEMALE",
                "nom_naissance": "Bernard",
            },
        ]

        postman_derived_patients_hardcoded = [
            {
                "nom": "D'Artagnan De L'Herault",
                "prenom": "Pierre-Alain",
                "dob": date(1990, 4, 14),
                "sexe": "MALE",
                "nom_naissance": "D'Artagnan De L'Herault",
            },
            {
                "nom": "Adrtrois",
                "prenom": "Toussaint",
                "dob": date(1960, 1, 1),
                "sexe": "MALE",
                "nom_naissance": "Adrtrois",
            },
            {
                "nom": "D’æion",
                "prenom": "Cœur",
                "dob": date(1987, 1, 25),
                "sexe": "MALE",
                "nom_naissance": "D’æion",
            },  # Note: Special character in prenom
            {
                "nom": "Ecetinsi",
                "prenom": "Pierre-Alain",
                "dob": date(2009, 7, 14),
                "sexe": "MALE",
                "nom_naissance": "Ecetinsi",
            },
            {
                "nom": "Provisoire",
                "prenom": "Matricule",
                "dob": date(2009, 7, 1),
                "sexe": "MALE",
                "nom_naissance": "Provisoire",
            },
            {
                "nom": "De Vinci",
                "prenom": "Leonardo",
                "dob": date(2014, 2, 1),
                "sexe": "MALE",
                "nom_naissance": "De Vinci",
            },
            {
                "nom": "Tchitchi",
                "prenom": "Ola",
                "dob": date(1936, 6, 21),
                "sexe": "FEMALE",
                "nom_naissance": "Tchitchi",
            },
            {
                "nom": "Houilles",
                "prenom": "Pierre",
                "dob": date(1993, 1, 27),
                "sexe": "MALE",
                "nom_naissance": "Houilles",
            },
            {
                "nom": "Corse",
                "prenom": "Anthony",
                "dob": date(1980, 3, 2),
                "sexe": "MALE",
                "nom_naissance": "Corse",
            },
            {
                "nom": "Herman",
                "prenom": "Gatien",
                "dob": date(1981, 3, 24),
                "sexe": "MALE",
                "nom_naissance": "Herman",
            },
            {
                "nom": "Adrun",
                "prenom": "Zoe",
                "dob": date(1975, 12, 31),
                "sexe": "FEMALE",
                "nom_naissance": "Adrun",
            },
            {
                "nom": "Nessi",
                "prenom": "Ruth",
                "dob": date(1977, 7, 14),
                "sexe": "FEMALE",
                "nom_naissance": "Nessi",
            },
            {
                "nom": "Nessi",
                "prenom": "Michelangelo",
                "dob": date(2010, 8, 7),
                "sexe": "MALE",
                "nom_naissance": "Nessi",
            },
            {
                "nom": "De Vinci",
                "prenom": "Donatello",
                "dob": date(2010, 8, 7),
                "sexe": "MALE",
                "nom_naissance": "De Vinci",
            },
            {
                "nom": "De Vinci",
                "prenom": "Raphael",
                "dob": date(2010, 8, 7),
                "sexe": "MALE",
                "nom_naissance": "De Vinci",
            },
            {
                "nom": "Corse",
                "prenom": "Joseph",
                "dob": date(1999, 1, 1),
                "sexe": "MALE",
                "nom_naissance": "Corse",
            },
            {
                "nom": "Tchitchi",
                "prenom": "Catarina",
                "dob": date(1936, 6, 21),
                "sexe": "FEMALE",
                "nom_naissance": "Tchitchi",
            },
            {
                "nom": "Adrun",
                "prenom": "Baptiste",
                "dob": date(1975, 1, 1),
                "sexe": "MALE",
                "nom_naissance": "Adrun",
            },
            {
                "nom": "Nessi",
                "prenom": "Nathalie",
                "dob": date(1977, 7, 14),
                "sexe": "FEMALE",
                "nom_naissance": "Nessi",
            },
        ]

        all_source_patients = initial_patients_data + postman_derived_patients_hardcoded

        patients_data = []
        existing_patients_identifiers = set()
        current_dossier_suffix_int = 1

        for p_source_data in all_source_patients:
            nom_key = p_source_data["nom"]
            prenom_key = p_source_data["prenom"]
            dob_key = p_source_data["dob"]

            patient_key = (nom_key, prenom_key, dob_key)

            if patient_key not in existing_patients_identifiers:
                existing_patients_identifiers.add(patient_key)

                new_patient_entry = dict(p_source_data)
                new_patient_entry["numero_dossier_suffix"] = str(
                    current_dossier_suffix_int
                ).zfill(3)

                patients_data.append(new_patient_entry)
                current_dossier_suffix_int += 1
            else:
                self.stdout.write(
                    self.style.NOTICE(
                        f"Patient {nom_key} {prenom_key} (DOB: {dob_key}) is a duplicate. Skipping."
                    )
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"Prepared {len(patients_data)} unique patients in total."
            )
        )
        self.stdout.write(
            self.style.WARNING(
                "Creating patients and their dossiers in the database..."
            )
        )
        for p_data in patients_data:
            try:
                patient = Patient.objects.create(
                    nom=p_data["nom"],
                    prenom=p_data["prenom"],
                    dob=p_data["dob"],
                    sexe=p_data["sexe"],
                    nom_naissance=p_data["nom_naissance"],
                    zkf_environnement_patient=dev_environnement,
                )
                patient.praticien_referent.add(mrouer_user)
                patient.save()
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Patient {patient.nom} {patient.prenom} created."
                    )
                )

                Dossier.objects.create(
                    zkf_patient=patient,
                    numero_dossier=f"DOSS-{patient.nom.upper()}-{p_data['numero_dossier_suffix']}",
                    zkf_service=dev_service,
                    zkf_specialite=dev_specialite,
                )
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Dossier created for {patient.nom} {patient.prenom}."
                    )
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error creating patient {p_data.get('nom', 'N/A')} {p_data.get('prenom', 'N/A')} or their dossier: {e}"
                    )
                )

        self.stdout.write(self.style.SUCCESS("Development environment setup complete."))
