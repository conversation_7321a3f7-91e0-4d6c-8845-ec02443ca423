=========
CHANGELOG
=========

0.5.5 (unreleased)
==================

-

0.5.4 (2019-12-02)
==================

- Django 3.0 compatibility

0.5.3 (2019-12-02)
==================

- Fix "Doing a .get on a queryset adds deleted objects #131"

0.5.2 (2019-08-19)
==================

- Fix performance issue with Django 2.2
- Fix executing soft delete on already soft-deleted items during cascade soft delete

0.5.1 (2018-07-02)
==================

- Fix possible unicode error in admin

0.5.0 (2018-05-29)
==================

- Remove support for Django 1.8 to 1.10 and Python 3.3.
     (it should still works for now but isn't tested, use at your own risks).
- Fix update_or_create (#98)

0.4.5 (2018-01-31)
==================

- Fix an issue with Django 1.8 and `values_list`
- Django 2.0 compatibility


0.4.4 (2018-01-09)
==================

** Bugfixes **

- Fix latest and earliest

0.4.3
=====

** Bugfixes **

- Set SafeDeleteMixin as abstract


0.4.2
=====

** Bugfixes **

- iterator() now filter the deleted objects correctly.
- Fix prefetch_related() with all()
- Fix: "Cannot filter a query once a slice has been taken" error.

** Refactoring **

- Resolve Django 1.9+ allow_tags deprecation warning
- Fix docstring typo in SafeDeleteManager, SOFT_DELETE should be DELETED_INVISIBLE


0.4.1
=====

** New **

- Filtering on the deleted field is done on evaluation.
- Added specific managers: all_objects and deleted_objects.
- Added 'force_policy' feature to SafeDeleteQuerySet.

** Removed **

-

** Bugfixes **

- Fixed abstract intermediate models not working with SOFT_DELETE_CASCADE

** Refactoring **

- Renamed SafeDeleteMixin to SafeDeleteModel to better reflect the intended behavior. Using SafeDeleteMixin now throws a DeprecationWarning.
- Moved SafeDeleteQueryset


0.4.0
=====

** New **

- ``deleted`` is now a datetime.

** Refactoring **

- Globals (HARD_DELETE, SOFT_DELETE, ...) have been moved `to config.py`.
- Removed support for Django 1.4 to 1.7. You should use the 0.3.x branch if you need to use safedelete in Django <= 1.7.
- Remove factories to use mixins instead.

0.3.5
=====

** New **

- Change ``DELETED_VISIBLE_BY_PK`` to ``DELETED_VISIBLE_BY_FIELD`` to be able to change the field used.

0.3.4
=====

** New **

- Add a ``SOFT_DELETE_CASCADE`` policy which perform a SOFT_DELETE on safedelete related objects
- Django 1.8 compatibility

0.3.2
=====

- Prevent migration errors on django 1.8 by declaring the SafeDeleteManager (internal class in managers) as global

0.3.1
=====

- Fix issue with release on pypi not being the good one


0.3.0
=====

** New **

- Add a ``NO_DELETE`` policy which never soft or hard deletes an instance
- Django 1.8 compatibility

** Removed **

- Support of Django 1.2 and Django 1.3 has been removed

** Bugfixes **

- ``all_with_deleted`` method doesn't lose current queryset on a related manager
- uniqueness is now checked against soft deleted instances too
- prefetch_related() now works with SafedeleteQuerySet
- Fix the undelete action in the administration for active objects.


0.2.1 (2014-12-15)
==================

** New **

- Extends Django compatibility to Django 1.2


0.2.0 (2014-12-09)
==================

** New **

- Django compatilibty 1.3 => 1.7
- Add administration utilities
