[tox]
skipsdist = True
envlist =
    {py27}-django-{111}
    {py34}-django-{111}
    {py35}-django-{111,20,21,22}
    {py36}-django-{111,20,21,22}
    {py37}-django-{21,22,30}
    {py38}-django-{30}
    {py27,py35}-flake8

[testenv]
passenv = TRAVIS TRAVIS_JOB_ID TRAVIS_BRANCH
setenv =
    PYTHONPATH = {toxinidir}:{toxinidir}
deps =
    coverage>=3,<4
    py27: mock
    flake8: flake8>=2.0,<3.0
    django: coveralls
    django-111: Django>=1.11,<2.0
    django-20: Django>=2.0,<2.1
    django-21: Django>=2.1,<2.2
    django-22: Django>=2.2,<2.3
    django-30: Django>=3.0,<3.1
commands =
    flake8: flake8 safedelete --ignore=E501
    django: coverage run --parallel-mode {toxinidir}/runtests.py {posargs}
    django: - coveralls

[testenv:docs]
basepython = python
changedir = docs
deps =
    sphinx
    sphinx_rtd_theme
    Django>=1.11,<2.0
commands =
    sphinx-build -W -b html -d build/doctrees . build/html
