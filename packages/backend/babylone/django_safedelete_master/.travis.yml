language: python
sudo: false

python:
  - 2.7
  - 3.4
  - 3.5
  - 3.6
  - 3.7
  - 3.8

env:
  - DJANGO_VERSION="stable/1.11.x"
  - DJANGO_VERSION="stable/2.0.x"
  - DJANGO_VERSION="stable/2.1.x"
  - DJANGO_VERSION="stable/2.2.x"
  - DJANGO_VERSION="stable/3.0.x"

install:
   # This is a dependency of our Django test script
 - pip install argparse

   # Install the dependencies of the app itself
   # - pip install -r requirements.txt

   # Clone Django from the GitHub repository
 - git clone -b $DJANGO_VERSION --single-branch https://github.com/django/django
 - pip install -e django/
 - pip install -e .

 - pip install 'coverage>=3,<4'

 - pip install pep8
 - pip install pyflakes

before_script:
    - "pep8 --ignore=E501 safedelete"
    - pyflakes safedelete

script:
    - coverage run `which django-admin.py` test --settings=safedelete.tests.settings

after_success:
 - pip install coveralls
 - coveralls


matrix:
  exclude:
    - python: 2.7
      env: DJ<PERSON><PERSON>O_VERSION="stable/2.0.x"
    - python: 2.7
      env: DJANGO_VERSION="stable/2.1.x"
    - python: 3.4
      env: DJANGO_VERSION="stable/2.1.x"
    - python: 2.7
      env: DJANGO_VERSION="stable/2.2.x"
    - python: 2.7
      env: DJANGO_VERSION="stable/3.0.x"
    - python: 3.4
      env: DJANGO_VERSION="stable/2.2.x"
    - python: 3.4
      env: DJANGO_VERSION="stable/3.0.x"
    - python: 3.5
      env: DJANGO_VERSION="stable/3.0.x"
    - python: 3.8
      env: DJANGO_VERSION="stable/1.11.x"
    - python: 3.8
      env: DJANGO_VERSION="stable/2.0.x"
    - python: 3.8
      env: DJANGO_VERSION="stable/2.1.x"
    - python: 3.8
      env: DJANGO_VERSION="stable/2.2.x"
