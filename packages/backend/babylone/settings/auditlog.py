"""
Auditlog settings for production.
"""

from django.conf import settings

# AuditLog configuration
AUDITLOG_INCLUDE_ALL_MODELS = False

# AuditLog settings
AUDITLOG_JSONFIELD_BACKEND = "jsonfield.fields.JSONField"
AUDITLOG_USE_JSONFIELD = True
AUDITLOG_USE_JSONFIELD_SERIALIZER = True

# AuditLog middleware:
# order of middleware is important
# 1. AuditlogActorMiddleware: set the actor (user) in the request
# 2. AuditlogMiddleware: set the auditlog in the request
AUDITLOG_MIDDLEWARE = [
    "audit.middleware.AuditlogMiddleware",
]

AUDITLOG_EXCLUDE_FIELDS = [
    "id",
    "created_on",
    "updated_on",
    "deleted_on",
    "deleted_by",
    "created_by",
    "updated_by",
]
""""
AUDITLOG_INCLUDE_TRACKING_MODELS

You can use this setting to configure your models registration and other behaviours. It must be a list or tuple. Each item in this setting can be a:

str: To register a model.

dict: To register a model and define its logging behaviour. e.g. include_fields, exclude_fields.

AUDITLOG_INCLUDE_TRACKING_MODELS = (
    "<appname>.<model1>",
    {
        "model": "<appname>.<model2>",
        "include_fields": ["field1", "field2"],
        "exclude_fields": ["field3", "field4"],
        "mapping_fields": {
            "field1": "FIELD",
        },
        => Mapping fields
        If you have field names on your models that aren’t intuitive or user friendly you can include a dictionary of field mappings during the register() call.
        
        "mask_fields": ["field5", "field6"],
        "m2m_fields": ["field7", "field8"],
        "serialize_data": True,
        "serialize_auditlog_fields_only": False,
        "serialize_kwargs": {"fields": ["foo", "bar", "biz", "baz"]},
    },
    "<appname>.<model3>",
)
"""

AUDITLOG_INCLUDE_TRACKING_MODELS = (
    {
        "model": "operation.operation",
        "exclude_fields": [
            "zkf_cro_type",
            "cro_types",
            "zkf_patient",
            "zkf_hospitalisation",
            "zkf_consultation_pre_op",
            "materiel_irradiation",
        ],
        # "mapping_fields": {
        #     "field1": "FIELD",
        # },
        # "mask_fields": ["field5", "field6"],
        "m2m_fields": [
            # "protocoles",
            # "operateur_new",
            # "anesthesiste_new",
            # "aides_operatoire",
        ],
        "serialize_data": True,
        "serialize_auditlog_fields_only": False,
        # "serialize_kwargs": {"fields": ["foo", "bar", "biz", "baz"]},
    },
    {
        "model": "hospitalisation.hospitalisation",
        "exclude_fields": [
            "zkf_consigne_sortie",
        ],
        # "mapping_fields": {
        #     "field1": "FIELD",
        # },
        # "mask_fields": ["field5", "field6"],
        "m2m_fields": [],
        "serialize_data": True,
        "serialize_auditlog_fields_only": False,
        # "serialize_kwargs": {"fields": ["foo", "bar", "biz", "baz"]},
    },
    {
        "model": "patient.patient",
        "exclude_fields": [],
        # "mapping_fields": {
        #     "field1": "FIELD",
        # },
        # "mask_fields": ["field5", "field6"],
        "m2m_fields": [],
        "serialize_data": True,
        "serialize_auditlog_fields_only": False,
        # "serialize_kwargs": {"fields": ["foo", "bar", "biz", "baz"]},
    },
    {
        "model": "operation.irradiation",
        "exclude_fields": [],
        # "mapping_fields": {
        #     "field1": "FIELD",
        # },
        # "mask_fields": ["field5", "field6"],
        "m2m_fields": [],
        "serialize_data": True,
        "serialize_auditlog_fields_only": False,
        # "serialize_kwargs": {"fields": ["foo", "bar", "biz", "baz"]},
    },
    {
        "model": "consultation.consultation",
        "exclude_fields": [
            "consultation_arrive",
            "consultation_debut_prevu",
            "consultation_fin_prevu",
            "consultation_debut_reel",
            "consultation_fin_reel",
            "premiere_consultation",
            "etat_consultation",
        ],
        # "mapping_fields": {
        #     "field1": "FIELD",
        # },
        # "mask_fields": ["field5", "field6"],
        "m2m_fields": [],
        "serialize_data": True,
        "serialize_auditlog_fields_only": False,
        # "serialize_kwargs": {"fields": ["foo", "bar", "biz", "baz"]},
    },
)
