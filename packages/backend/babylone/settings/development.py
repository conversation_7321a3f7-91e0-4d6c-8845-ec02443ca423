"""
Development environment settings.
This file overrides settings from base.py for development environment.

Note: For security improvements that could be made in the future, see production.py comments.
"""

from .base import *  # noqa: F403
import os

# Override base settings
DEBUG = True
ALLOWED_HOSTS = ["*"]

# Development CSRF settings - allow all in development
CSRF_TRUSTED_ORIGINS = ["http://*", "https://*"]

# Logging configuration to display DEBUG logs for audio transcription signals
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
        },
    },
    "loggers": {
        "document.signals.audio_recording_transcription": {
            "handlers": ["console"],
            "level": "DEBUG",
            "propagate": True,
        },
    },
}


# Database configuration
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("POSTGRES_DB", "meiflow"),
        "USER": os.environ.get("POSTGRES_USER", "meiflow"),
        "PASSWORD": os.environ.get("POSTGRES_PASSWORD", "meiflow123"),
        "HOST": os.environ.get(
            "POSTGRES_HOST", "localhost"
        ),  # Will be 'postgres' from compose.yml env
        "PORT": os.environ.get(
            "POSTGRES_PORT", "5435"
        ),  # Will be '5432' from compose.yml env
    }
}


# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.postgresql",
#         "NAME": "babylone_dev",
#         "USER": "maxherbecq",
#         "PASSWORD": "mdp_test",
#         "HOST": "localhost",
#         "PORT": "5432",
#     }
# }


# Development-specific Silk settings
SILKY_INTERCEPT_PERCENT = 50  # log 50% of requests in development
# SILKY_META = True
SILKY_MAX_RECORDED_REQUESTS = 10**10
# SILKY_MAX_RECORDED_REQUESTS_CHECK_PERCENT = 10 # check only 10% of requests
SILKY_MAX_REQUEST_BODY_SIZE = -1
SILKY_MAX_RESPONSE_BODY_SIZE = 1024
# SILKY_AUTHENTICATION = True  # User must login
# SILKY_AUTHORISATION = True  # User must have permissions


# Development-specific template settings
# TODO CAREFUL: This setting is not recommended for production
# PLUS: in ordonnances, return a path fo "{% extends ordonnance.zkf_patient.couverture_maladie.ordo_bizone|default:False|yesno:"impression/ordonnance/ordonnance_ald.html,impression/ordonnance/ordonnance_simple.html" %}"
# => bug for printing ordonnances !!!

# TEMPLATES[0]["OPTIONS"].update({"string_if_invalid": "{{%s}}"})  # noqa: F405

# Development-specific settings
KEYCLOAK_SERVER_URL = "https://sso.preprod.meiflow.fr"

INS_IDENTITY_URL = "https://sandbox.api.akimed.io"
INS_SEARCH_URL = "https://sandbox.api.akimed.io"
INS_API_USERNAME = "<EMAIL>"
INS_API_PASSWORD = "123456"
INSTALLED_APPS += [
    "django_extensions",
    "silk",
]
