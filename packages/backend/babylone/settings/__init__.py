#!/usr/bin/env python

import os
import sys

PROJECT_PATH = os.getcwd()
ENV = os.environ.get("ENV")

# Use the provided ENV, defaulting to development if no ENV set
if ENV == "production":
    from .production import *  # noqa: F403, F401

    ENV = "production"
elif ENV == "preproduction":
    from .preproduction import *  # noqa: F403, F401

    ENV = "preproduction"
elif ENV == "development" or not ENV:
    from .development import *  # noqa: F403, F401

    ENV = "development"
else:
    print(
        f"ERROR: Invalid ENV value: {ENV}. Must be one of: production, preproduction, development",
        file=sys.stderr,
    )
    sys.exit(1)
