"""
Django settings for babylone project.

This is the base settings file containing all shared settings.
Environment-specific settings should override these in their respective files.
"""

import os
from pathlib import Path
from keycloak import KeycloakAdmin
from keycloak import KeycloakOpenIDConnection

# Build paths inside the project like this: BASE_DIR / 'subdir'
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "tk&58j440c%=s@683zibv7%%4n-as(zpr(+))f1o(uaqd!(jwl"

# Application definition

INSTALLED_APPS = [
    # "silk",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Celery
    "django_celery_results",
    "django_celery_beat",
    # config
    "babylone.apps.BabyloneConfig",
    # REST
    "rest_framework",
    "api",
    "rest_framework.authtoken",
    "accounts.apps.AccountsConfig",
    "patient.apps.PatientConfig",
    "hospitalisation.apps.HospitalisationConfig",
    # Operation
    "operation.apps.OperationConfig",
    # staff
    "staff.apps.StaffConfig",
    # Specialites
    "specialites.apps.SpecialitesConfig",
    "specialites.chirurgie_vasculaire.apps.ChirurgieVasculaireConfig",
    "specialites.angiologie.apps.AngiologieConfig",
    "specialites.infectiologie.apps.InfectiologieConfig",
    "specialites.anesthesie.apps.AnesthesieConfig",
    "specialites.nephrologie.apps.NephrologieConfig",
    "consultation.apps.ConsultationConfig",
    "schedule.apps.ScheduleConfig",
    # codage
    "codage.ccam.apps.CcamConfig",
    "codage.cim.apps.CimConfig",
    # documents (photos, ordonnances...)
    "document.apps.DocumentConfig",
    "widget_tweaks",  # render forms
    # audit
    "audit.apps.AuditConfig",
    # tags - sort
    "sort.apps.SortAppConfig",
    "sort.tag.apps.TagAppConfig",
    # Contact : patient, correspondant, adresse, tel, email.....
    "contact.apps.ContactConfig",
    # impression
    "impression.apps.ImpressionConfig",
    # dashboard  https://stackoverflow.com/questions/********/django-cant-import-module-check-that-module-appconfig-name-is-correct
    "accounts.dashboard.apps.DashboardConfig",
    "accounts.admindashboard.apps.AdminDashboardConfig",
    # comptabilite
    "comptabilite.apps.ComptabiliteConfig",
    # assurance sante
    "assurance_sante.apps.AssuranceSanteConfig",
    # Protocoles
    "protocole.apps.ProtocoleConfig",
    # Materiel
    "materiel.apps.MaterielConfig",
    # notifications
    "notification.apps.NotificationConfig",
    # Rappel
    "rappel.apps.RappelConfig",
    "drf_spectacular",
    "corsheaders",
    # Annuaire Santé
    "annuaire_sante.apps.AnnuaireSanteConfig",
    # Logs: audit log has to be before DRF app
    "auditlog",
]

MIDDLEWARE = [
    # "silk.middleware.SilkyMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "babylone.urls"

# https://django-taggit.readthedocs.io/en/latest/getting_started.html
TAGGIT_CASE_INSENSITIVE = True

REST_FRAMEWORK = {
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 20,
    # https://medium.com/powered-by-django/create-api-django-rest-framework-c6f727a93409
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.DjangoModelPermissions",
    ],
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.TokenAuthentication",
        "rest_framework.authentication.SessionAuthentication",
        "babylone.authentication.KeycloakAuthentication",
    ],
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    # "DEFAULT_THROTTLE_CLASSES": [
    #     "rest_framework.throttling.AnonRateThrottle",
    #     "rest_framework.throttling.UserRateThrottle",
    # ],
    # "DEFAULT_THROTTLE_RATES": {"anon": "1000/day", "user": "100000/day"},
}


TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(BASE_DIR, "babylone/templates"),
            os.path.join(BASE_DIR, "templates"),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
            # 'string_if_invalid': '{{' + '%s' + '}}',
            "libraries": {
                # load additional custom tags
                "babylone_extras": "babylone.templatetags.babylone_extras",
                "routeur_antecedents": "babylone.templatetags.routeur_antecedents",
            },
        },
    },
]

WSGI_APPLICATION = "babylone.wsgi.application"

# Password validation
# https://docs.djangoproject.com/en/2.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/2.0/topics/i18n/

LANGUAGE_CODE = "fr"  # 'en-us'

TIME_ZONE = "Europe/Paris"
# DECIMAL_SEPARATOR = ','
USE_I18N = True
USE_L10N = False  # set to false otherwise dates will be in a wrong format !

USE_TZ = True

# PROJECT_PATH = os.path.realpath(os.path.join(os.path.dirname(__file__), '..'))
# SETTINGS_PATHS = [os.path.dirname(__file__), ]

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.0/howto/static-files/

STATIC_URL = "/static/"
STATICFILES_DIRS = [
    # os.path.join(BASE_DIR, "babylone/static"),
    os.path.join(BASE_DIR, "assets/distri/"),
    # os.path.join(BASE_DIR, "media/icons/"),  # for icons in the back
]

# https://github.com/DMOJ/docs/blob/master/sample_files/local_settings.py
# ##################################################
# ########### Static files configuration. ##########
# ##################################################
# # See <https://docs.djangoproject.com/en/1.11/howto/static-files/>.

# # Change this to somewhere more permanent., especially if you are using a
# # webserver to serve the static files. This is the directory where all the
# # static files will be collected to.
# # You must configure your webserver to serve this directory as /static/ in production.
STATIC_ROOT = os.path.join(BASE_DIR, "static")

MEDIA_ROOT = os.path.join(BASE_DIR, "media")
MEDIA_URL = "/api/media/"


# allow upload big file
# DATA_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024 * 2.5  # 2.5M
# FILE_UPLOAD_MAX_MEMORY_SIZE = DATA_UPLOAD_MAX_MEMORY_SIZE

# Disable limitation of forms fields: useful to uplaod cropped image (sending a FormData)
# DATA_UPLOAD_MAX_NUMBER_FIELDS = None

# Post requests
DATA_UPLOAD_MAX_MEMORY_SIZE = 26214400  # 2621440
DATA_UPLOAD_MAX_NUMBER_FIELDS = 4000  # 1000


# Celery configuration
CELERY_RESULT_BACKEND = "django-db"
CELERY_CACHE_BACKEND = "django-cache"


# Celery settings
CELERY_BROKER_URL = "redis://localhost"

#: Only add pickle to this list if your broker is secured
#: from unwanted access (see userguide/security.html)
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_RESULT_BACKEND = "db+sqlite:///results.sqlite"
CELERY_TASK_SERIALIZER = "json"


# NOTIFICATIONS
NOTIFY_UPDATE_TIME_INTERVAL = 1000 * 60  # in ms

# LOGIN USER
LOGIN_INTERVAL = 600  # in s

# upgrade django 3.2
DEFAULT_AUTO_FIELD = "django.db.models.AutoField"

CORS_ALLOW_ALL_ORIGINS = True
# This needs to be restricted in production

# Keycloak parameters
KEYCLOAK_REALM_NAME = os.environ.get("KEYCLOAK_REALM_NAME", "meiflow_realm")
KEYCLOAK_CLIENT_ID = os.environ.get("KEYCLOAK_CLIENT_ID", "backend_connexion")
KEYCLOAK_CLIENT_SECRET_KEY = os.environ.get("KEYCLOAK_CLIENT_SECRET_KEY", "")

keycloak_connection = KeycloakOpenIDConnection(
    server_url=globals().get("KEYCLOAK_SERVER_URL", "https://sso.preprod.meiflow.fr"),
    realm_name=KEYCLOAK_REALM_NAME,
    client_id=KEYCLOAK_CLIENT_ID,
    client_secret_key=KEYCLOAK_CLIENT_SECRET_KEY,
    verify=True,
)

keycloak_admin = KeycloakAdmin(connection=keycloak_connection)

# Email settings
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
DEFAULT_FROM_EMAIL = "<EMAIL>"
EMAIL_HOST_USER = "<EMAIL>"
EMAIL_HOST_PASSWORD = os.environ.get(
    "EMAIL_CONTACT_PASSWORD", ""
)  # Default password kept for backward compatibility
EMAIL_USE_SSL = True
EMAIL_USE_TLS = False
EMAIL_PORT = 465
EMAIL_HOST = "ssl0.ovh.net"

# https://ansforge.github.io/annuaire-sante-fhir-documentation/pages/guide/version-1/getting-started/introduction.html
ESANTE_API_KEY = "bdd9255f-673c-40c7-92e8-366e04207b2d"
# ESANTE_API_URL = "https://gateway.api.esante.gouv.fr/fhir/"

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("POSTGRES_DB", "meiflow"),
        "USER": os.environ.get("POSTGRES_USER", "meiflow"),
        "PASSWORD": os.environ.get("POSTGRES_PASSWORD", "meiflow123"),
        "HOST": os.environ.get("POSTGRES_HOST", "localhost"),
        "PORT": os.environ.get("POSTGRES_PORT", "5435"),
    }
}

MISTRAL_API_KEY = os.environ.get("MISTRAL_API_KEY")

# INS API configuration
INS_IDENTITY_URL = os.environ.get("INS_IDENTITY_URL")
INS_SEARCH_URL = os.environ.get("INS_SEARCH_URL")
INS_API_USERNAME = os.environ.get("INS_API_USERNAME")
INS_API_PASSWORD = os.environ.get("INS_API_PASSWORD")
from .auditlog import *

MIDDLEWARE += AUDITLOG_MIDDLEWARE

SPECTACULAR_SETTINGS = {
    "APPEND_OPERATION_ID": True,
    "ENUM_NAME_OVERRIDES": {
        "StatutCrhEnum": "StatutCrh",
    },
}
MISTRAL_MODEL = os.environ.get(
    "MISTRAL_MODEL", "mistral-medium-latest"
)  # mistral-small-latest
# mistral-large-latest

# Path to the Google Cloud service account JSON key file for Google Speech-to-Text authentication
GOOGLE_APPLICATION_CREDENTIALS = os.environ.get("GOOGLE_APPLICATION_CREDENTIALS")
