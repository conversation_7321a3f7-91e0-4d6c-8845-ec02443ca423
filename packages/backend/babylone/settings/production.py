"""
Production environment settings.
This file overrides settings from base.py for production environment.

Future security improvements to consider:
- SECURE_SSL_REDIRECT = True
- SECURE_HSTS_SECONDS = 31536000  # 1 year
- SECURE_HSTS_INCLUDE_SUBDOMAINS = True
- SECURE_HSTS_PRELOAD = True
- SESSION_COOKIE_SECURE = True
- CSRF_COOKIE_SECURE = True
- Restrict ALLOWED_HOSTS to specific domains
"""

from .base import *  # noqa: F403

# Override base settings
DEBUG = False
ALLOWED_HOSTS = [".meiflow.fr"]

# Production CSRF settings
CSRF_TRUSTED_ORIGINS = ["https://backend.meiflow.fr"]

# Production admin settings
ADMINS = [("mrouer", "<EMAIL>")]

# Production Keycloak settings
KEYCLOAK_SERVER_URL = "https://sso.meiflow.fr"

# Production proxy settings
USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Production Silk settings
SILKY_INTERCEPT_PERCENT = 5  # log only 5% of requests in production
SILKY_MAX_RECORDED_REQUESTS = 10**10
SILKY_MAX_RECORDED_REQUESTS_CHECK_PERCENT = 10
SILKY_MAX_REQUEST_BODY_SIZE = 5
SILKY_MAX_RESPONSE_BODY_SIZE = 1024
SILKY_AUTHENTICATION = True
SILKY_AUTHORISATION = True

# Remove Silk middleware in production for better performance
# MIDDLEWARE.remove("silk.middleware.SilkyMiddleware")  # noqa: F405

# Production-specific Keycloak settings
KEYCLOAK_SERVER_URL = "https://sso.meiflow.fr"
