from django.conf import settings
from .models import Patient
import logging
import httpx
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)


@sync_to_async
def get_patient_for_async(patient_id):
    try:
        # Pre-fetch the related environnement to avoid sync DB calls later
        return Patient.objects.select_related("zkf_environnement_patient").get(
            id=patient_id
        )
    except Patient.DoesNotExist:
        return None


@sync_to_async
def save_patient_ins_checked_async(patient):
    patient.save(update_fields=["ins_checked"])


@sync_to_async
def save_patient_lookup_status(patient, status, details=None):
    """Saves lookup status and details, always marks checked=True."""
    patient.ins_lookup_status = status
    patient.ins_lookup_details = details
    patient.ins_checked = True  # Mark as checked on any terminal status
    patient.save(
        update_fields=["ins_lookup_status", "ins_lookup_details", "ins_checked"]
    )


@sync_to_async
def save_patient_full_ins_async(patient):
    """Saves successful INS lookup results."""
    update_fields = [
        "ins",
        "ins_oid",
        "ins_checked",
        "ins_lookup_status",
        "ins_lookup_details",
    ]
    if hasattr(patient, "_changed_prenom") and patient._changed_prenom:
        update_fields.append("prenom")
    if hasattr(patient, "_changed_nom_naissance") and patient._changed_nom_naissance:
        update_fields.append("nom_naissance")
    if (
        hasattr(patient, "_changed_birth_place_code")
        and patient._changed_birth_place_code
    ):
        update_fields.append("birth_place_code")
    # Ensure status/details are set before saving
    patient.ins_checked = True
    patient.ins_lookup_status = Patient.INS_LOOKUP_SUCCESS
    patient.ins_lookup_details = None
    patient.save(update_fields=update_fields)


@sync_to_async
def clear_patient_ins_after_verification(patient):
    """Clears INS fields and sets lookup status after failed verification."""
    patient.ins = None
    patient.ins_oid = None
    patient.ins_checked = (
        False  # Set to False to allow re-lookup after failed verification
    )
    patient.ins_lookup_status = Patient.INS_LOOKUP_PENDING
    patient.save(
        update_fields=[
            "ins",
            "ins_oid",
            "ins_checked",
            "ins_lookup_status",
            "ins_lookup_details",
        ]
    )


@sync_to_async
def reset_patient_ins_status(patient):
    """Resets all INS fields and status for a fresh lookup attempt."""
    patient.ins = None
    patient.ins_oid = None
    patient.ins_checked = False  # Allow re-lookup
    patient.ins_lookup_status = Patient.INS_LOOKUP_PENDING
    patient.ins_lookup_details = None
    patient.save(
        update_fields=[
            "ins",
            "ins_oid",
            "ins_checked",
            "ins_lookup_status",
            "ins_lookup_details",
        ]
    )


async def get_ins_auth_token_async(struct_type, struct):
    if (
        not settings.INS_IDENTITY_URL
        or not settings.INS_API_USERNAME
        or not settings.INS_API_PASSWORD
    ):
        error = "INS API not configured"
        logger.error(f"INS Async Auth: {error}")
        return None, error
    identity_url = settings.INS_IDENTITY_URL.rstrip("/")
    auth_body = {
        "username": settings.INS_API_USERNAME,
        "password": settings.INS_API_PASSWORD,
        "organization": {"type": struct_type, "value": struct},
    }
    try:
        async with httpx.AsyncClient(timeout=10) as client:
            logger.debug(f"INS Async Auth: Requesting token for {struct_type}:{struct}")
            auth_resp = await client.post(
                f"{identity_url}/identity/token", json=auth_body
            )
            auth_resp.raise_for_status()
            token = auth_resp.json().get("access_token")
            if not token:
                logger.warning(
                    f"INS Async Auth: No token received for {struct_type}:{struct}"
                )
                return None, "Échec obtention token (détails inconnus)"
            logger.debug(f"INS Async Auth: Token obtained for {struct_type}:{struct}")
            return token, None
    except httpx.RequestError as e:
        logger.error(
            f"INS Async Auth: Token request failed for {struct_type}:{struct}: {e}",
            exc_info=True,
        )
        return None, f"Erreur réseau lors de l'authentification: {e}"
    except httpx.HTTPStatusError as e:
        logger.error(
            f"INS Async Auth: HTTP error during token request for {struct_type}:{struct}: {e}",
            exc_info=True,
        )
        return None, f"Erreur HTTP lors de l'authentification: {e}"
    except ValueError as e:
        logger.error(
            f"INS Async Auth: JSON decoding error during token request for {struct_type}:{struct}: {e}",
            exc_info=True,
        )
        return None, f"Erreur de décodage JSON lors de l'authentification: {e}"


async def perform_ins_lookup_async(patient_id):
    patient = await get_patient_for_async(patient_id)
    if not patient:
        logger.error(f"INS Async Lookup: Patient {patient_id} not found.")
        return

    # Reset status if PENDING for a fresh attempt (avoids race conditions)
    if patient.ins_lookup_status == Patient.INS_LOOKUP_PENDING:
        patient.ins_lookup_details = None
        # Don't save yet, just reset in memory for this run

    logger.debug(
        f"INS Async Lookup: Starting for patient {patient.id} (Current status: {patient.ins_lookup_status})"
    )

    # Only proceed if PENDING
    if (
        patient.ins_lookup_status != Patient.INS_LOOKUP_PENDING
        and not patient.ins_checked
    ):
        # If it's not PENDING but also not checked, maybe it was reset manually?
        # Treat as PENDING for this run.
        logger.info(
            f"INS Async Lookup: Status was {patient.ins_lookup_status} but ins_checked=False. Treating as PENDING."
        )
        patient.ins_lookup_status = Patient.INS_LOOKUP_PENDING
        patient.ins_lookup_details = None
    elif patient.ins or patient.ins_checked:
        logger.debug(
            f"INS Async Lookup: Skipping patient {patient.id}, already has INS or is checked (Status: {patient.ins_lookup_status}, Checked: {patient.ins_checked})"
        )
        return

    struct = patient.zkf_environnement_patient.ins_struct_number
    struct_type = patient.zkf_environnement_patient.ins_struct_type
    if not struct or not struct_type:
        await save_patient_lookup_status(
            patient,
            Patient.INS_LOOKUP_NOT_ELIGIBLE_ENV,
            "Informations structure/établissement manquantes.",
        )
        return

    if not all([patient.nom_naissance, patient.prenom, patient.dob, patient.sexe]):
        missing = []
        if not patient.nom_naissance:
            missing.append("nom naissance")
        if not patient.prenom:
            missing.append("prénom")
        if not patient.dob:
            missing.append("date naissance")
        if not patient.sexe:
            missing.append("sexe")
        details = f"Informations patient manquantes: {', '.join(missing)}"
        await save_patient_lookup_status(
            patient, Patient.INS_LOOKUP_NOT_ELIGIBLE_INFO, details
        )
        return

    token, token_error_details = await get_ins_auth_token_async(struct_type, struct)
    if not token:
        await save_patient_lookup_status(
            patient,
            Patient.INS_LOOKUP_API_ERROR,
            token_error_details or "Échec obtention token (détails inconnus)",
        )
        return

    if not settings.INS_SEARCH_URL:
        await save_patient_lookup_status(
            patient, Patient.INS_LOOKUP_API_ERROR, "INS API not configured"
        )
        return

    headers = {"Authorization": f"Bearer {token}"}
    search_url = settings.INS_SEARCH_URL.rstrip("/")

    # --- Construct Payload ---
    payload = {
        "name": {"family": patient.nom_naissance, "given": patient.prenom},
        "gender": patient.sexe.lower(),
        "birthDate": patient.dob.isoformat(),
    }
    if patient.birth_place_code:
        payload["birthPlace"] = {"frInseeCode": {"code": patient.birth_place_code}}
        logger.debug(
            f"INS Async Lookup: Including birthplace code {patient.birth_place_code} in search payload."
        )
    # --- End Payload Construction ---

    async with httpx.AsyncClient(timeout=15) as client:
        try:
            logger.debug(
                f"INS Async Lookup: Performing search for patient {patient.id}"
            )
            resp = await client.post(
                f"{search_url}/ins/search", json=payload, headers=headers
            )

            # Handle 403 as a specific "search failed" case, not an exception
            if resp.status_code == 403:
                response_text = resp.text
                api_message = response_text  # Default to raw text
                try:
                    error_payload = resp.json()
                    if isinstance(error_payload, dict) and "message" in error_payload:
                        api_message = error_payload["message"]
                except ValueError:  # Not JSON
                    pass  # api_message remains response_text

                logger.warning(  # Log as warning or info, as 403 is an "expected" API failure response
                    f"INS Async Lookup: Search failed for patient {patient.id} with 403. API Message: {api_message}"
                )
                await save_patient_lookup_status(
                    patient, Patient.INS_LOOKUP_API_ERROR, api_message
                )
                return

            # For other errors (4xx not 403, 5xx), raise an exception to be caught below
            resp.raise_for_status()

            # Successful search (2xx)
            data = resp.json()
            name_data = data.get("name", {})
            given_name = name_data.get("given")
            family_name = name_data.get("family")

            # Extract birthplace information from response
            birth_place_data = data.get("birthPlace", {})
            birth_place_code = None
            if birth_place_data:
                fr_insee_code = birth_place_data.get("frInseeCode", {})
                if fr_insee_code:
                    birth_place_code = fr_insee_code.get("code")

            ids = data.get("identifier", [])
            if ids:
                patient.ins = ids[0].get("value")
                patient.ins_oid = ids[0].get("system")
                patient._changed_prenom = False  # Temp flags for save helper
                patient._changed_nom_naissance = False
                patient._changed_birth_place_code = False  # Flag for birthplace changes
                if given_name and patient.prenom != given_name:
                    patient.prenom = given_name
                    patient._changed_prenom = True
                if family_name and patient.nom_naissance != family_name:
                    patient.nom_naissance = family_name
                    patient._changed_nom_naissance = True
                if birth_place_code and patient.birth_place_code != birth_place_code:
                    patient.birth_place_code = birth_place_code
                    patient._changed_birth_place_code = True
                    logger.info(
                        f"INS Async Lookup: Updated birthplace code for patient {patient.id} from '{patient.birth_place_code}' to '{birth_place_code}'"
                    )
                await save_patient_full_ins_async(patient)  # This sets SUCCESS status
                return  # Success
            else:
                # Success response but no identifiers
                await save_patient_lookup_status(
                    patient,
                    Patient.INS_LOOKUP_API_ERROR,
                    "Réponse API OK mais aucun identifiant retourné.",
                )  # Use API_ERROR
                return

        except httpx.HTTPStatusError as e:
            response_text = e.response.text
            api_message = response_text  # Default to raw text

            try:
                error_payload = e.response.json()
                if isinstance(error_payload, dict) and "message" in error_payload:
                    api_message = error_payload["message"]
                # If not a dict or "message" key is not present, api_message remains response_text
            except ValueError:  # Not JSON, api_message remains response_text
                pass

            logger.error(
                f"INS Async Lookup: HTTP error for patient {patient.id} - {e.response.status_code} "
                f"(API Message: {api_message}, Raw Response: {response_text})",
                exc_info=True,
            )
            await save_patient_lookup_status(
                patient, Patient.INS_LOOKUP_API_ERROR, api_message
            )
            return

        except httpx.RequestError as e:
            # --- Network/Request Error on search ---
            details = f"Erreur réseau lors de la recherche: {e}"
            await save_patient_lookup_status(
                patient, Patient.INS_LOOKUP_API_ERROR, details
            )
            return
        except Exception as e:
            # --- Other Error on search ---
            details = f"Erreur inattendue lors de la recherche: {e}"
            await save_patient_lookup_status(
                patient, Patient.INS_LOOKUP_API_ERROR, details
            )
            return
