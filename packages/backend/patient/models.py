from django.db import models
import uuid
import datetime
from typing import TYPE_CHECKING
from django.contrib.auth.models import User
from audit.models import Auditable

from accounts.models import Specialite, Service

# contact for correspondants
from contact.models import Contact, Adresse, Telephone, Email

from assurance_sante.models import AssuranceSante

# tags
from sort.tag.models import TagEveryWhere
from sort.tag.managers import TaggableManager

# validators for INS (NIR) and OID
from django.core.validators import RegexValidator

if TYPE_CHECKING:
    from django.db.models.manager import RelatedManager


class CouvertureMaladie(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    nom = models.CharField(max_length=254)
    tiers_payant = models.BooleanField()
    couverture = models.IntegerField()
    ordo_bizone = models.BooleanField()

    def __str__(self):
        return self.nom


# table manytomany entre patient et contacts: ajouter plusieurs champs de détail !
# TO BE CONTINUED !!!!!
class DetailContactPatient(models.Model):
    patient = models.ForeignKey("patient.Patient", on_delete=models.CASCADE)
    contact = models.ForeignKey("contact.Contact", on_delete=models.CASCADE)
    date_created = models.DateField(auto_now_add=True)
    medecin_adresseur = models.BooleanField(default=False)


class Patient(Auditable):
    # 	https://stackoverflow.com/questions/3052975/django-models-avoid-duplicates
    class Meta(Auditable.Meta):
        unique_together = ["nom", "prenom", "dob", "zkf_environnement_patient"]

    if TYPE_CHECKING:
        adresse_set: "RelatedManager[Adresse]"
        telephone_set: "RelatedManager[Telephone]"
        email_set: "RelatedManager[Email]"

    MALE = "MALE"
    FEMALE = "FEMALE"

    SEXE = (
        (MALE, "Homme"),
        (FEMALE, "Femme"),
    )

    VIVANT = "VIVANT"
    DCD = "DCD"
    ETAT = (
        (VIVANT, "Vivant"),
        (DCD, "Décédé"),
    )

    # --- INS Lookup Status Choices ---
    INS_LOOKUP_PENDING = "PENDING"
    INS_LOOKUP_IN_PROGRESS = "IN_PROGRESS"
    INS_LOOKUP_SUCCESS = "SUCCESS"
    INS_LOOKUP_NOT_ELIGIBLE_INFO = "NOT_ELIGIBLE_INFO"
    INS_LOOKUP_NOT_ELIGIBLE_ENV = "NOT_ELIGIBLE_ENV"
    INS_LOOKUP_API_ERROR = "API_ERROR"

    INS_LOOKUP_STATUS_CHOICES = (
        (INS_LOOKUP_PENDING, "En attente"),
        (INS_LOOKUP_IN_PROGRESS, "Recherche en cours"),
        (INS_LOOKUP_SUCCESS, "Succès"),
        (INS_LOOKUP_NOT_ELIGIBLE_INFO, "Non éligible (Infos Patient)"),
        (INS_LOOKUP_NOT_ELIGIBLE_ENV, "Non éligible (Infos Etab)"),
        (
            INS_LOOKUP_API_ERROR,
            "Erreur API Externe / Non Trouvé / Multiples",
        ),
    )

    # --- Checked Identity Choices ---
    ID_CHECK_NONE = "NONE"
    ID_CHECK_FR_ID = "FR_ID"
    ID_CHECK_EU_ID = "EU_ID"
    ID_CHECK_PASSPORT = "PASSPORT"
    ID_CHECK_SEJOUR = "SEJOUR"
    ID_CHECK_FRANCE_IDENTITE = "FRANCE_IDENTITE"
    ID_CHECK_LAPOSTE = "LAPOSTE"
    ID_CHECK_VITALE_APP = "VITALE_APP"
    ID_CHECK_ATTESTATION = "ATTESTATION"

    CHECKED_IDENTITY_CHOICES = (
        (ID_CHECK_NONE, "Identité non vérifiée"),
        (ID_CHECK_FR_ID, "Carte Nationale d'Identité Française"),
        (ID_CHECK_EU_ID, "Carte d'Identité (UE/EEE/CH/UK/Microétats)"),
        (ID_CHECK_PASSPORT, "Passeport"),
        (ID_CHECK_SEJOUR, "Titre de Séjour"),
        (ID_CHECK_FRANCE_IDENTITE, "France Identité"),
        (ID_CHECK_LAPOSTE, "Identité Numérique La Poste"),
        (ID_CHECK_VITALE_APP, "Application Carte Vitale"),
        (ID_CHECK_ATTESTATION, "Attestation du professionnel"),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_environnement_patient = models.ForeignKey(
        "accounts.Environnement", on_delete=models.PROTECT
    )
    nom = models.CharField(
        max_length=50,
    )
    nom_naissance = models.CharField(
        max_length=50,
    )
    prenom = models.CharField(
        max_length=150,
    )
    sexe = models.CharField(
        max_length=100,
        choices=SEXE,
    )
    dob = models.DateField(
        auto_now=False,
        auto_now_add=False,
    )
    etat = models.CharField(
        max_length=50,
        default="VIVANT",
        choices=ETAT,
    )
    date_etat = models.DateField(
        auto_now=False, auto_now_add=False, default=datetime.date.today
    )
    mode_information_dernier_etat = models.CharField(
        max_length=50,
        null=True,
        blank=True,
    )
    cause_deces = models.TextField(blank=True, null=True)
    couverture_maladie = models.ForeignKey(
        CouvertureMaladie, on_delete=models.PROTECT, blank=True, null=True
    )
    numero_securite_sociale = models.CharField(max_length=60, blank=True, null=True)
    # INS API fields
    ins_validator = RegexValidator(
        regex=r"^[0-9A-Z]{13}-[0-9A-Z]{2}$",
        message="Format INS invalide: 13 caractères alphanumériques (chiffres ou lettres majuscules), tiret, 2 caractères alphanumériques",
    )
    oid_validator = RegexValidator(
        regex=r"^urn:oid:\d+(?:\.\d+)*$", message="Format OID invalide"
    )
    ins = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        editable=False,
        validators=[ins_validator],
        help_text="Identifiant INS du patient (qualifié ou provisoire)",
    )
    ins_oid = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        editable=False,
        validators=[oid_validator],
        help_text="OID du système INS qui a fourni l'identifiant",
    )
    birth_place_code = models.CharField(
        max_length=5,
        blank=True,
        null=True,
        help_text="Code INSEE de lieu de naissance",
    )
    ins_checked = models.BooleanField(
        default=False,
        editable=False,
        help_text="Indique si une tentative de recherche INS a été effectuée (succès ou échec).",
    )
    ins_lookup_status = models.CharField(
        max_length=30,
        choices=INS_LOOKUP_STATUS_CHOICES,
        default=INS_LOOKUP_PENDING,
        editable=False,
        help_text="Statut de la dernière tentative de recherche INS automatique.",
    )
    ins_lookup_details = models.TextField(
        blank=True,
        null=True,
        editable=False,
        help_text="Détails ou message d'erreur de la dernière recherche INS.",
    )
    checked_identity = models.CharField(
        max_length=20,
        choices=CHECKED_IDENTITY_CHOICES,
        default=ID_CHECK_NONE,
        help_text="Document utilisé pour vérifier l'identité du patient.",
    )
    praticien_referent = models.ManyToManyField(
        User, blank=True
    )  # set redacteur when creating a consultation if not in list !
    # opposition to clinical research/data user
    opposition_data_exploitation = models.BooleanField(default=False)
    note_opposition_data_exploitation = models.TextField(blank=True, null=True)

    """Complementaire sante"""
    zkf_assurance_sante = models.ForeignKey(
        AssuranceSante, on_delete=models.CASCADE, blank=True, null=True
    )

    # atcd_med = models.TextField(blank = True)
    # atcd_chir = models.TextField(blank = True)
    # fdrcv = models.TextField(blank = True)
    # traitement = models.TextField(blank = True, null = True)

    """Correspondants"""
    # Create a manytomany relationship for correspondants
    # correspondants = models.ManyToManyField(
    #     Contact, blank=True,
    # )
    correspondants = models.ManyToManyField(
        "contact.Contact",
        through="contact.CorrespondantPatient",
        blank=True,
        related_name="patients_correspondants_detail",
    )

    """Tags"""
    tags = TaggableManager(through=TagEveryWhere, blank=True)

    def __str__(self):
        return "{0} {1}".format(self.nom, self.prenom)

    def full_name(self):
        dob = "{:%d/%m/%Y}".format(self.dob)
        ne = "né"
        if self.sexe == "FEMALE":
            ne = "née"
        return "{0}, {1}, {3} {5}, {3} le {2} - {4}ans".format(
            self.nom, self.prenom, dob, ne, self.age, self.nom_naissance
        )

    def full_name_politesse(self):
        denomination = "Monsieur"
        if self.sexe == "FEMALE":
            denomination = "Madame"
        return "{0} {1} {2}".format(denomination, self.nom, self.prenom)

    def full_name_short(self):
        # dob = "{:%d/%m/%Y}".format(self.dob)
        return "{0} {1}".format(self.nom, self.prenom)

    def full_name_courrier(self):
        dob = "{:%d/%m/%Y}".format(self.dob)
        denomination = "Monsieur"
        if self.sexe == "FEMALE":
            denomination = "Madame"
        return "{0} {1} {2} ({3})".format(
            denomination, self.nom.upper(), self.prenom, dob
        )

    def full_name_courrier_short(self):
        dob = "{:%d/%m/%Y}".format(self.dob)
        denomination = "Monsieur"
        if self.sexe == "FEMALE":
            denomination = "Madame"
        return "{0} {1}".format(denomination, self.nom.upper())

    @property
    def age(self, on=None):  # on is to pick a different date than today !
        when = self.dob
        if on is None:
            on = datetime.date.today()
        was_earlier = (on.month, on.day) < (when.month, when.day)

        return on.year - when.year - (was_earlier)

    def sexe_politesse(self):
        sexe = "Homme"
        if self.sexe == "FEMALE":
            sexe = "Femme"
        return sexe

    def sexe_courrier(self):
        sexe = "Autre selon situation administrative"
        if self.sexe == "FEMALE":
            sexe = "F"
        if self.sexe == "MALE":
            sexe = "M"
        return sexe

    def template_admin(self):
        template = str("patient/pdf/fiche_administrative_pdf.html")
        return template

    @property
    def anciennete_etat(self):
        today = datetime.date.today()
        return (today - self.date_etat).days / 30

    @property
    def adresse(self):
        if self.adresse_set.all():
            adresse = ""
            for a in self.adresse_set.all():
                adresse += (
                    str(a.numero_rue)
                    + " "
                    + str(a.rue)
                    + ", "
                    + str(a.code_postal)
                    + " "
                    + str(a.ville)
                )
            return adresse
        else:
            return "pas d'adresse crée"

    @property
    def telephone(self):
        if self.telephone_set.all():
            telephone = ""
            for t in self.telephone_set.all():
                telephone += str(t.numero) + " "
            return telephone
        else:
            return "pas de telephone associé"


# Informations related to the patient dossier.
# Specific to a service and a specialite
# (there can be many specialite in the same service,
# and many services with the same specialite...)
class Dossier(Auditable):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    zkf_patient = models.ForeignKey(Patient, on_delete=models.CASCADE, null=True)
    numero_dossier = models.CharField(max_length=254)
    zkf_service = models.ForeignKey(Service, on_delete=models.CASCADE)
    zkf_specialite = models.ForeignKey(Specialite, on_delete=models.CASCADE)
    date_creation = models.DateField(auto_now=False, auto_now_add=True)
