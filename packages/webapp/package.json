{"name": "webapp", "version": "0.1.0", "private": true, "options": {"allowedHosts": ["localhost"], "proxy": "http://localhost:8000"}, "dependencies": {"@axa-fr/react-oidc": "^7.22.20", "@chakra-ui/react": "^2.8.2", "@ckeditor/ckeditor5-build-classic": "^43.3.1", "@ckeditor/ckeditor5-react": "^9.3.1", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^5.0.18", "@fontsource/open-sans": "^5.0.28", "@mui/icons-material": "^5.15.18", "@mui/joy": "^5.0.0-beta.36", "@mui/x-date-pickers": "^7.6.2", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/full-screen": "^3.12.0", "@react-pdf-viewer/print": "3.12.0", "@react-pdf-viewer/zoom": "^3.12.0", "@react-pdf/renderer": "3.4.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.97", "@types/react": "^18.3.3", "@types/react-big-calendar": "^1.8.9", "@types/react-dom": "^18.3.0", "axios": "^1.6.8", "ckeditor5": "^43.3.0", "dayjs": "^1.11.11", "diff": "^7.0.0", "diff2html": "^3.4.51", "dompurify": "^3.2.5", "framer-motion": "^11.3.19", "mammoth": "^1.8.0", "pdfjs-dist": "^3.11.174", "react": "^18.3.1", "react-big-calendar": "^1.13.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.10", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "recharts": "^2.15.3", "sass": "^1.77.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "react-scripts start", "build": "react-scripts build", "test": "cypress run --browser chrome", "eject": "react-scripts eject", "generate-types": "npx openapi-typescript schema.yml -o src/models/ts.d.ts", "lint": "eslint src --ext .js,.jsx,.ts,.tsx"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/diff": "^7.0.2", "@types/dompurify": "^3.2.0", "@types/uuid": "^10.0.0", "cypress": "^14.4.0", "prettier": "3.3.3"}}