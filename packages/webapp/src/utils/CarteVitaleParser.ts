import { SexeEnum } from "../models/types";
import {
  CarteVitaleAyantDroit,
  CarteVitaleBeneficiaire,
  CarteVitaleInfo,
  CarteVitalePatientData,
  CarteVitaleProcessingResult,
  CarteVitaleRawData,
} from "../types/CarteVitaleTypes";

/**
 * Parse XML string to JavaScript object
 */
function parseXML(xmlString: string): CarteVitaleRawData {
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xmlString, "text/xml");

  if (xmlDoc.querySelector("parsererror")) {
    throw new Error("Invalid XML format");
  }

  const carteVitaleElement = xmlDoc.querySelector("carteVitale");
  if (!carteVitaleElement) {
    throw new Error("Invalid carte vitale XML structure");
  }

  // Parse beneficiaire
  const beneficiaireElement = carteVitaleElement.querySelector("beneficiaire");
  if (!beneficiaireElement) {
    throw new Error("Missing beneficiaire information");
  }

  const beneficiaire: CarteVitaleBeneficiaire = {
    nom: getElementText(beneficiaireElement, "nom"),
    prenom: getElementText(beneficiaireElement, "prenom"),
    dateNaissance: getElementText(beneficiaireElement, "dateNaissance"),
    sexe: getElementText(beneficiaireElement, "sexe") as "M" | "F",
    numeroSecu: getElementText(beneficiaireElement, "numeroSecu", true),
    lieuNaissance: getElementText(beneficiaireElement, "lieuNaissance", true),
    codePostalNaissance: getElementText(beneficiaireElement, "codePostalNaissance", true),
    villeNaissance: getElementText(beneficiaireElement, "villeNaissance", true),
  };

  // Parse ayants droit
  const ayantsDroitElement = carteVitaleElement.querySelector("ayantsDroit");
  let ayantsDroit: CarteVitaleAyantDroit[] = [];

  if (ayantsDroitElement) {
    const ayantDroitElements = ayantsDroitElement.querySelectorAll("ayantDroit");
    ayantsDroit = Array.from(ayantDroitElements).map((element) => ({
      nom: getElementText(element, "nom"),
      prenom: getElementText(element, "prenom"),
      dateNaissance: getElementText(element, "dateNaissance"),
      sexe: getElementText(element, "sexe") as "M" | "F",
      lien: getElementText(element, "lien") as "CONJOINT" | "ENFANT" | "AUTRE",
      numeroSecu: getElementText(element, "numeroSecu", true),
    }));
  }

  return {
    carteVitale: {
      beneficiaire,
      ayantsDroit: ayantsDroit.length > 0 ? { ayantDroit: ayantsDroit } : undefined,
    },
  };
}

/**
 * Helper function to get text content from XML element
 */
function getElementText(parent: Element, tagName: string, optional: boolean = false): string {
  const element = parent.querySelector(tagName);
  if (!element) {
    if (optional) {
      return "";
    }
    throw new Error(`Missing required element: ${tagName}`);
  }
  return element.textContent?.trim() || "";
}

/**
 * Convert carte vitale sex to application sex enum
 */
function convertSexe(carteVitaleSexe: "M" | "F"): SexeEnum {
  return carteVitaleSexe === "M" ? "MALE" : "FEMALE";
}

/**
 * Convert carte vitale beneficiaire to patient data
 */
function convertBeneficiaireToPatientData(
  beneficiaire: CarteVitaleBeneficiaire
): CarteVitalePatientData {
  return {
    nom: beneficiaire.nom.toUpperCase(),
    prenom: capitalizeFirstLetter(beneficiaire.prenom),
    sexe: convertSexe(beneficiaire.sexe),
    dob: beneficiaire.dateNaissance,
    nom_naissance: beneficiaire.nom.toUpperCase(), // For beneficiaire, nom_naissance = nom
    birth_place_code: beneficiaire.codePostalNaissance || "",
    numeroSecu: beneficiaire.numeroSecu,
    source: "BENEFICIAIRE",
  };
}

/**
 * Convert carte vitale ayant droit to patient data
 */
function convertAyantDroitToPatientData(ayantDroit: CarteVitaleAyantDroit): CarteVitalePatientData {
  return {
    nom: ayantDroit.nom.toUpperCase(),
    prenom: capitalizeFirstLetter(ayantDroit.prenom),
    sexe: convertSexe(ayantDroit.sexe),
    dob: ayantDroit.dateNaissance,
    nom_naissance: ayantDroit.nom.toUpperCase(),
    numeroSecu: ayantDroit.numeroSecu,
    source: "AYANT_DROIT",
    lien: ayantDroit.lien,
  };
}

/**
 * Capitalize first letter of each word
 */
function capitalizeFirstLetter(str: string): string {
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Validate date format (YYYY-MM-DD)
 */
function validateDate(dateString: string): boolean {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) {
    return false;
  }

  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * Main function to process carte vitale XML data
 */
export function processCarteVitaleData(xmlData: string): CarteVitaleProcessingResult {
  try {
    // Parse XML
    const rawData = parseXML(xmlData);

    // Validate beneficiaire data
    const beneficiaire = rawData.carteVitale.beneficiaire;
    if (!beneficiaire.nom || !beneficiaire.prenom || !beneficiaire.dateNaissance) {
      throw new Error("Missing required beneficiaire information");
    }

    if (!validateDate(beneficiaire.dateNaissance)) {
      throw new Error("Invalid beneficiaire birth date format");
    }

    // Convert beneficiaire
    const beneficiairePatientData = convertBeneficiaireToPatientData(beneficiaire);

    // Convert ayants droit
    const ayantsDroitPatientData: CarteVitalePatientData[] = [];
    if (rawData.carteVitale.ayantsDroit) {
      const ayantsDroitArray = Array.isArray(rawData.carteVitale.ayantsDroit.ayantDroit)
        ? rawData.carteVitale.ayantsDroit.ayantDroit
        : [rawData.carteVitale.ayantsDroit.ayantDroit];

      for (const ayantDroit of ayantsDroitArray) {
        if (!ayantDroit.nom || !ayantDroit.prenom || !ayantDroit.dateNaissance) {
          console.warn("Skipping ayant droit with missing information:", ayantDroit);
          continue;
        }

        if (!validateDate(ayantDroit.dateNaissance)) {
          console.warn("Skipping ayant droit with invalid birth date:", ayantDroit);
          continue;
        }

        ayantsDroitPatientData.push(convertAyantDroitToPatientData(ayantDroit));
      }
    }

    const carteVitaleInfo: CarteVitaleInfo = {
      beneficiaire: beneficiairePatientData,
      ayantsDroit: ayantsDroitPatientData,
      totalPersons: 1 + ayantsDroitPatientData.length,
    };

    return {
      success: true,
      carteVitaleInfo,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Unknown error occurred while processing carte vitale data",
    };
  }
}
