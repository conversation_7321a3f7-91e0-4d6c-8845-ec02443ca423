import { Patient } from "../models/types";
import { CarteVitalePatientData, PatientMatchResult } from "../types/CarteVitaleTypes";

/**
 * Calculate similarity between two strings (0-1, where 1 is identical)
 */
function stringSimilarity(str1: string, str2: string): number {
  if (str1 === str2) return 1;
  
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substitution
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j] + 1      // deletion
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * Normalize string for comparison (remove accents, convert to uppercase, etc.)
 */
function normalizeString(str: string): string {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove accents
    .toUpperCase()
    .trim();
}

/**
 * Check if two dates are the same
 */
function datesMatch(date1: string, date2: string): boolean {
  // Normalize dates to YYYY-MM-DD format
  const normalizeDate = (date: string): string => {
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    return d.toISOString().split('T')[0];
  };
  
  return normalizeDate(date1) === normalizeDate(date2);
}

/**
 * Calculate match score between carte vitale data and existing patient
 */
function calculateMatchScore(
  carteVitaleData: CarteVitalePatientData,
  existingPatient: Patient
): { score: number; reasons: string[] } {
  const reasons: string[] = [];
  let score = 0;
  
  // Name matching (40% of total score)
  const nomSimilarity = stringSimilarity(
    normalizeString(carteVitaleData.nom),
    normalizeString(existingPatient.nom)
  );
  const prenomSimilarity = stringSimilarity(
    normalizeString(carteVitaleData.prenom),
    normalizeString(existingPatient.prenom)
  );
  
  const nameScore = (nomSimilarity + prenomSimilarity) / 2;
  score += nameScore * 40;
  
  if (nomSimilarity > 0.8) reasons.push('Nom similaire');
  if (prenomSimilarity > 0.8) reasons.push('Prénom similaire');
  
  // Birth date matching (40% of total score)
  if (datesMatch(carteVitaleData.dob, existingPatient.dob)) {
    score += 40;
    reasons.push('Date de naissance identique');
  } else {
    // Partial credit for close dates
    const date1 = new Date(carteVitaleData.dob);
    const date2 = new Date(existingPatient.dob);
    const daysDiff = Math.abs((date1.getTime() - date2.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff <= 1) {
      score += 30;
      reasons.push('Date de naissance très proche');
    } else if (daysDiff <= 7) {
      score += 20;
      reasons.push('Date de naissance proche');
    }
  }
  
  // Sex matching (10% of total score)
  if (carteVitaleData.sexe === existingPatient.sexe) {
    score += 10;
    reasons.push('Sexe identique');
  }
  
  // Birth name matching (10% of total score)
  if (carteVitaleData.nom_naissance && existingPatient.nom_naissance) {
    const nomNaissanceSimilarity = stringSimilarity(
      normalizeString(carteVitaleData.nom_naissance),
      normalizeString(existingPatient.nom_naissance)
    );
    
    score += nomNaissanceSimilarity * 10;
    
    if (nomNaissanceSimilarity > 0.8) {
      reasons.push('Nom de naissance similaire');
    }
  }
  
  return { score: Math.round(score), reasons };
}

/**
 * Find potential matches for carte vitale data among existing patients
 */
export async function findPatientMatches(
  carteVitaleData: CarteVitalePatientData[],
  searchPatients: (query: string) => Promise<{ results: Patient[] }>
): Promise<PatientMatchResult[]> {
  const matches: PatientMatchResult[] = [];
  
  for (const patientData of carteVitaleData) {
    // Search for patients with similar names
    const searchQuery = `${patientData.prenom} ${patientData.nom}`;
    
    try {
      const searchResults = await searchPatients(searchQuery);
      
      let bestMatch: PatientMatchResult['existingPatient'] | undefined;
      let bestScore = 0;
      
      for (const existingPatient of searchResults.results) {
        const { score, reasons } = calculateMatchScore(patientData, existingPatient);
        
        // Consider it a potential match if score is above 60%
        if (score > 60 && score > bestScore) {
          bestScore = score;
          bestMatch = {
            id: existingPatient.id,
            nom: existingPatient.nom,
            prenom: existingPatient.prenom,
            dob: existingPatient.dob,
            matchScore: score,
            matchReasons: reasons,
          };
        }
      }
      
      const matchResult: PatientMatchResult = {
        patientData,
        existingPatient: bestMatch,
        action: bestMatch && bestMatch.matchScore > 80 ? 'UPDATE' : 'CREATE',
      };
      
      // If we found a very strong match (>90%), suggest confirmation
      if (bestMatch && bestMatch.matchScore > 90) {
        matchResult.action = 'CONFIRM';
      }
      
      matches.push(matchResult);
    } catch (error) {
      console.error('Error searching for patient matches:', error);
      // If search fails, default to CREATE
      matches.push({
        patientData,
        action: 'CREATE',
      });
    }
  }
  
  return matches;
}

/**
 * Simple patient matching for basic name/date comparison
 */
export function quickPatientMatch(
  carteVitaleData: CarteVitalePatientData,
  existingPatients: Patient[]
): Patient | null {
  for (const patient of existingPatients) {
    const { score } = calculateMatchScore(carteVitaleData, patient);
    
    // Return first patient with high confidence match
    if (score > 85) {
      return patient;
    }
  }
  
  return null;
}
