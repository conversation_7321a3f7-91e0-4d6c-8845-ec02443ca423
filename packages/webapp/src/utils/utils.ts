import React from "react";
import { useLocation } from "react-router-dom";

import { EtatConsultationEnum, Patient, User } from "../models/types";

export function ISODateToAge(date: string): string {
  // Calculate the age of a person given their date of birth format YYYY-MM-DD
  // const today = new Date();
  // const birthDate = new Date(date);
  // let age = today.getFullYear() - birthDate.getFullYear();
  // const monthDifference = today.getMonth() - birthDate.getMonth();

  // if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
  //   age--;
  // }
  // console.log(age);
  // return age;

  //   const today = new Date();
  //   const birthDate = new Date(date);
  //   // Calculate the difference in time (in milliseconds)
  //   const timeDifference = today.getTime() - birthDate.getTime();

  //   // Convert the time difference into years
  //   const ageInYears = timeDifference / (1000 * 60 * 60 * 24 * 365.25); // 365.25 days per year

  //   return parseFloat(ageInYears.toFixed(1)); // Return age with 2 decimal precision
  // }

  // Split the date string into year and month
  const [year, month] = date.split("-");

  // Get the current year and month
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1; // months are 0-indexed in JavaScript

  // Calculate the difference in years and months
  let ageYears = currentYear - Number(year);
  let ageMonths = currentMonth - Number(month);

  // If the age in months is negative, subtract 1 from the age in years and add 12 to the age in months
  if (ageMonths < 0) {
    ageYears -= 1;
    ageMonths += 12;
  }

  // Return the age in years and months as a string
  return `${ageYears ? `${ageYears}an${ageYears > 1 ? `s` : ""}` : ""} ${
    ageMonths ? `${ageMonths}mois` : ""
  }`;
}

export function formatDateTime(date?: string | null): string {
  // Format an ISO date to a more readable format
  if (!date) return "";
  const d = new Date(date);
  return d.toLocaleDateString("fr-FR", {
    day: "numeric",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

export function formatDate(date?: string | null): string {
  // Format an ISO date to a more readable format
  if (!date) return "";
  const d = new Date(date);
  return d.toLocaleDateString("fr-FR", {
    day: "numeric",
    month: "short",
    year: "numeric",
  });
}

export function formatHour(hour?: string | null): string {
  // hour is formtted as "HH:MM:SS" convert to "HH:MM"
  if (!hour) return "";
  return hour.slice(0, 5);
}

export function isToday(dateStr: string): boolean {
  const today = new Date();
  const date = new Date(dateStr);

  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  );
}

export function formatNameAndSurname(name?: string | null, surname?: string | null): string {
  // Format the name and surname of a person
  return `${name ? name.toUpperCase() : ""} ${surname ? surname.slice(0, 1).toUpperCase() + surname.slice(1).toLowerCase() : ""}`;
}

export function sexeToGender(sexe: string): string {
  // convert MALE to "Homme" and FEMALE to "Femme"
  if (sexe.toLowerCase() === "male") {
    return "Homme";
  } else if (sexe.toLowerCase() === "female") {
    return "Femme";
  } else {
    return "Sexe inconnu";
  }
}

export function CurrentTime() {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");

  return `${hours}:${minutes}`;
}

export function useQuery() {
  const { search } = useLocation();

  return React.useMemo(() => new URLSearchParams(search), [search]);
}

export function isMacintosh() {
  return navigator.platform.indexOf("Mac") > -1;
}

export function isWindows() {
  return navigator.platform.indexOf("Win") > -1;
}

export function niceFormattingOfName(patient: Patient): string {
  return `${patient.nom.toUpperCase()} ${patient.prenom.slice(0, 1).toUpperCase() + patient.prenom.slice(1).toLowerCase()}`;
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + "[...]";
}

export function roundTo2Decimals(num: number): number {
  return Math.round(num * 100) / 100;
}

export function formatNumbersToFrench(num: number): string {
  return num.toLocaleString("fr-FR", {
    minimumFractionDigits: 2, // Ensures at least two decimal places
    maximumFractionDigits: 2, // Limits to two decimal places
  });
}

export const capitalizeFirstLetter = (text: string): string =>
  text ? text[0].toUpperCase() + text.slice(1) : text;

export const displayEtatConsultation = (etat?: EtatConsultationEnum) => {
  switch (etat) {
    case "a_venir":
      return "A venir";
    case "arrive":
      return "Arrivé";
    case "debutee":
      return "A Debutée";
    case "finie":
      return "Finie";
    case "non_venu":
      return "Non venu";
    case "annule":
      return "Annulé";
    default:
      return "Pas de statut";
  }
};

export const GROUP_MAP = {
  DOCTOR: 1,
};

/**
 * Returns the correct file extension for a given audio blob MIME type.
 * Handles MIME types with codecs (e.g. 'audio/webm;codecs=opus').
 * @param mimeType The MIME type of the audio blob (e.g. 'audio/webm;codecs=opus')
 * @returns The corresponding file extension (e.g. 'webm')
 */
export function getAudioExtension(mimeType: string): string {
  if (!mimeType || mimeType === "" || mimeType === "application/octet-stream") {
    // Fallback: la plupart des navigateurs utilisent webm si rien n'est spécifié
    return "webm";
  }
  if (mimeType.startsWith("audio/webm")) return "webm";
  if (mimeType.startsWith("audio/ogg")) return "ogg";
  if (mimeType.startsWith("audio/mp4")) return "mp4";
  if (mimeType.startsWith("audio/x-m4a")) return "m4a";
  if (mimeType.startsWith("audio/m4a")) return "m4a";
  if (mimeType.startsWith("audio/aac")) return "aac";
  if (mimeType.startsWith("audio/wav")) return "wav";
  if (mimeType.startsWith("audio/mpeg")) return "mp3";
  return "blob";
}

export const isUserDoctor = (user: User) => {
  return user.groups?.includes(GROUP_MAP.DOCTOR);
};

// Centralized mapping for Checked Identity choices
export const identityChoices: Record<string, string> = {
  NONE: "Identité non vérifiée",
  FR_ID: "Carte Nationale d'Identité Française",
  EU_ID: "Carte d'Identité (UE/EEE/CH/UK/Microétats)",
  PASSPORT: "Passeport",
  SEJOUR: "Titre de Séjour",
  FRANCE_IDENTITE: "France Identité",
  LAPOSTE: "Identité Numérique La Poste",
  VITALE_APP: "Application Carte Vitale",
  ATTESTATION: "Attestation du professionnel",
};

export const attestationText =
  "J'atteste déjà connaître l'identité de cette personne et sa correspondance aux traits d'identité INSi. Cela me permet de partager ses données en toute sécurité";

export const convertDepthObjToZkfString = (obj: any): any => {
  const newObj = { ...obj };
  Object.keys(newObj).forEach((key) => {
    if (
      key.startsWith("zkf_") &&
      newObj[key] &&
      typeof newObj[key] === "object" &&
      "id" in newObj[key]
    ) {
      newObj[key] = newObj[key].id;
    }
  });
  return newObj;
};
