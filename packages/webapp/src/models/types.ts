import { components } from "./ts";

export type AffichageServiceEnum = components["schemas"]["AffichageServiceEnum"];
export type AffichageServiceStatusEnum = components["schemas"]["AffichageServiceStatusEnum"];
export type AuthToken = components["schemas"]["AuthToken"];
export type BlankEnum = components["schemas"]["BlankEnum"];
export type EtatEnum = components["schemas"]["EtatEnum"];
export type Hospitalisation = components["schemas"]["Hospitalisation"];
export type ModeEntreeEnum = components["schemas"]["ModeEntreeEnum"];
export type NullEnum = components["schemas"]["NullEnum"];
export type Operation = components["schemas"]["Operation"];
export type PaginatedHospitalisationList = components["schemas"]["PaginatedHospitalisationList"];
export type PaginatedConsultationList = components["schemas"]["PaginatedConsultationList"];
export type PaginatedPatientList = components["schemas"]["PaginatedPatientList"];
export type PaginatedUserList = components["schemas"]["PaginatedUserList"];
export type PatchedHospitalisation = components["schemas"]["PatchedHospitalisation"];
export type PatchedOperation = components["schemas"]["PatchedOperation"];
export type PatchedPatient = components["schemas"]["PatchedPatient"];
export type PatchedEmail = components["schemas"]["PatchedEmail"];
export type PatchedTelephone = components["schemas"]["PatchedTelephone"];
export type PatchedAdresse = components["schemas"]["PatchedAdresse"];
export type PatchedConsultation = components["schemas"]["PatchedConsultation"];
export type Patient = components["schemas"]["Patient"];
export type PreferenceUserSerialyzer = components["schemas"]["PreferenceUserSerialyzer"];
export type SexeEnum = components["schemas"]["SexeEnum"];
export type StatutCrhEnum = components["schemas"]["StatutCrhEnum"];
export type UrgenceEnum = components["schemas"]["UrgenceEnum"];
export type User = components["schemas"]["User"];
export type UserSites = components["schemas"]["UserSites"];
export type UserSitesLieux = components["schemas"]["UserSitesLieux"];
export type ModeConsultationEnum = components["schemas"]["ModeConsultationEnum"];
export type Consultation = components["schemas"]["Consultation"];
export type StatutCourrierConsultationEnum =
  components["schemas"]["StatutCourrierConsultationEnum"];
export type Specialite = components["schemas"]["Specialite"];
export type TypeVacationEnum = components["schemas"]["TypeVacationEnum"];
export type Vacation = components["schemas"]["Vacation"];
export type RepetitionSemaineEnum = components["schemas"]["RepetitionSemaineEnum"];
// export type AgendaEvent = components["schemas"]["Evenement"];
export type Notification = components["schemas"]["Notification"];
export type NfTypeEnum = components["schemas"]["NfTypeEnum"];
export type UserEvent = components["schemas"]["UserEvent"];
export type PaginatedUserEventList = components["schemas"]["PaginatedUserEventList"];
export type UserEventCategory = components["schemas"]["UserEventCategory"];
// export type Absence = components["schemas"]["Absence"];
export type Protocole = components["schemas"]["Protocole"];
export type Ordonnance = components["schemas"]["Ordonnance"];
export type Acte = components["schemas"]["Acte"];
export type OperationDevisCcam = components["schemas"]["OperationDevisCcam"];
export type PaginatedDevisList = components["schemas"]["PaginatedDevisList"];
export type Devis = components["schemas"]["Devis"];
export type Intervenant = components["schemas"]["Intervenant"];
export type OrdonnanceTemplate = components["schemas"]["OrdonnanceTemplate"];
export type Contact = components["schemas"]["Contact"];
export type Email = components["schemas"]["Email"];
export type Telephone = components["schemas"]["Telephone"];
export type Adresse = components["schemas"]["Adresse"];
export type CompteRendu = components["schemas"]["CompteRendu"] & {
  // // Champs supplémentaires retournés par l'API mais non définis dans le schéma OpenAPI
  // auteur_nom?: string;
  // patient_nom?: string;
  contact_not_found?: string;
  // specialites?: Specialite[];
};
export type TypeCompteRenduEnum = components["schemas"]["TypeCompteRenduEnum"];
export type CalendarItem = components["schemas"]["AllCalendarItems"];
export type ContactTitreEnum = components["schemas"]["ContactTitreEnum"];
export type Ville = components["schemas"]["Ville"];
export type Profile = components["schemas"]["ProfileUserSerialyzer"];
export type DocumentLibreTemplate = components["schemas"]["DocumentLibreTemplate"];
export type DocumentLibre = components["schemas"]["DocumentLibre"];
export type CategorieOrdonnance = components["schemas"]["CategorieOrdonnance"];
export type MatrielIrradiation = components["schemas"]["MatrielIrradiation"];
export type PdsUniteEnum = components["schemas"]["PdsUniteEnum"];
export type ConsignesSortie = components["schemas"]["ConsignesSortie"];
export type CroType = components["schemas"]["CroType"];
export type ConsultationType = components["schemas"]["ConsultationType"];
export type R_activite = components["schemas"]["R_activite"];
export type EnteteDocument = components["schemas"]["EnteteDocument"];
export type FullTag = components["schemas"]["FullTag"];
export type Irradiation = components["schemas"]["Irradiation"];
export type Contraste = components["schemas"]["Contraste"];
export type CouvertureMaladie = components["schemas"]["CouvertureMaladie"];
export type EtatConsultationEnum = components["schemas"]["EtatConsultationEnum"];
export type Suivi = components["schemas"]["Suivi"];
export type PatchedSuivi = components["schemas"]["PatchedSuivi"];
export type R_menu = components["schemas"]["R_menu"];
export type R_activite_modificateur = components["schemas"]["R_activite_modificateur"];
export type R_tb_23 = components["schemas"]["R_tb_23"];
export type AuditLog = components["schemas"]["AuditLog"];
export type PaginatedAuditLogList = components["schemas"]["PaginatedAuditLogList"];
export type AudioRecording = components["schemas"]["AudioRecording"];
export type Group = components["schemas"]["Group"];
export type CorrespondantPatient = components["schemas"]["CorrespondantPatient"];
