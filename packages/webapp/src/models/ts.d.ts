/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  "/api/agenda/day-events-pdf/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_day_events_pdf_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/agenda/items/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_items_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/agenda/leave/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_leave_list"];
    put?: never;
    post: operations["api_agenda_leave_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/agenda/leave/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_leave_retrieve"];
    put: operations["api_agenda_leave_update"];
    post?: never;
    delete: operations["api_agenda_leave_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_agenda_leave_partial_update"];
    trace?: never;
  };
  "/api/agenda/user-event/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_user_event_list"];
    put?: never;
    post: operations["api_agenda_user_event_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/agenda/user-event-category/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_user_event_category_list"];
    put?: never;
    post: operations["api_agenda_user_event_category_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/agenda/user-event/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_user_event_retrieve"];
    put: operations["api_agenda_user_event_update"];
    post?: never;
    delete: operations["api_agenda_user_event_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_agenda_user_event_partial_update"];
    trace?: never;
  };
  "/api/agenda/vacation/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_vacation_list"];
    put?: never;
    post: operations["api_agenda_vacation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/agenda/vacation/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_agenda_vacation_retrieve"];
    put: operations["api_agenda_vacation_update"];
    post?: never;
    delete: operations["api_agenda_vacation_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_agenda_vacation_partial_update"];
    trace?: never;
  };
  "/api/antecedent/{patient_id}/{specialite}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_antecedent_retrieve"];
    put: operations["api_antecedent_update"];
    post?: never;
    delete: operations["api_antecedent_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_antecedent_partial_update"];
    trace?: never;
  };
  "/api/auditlog/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description List all AuditLog entries. */
    get: operations["api_auditlog_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/auditlog/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Retrieve a single AuditLog entry. */
    get: operations["api_auditlog_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/acte": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_acte_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/acte/{acte}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a Dictionary of objects from multiple models/serializers.
     *
     *     Mixin is expecting the view will have a querylist variable, which is
     *     a list/tuple of dicts containing, at mininum, a `queryset` key and a
     *     `serializer_class` key,  as below:
     *
     *     queryList = [
     *         {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
     *         {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
     *         {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
     *         ...
     *     ]
     *
     *     This mixin returns a dictionary of serialized data separated by object type, e.g.:
     *
     *     {
     *         'MyModelA': [
     *             { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
     *             { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
     *             ...
     *         ],
     *         'MyModelB': [
     *             { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
     *             ...
     *         ]
     *         ...
     *     } */
    get: operations["api_ccam_acte_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/menu": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_menu_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/menu/{menu_id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_menu_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/modificateur": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a Dictionary of objects from multiple models/serializers.
     *
     *     Mixin is expecting the view will have a querylist variable, which is
     *     a list/tuple of dicts containing, at mininum, a `queryset` key and a
     *     `serializer_class` key,  as below:
     *
     *     queryList = [
     *         {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
     *         {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
     *         {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
     *         ...
     *     ]
     *
     *     This mixin returns a dictionary of serialized data separated by object type, e.g.:
     *
     *     {
     *         'MyModelA': [
     *             { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
     *             { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
     *             ...
     *         ],
     *         'MyModelB': [
     *             { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
     *             ...
     *         ]
     *         ...
     *     } */
    get: operations["api_ccam_modificateur_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/modificateur-list": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_modificateur_list_list"];
    put?: never;
    post: operations["api_ccam_modificateur_list_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/r_activite": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_r_activite_list"];
    put?: never;
    post: operations["api_ccam_r_activite_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/r_tb_23": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_r_tb_23_list"];
    put?: never;
    post: operations["api_ccam_r_tb_23_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/search": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_search_list"];
    put?: never;
    post: operations["api_ccam_search_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ccam/suggested-actes": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ccam_suggested_actes_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/comptabilite/avoir/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_avoir_list"];
    put?: never;
    post: operations["api_comptabilite_avoir_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/comptabilite/avoir/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_avoir_retrieve"];
    put: operations["api_comptabilite_avoir_update"];
    post?: never;
    delete: operations["api_comptabilite_avoir_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_comptabilite_avoir_partial_update"];
    trace?: never;
  };
  "/api/comptabilite/code": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_code_list"];
    put?: never;
    post: operations["api_comptabilite_code_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/comptabilite/code/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_code_retrieve"];
    put: operations["api_comptabilite_code_update"];
    post?: never;
    delete: operations["api_comptabilite_code_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_comptabilite_code_partial_update"];
    trace?: never;
  };
  "/api/comptabilite/devis/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_devis_list"];
    put?: never;
    post: operations["api_comptabilite_devis_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/comptabilite/devis/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_devis_retrieve"];
    put: operations["api_comptabilite_devis_update"];
    post?: never;
    delete: operations["api_comptabilite_devis_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_comptabilite_devis_partial_update"];
    trace?: never;
  };
  "/api/comptabilite/facture/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_facture_list"];
    put?: never;
    post: operations["api_comptabilite_facture_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/comptabilite/facture/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_facture_retrieve"];
    put: operations["api_comptabilite_facture_update"];
    post?: never;
    delete: operations["api_comptabilite_facture_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_comptabilite_facture_partial_update"];
    trace?: never;
  };
  "/api/comptabilite/groupe-facturation/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_groupe_facturation_list"];
    put?: never;
    post: operations["api_comptabilite_groupe_facturation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/comptabilite/groupe-facturation/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_groupe_facturation_retrieve"];
    put: operations["api_comptabilite_groupe_facturation_update"];
    post?: never;
    delete: operations["api_comptabilite_groupe_facturation_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_comptabilite_groupe_facturation_partial_update"];
    trace?: never;
  };
  "/api/comptabilite/relance-facture/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_relance_facture_list"];
    put?: never;
    post: operations["api_comptabilite_relance_facture_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/comptabilite/relance-facture/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_comptabilite_relance_facture_retrieve"];
    put: operations["api_comptabilite_relance_facture_update"];
    post?: never;
    delete: operations["api_comptabilite_relance_facture_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_comptabilite_relance_facture_partial_update"];
    trace?: never;
  };
  "/api/compte-rendu/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_compte_rendu_list"];
    put?: never;
    post: operations["api_compte_rendu_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/compte-rendu/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_compte_rendu_retrieve"];
    put: operations["api_compte_rendu_update"];
    post?: never;
    delete: operations["api_compte_rendu_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_compte_rendu_partial_update"];
    trace?: never;
  };
  "/api/compte-rendu/duplicate": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Check for duplicate CompteRenduCorrespondant entries based on the provided zkf_patient, auteur and date_examen. */
    post: operations["api_compte_rendu_duplicate_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/consultation/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_consultation_list"];
    put?: never;
    post: operations["api_consultation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/consultation/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_consultation_retrieve"];
    put: operations["api_consultation_update"];
    post?: never;
    delete: operations["api_consultation_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_consultation_partial_update"];
    trace?: never;
  };
  "/api/consultation/consultation-type/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_consultation_consultation_type_list"];
    put?: never;
    post: operations["api_consultation_consultation_type_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/consultation/consultation-type/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_consultation_consultation_type_retrieve"];
    put: operations["api_consultation_consultation_type_update"];
    post?: never;
    delete: operations["api_consultation_consultation_type_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_consultation_consultation_type_partial_update"];
    trace?: never;
  };
  "/api/consultation/generate-consultation-type": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_consultation_generate_consultation_type_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/consultation/recent-consultations/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Vue pour lister les consultations récentes, tous patients confondus.
     *     Supporte le tri (par défaut par date de consultation décroissante) et la pagination. */
    get: operations["api_consultation_recent_consultations_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/consultation/suggested-consultations": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_consultation_suggested_consultations_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/contact/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_list"];
    put?: never;
    post: operations["api_contact_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/contact/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_retrieve"];
    put: operations["api_contact_update"];
    post?: never;
    delete: operations["api_contact_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_contact_partial_update"];
    trace?: never;
  };
  "/api/contact/adresse": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_adresse_list"];
    put?: never;
    post: operations["api_contact_adresse_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/contact/adresse/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_adresse_retrieve"];
    put: operations["api_contact_adresse_update"];
    post?: never;
    delete: operations["api_contact_adresse_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_contact_adresse_partial_update"];
    trace?: never;
  };
  "/api/contact/correspondant": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_correspondant_list"];
    put?: never;
    post: operations["api_contact_correspondant_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/contact/correspondant-patient": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_correspondant_patient_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/contact/correspondant/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_correspondant_retrieve"];
    put: operations["api_contact_correspondant_update"];
    post?: never;
    delete: operations["api_contact_correspondant_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_contact_correspondant_partial_update"];
    trace?: never;
  };
  "/api/contact/email": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_email_list"];
    put?: never;
    post: operations["api_contact_email_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/contact/email/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_email_retrieve"];
    put: operations["api_contact_email_update"];
    post?: never;
    delete: operations["api_contact_email_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_contact_email_partial_update"];
    trace?: never;
  };
  "/api/contact/telephone": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_telephone_list"];
    put?: never;
    post: operations["api_contact_telephone_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/contact/telephone/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_telephone_retrieve"];
    put: operations["api_contact_telephone_update"];
    post?: never;
    delete: operations["api_contact_telephone_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_contact_telephone_partial_update"];
    trace?: never;
  };
  "/api/contact/ville": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_contact_ville_list"];
    put?: never;
    post: operations["api_contact_ville_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/document-libre/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_document_libre_list"];
    put?: never;
    post: operations["api_document_libre_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/document-libre/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_document_libre_retrieve"];
    put: operations["api_document_libre_update"];
    post?: never;
    delete: operations["api_document_libre_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_document_libre_partial_update"];
    trace?: never;
  };
  "/api/document-libre/template": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_document_libre_template_list"];
    put?: never;
    post: operations["api_document_libre_template_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/document-libre/template/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_document_libre_template_retrieve"];
    put: operations["api_document_libre_template_update"];
    post?: never;
    delete: operations["api_document_libre_template_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_document_libre_template_partial_update"];
    trace?: never;
  };
  "/api/hospitalisation/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_list"];
    put?: never;
    post: operations["api_hospitalisation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/hospitalisation/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_retrieve"];
    put: operations["api_hospitalisation_update"];
    post?: never;
    delete: operations["api_hospitalisation_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_hospitalisation_partial_update"];
    trace?: never;
  };
  "/api/hospitalisation/{id}/generate-crh": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_generate_crh_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/hospitalisation/{id}/suivi": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_suivi_list"];
    put?: never;
    post: operations["api_hospitalisation_suivi_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/hospitalisation/{id}/suivi/{suivi_pk}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_suivi_retrieve"];
    put: operations["api_hospitalisation_suivi_update"];
    post?: never;
    delete: operations["api_hospitalisation_suivi_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_hospitalisation_suivi_partial_update"];
    trace?: never;
  };
  "/api/hospitalisation/{zkf_patient}/create": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a Dictionary of objects from multiple models/serializers.
     *
     *     Mixin is expecting the view will have a querylist variable, which is
     *     a list/tuple of dicts containing, at mininum, a `queryset` key and a
     *     `serializer_class` key,  as below:
     *
     *     queryList = [
     *         {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
     *         {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
     *         {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
     *         ...
     *     ]
     *
     *     This mixin returns a dictionary of serialized data separated by object type, e.g.:
     *
     *     {
     *         'MyModelA': [
     *             { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
     *             { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
     *             ...
     *         ],
     *         'MyModelB': [
     *             { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
     *             ...
     *         ]
     *         ...
     *     } */
    get: operations["api_hospitalisation_create_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/hospitalisation/consigne-sortie/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_consigne_sortie_list"];
    put?: never;
    post: operations["api_hospitalisation_consigne_sortie_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/hospitalisation/consigne-sortie/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_consigne_sortie_retrieve"];
    put: operations["api_hospitalisation_consigne_sortie_update"];
    post?: never;
    delete: operations["api_hospitalisation_consigne_sortie_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_hospitalisation_consigne_sortie_partial_update"];
    trace?: never;
  };
  "/api/hospitalisation/generate-consigne-sortie": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_generate_consigne_sortie_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/hospitalisation/service": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_hospitalisation_service_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/intervenant/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a Dictionary of objects from multiple models/serializers.
     *
     *     Mixin is expecting the view will have a querylist variable, which is
     *     a list/tuple of dicts containing, at mininum, a `queryset` key and a
     *     `serializer_class` key,  as below:
     *
     *     queryList = [
     *         {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
     *         {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
     *         {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
     *         ...
     *     ]
     *
     *     This mixin returns a dictionary of serialized data separated by object type, e.g.:
     *
     *     {
     *         'MyModelA': [
     *             { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
     *             { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
     *             ...
     *         ],
     *         'MyModelB': [
     *             { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
     *             ...
     *         ]
     *         ...
     *     } */
    get: operations["api_intervenant_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/media/{url}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Serve a file from the media directory */
    get: operations["api_media_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/notification/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_notification_list"];
    put?: never;
    post: operations["api_notification_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/notification/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_notification_retrieve"];
    put: operations["api_notification_update"];
    post?: never;
    delete: operations["api_notification_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_notification_partial_update"];
    trace?: never;
  };
  "/api/notification/read": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Delete all notifications read for the user
     *     # */
    post: operations["api_notification_read_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_list"];
    put?: never;
    post: operations["api_operation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation-templates/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_templates_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation-templates/{specialite}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_templates_retrieve_2"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation-templates/{specialite}/{template_name}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_templates_retrieve_3"];
    put?: never;
    post: operations["api_operation_templates_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation-templates/{specialite}/{template_name}/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_templates_retrieve_4"];
    put: operations["api_operation_templates_update"];
    post?: never;
    delete: operations["api_operation_templates_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_operation_templates_partial_update"];
    trace?: never;
  };
  "/api/operation/{operation_pk}/{cro_type_pk}/generate-cro": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_generate_cro_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/{operation_pk}/peri-op": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a Dictionary of objects from multiple models/serializers.
     *
     *     Mixin is expecting the view will have a querylist variable, which is
     *     a list/tuple of dicts containing, at mininum, a `queryset` key and a
     *     `serializer_class` key,  as below:
     *
     *     queryList = [
     *         {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
     *         {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
     *         {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
     *         ...
     *     ]
     *
     *     This mixin returns a dictionary of serialized data separated by object type, e.g.:
     *
     *     {
     *         'MyModelA': [
     *             { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
     *             { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
     *             ...
     *         ],
     *         'MyModelB': [
     *             { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
     *             ...
     *         ]
     *         ...
     *     } */
    get: operations["api_operation_peri_op_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_retrieve"];
    put: operations["api_operation_update"];
    post?: never;
    delete: operations["api_operation_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_operation_partial_update"];
    trace?: never;
  };
  "/api/operation/anesthesie-type": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_anesthesie_type_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/appareil-scopie": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_appareil_scopie_list"];
    put?: never;
    post: operations["api_operation_appareil_scopie_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/appareil-scopie/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_appareil_scopie_retrieve"];
    put: operations["api_operation_appareil_scopie_update"];
    post?: never;
    delete: operations["api_operation_appareil_scopie_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_operation_appareil_scopie_partial_update"];
    trace?: never;
  };
  "/api/operation/complications-post-op": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_complications_post_op_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/complications-post-op/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_complications_post_op_retrieve_2"];
    put: operations["api_operation_complications_post_op_update"];
    post?: never;
    delete: operations["api_operation_complications_post_op_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_operation_complications_post_op_partial_update"];
    trace?: never;
  };
  "/api/operation/contrast": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_contrast_list"];
    put?: never;
    post: operations["api_operation_contrast_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/contrast/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_contrast_retrieve"];
    put: operations["api_operation_contrast_update"];
    post?: never;
    delete: operations["api_operation_contrast_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_operation_contrast_partial_update"];
    trace?: never;
  };
  "/api/operation/cro-type": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_cro_type_list"];
    put?: never;
    post: operations["api_operation_cro_type_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/cro-type/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_cro_type_retrieve"];
    put: operations["api_operation_cro_type_update"];
    post?: never;
    delete: operations["api_operation_cro_type_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_operation_cro_type_partial_update"];
    trace?: never;
  };
  "/api/operation/irradiation": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_irradiation_list"];
    put?: never;
    post: operations["api_operation_irradiation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/irradiation/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_irradiation_retrieve"];
    put: operations["api_operation_irradiation_update"];
    post?: never;
    delete: operations["api_operation_irradiation_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_operation_irradiation_partial_update"];
    trace?: never;
  };
  "/api/operation/list-fields": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_list_fields_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/operation/operation-model-fields": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_operation_operation_model_fields_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ordonnance/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ordonnance_list"];
    put?: never;
    post: operations["api_ordonnance_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ordonnance/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ordonnance_retrieve"];
    put: operations["api_ordonnance_update"];
    post?: never;
    delete: operations["api_ordonnance_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_ordonnance_partial_update"];
    trace?: never;
  };
  "/api/ordonnance/categorie": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ordonnance_categorie_list"];
    put?: never;
    post: operations["api_ordonnance_categorie_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ordonnance/template/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ordonnance_template_list"];
    put?: never;
    post: operations["api_ordonnance_template_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/ordonnance/template/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_ordonnance_template_retrieve"];
    put: operations["api_ordonnance_template_update"];
    post?: never;
    delete: operations["api_ordonnance_template_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_ordonnance_template_partial_update"];
    trace?: never;
  };
  "/api/patient/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_patient_list"];
    put?: never;
    post: operations["api_patient_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/patient/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_patient_retrieve"];
    put: operations["api_patient_update"];
    post?: never;
    delete: operations["api_patient_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_patient_partial_update"];
    trace?: never;
  };
  "/api/patient/{id}/consultations/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_patient_consultations_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/patient/{id}/events/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_patient_events_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/patient/{id}/reset_ins/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations["api_patient_reset_ins_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/patient/{id}/verify_ins/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations["api_patient_verify_ins_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/patient/coverage/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_patient_coverage_list"];
    put?: never;
    post: operations["api_patient_coverage_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/patient/model/fields/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_patient_model_fields_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/patient/search/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_patient_search_list"];
    put?: never;
    post: operations["api_patient_search_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/print-manager/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_print_manager_list"];
    put?: never;
    post: operations["api_print_manager_create"];
    delete: operations["api_print_manager_destroy"];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/print-manager/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_print_manager_retrieve"];
    put: operations["api_print_manager_update"];
    post?: never;
    delete: operations["api_print_manager_destroy_2"];
    options?: never;
    head?: never;
    patch: operations["api_print_manager_partial_update"];
    trace?: never;
  };
  "/api/print-manager/compte-rendus/consultation": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_print_manager_compte_rendus_consultation_list"];
    put?: never;
    post: operations["api_print_manager_compte_rendus_consultation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/print-manager/compte-rendus/hospitalisation": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_print_manager_compte_rendus_hospitalisation_list"];
    put?: never;
    post: operations["api_print_manager_compte_rendus_hospitalisation_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/print-manager/print": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_print_manager_print_retrieve"];
    put?: never;
    post: operations["api_print_manager_print_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/print-manager/print-selected": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_print_manager_print_selected_retrieve"];
    put?: never;
    post: operations["api_print_manager_print_selected_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/protocole/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_list"];
    put?: never;
    post: operations["api_protocole_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/protocole/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_retrieve"];
    put: operations["api_protocole_update"];
    post?: never;
    delete: operations["api_protocole_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_protocole_partial_update"];
    trace?: never;
  };
  "/api/protocole/answerformfield/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_answerformfield_list"];
    put?: never;
    post: operations["api_protocole_answerformfield_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/protocole/answerformfield/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_answerformfield_retrieve"];
    put: operations["api_protocole_answerformfield_update"];
    post?: never;
    delete: operations["api_protocole_answerformfield_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_protocole_answerformfield_partial_update"];
    trace?: never;
  };
  "/api/protocole/form/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_form_list"];
    put?: never;
    post: operations["api_protocole_form_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/protocole/form/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_form_retrieve"];
    put: operations["api_protocole_form_update"];
    post?: never;
    delete: operations["api_protocole_form_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_protocole_form_partial_update"];
    trace?: never;
  };
  "/api/protocole/formfield/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_formfield_list"];
    put?: never;
    post: operations["api_protocole_formfield_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/protocole/formfield/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_formfield_retrieve"];
    put: operations["api_protocole_formfield_update"];
    post?: never;
    delete: operations["api_protocole_formfield_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_protocole_formfield_partial_update"];
    trace?: never;
  };
  "/api/protocole/formtemplate/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_formtemplate_list"];
    put?: never;
    post: operations["api_protocole_formtemplate_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/protocole/formtemplates/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_protocole_formtemplates_retrieve"];
    put: operations["api_protocole_formtemplates_update"];
    post?: never;
    delete: operations["api_protocole_formtemplates_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_protocole_formtemplates_partial_update"];
    trace?: never;
  };
  "/api/settings/model-fields": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_settings_model_fields_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/tag/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_tag_list"];
    put?: never;
    post: operations["api_tag_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/token-auth/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations["api_token_auth_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_list"];
    put?: never;
    post: operations["api_user_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_retrieve"];
    put: operations["api_user_update"];
    post?: never;
    delete: operations["api_user_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_user_partial_update"];
    trace?: never;
  };
  "/api/user/{user_id}/intervenant": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_intervenant_retrieve_2"];
    put: operations["api_user_intervenant_update_2"];
    post?: never;
    delete: operations["api_user_intervenant_destroy_2"];
    options?: never;
    head?: never;
    patch: operations["api_user_intervenant_partial_update_2"];
    trace?: never;
  };
  "/api/user/entete-document": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_entete_document_list"];
    put?: never;
    post: operations["api_user_entete_document_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/entete-document/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_entete_document_retrieve"];
    put: operations["api_user_entete_document_update"];
    post?: never;
    delete: operations["api_user_entete_document_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_user_entete_document_partial_update"];
    trace?: never;
  };
  "/api/user/groups": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_groups_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/intervenant": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_intervenant_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/intervenant/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_intervenant_retrieve"];
    put: operations["api_user_intervenant_update"];
    post?: never;
    delete: operations["api_user_intervenant_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_user_intervenant_partial_update"];
    trace?: never;
  };
  "/api/user/intervenants-event": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a Dictionary of objects from multiple models/serializers.
     *
     *     Mixin is expecting the view will have a querylist variable, which is
     *     a list/tuple of dicts containing, at mininum, a `queryset` key and a
     *     `serializer_class` key,  as below:
     *
     *     queryList = [
     *         {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
     *         {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
     *         {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
     *         ...
     *     ]
     *
     *     This mixin returns a dictionary of serialized data separated by object type, e.g.:
     *
     *     {
     *         'MyModelA': [
     *             { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
     *             { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
     *             ...
     *         ],
     *         'MyModelB': [
     *             { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
     *             ...
     *         ]
     *         ...
     *     } */
    get: operations["api_user_intervenants_event_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/linked-users": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_linked_users_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/me": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_me_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/notification-choices/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_notification_choices_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/preferences-user/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_preferences_user_retrieve"];
    put: operations["api_user_preferences_user_update"];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch: operations["api_user_preferences_user_partial_update"];
    trace?: never;
  };
  "/api/user/profile-user/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_profile_user_retrieve"];
    put: operations["api_user_profile_user_update"];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch: operations["api_user_profile_user_partial_update"];
    trace?: never;
  };
  "/api/user/profile/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_profile_retrieve"];
    put: operations["api_user_profile_update"];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch: operations["api_user_profile_partial_update"];
    trace?: never;
  };
  "/api/user/service": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_service_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/service/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_service_retrieve"];
    put: operations["api_user_service_update"];
    post?: never;
    delete: operations["api_user_service_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_user_service_partial_update"];
    trace?: never;
  };
  "/api/user/site": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_site_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/site/{site_id}/lieu": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_site_lieu_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/specialites": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_specialites_list"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/api/user/specialites/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["api_user_specialites_retrieve"];
    put: operations["api_user_specialites_update"];
    post?: never;
    delete: operations["api_user_specialites_destroy"];
    options?: never;
    head?: never;
    patch: operations["api_user_specialites_partial_update"];
    trace?: never;
  };
  "/api/user/users-lies-all": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** @description Create a Dictionary of objects from multiple models/serializers.
     *
     *     Mixin is expecting the view will have a querylist variable, which is
     *     a list/tuple of dicts containing, at mininum, a `queryset` key and a
     *     `serializer_class` key,  as below:
     *
     *     queryList = [
     *         {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
     *         {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
     *         {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
     *         ...
     *     ]
     *
     *     This mixin returns a dictionary of serialized data separated by object type, e.g.:
     *
     *     {
     *         'MyModelA': [
     *             { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
     *             { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
     *             ...
     *         ],
     *         'MyModelB': [
     *             { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
     *             ...
     *         ]
     *         ...
     *     } */
    get: operations["api_user_users_lies_all_retrieve"];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/audio-recording/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["audio_recording_list"];
    put?: never;
    post: operations["audio_recording_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/audio-recording/{id}/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get: operations["audio_recording_retrieve"];
    put: operations["audio_recording_update"];
    post?: never;
    delete: operations["audio_recording_destroy"];
    options?: never;
    head?: never;
    patch: operations["audio_recording_partial_update"];
    trace?: never;
  };
  "/audio-recording/{id}/correct/": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** @description Corrects the medical transcription using Mistral LLM and saves the result. */
    post: operations["audio_recording_correct_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/audio-recording/update-audio/{id}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put: operations["audio_recording_update_audio_update"];
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  "/audio-recording/upload-audio/{patient_pk}": {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post: operations["audio_recording_upload_audio_create"];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    Absence: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date-time */
      date_heure_start: string;
      /** Format: date-time */
      date_heure_end: string;
      motif: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user: number;
    };
    Acte: {
      /** Format: uuid */
      readonly id: string;
      readonly pu_base: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      cod_acte?: string | null;
      /** Format: date */
      dt_modif?: string | null;
      menu_cod?: string | null;
      fraidp_cod?: string | null;
      rembou_cod?: string | null;
      type_cod?: string | null;
      cod_struct?: string | null;
      nom_court?: string | null;
      nom_long?: string | null;
      nom_long0?: string | null;
      nom_long1?: string | null;
      nom_long2?: string | null;
      nom_long3?: string | null;
      nom_long4?: string | null;
      nom_long5?: string | null;
      nom_long6?: string | null;
      nom_long7?: string | null;
      nom_long8?: string | null;
      nom_long9?: string | null;
      nom_longa?: string | null;
      nom_longb?: string | null;
      nom_longc?: string | null;
      nom_longd?: string | null;
      nom_longe?: string | null;
      sexe?: string | null;
      /** Format: date */
      dt_creatio?: string | null;
      /** Format: date */
      dt_fin?: string | null;
      entente?: string | null;
      /** Format: date */
      dt_effet?: string | null;
      /** Format: date */
      dt_arrete?: string | null;
      /** Format: date */
      dt_jo?: string | null;
      mfic_place?: string | null;
      precedent?: string | null;
      suivant?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    /**
     * @description * `0` - create
     *     * `1` - update
     *     * `2` - delete
     *     * `3` - access
     * @enum {integer}
     */
    ActionEnum: 0 | 1 | 2 | 3;
    Adresse: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: string | null;
      structure?: string | null;
      numero_rue?: string | null;
      rue?: string | null;
      complement_adresse?: string | null;
      code_postal: number;
      ville: string;
      editable?: boolean;
      /** @description Mode de communication préféré ? */
      preferred?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
    };
    /**
     * @description * `SPE` - Specialite
     *     * `USE` - User
     * @enum {string}
     */
    AffichageServiceEnum: "SPE" | "USE";
    /**
     * @description * `STA` - Static
     *     * `DYN` - Dynamique
     * @enum {string}
     */
    AffichageServiceStatusEnum: "STA" | "DYN";
    AllCalendarItems: {
      id: string;
      title: string;
      corps: string;
      start: number;
      end: number;
      type: string;
      zkf_user: string;
      zkf_patient?: {
        [key: string]: string;
      };
      text_color?: string;
      background_color?: string;
      resource_id?: string;
      operations?: unknown[];
      event_data?: {
        [key: string]: string;
      };
      /** @default false */
      all_day?: boolean;
    };
    AnswerFormField: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      response: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_protocole: string;
      /** Format: uuid */
      zkf_form_field: string;
      /** Format: uuid */
      form: string;
    };
    AudioRecording: {
      /** Format: uuid */
      readonly id: string;
      /**
       * Format: date-time
       * @description Date of creation.
       */
      readonly created_at: string;
      auteur?: number | null;
      auteur_id?: number;
      /** Format: uuid */
      document?: string | null;
      readonly consultation: components["schemas"]["Consultation"];
      /** Format: uuid */
      consultation_id?: string;
      /** Format: uuid */
      hospitalisation?: string | null;
      readonly patient: components["schemas"]["Patient"];
      /** Format: uuid */
      patient_id?: string;
      /** Format: uri */
      file: string;
      /** @description Title of the audio recording. */
      title?: string | null;
      /** @description Transcription of the audio recording. */
      transcription?: string | null;
      /** @description LLM-corrected medical transcription. */
      transcription_corrected?: string | null;
      /** @description Status of the audio recording validation and lifecycle.
       *
       *     * `unverified` - UNVERIFIED
       *     * `pending_validation` - PENDING_VALIDATION
       *     * `validated` - VALIDATED
       *     * `pending_deletion_validated` - PENDING_DELETION_VALIDATED
       *     * `pending_deletion_inactive` - PENDING_DELETION_INACTIVE
       *     * `pending_deletion_7day_warning_sent` - PENDING_DELETION_7DAY_WARNING_SENT
       *     * `deleted` - DELETED */
      status?: components["schemas"]["StatusEnum"];
    };
    /** @description Serializer for the AuditLog model. */
    AuditLog: {
      readonly id: number;
      readonly actor: components["schemas"]["User"];
      readonly object: string;
      readonly object_pk: string;
      /**
       * Id de l’objet
       * Format: int64
       */
      readonly object_id: number | null;
      /** Object representation */
      readonly object_repr: string;
      readonly serialized_data: unknown;
      readonly action: components["schemas"]["ActionEnum"];
      /** Message de modification */
      readonly changes_text: string;
      /** Message de modification */
      readonly changes: unknown;
      /** Correlation ID */
      readonly cid: string | null;
      /** Remote address */
      readonly remote_addr: string | null;
      readonly remote_port: number | null;
      /** Format: date-time */
      readonly timestamp: string;
      readonly additional_data: unknown;
      readonly actor_email: string | null;
      /** Type de contenu */
      readonly content_type: number;
    };
    AuthToken: {
      /** Nom de l'utilisateur */
      username: string;
      /** Mot de passe */
      password: string;
      /** Jeton */
      readonly token: string;
    };
    Avoir: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_operation?: string | null;
      /** Format: date */
      date_avoir?: string | null;
      numero_avoir?: string | null;
      /** Format: decimal */
      montant?: string | null;
      note?: string | null;
      avoir_valide?: boolean;
      /** Format: date-time */
      date_avoir_validation?: string | null;
      avoir_acquite?: boolean;
      /** Format: date */
      date_avoir_acquite?: string | null;
      mode_reglement?: string | null;
      numero_cheque?: string | null;
      numero_banque_cheque?: string | null;
      iban?: string | null;
      /** Format: date */
      date_depot_cheque?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_patient: string;
      zkf_createur?: number | null;
      /** @description Personne ayant réalisé l'acte */
      zkf_acte_realisateur?: number | null;
      /** Format: uuid */
      zkf_groupe_facturation: string;
      /** Format: uuid */
      zkf_facture?: string | null;
      avoir_validation_user?: number | null;
      /** @description Personnes liées à l'acte (chirurgien, etc...) */
      zkf_acteur_lie?: number[];
    };
    /** @enum {unknown} */
    BlankEnum: "";
    /**
     * @description * `PROFESSIONNEL` - Professionnel
     *     * `PORTABLE` - Portable
     *     * `DOMICILE` - Domicile
     *     * `AUTRE` - Autre
     *     * `FAX` - Fax
     * @enum {string}
     */
    CategorieEnum: "PROFESSIONNEL" | "PORTABLE" | "DOMICILE" | "AUTRE" | "FAX";
    CategorieOrdonnance: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      categorie: string;
      information_patient_ordonnance: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    /**
     * @description * `NONE` - Identité non vérifiée
     *     * `FR_ID` - Carte Nationale d'Identité Française
     *     * `EU_ID` - Carte d'Identité (UE/EEE/CH/UK/Microétats)
     *     * `PASSPORT` - Passeport
     *     * `SEJOUR` - Titre de Séjour
     *     * `FRANCE_IDENTITE` - France Identité
     *     * `LAPOSTE` - Identité Numérique La Poste
     *     * `VITALE_APP` - Application Carte Vitale
     *     * `ATTESTATION` - Attestation du professionnel
     * @enum {string}
     */
    CheckedIdentityEnum:
      | "NONE"
      | "FR_ID"
      | "EU_ID"
      | "PASSPORT"
      | "SEJOUR"
      | "FRANCE_IDENTITE"
      | "LAPOSTE"
      | "VITALE_APP"
      | "ATTESTATION";
    CompteRendu: {
      /** Format: uuid */
      readonly id: string;
      readonly patient_nom: string;
      readonly auteur_nom: string;
      readonly contact_not_found: string;
      should_be_contacted?: boolean;
      readonly specialites: components["schemas"]["Specialite"][];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** @description Nom descriptif du document */
      nom?: string | null;
      type_compte_rendu?: components["schemas"]["TypeCompteRenduEnum"];
      /** Format: date */
      date_examen?: string | null;
      /** Format: date */
      date_entree?: string | null;
      /** Format: date */
      date_sortie?: string | null;
      /** Format: uri */
      examen_file: string;
      status?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_consultation?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      auteur?: string | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      specialite?: string[];
      type_examen?: string[];
    };
    ConsignesSortie: {
      /** Format: uuid */
      readonly id: string;
      readonly suggested: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_consigne?: string | null;
      texte_suivi: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      readonly zkf_user: number;
      /** Format: uuid */
      readonly zkf_environnement: string;
      /** Format: uuid */
      zkf_service?: string | null;
      /** Format: uuid */
      zkf_specialite?: string | null;
      /** Format: uuid */
      zkf_protocole?: string | null;
    };
    Consultation: {
      /** Format: uuid */
      readonly id: string;
      tags?: string[];
      readonly model_app_datas: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      consultation_date?: string | null;
      /** Format: time */
      consultation_arrive?: string | null;
      /** Format: time */
      consultation_debut_prevu?: string | null;
      /** Format: time */
      consultation_fin_prevu?: string | null;
      /** Format: time */
      consultation_debut_reel?: string | null;
      /** Format: time */
      consultation_fin_reel?: string | null;
      premiere_consultation?: boolean;
      mode_consultation?:
        | (
            | components["schemas"]["ModeConsultationEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      motif_consultation?: string | null;
      reminder_consultation?: string | null;
      observation_consultation?: string | null;
      statut_courrier_consultation?: components["schemas"]["StatutCourrierConsultationEnum"];
      courrier_consultation?: string;
      courrier_consultation_bas_page?: string;
      etat_consultation?: components["schemas"]["EtatConsultationEnum"];
      /** Format: decimal */
      depassement_honoraire?: string | null;
      mode_reglement?:
        | (
            | components["schemas"]["ModeReglementEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      /** Format: decimal */
      total_prix_consultation?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient: string;
      /** Format: uuid */
      zkf_site: string;
      /** Format: uuid */
      zkf_lieu?: string | null;
      zkf_redacteur?: number | null;
      /** Format: uuid */
      zkf_statut_consultation?: string | null;
      operation_liee?: string[];
      consultations_type?: string[];
    };
    ConsultationType: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre: string;
      corps?: string | null;
      bas_de_page?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      readonly zkf_environnement: string;
      readonly zkf_user: number | null;
      users?: number[];
      sites?: string[];
      lieus?: string[];
      services?: string[];
    };
    Contact: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom?: string | null;
      prenom?: string | null;
      /** Format: int64 */
      rpps?: number | null;
      sexe?:
        | (
            | components["schemas"]["SexeEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      structure?: string | null;
      titre?: components["schemas"]["ContactTitreEnum"];
      en_activite?: boolean;
      comment?: string | null;
      created_from_annuaire_sante?: boolean;
      /** Format: date-time */
      annuaire_sante_verification?: string | null;
      /** Format: date-time */
      annuaire_sante_last_attempt?: string | null;
      id_annuaire_sante?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      readonly zkf_contactcategorie: string;
      /** Format: uuid */
      readonly zkf_environnement_contact: string;
      specialite?: string[];
    };
    /**
     * @description * `Docteur` - Docteur
     *     * `Professeur` - Professeur
     *     * `Madame` - Madame
     *     * `Monsieur` - Monsieur
     * @enum {string}
     */
    ContactTitreEnum: "Docteur" | "Professeur" | "Madame" | "Monsieur";
    Contraste: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      denomination: string;
      composition?: string | null;
      teneur_iode?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      service: string[];
    };
    Correspondant: {
      /** Format: uuid */
      readonly id: string;
      readonly specialite_string: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: components["schemas"]["CorrespondantTitreEnum"];
      en_activite?: boolean;
      readonly created_by: number | null;
      readonly modified_by: number | null;
      deleted_by?: number | null;
      specialite?: string[];
    };
    CorrespondantPatient: {
      readonly contact: string;
      /** @description Envoyer les rapports/courriers à ce contact */
      send_report?: boolean;
      /** @description Contact adresseur */
      referral_source?: boolean;
    };
    /**
     * @description * `Docteur` - Docteur
     *     * `Professeur` - Professeur
     * @enum {string}
     */
    CorrespondantTitreEnum: "Docteur" | "Professeur";
    CouvertureMaladie: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom: string;
      tiers_payant: boolean;
      couverture: number;
      ordo_bizone: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    CroType: {
      /** Format: uuid */
      readonly id: string;
      readonly suggested: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** @description Titre court pour selection dans la liste */
      cro_type_titre_descriptif?: string | null;
      /** @description Titre du CRO type à inserer dans une operation */
      cro_type_titre?: string | null;
      /** @description Indication opératoire du CRO type à inserer dans une operation */
      cro_type_indication_operatoire?: string | null;
      cro_type?: string | null;
      nom_modele_specialite?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user: number;
    };
    Devis: {
      /** Format: uuid */
      readonly id: string;
      readonly model_app_datas: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_devis?: string;
      /** Format: decimal */
      depassement_honoraire?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_patient: string;
      zkf_createur?: number | null;
      /** Format: uuid */
      zkf_devisfavoris?: string | null;
    };
    DocumentLibre: {
      /** Format: uuid */
      readonly id: string;
      readonly model_app_datas: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_document: string;
      /** Format: date */
      readonly date_document: string;
      corps_document?: string | null;
      /** Format: uri */
      document_libre_file?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient: string;
      /** Format: uuid */
      zkf_consultation?: string | null;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_document_template?: string | null;
      redacteur_document?: number | null;
    };
    DocumentLibreTemplate: {
      /** Format: uuid */
      readonly id: string;
      readonly suggested: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_document: string;
      description_document?: string | null;
      corps_document?: string | null;
      /** Format: uri */
      document_type_file?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user?: number | null;
      /** Format: uuid */
      zkf_environnement?: string;
      specialite?: string[];
      service?: string[];
    };
    Email: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /**
       * Format: email
       * @description Entrer l'email
       */
      email: string;
      /** @description Email messagerie sécurisée ? */
      email_mssante_boolean?: boolean;
      editable?: boolean;
      categorie?: string | null;
      /** @description Mode de communication préféré ? */
      preferred?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
    };
    EnteteDocument: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre: string;
      texte: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement: string;
    };
    /**
     * @description * `a_venir` - A venir
     *     * `arrive` - Arrive
     *     * `debutee` - Debutee
     *     * `finie` - Finie
     *     * `non_venu` - Non venu
     *     * `annule` - annulée
     *     * `excuse` - Excusée
     * @enum {string}
     */
    EtatConsultationEnum:
      | "a_venir"
      | "arrive"
      | "debutee"
      | "finie"
      | "non_venu"
      | "annule"
      | "excuse";
    /**
     * @description * `VIVANT` - Vivant
     *     * `DCD` - Décédé
     * @enum {string}
     */
    EtatEnum: "VIVANT" | "DCD";
    ExtendedConsultation: {
      /** Format: uuid */
      readonly id: string;
      tags?: string[];
      readonly model_app_datas: string;
      zkf_patient: components["schemas"]["Patient"];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      consultation_date?: string | null;
      /** Format: time */
      consultation_arrive?: string | null;
      /** Format: time */
      consultation_debut_prevu?: string | null;
      /** Format: time */
      consultation_fin_prevu?: string | null;
      /** Format: time */
      consultation_debut_reel?: string | null;
      /** Format: time */
      consultation_fin_reel?: string | null;
      premiere_consultation?: boolean;
      mode_consultation?:
        | (
            | components["schemas"]["ModeConsultationEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      motif_consultation?: string | null;
      reminder_consultation?: string | null;
      observation_consultation?: string | null;
      statut_courrier_consultation?: components["schemas"]["StatutCourrierConsultationEnum"];
      courrier_consultation?: string;
      courrier_consultation_bas_page?: string;
      etat_consultation?: components["schemas"]["EtatConsultationEnum"];
      /** Format: decimal */
      depassement_honoraire?: string | null;
      mode_reglement?:
        | (
            | components["schemas"]["ModeReglementEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      /** Format: decimal */
      total_prix_consultation?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_site: string;
      /** Format: uuid */
      zkf_lieu?: string | null;
      zkf_redacteur?: number | null;
      /** Format: uuid */
      zkf_statut_consultation?: string | null;
      operation_liee?: string[];
      consultations_type?: string[];
    };
    ExtendedHospitalisation: {
      /** Format: uuid */
      readonly id: string;
      tags?: string[];
      readonly operations: string;
      readonly model_app_datas: string;
      zkf_patient: components["schemas"]["Patient"];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      entree_date?: string | null;
      /** Format: time */
      entree_heure?: string | null;
      /** Format: date */
      sortie_date?: string | null;
      hospitalisation_ambulatoire?: boolean;
      a_programmer?: boolean;
      annulation_hospitalisation?: boolean;
      commentaire_annulation_hospitalisation?: string;
      motif_hospitalisation?: string;
      histoire_maladie?: string;
      examen_clinique?: string;
      traitement_sortie?: string | null;
      crh?: string;
      /** Format: date */
      date_redaction_crh?: string | null;
      statut_crh?: components["schemas"]["StatutCrhEnum"];
      complication_post_op?: boolean | null;
      detail_complication_post_op?: string;
      mode_entree?:
        | (
            | components["schemas"]["ModeEntreeEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      origine_transfert?: string | null;
      transfusion?: boolean;
      test_portage_bmr?: boolean;
      examens_biologie_radiologie?: boolean;
      regles_hygieno_dietetiques?: boolean;
      remise_document_patient?: boolean;
      destination_retour_domicile?: boolean;
      destination_retour_detail?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_consigne_sortie?: string | null;
      /** Format: uuid */
      service_hospitalisation: string;
      /** Format: uuid */
      specialite_referente: string;
      zkf_redacteur?: number | null;
      operation_liee?: string[];
      consignes_sortie?: string[];
      /** @description Choisir un ou plusieurs médecin(s) référent(s) */
      medecin_referent: number[];
    };
    Facture: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_operation?: string | null;
      /** Format: date */
      date_facture?: string | null;
      numero_facture?: string | null;
      /** Format: decimal */
      montant?: string | null;
      note?: string | null;
      facture_validee?: boolean;
      /** Format: date-time */
      date_facture_validation?: string | null;
      facture_acquitee?: boolean;
      /** Format: date */
      date_facture_acquitee?: string | null;
      mode_reglement?: string | null;
      numero_cheque?: string | null;
      numero_banque_cheque?: string | null;
      iban?: string | null;
      /** Format: date */
      date_depot_cheque?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_patient: string;
      zkf_createur?: number | null;
      /** @description Personne ayant réalisé l'acte */
      zkf_acte_realisateur?: number | null;
      /** Format: uuid */
      zkf_groupe_facturation: string;
      facture_validation_user?: number | null;
      /** @description Personnes liées à l'acte (chirurgien, etc...) */
      zkf_acteur_lie?: string[];
    };
    /**
     * @description * `text` - Text
     *     * `number` - Number
     *     * `date` - Date
     *     * `select` - Select
     *     * `boolean` - Boolean
     * @enum {string}
     */
    FieldTypeEnum: "text" | "number" | "date" | "select" | "boolean";
    Form: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_protocole: string;
      /** Format: uuid */
      zkf_form_template: string;
      responses?: string[];
    };
    FormField: {
      /** Format: uuid */
      readonly id: string;
      zkf_protocole: components["schemas"]["Protocole"];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      label: string;
      field_type: components["schemas"]["FieldTypeEnum"];
      /**
       * Timing par rapport à l'operation
       * @description pre, per, post ou suivi
       */
      timing_operation?: string | null;
      description: string;
      default?: string | null;
      /** @description Options séparées par des virgules */
      options?: string | null;
      /** Format: decimal */
      min?: string | null;
      /** Format: decimal */
      max?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    FormTemplate: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      name: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_protocole: string;
      /** Format: uuid */
      zkf_specialite: string;
      formfields: string[];
    };
    FullTag: {
      /** Format: uuid */
      readonly id: string;
      name: string;
      slug: string;
      /** Format: uuid */
      specialite: string;
      specialites?: string[];
      user?: number[];
    };
    GestionnaireImpression: {
      /** Format: uuid */
      readonly id: string;
      readonly patient: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      string_of_html?: string | null;
      /** Format: uuid */
      zkf_id?: string | null;
      titre_document?: string | null;
      /** Format: uri */
      url_document?: string | null;
      type_document?: string | null;
      /** Format: uuid */
      object_id?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string | null;
      zkf_user: number;
      /** Format: uuid */
      zkf_patient: string;
      content_type?: number | null;
    };
    Group: {
      readonly id: number;
      /** Nom */
      name: string;
      permissions?: number[];
    };
    GroupeFacturation: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_groupe: string;
      nom_societe: string;
      siren: string;
      siret: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement: string;
      utilisateurs_lies?: number[];
    };
    Hospitalisation: {
      /** Format: uuid */
      readonly id: string;
      tags?: string[];
      readonly operations: string;
      readonly model_app_datas: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      entree_date?: string | null;
      /** Format: time */
      entree_heure?: string | null;
      /** Format: date */
      sortie_date?: string | null;
      hospitalisation_ambulatoire?: boolean;
      a_programmer?: boolean;
      annulation_hospitalisation?: boolean;
      commentaire_annulation_hospitalisation?: string;
      motif_hospitalisation?: string;
      histoire_maladie?: string;
      examen_clinique?: string;
      traitement_sortie?: string | null;
      crh?: string;
      /** Format: date */
      date_redaction_crh?: string | null;
      statut_crh?: components["schemas"]["StatutCrhEnum"];
      complication_post_op?: boolean | null;
      detail_complication_post_op?: string;
      mode_entree?:
        | (
            | components["schemas"]["ModeEntreeEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      origine_transfert?: string | null;
      transfusion?: boolean;
      test_portage_bmr?: boolean;
      examens_biologie_radiologie?: boolean;
      regles_hygieno_dietetiques?: boolean;
      remise_document_patient?: boolean;
      destination_retour_domicile?: boolean;
      destination_retour_detail?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient: string;
      /** Format: uuid */
      zkf_consigne_sortie?: string | null;
      /** Format: uuid */
      service_hospitalisation: string;
      /** Format: uuid */
      specialite_referente: string;
      zkf_redacteur?: number | null;
      operation_liee?: string[];
      consignes_sortie?: string[];
      /** @description Choisir un ou plusieurs médecin(s) référent(s) */
      medecin_referent: number[];
    };
    Intervenant: {
      /** Format: uuid */
      readonly id: string;
      readonly default: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: string | null;
      nom?: string | null;
      prenom?: string | null;
      is_active?: boolean;
      /** Format: date */
      date_debut?: string | null;
      /** Format: date */
      date_fin?: string | null;
      remplacant?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement: string;
      zkf_user?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      specialite_principale?: string | null;
      specialite_secondaire?: string[];
      service?: string[];
      site?: string[];
      groupe?: number[];
    };
    Irradiation: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: double */
      pds_total?: number | null;
      pds_unite?: string | null;
      /** Format: double */
      air_kerma_total?: number | null;
      air_kerma_unite?: string | null;
      /** Format: time */
      duree_scopie_total?: string | null;
      /** Format: double */
      volume_iode?: number | null;
      /** Format: double */
      pds_scopie?: number | null;
      /** Format: double */
      air_kerma_scopie?: number | null;
      /** Format: time */
      duree_scopie_scopie?: string | null;
      /** Format: double */
      pds_acquisition?: number | null;
      /** Format: double */
      air_kerma_acquisition?: number | null;
      /** Format: time */
      duree_scopie_acquisition?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      produit_contraste?: string | null;
    };
    /**
     * @description * `SUCCESS` - success
     *     * `INFO` - info
     *     * `WARNING` - warning
     *     * `ERROR` - error
     * @enum {string}
     */
    LevelEnum: "SUCCESS" | "INFO" | "WARNING" | "ERROR";
    MatrielIrradiation: {
      /** Format: uuid */
      readonly id: string;
      /** Format: uuid */
      site?: string;
      /** Format: uuid */
      specialite?: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_appareil_radio: string;
      marque_appareil_radio: string;
      /** Format: date */
      date_premiere_utilisation?: string | null;
      en_utilisation: boolean;
      pds_unite?:
        | (
            | components["schemas"]["PdsUniteEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      air_kerma_unite?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string;
      zkf_site?: string[];
      zkf_specialite?: string[];
    };
    /**
     * @description * `programme` - Programmée
     *     * `urgent` - Urgence
     * @enum {string}
     */
    ModeConsultationEnum: "programme" | "urgent";
    /**
     * @description * `programme` - Programmé
     *     * `urgence` - Urgence
     *     * `transfert` - Transfert
     * @enum {string}
     */
    ModeEntreeEnum: "programme" | "urgence" | "transfert";
    /**
     * @description * `cheque` - Cheque
     *     * `especes` - Espèces
     *     * `gratuit` - Gratuit
     *     * `cb` - Carte banquaire
     *     * `tiers_payant` - Tiers Payant
     *     * `CMU` - cmu
     * @enum {string}
     */
    ModeReglementEnum: "cheque" | "especes" | "gratuit" | "cb" | "tiers_payant" | "CMU";
    Nested: {
      readonly id: number;
      /** Mot de passe */
      password: string;
      /**
       * Dernière connexion
       * Format: date-time
       */
      last_login?: string | null;
      /**
       * Statut super-utilisateur
       * @description Précise que l’utilisateur possède toutes les permissions sans les assigner explicitement.
       */
      is_superuser?: boolean;
      /**
       * Nom d’utilisateur
       * @description Requis. 150 caractères maximum. Uniquement des lettres, nombres et les caractères « @ », « . », « + », « - » et « _ ».
       */
      username: string;
      /** Prénom */
      first_name?: string;
      /** Nom */
      last_name?: string;
      /**
       * Adresse électronique
       * Format: email
       */
      email?: string;
      /**
       * Statut équipe
       * @description Précise si l’utilisateur peut se connecter à ce site d'administration.
       */
      is_staff?: boolean;
      /**
       * Actif
       * @description Précise si l’utilisateur doit être considéré comme actif. Décochez ceci plutôt que de supprimer le compte.
       */
      is_active?: boolean;
      /**
       * Date d’inscription
       * Format: date-time
       */
      date_joined?: string;
      /**
       * Groupes
       * @description Les groupes dont fait partie cet utilisateur. Celui-ci obtient tous les droits de tous les groupes auxquels il appartient.
       */
      groups?: number[];
      /**
       * Permissions de l’utilisateur
       * @description Permissions spécifiques à cet utilisateur.
       */
      user_permissions?: number[];
    };
    /**
     * @description * `cr_consultation` - Compte rendu de consultation
     *     * `cr_operation` - Compte rendu d'opération
     *     * `cr_hospitalisation` - Compte rendu d'hospitalisation
     *     * `user_added_to_staff` - Utilisateur ajouté à un staff
     *     * `cr_correspondant` - Compte rendu de correspondant ajouté
     *     * `patient_added_in_protocol` - Patient ajouté dans un protocole
     *     * `user_added_in_protocol` - Utilisateur ajouté dans un protocole
     *     * `audio_deletion_scheduled_validated` - Suppression audio (consult. validée) programmée
     *     * `audio_deletion_scheduled_inactive` - Suppression audio (consult. inactive) programmée
     *     * `audio_deleted` - Audio supprimé
     *     * `audio_deletion_cancelled` - Suppression audio annulée
     * @enum {string}
     */
    NfTypeEnum:
      | "cr_consultation"
      | "cr_operation"
      | "cr_hospitalisation"
      | "user_added_to_staff"
      | "cr_correspondant"
      | "patient_added_in_protocol"
      | "user_added_in_protocol"
      | "audio_deletion_scheduled_validated"
      | "audio_deletion_scheduled_inactive"
      | "audio_deleted"
      | "audio_deletion_cancelled";
    Notification: {
      /** Format: uuid */
      readonly id: string;
      readonly recipients: components["schemas"]["User"][];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      level?: components["schemas"]["LevelEnum"];
      flagged?: boolean;
      /**
       * ID of the actor object
       * Format: uuid
       */
      actor_object_id?: string | null;
      /** Anonymous text for actor */
      actor_text?: string | null;
      /** Anonymous URL for actor */
      actor_url_text?: string | null;
      /** Verb of the action */
      verb: string;
      /** Title of the notification */
      title: string;
      /** Description of the notification */
      description?: string | null;
      /** Type of notification */
      nf_type?: components["schemas"]["NfTypeEnum"];
      /**
       * ID of the target object
       * Format: uuid
       */
      target_object_id?: string | null;
      /** Anonymous text for target */
      target_text?: string | null;
      /** Anonymous URL for target */
      target_url_text?: string | null;
      /**
       * ID of the target object
       * Format: uuid
       */
      obj_object_id?: string | null;
      /** Anonymous text for action object */
      obj_text?: string | null;
      /** Anonymous URL for action object */
      obj_url_text?: string | null;
      /** JSONField to store addtional data */
      extra?: unknown;
      doc_link?: string | null;
      /** Read status */
      read?: boolean;
      /** Route to view object */
      route_view_object?: string | null;
      readonly created_by: components["schemas"]["Nested"];
      readonly modified_by: components["schemas"]["Nested"];
      readonly deleted_by: components["schemas"]["Nested"];
      readonly zkf_recipient: components["schemas"]["Nested"];
      readonly actor_content_type: components["schemas"]["Nested"];
      readonly target_content_type: components["schemas"]["Nested"];
      readonly obj_content_type: components["schemas"]["Nested"];
    };
    /** @enum {unknown} */
    NullEnum: null;
    Operation: {
      /** Format: uuid */
      readonly id: string;
      tags?: string[];
      readonly model_app_datas: string;
      protocoles?: string[];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      operation_date?: string | null;
      /** Format: date-time */
      operation_start?: string | null;
      /** Format: date-time */
      operation_end?: string | null;
      a_programmer?: boolean;
      annulation_operation?: boolean;
      commentaire_programmation_operation?: string;
      consentement_pre_operatoire_recupere?: boolean;
      urgence?: components["schemas"]["UrgenceEnum"] | components["schemas"]["BlankEnum"];
      commentaire_operation?: string;
      titre_operation: string;
      indication_operatoire?: string;
      anesthesie?: string;
      cro?: string;
      complication?: boolean | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_cro_type?: string | null;
      /** Format: uuid */
      zkf_site?: string | null;
      /** Format: uuid */
      zkf_patient: string;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_consultation_pre_op?: string | null;
      anesthesiste_old?: number | null;
      zkf_redacteur?: number | null;
      /** Format: uuid */
      materiel_irradiation?: string | null;
      cro_types?: string[];
      operateurs_old?: number[];
      aide_operatoire_old?: number[];
      operateur_new?: string[];
      anesthesiste_new?: string[];
      aides_operatoire?: string[];
    };
    OperationDevisCcam: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      acte_ccam: string;
      acte_nom_long?: string | null;
      acte_principal_secondaire_supplementaire?: number | null;
      acte_activite?: number | null;
      acte_phase?: number | null;
      acte_grille?: number | null;
      acte_modificateur_urgence?: string | null;
      acte_modificateur_chir?: string | null;
      acte_modificateur?: string | null;
      acte_association?: number | null;
      acte_supplementaire?: number | null;
      /** Format: decimal */
      prix_base?: string | null;
      /** Format: decimal */
      prix_modificateurs?: string | null;
      /** Format: decimal */
      depassement_honoraire_acte?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_devis?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_facture?: string | null;
      /** Format: uuid */
      zkf_devis_favoris?: string | null;
      /** Format: uuid */
      zkf_acte?: string | null;
    };
    Ordonnance: {
      /** Format: uuid */
      readonly id: string;
      readonly model_app_datas: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_ordonnance?: string | null;
      /** Format: date-time */
      date_ordonnance: string;
      corps_ordonnance?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_ordo_template?: string | null;
      /** Format: uuid */
      zkf_patient: string;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_consultation?: string | null;
      redacteur?: number | null;
      ordonnance_template_lie?: string[];
    };
    OrdonnanceTemplate: {
      /** Format: uuid */
      readonly id: string;
      readonly suggested: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_ordonnance: string;
      description_ordonnance?: string | null;
      corps_ordonnance?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user: number;
      /** Format: uuid */
      categorie_ordonnance?: string | null;
      specialite: string[];
    };
    PaginatedActeList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Acte"][];
    };
    PaginatedAnswerFormFieldList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["AnswerFormField"][];
    };
    PaginatedAudioRecordingList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["AudioRecording"][];
    };
    PaginatedAuditLogList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["AuditLog"][];
    };
    PaginatedAvoirList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Avoir"][];
    };
    PaginatedCompteRenduList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["CompteRendu"][];
    };
    PaginatedConsultationList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Consultation"][];
    };
    PaginatedDevisList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Devis"][];
    };
    PaginatedExtendedConsultationList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?offset=400&limit=100
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?offset=200&limit=100
       */
      previous?: string | null;
      results: components["schemas"]["ExtendedConsultation"][];
    };
    PaginatedExtendedHospitalisationList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?offset=400&limit=100
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?offset=200&limit=100
       */
      previous?: string | null;
      results: components["schemas"]["ExtendedHospitalisation"][];
    };
    PaginatedFactureList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Facture"][];
    };
    PaginatedFormFieldList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["FormField"][];
    };
    PaginatedFormList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Form"][];
    };
    PaginatedFormTemplateList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["FormTemplate"][];
    };
    PaginatedGroupeFacturationList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["GroupeFacturation"][];
    };
    PaginatedHospitalisationList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Hospitalisation"][];
    };
    PaginatedNotificationList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Notification"][];
    };
    PaginatedOrdonnanceList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Ordonnance"][];
    };
    PaginatedPatientList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["Patient"][];
    };
    PaginatedR_activite_modificateurList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["R_activite_modificateur"][];
    };
    PaginatedR_tb_23List: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["R_tb_23"][];
    };
    PaginatedRelanceFactureList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["RelanceFacture"][];
    };
    PaginatedUserEventList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["UserEvent"][];
    };
    PaginatedUserList: {
      /** @example 123 */
      count: number;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=4
       */
      next?: string | null;
      /**
       * Format: uri
       * @example http://api.example.org/accounts/?page=2
       */
      previous?: string | null;
      results: components["schemas"]["User"][];
    };
    PatchedAbsence: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date-time */
      date_heure_start?: string;
      /** Format: date-time */
      date_heure_end?: string;
      motif?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user?: number;
    };
    PatchedAdresse: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: string | null;
      structure?: string | null;
      numero_rue?: string | null;
      rue?: string | null;
      complement_adresse?: string | null;
      code_postal?: number;
      ville?: string;
      editable?: boolean;
      /** @description Mode de communication préféré ? */
      preferred?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
    };
    PatchedAnswerFormField: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      response?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_protocole?: string;
      /** Format: uuid */
      zkf_form_field?: string;
      /** Format: uuid */
      form?: string;
    };
    PatchedAudioRecording: {
      /** Format: uuid */
      readonly id?: string;
      /**
       * Format: date-time
       * @description Date of creation.
       */
      readonly created_at?: string;
      auteur?: number | null;
      auteur_id?: number;
      /** Format: uuid */
      document?: string | null;
      readonly consultation?: components["schemas"]["Consultation"];
      /** Format: uuid */
      consultation_id?: string;
      /** Format: uuid */
      hospitalisation?: string | null;
      readonly patient?: components["schemas"]["Patient"];
      /** Format: uuid */
      patient_id?: string;
      /** Format: uri */
      file?: string;
      /** @description Title of the audio recording. */
      title?: string | null;
      /** @description Transcription of the audio recording. */
      transcription?: string | null;
      /** @description LLM-corrected medical transcription. */
      transcription_corrected?: string | null;
      /** @description Status of the audio recording validation and lifecycle.
       *
       *     * `unverified` - UNVERIFIED
       *     * `pending_validation` - PENDING_VALIDATION
       *     * `validated` - VALIDATED
       *     * `pending_deletion_validated` - PENDING_DELETION_VALIDATED
       *     * `pending_deletion_inactive` - PENDING_DELETION_INACTIVE
       *     * `pending_deletion_7day_warning_sent` - PENDING_DELETION_7DAY_WARNING_SENT
       *     * `deleted` - DELETED */
      status?: components["schemas"]["StatusEnum"];
    };
    PatchedAvoir: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_operation?: string | null;
      /** Format: date */
      date_avoir?: string | null;
      numero_avoir?: string | null;
      /** Format: decimal */
      montant?: string | null;
      note?: string | null;
      avoir_valide?: boolean;
      /** Format: date-time */
      date_avoir_validation?: string | null;
      avoir_acquite?: boolean;
      /** Format: date */
      date_avoir_acquite?: string | null;
      mode_reglement?: string | null;
      numero_cheque?: string | null;
      numero_banque_cheque?: string | null;
      iban?: string | null;
      /** Format: date */
      date_depot_cheque?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_patient?: string;
      zkf_createur?: number | null;
      /** @description Personne ayant réalisé l'acte */
      zkf_acte_realisateur?: number | null;
      /** Format: uuid */
      zkf_groupe_facturation?: string;
      /** Format: uuid */
      zkf_facture?: string | null;
      avoir_validation_user?: number | null;
      /** @description Personnes liées à l'acte (chirurgien, etc...) */
      zkf_acteur_lie?: number[];
    };
    PatchedCompteRendu: {
      /** Format: uuid */
      readonly id?: string;
      readonly patient_nom?: string;
      readonly auteur_nom?: string;
      readonly contact_not_found?: string;
      should_be_contacted?: boolean;
      readonly specialites?: components["schemas"]["Specialite"][];
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** @description Nom descriptif du document */
      nom?: string | null;
      type_compte_rendu?: components["schemas"]["TypeCompteRenduEnum"];
      /** Format: date */
      date_examen?: string | null;
      /** Format: date */
      date_entree?: string | null;
      /** Format: date */
      date_sortie?: string | null;
      /** Format: uri */
      examen_file?: string;
      status?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_consultation?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      auteur?: string | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      specialite?: string[];
      type_examen?: string[];
    };
    PatchedConsignesSortie: {
      /** Format: uuid */
      readonly id?: string;
      readonly suggested?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_consigne?: string | null;
      texte_suivi?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      readonly zkf_user?: number;
      /** Format: uuid */
      readonly zkf_environnement?: string;
      /** Format: uuid */
      zkf_service?: string | null;
      /** Format: uuid */
      zkf_specialite?: string | null;
      /** Format: uuid */
      zkf_protocole?: string | null;
    };
    PatchedConsultation: {
      /** Format: uuid */
      readonly id?: string;
      tags?: string[];
      readonly model_app_datas?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      consultation_date?: string | null;
      /** Format: time */
      consultation_arrive?: string | null;
      /** Format: time */
      consultation_debut_prevu?: string | null;
      /** Format: time */
      consultation_fin_prevu?: string | null;
      /** Format: time */
      consultation_debut_reel?: string | null;
      /** Format: time */
      consultation_fin_reel?: string | null;
      premiere_consultation?: boolean;
      mode_consultation?:
        | (
            | components["schemas"]["ModeConsultationEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      motif_consultation?: string | null;
      reminder_consultation?: string | null;
      observation_consultation?: string | null;
      statut_courrier_consultation?: components["schemas"]["StatutCourrierConsultationEnum"];
      courrier_consultation?: string;
      courrier_consultation_bas_page?: string;
      etat_consultation?: components["schemas"]["EtatConsultationEnum"];
      /** Format: decimal */
      depassement_honoraire?: string | null;
      mode_reglement?:
        | (
            | components["schemas"]["ModeReglementEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      /** Format: decimal */
      total_prix_consultation?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient?: string;
      /** Format: uuid */
      zkf_site?: string;
      /** Format: uuid */
      zkf_lieu?: string | null;
      zkf_redacteur?: number | null;
      /** Format: uuid */
      zkf_statut_consultation?: string | null;
      operation_liee?: string[];
      consultations_type?: string[];
    };
    PatchedConsultationType: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: string;
      corps?: string | null;
      bas_de_page?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      readonly zkf_environnement?: string;
      readonly zkf_user?: number | null;
      users?: number[];
      sites?: string[];
      lieus?: string[];
      services?: string[];
    };
    PatchedContact: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom?: string | null;
      prenom?: string | null;
      /** Format: int64 */
      rpps?: number | null;
      sexe?:
        | (
            | components["schemas"]["SexeEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      structure?: string | null;
      titre?: components["schemas"]["ContactTitreEnum"];
      en_activite?: boolean;
      comment?: string | null;
      created_from_annuaire_sante?: boolean;
      /** Format: date-time */
      annuaire_sante_verification?: string | null;
      /** Format: date-time */
      annuaire_sante_last_attempt?: string | null;
      id_annuaire_sante?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      readonly zkf_contactcategorie?: string;
      /** Format: uuid */
      readonly zkf_environnement_contact?: string;
      specialite?: string[];
    };
    PatchedContraste: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      denomination?: string;
      composition?: string | null;
      teneur_iode?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      service?: string[];
    };
    PatchedCorrespondant: {
      /** Format: uuid */
      readonly id?: string;
      readonly specialite_string?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: components["schemas"]["CorrespondantTitreEnum"];
      en_activite?: boolean;
      readonly created_by?: number | null;
      readonly modified_by?: number | null;
      deleted_by?: number | null;
      specialite?: string[];
    };
    PatchedCroType: {
      /** Format: uuid */
      readonly id?: string;
      readonly suggested?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** @description Titre court pour selection dans la liste */
      cro_type_titre_descriptif?: string | null;
      /** @description Titre du CRO type à inserer dans une operation */
      cro_type_titre?: string | null;
      /** @description Indication opératoire du CRO type à inserer dans une operation */
      cro_type_indication_operatoire?: string | null;
      cro_type?: string | null;
      nom_modele_specialite?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user?: number;
    };
    PatchedDevis: {
      /** Format: uuid */
      readonly id?: string;
      readonly model_app_datas?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_devis?: string;
      /** Format: decimal */
      depassement_honoraire?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_patient?: string;
      zkf_createur?: number | null;
      /** Format: uuid */
      zkf_devisfavoris?: string | null;
    };
    PatchedDocumentLibre: {
      /** Format: uuid */
      readonly id?: string;
      readonly model_app_datas?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_document?: string;
      /** Format: date */
      readonly date_document?: string;
      corps_document?: string | null;
      /** Format: uri */
      document_libre_file?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient?: string;
      /** Format: uuid */
      zkf_consultation?: string | null;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_document_template?: string | null;
      redacteur_document?: number | null;
    };
    PatchedDocumentLibreTemplate: {
      /** Format: uuid */
      readonly id?: string;
      readonly suggested?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_document?: string;
      description_document?: string | null;
      corps_document?: string | null;
      /** Format: uri */
      document_type_file?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user?: number | null;
      /** Format: uuid */
      zkf_environnement?: string;
      specialite?: string[];
      service?: string[];
    };
    PatchedEmail: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /**
       * Format: email
       * @description Entrer l'email
       */
      email?: string;
      /** @description Email messagerie sécurisée ? */
      email_mssante_boolean?: boolean;
      editable?: boolean;
      categorie?: string | null;
      /** @description Mode de communication préféré ? */
      preferred?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
    };
    PatchedEnteteDocument: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: string;
      texte?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string;
    };
    PatchedFacture: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_operation?: string | null;
      /** Format: date */
      date_facture?: string | null;
      numero_facture?: string | null;
      /** Format: decimal */
      montant?: string | null;
      note?: string | null;
      facture_validee?: boolean;
      /** Format: date-time */
      date_facture_validation?: string | null;
      facture_acquitee?: boolean;
      /** Format: date */
      date_facture_acquitee?: string | null;
      mode_reglement?: string | null;
      numero_cheque?: string | null;
      numero_banque_cheque?: string | null;
      iban?: string | null;
      /** Format: date */
      date_depot_cheque?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_patient?: string;
      zkf_createur?: number | null;
      /** @description Personne ayant réalisé l'acte */
      zkf_acte_realisateur?: number | null;
      /** Format: uuid */
      zkf_groupe_facturation?: string;
      facture_validation_user?: number | null;
      /** @description Personnes liées à l'acte (chirurgien, etc...) */
      zkf_acteur_lie?: string[];
    };
    PatchedForm: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_protocole?: string;
      /** Format: uuid */
      zkf_form_template?: string;
      responses?: string[];
    };
    PatchedFormField: {
      /** Format: uuid */
      readonly id?: string;
      zkf_protocole?: components["schemas"]["Protocole"];
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      label?: string;
      field_type?: components["schemas"]["FieldTypeEnum"];
      /**
       * Timing par rapport à l'operation
       * @description pre, per, post ou suivi
       */
      timing_operation?: string | null;
      description?: string;
      default?: string | null;
      /** @description Options séparées par des virgules */
      options?: string | null;
      /** Format: decimal */
      min?: string | null;
      /** Format: decimal */
      max?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    PatchedFormTemplate: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      name?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_protocole?: string;
      /** Format: uuid */
      zkf_specialite?: string;
      formfields?: string[];
    };
    PatchedGestionnaireImpression: {
      /** Format: uuid */
      readonly id?: string;
      readonly patient?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      string_of_html?: string | null;
      /** Format: uuid */
      zkf_id?: string | null;
      titre_document?: string | null;
      /** Format: uri */
      url_document?: string | null;
      type_document?: string | null;
      /** Format: uuid */
      object_id?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string | null;
      zkf_user?: number;
      /** Format: uuid */
      zkf_patient?: string;
      content_type?: number | null;
    };
    PatchedGroupeFacturation: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_groupe?: string;
      nom_societe?: string;
      siren?: string;
      siret?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string;
      utilisateurs_lies?: number[];
    };
    PatchedHospitalisation: {
      /** Format: uuid */
      readonly id?: string;
      tags?: string[];
      readonly operations?: string;
      readonly model_app_datas?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      entree_date?: string | null;
      /** Format: time */
      entree_heure?: string | null;
      /** Format: date */
      sortie_date?: string | null;
      hospitalisation_ambulatoire?: boolean;
      a_programmer?: boolean;
      annulation_hospitalisation?: boolean;
      commentaire_annulation_hospitalisation?: string;
      motif_hospitalisation?: string;
      histoire_maladie?: string;
      examen_clinique?: string;
      traitement_sortie?: string | null;
      crh?: string;
      /** Format: date */
      date_redaction_crh?: string | null;
      statut_crh?: components["schemas"]["StatutCrhEnum"];
      complication_post_op?: boolean | null;
      detail_complication_post_op?: string;
      mode_entree?:
        | (
            | components["schemas"]["ModeEntreeEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      origine_transfert?: string | null;
      transfusion?: boolean;
      test_portage_bmr?: boolean;
      examens_biologie_radiologie?: boolean;
      regles_hygieno_dietetiques?: boolean;
      remise_document_patient?: boolean;
      destination_retour_domicile?: boolean;
      destination_retour_detail?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient?: string;
      /** Format: uuid */
      zkf_consigne_sortie?: string | null;
      /** Format: uuid */
      service_hospitalisation?: string;
      /** Format: uuid */
      specialite_referente?: string;
      zkf_redacteur?: number | null;
      operation_liee?: string[];
      consignes_sortie?: string[];
      /** @description Choisir un ou plusieurs médecin(s) référent(s) */
      medecin_referent?: number[];
    };
    PatchedIntervenant: {
      /** Format: uuid */
      readonly id?: string;
      readonly default?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre?: string | null;
      nom?: string | null;
      prenom?: string | null;
      is_active?: boolean;
      /** Format: date */
      date_debut?: string | null;
      /** Format: date */
      date_fin?: string | null;
      remplacant?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string;
      zkf_user?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      specialite_principale?: string | null;
      specialite_secondaire?: string[];
      service?: string[];
      site?: string[];
      groupe?: number[];
    };
    PatchedIrradiation: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: double */
      pds_total?: number | null;
      pds_unite?: string | null;
      /** Format: double */
      air_kerma_total?: number | null;
      air_kerma_unite?: string | null;
      /** Format: time */
      duree_scopie_total?: string | null;
      /** Format: double */
      volume_iode?: number | null;
      /** Format: double */
      pds_scopie?: number | null;
      /** Format: double */
      air_kerma_scopie?: number | null;
      /** Format: time */
      duree_scopie_scopie?: string | null;
      /** Format: double */
      pds_acquisition?: number | null;
      /** Format: double */
      air_kerma_acquisition?: number | null;
      /** Format: time */
      duree_scopie_acquisition?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      produit_contraste?: string | null;
    };
    PatchedMatrielIrradiation: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: uuid */
      site?: string;
      /** Format: uuid */
      specialite?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_appareil_radio?: string;
      marque_appareil_radio?: string;
      /** Format: date */
      date_premiere_utilisation?: string | null;
      en_utilisation?: boolean;
      pds_unite?:
        | (
            | components["schemas"]["PdsUniteEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      air_kerma_unite?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement?: string;
      zkf_site?: string[];
      zkf_specialite?: string[];
    };
    PatchedNotification: {
      /** Format: uuid */
      readonly id?: string;
      readonly recipients?: components["schemas"]["User"][];
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      level?: components["schemas"]["LevelEnum"];
      flagged?: boolean;
      /**
       * ID of the actor object
       * Format: uuid
       */
      actor_object_id?: string | null;
      /** Anonymous text for actor */
      actor_text?: string | null;
      /** Anonymous URL for actor */
      actor_url_text?: string | null;
      /** Verb of the action */
      verb?: string;
      /** Title of the notification */
      title?: string;
      /** Description of the notification */
      description?: string | null;
      /** Type of notification */
      nf_type?: components["schemas"]["NfTypeEnum"];
      /**
       * ID of the target object
       * Format: uuid
       */
      target_object_id?: string | null;
      /** Anonymous text for target */
      target_text?: string | null;
      /** Anonymous URL for target */
      target_url_text?: string | null;
      /**
       * ID of the target object
       * Format: uuid
       */
      obj_object_id?: string | null;
      /** Anonymous text for action object */
      obj_text?: string | null;
      /** Anonymous URL for action object */
      obj_url_text?: string | null;
      /** JSONField to store addtional data */
      extra?: unknown;
      doc_link?: string | null;
      /** Read status */
      read?: boolean;
      /** Route to view object */
      route_view_object?: string | null;
      readonly created_by?: components["schemas"]["Nested"];
      readonly modified_by?: components["schemas"]["Nested"];
      readonly deleted_by?: components["schemas"]["Nested"];
      readonly zkf_recipient?: components["schemas"]["Nested"];
      readonly actor_content_type?: components["schemas"]["Nested"];
      readonly target_content_type?: components["schemas"]["Nested"];
      readonly obj_content_type?: components["schemas"]["Nested"];
    };
    PatchedOperation: {
      /** Format: uuid */
      readonly id?: string;
      tags?: string[];
      readonly model_app_datas?: string;
      protocoles?: string[];
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      operation_date?: string | null;
      /** Format: date-time */
      operation_start?: string | null;
      /** Format: date-time */
      operation_end?: string | null;
      a_programmer?: boolean;
      annulation_operation?: boolean;
      commentaire_programmation_operation?: string;
      consentement_pre_operatoire_recupere?: boolean;
      urgence?: components["schemas"]["UrgenceEnum"] | components["schemas"]["BlankEnum"];
      commentaire_operation?: string;
      titre_operation?: string;
      indication_operatoire?: string;
      anesthesie?: string;
      cro?: string;
      complication?: boolean | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_cro_type?: string | null;
      /** Format: uuid */
      zkf_site?: string | null;
      /** Format: uuid */
      zkf_patient?: string;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_consultation_pre_op?: string | null;
      anesthesiste_old?: number | null;
      zkf_redacteur?: number | null;
      /** Format: uuid */
      materiel_irradiation?: string | null;
      cro_types?: string[];
      operateurs_old?: number[];
      aide_operatoire_old?: number[];
      operateur_new?: string[];
      anesthesiste_new?: string[];
      aides_operatoire?: string[];
    };
    PatchedOperationDevisCcam: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      acte_ccam?: string;
      acte_nom_long?: string | null;
      acte_principal_secondaire_supplementaire?: number | null;
      acte_activite?: number | null;
      acte_phase?: number | null;
      acte_grille?: number | null;
      acte_modificateur_urgence?: string | null;
      acte_modificateur_chir?: string | null;
      acte_modificateur?: string | null;
      acte_association?: number | null;
      acte_supplementaire?: number | null;
      /** Format: decimal */
      prix_base?: string | null;
      /** Format: decimal */
      prix_modificateurs?: string | null;
      /** Format: decimal */
      depassement_honoraire_acte?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_devis?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_facture?: string | null;
      /** Format: uuid */
      zkf_devis_favoris?: string | null;
      /** Format: uuid */
      zkf_acte?: string | null;
    };
    PatchedOrdonnance: {
      /** Format: uuid */
      readonly id?: string;
      readonly model_app_datas?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_ordonnance?: string | null;
      /** Format: date-time */
      date_ordonnance?: string;
      corps_ordonnance?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_ordo_template?: string | null;
      /** Format: uuid */
      zkf_patient?: string;
      /** Format: uuid */
      zkf_hospitalisation?: string | null;
      /** Format: uuid */
      zkf_operation?: string | null;
      /** Format: uuid */
      zkf_consultation?: string | null;
      redacteur?: number | null;
      ordonnance_template_lie?: string[];
    };
    PatchedOrdonnanceTemplate: {
      /** Format: uuid */
      readonly id?: string;
      readonly suggested?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      titre_ordonnance?: string;
      description_ordonnance?: string | null;
      corps_ordonnance?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user?: number;
      /** Format: uuid */
      categorie_ordonnance?: string | null;
      specialite?: string[];
    };
    PatchedPatient: {
      /** Format: uuid */
      readonly id?: string;
      tags?: string[];
      /** @default false */
      reset_ins_confirmation?: boolean;
      readonly model_app_datas?: string;
      readonly correspondants?: components["schemas"]["CorrespondantPatient"][];
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom?: string;
      nom_naissance?: string;
      prenom?: string;
      sexe?: components["schemas"]["SexeEnum"];
      /** Format: date */
      dob?: string;
      etat?: components["schemas"]["EtatEnum"];
      /** Format: date */
      date_etat?: string;
      mode_information_dernier_etat?: string | null;
      cause_deces?: string | null;
      numero_securite_sociale?: string | null;
      /** @description Identifiant INS du patient (qualifié ou provisoire) */
      readonly ins?: string | null;
      /** @description OID du système INS qui a fourni l'identifiant */
      readonly ins_oid?: string | null;
      /** @description Code INSEE de lieu de naissance */
      birth_place_code?: string | null;
      /** @description Indique si une tentative de recherche INS a été effectuée (succès ou échec). */
      readonly ins_checked?: boolean;
      /** @description Statut de la dernière tentative de recherche INS automatique. */
      readonly ins_lookup_status?: string;
      /** @description Détails ou message d'erreur de la dernière recherche INS. */
      readonly ins_lookup_details?: string | null;
      /** @description Document utilisé pour vérifier l'identité du patient.
       *
       *     * `NONE` - Identité non vérifiée
       *     * `FR_ID` - Carte Nationale d'Identité Française
       *     * `EU_ID` - Carte d'Identité (UE/EEE/CH/UK/Microétats)
       *     * `PASSPORT` - Passeport
       *     * `SEJOUR` - Titre de Séjour
       *     * `FRANCE_IDENTITE` - France Identité
       *     * `LAPOSTE` - Identité Numérique La Poste
       *     * `VITALE_APP` - Application Carte Vitale
       *     * `ATTESTATION` - Attestation du professionnel */
      checked_identity?: components["schemas"]["CheckedIdentityEnum"];
      opposition_data_exploitation?: boolean;
      note_opposition_data_exploitation?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      couverture_maladie?: string | null;
      /** Format: uuid */
      zkf_assurance_sante?: string | null;
      praticien_referent?: number[];
    };
    PatchedPreferenceUserSerialyzer: {
      readonly id?: number;
      current_praticiens_lies_selectionnes?: components["schemas"]["User"][];
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      affichage_service?: components["schemas"]["AffichageServiceEnum"];
      /** @description Durée moyenne d'une consultation (HH:MM:SS) */
      consultation_duree?: string;
      affichage_service_status?: components["schemas"]["AffichageServiceStatusEnum"];
      report_hebdo?: boolean;
      impression_recto_verso?: boolean;
      preference_notification?: unknown;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      user?: number;
      /** Format: uuid */
      ordonnance_default?: string | null;
    };
    PatchedProfileUserSerialyzer: {
      readonly id?: number;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      secretaire?: string;
      role?: components["schemas"]["RoleEnum"] | components["schemas"]["BlankEnum"];
      titre?:
        | components["schemas"]["ProfileUserSerialyzerTitreEnum"]
        | components["schemas"]["BlankEnum"];
      /** @description Modificateurs par défaut pour les actes CCAM: lettres séparées par une virgule. Ex: J,K  */
      modificateurs_default?: string | null;
      /** Format: int64 */
      rpps?: number | null;
      /** Format: date-time */
      annuaire_sante_verification?: string | null;
      /** Format: date-time */
      annuaire_sante_last_attempt?: string | null;
      entete_ordonnance?: string;
      /** Format: date-time */
      last_seen?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      user?: number;
      /** Format: uuid */
      zkf_environnement_user?: string | null;
      /** Format: uuid */
      specialite_active?: string | null;
      /** Format: uuid */
      entete?: string | null;
      /** Format: uuid */
      conventionnement?: string | null;
      /** Format: uuid */
      activite_ccam?: string | null;
      praticiens_lies?: number[];
      zkf_environnement_lie?: string[];
      secretariat?: string[];
      specialite_profile?: string[];
      service?: string[];
      site?: string[];
      groupe_facturation?: string[];
    };
    PatchedProtocole: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      libelle?: string;
      description?: string;
      /** @description en mois, séparés par un point virgule */
      frequence_rappel?: string | null;
      /** @description jour, semaine ou mois */
      temporalite_rappel?: string;
      default_fields?: string | null;
      protocole_summary?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement_protocole?: string;
      protocole_users?: number[];
    };
    PatchedRelanceFacture: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_relance?: string | null;
      note_relance?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_facture?: string;
    };
    PatchedService: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_service?: string;
      template_document?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      readonly zkf_environnement?: string;
      /** Format: uuid */
      site_service?: string;
      specialite_service?: string[];
    };
    PatchedSpecialite: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      spe?: string;
      spe_medecin?: string;
      type_specialite?: components["schemas"]["TypeSpecialiteEnum"];
      data_app?: string;
      omop_code?: number | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    PatchedSuivi: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      texte_suivi?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient?: string;
      /** Format: uuid */
      zkf_hospitalisation?: string;
    };
    PatchedTelephone: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      numero?: string;
      categorie?:
        | (
            | components["schemas"]["CategorieEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      editable?: boolean;
      /** @description Mode de communication préféré ? */
      preferred?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
    };
    PatchedUser: {
      readonly id?: number;
      readonly profile_id?: string;
      readonly preferences_id?: string;
      readonly default?: string;
      readonly annuaire_sante_verification?: string;
      readonly profile?: components["schemas"]["ProfileUserSerialyzer"];
      readonly specialite_principale?: string;
      readonly intervenant?: string;
      /**
       * Dernière connexion
       * Format: date-time
       */
      last_login?: string | null;
      /**
       * Statut super-utilisateur
       * @description Précise que l’utilisateur possède toutes les permissions sans les assigner explicitement.
       */
      is_superuser?: boolean;
      /**
       * Nom d’utilisateur
       * @description Requis. 150 caractères maximum. Uniquement des lettres, nombres et les caractères « @ », « . », « + », « - » et « _ ».
       */
      username?: string;
      /** Prénom */
      first_name?: string;
      /** Nom */
      last_name?: string;
      /**
       * Adresse électronique
       * Format: email
       */
      email?: string;
      /**
       * Statut équipe
       * @description Précise si l’utilisateur peut se connecter à ce site d'administration.
       */
      is_staff?: boolean;
      /**
       * Actif
       * @description Précise si l’utilisateur doit être considéré comme actif. Décochez ceci plutôt que de supprimer le compte.
       */
      is_active?: boolean;
      /**
       * Date d’inscription
       * Format: date-time
       */
      date_joined?: string;
      /**
       * Groupes
       * @description Les groupes dont fait partie cet utilisateur. Celui-ci obtient tous les droits de tous les groupes auxquels il appartient.
       */
      groups?: number[];
      /**
       * Permissions de l’utilisateur
       * @description Permissions spécifiques à cet utilisateur.
       */
      user_permissions?: number[];
    };
    PatchedUserEvent: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      notify_users?: boolean;
      notify_other_users?: string | null;
      event_title?: string;
      event_note?: string | null;
      /** Format: date-time */
      event_start?: string;
      /** Format: date-time */
      event_end?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      event_category?: string | null;
      users?: number[];
    };
    PatchedVacation: {
      /** Format: uuid */
      readonly id?: string;
      /** Format: date-time */
      readonly created_on?: string;
      /** Format: date-time */
      readonly modified_on?: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      type_vacation?: components["schemas"]["TypeVacationEnum"];
      /** @description Lundi:1 Dimanche:7 */
      jour_semaine?: number;
      /** Format: time */
      debut_vacation?: string;
      /** Format: time */
      fin_vacation?: string;
      repetition_semaine?: components["schemas"]["RepetitionSemaineEnum"];
      /** Format: date */
      debut_periode?: string;
      /** Format: date */
      fin_periode?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user?: number;
      /** Format: uuid */
      site?: string;
    };
    Patient: {
      /** Format: uuid */
      readonly id: string;
      tags?: string[];
      /** @default false */
      reset_ins_confirmation?: boolean;
      readonly model_app_datas: string;
      readonly correspondants: components["schemas"]["CorrespondantPatient"][];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom: string;
      nom_naissance: string;
      prenom: string;
      sexe: components["schemas"]["SexeEnum"];
      /** Format: date */
      dob: string;
      etat?: components["schemas"]["EtatEnum"];
      /** Format: date */
      date_etat?: string;
      mode_information_dernier_etat?: string | null;
      cause_deces?: string | null;
      numero_securite_sociale?: string | null;
      /** @description Identifiant INS du patient (qualifié ou provisoire) */
      readonly ins: string | null;
      /** @description OID du système INS qui a fourni l'identifiant */
      readonly ins_oid: string | null;
      /** @description Code INSEE de lieu de naissance */
      birth_place_code?: string | null;
      /** @description Indique si une tentative de recherche INS a été effectuée (succès ou échec). */
      readonly ins_checked: boolean;
      /** @description Statut de la dernière tentative de recherche INS automatique. */
      readonly ins_lookup_status: string;
      /** @description Détails ou message d'erreur de la dernière recherche INS. */
      readonly ins_lookup_details: string | null;
      /** @description Document utilisé pour vérifier l'identité du patient.
       *
       *     * `NONE` - Identité non vérifiée
       *     * `FR_ID` - Carte Nationale d'Identité Française
       *     * `EU_ID` - Carte d'Identité (UE/EEE/CH/UK/Microétats)
       *     * `PASSPORT` - Passeport
       *     * `SEJOUR` - Titre de Séjour
       *     * `FRANCE_IDENTITE` - France Identité
       *     * `LAPOSTE` - Identité Numérique La Poste
       *     * `VITALE_APP` - Application Carte Vitale
       *     * `ATTESTATION` - Attestation du professionnel */
      checked_identity?: components["schemas"]["CheckedIdentityEnum"];
      opposition_data_exploitation?: boolean;
      note_opposition_data_exploitation?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      couverture_maladie?: string | null;
      /** Format: uuid */
      zkf_assurance_sante?: string | null;
      praticien_referent?: number[];
    };
    /**
     * @description * `Gycm2` - Gycm2
     *     * `mGycm2` - mGycm2
     * @enum {string}
     */
    PdsUniteEnum: "Gycm2" | "mGycm2";
    PreferenceUserSerialyzer: {
      readonly id: number;
      current_praticiens_lies_selectionnes: components["schemas"]["User"][];
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      affichage_service?: components["schemas"]["AffichageServiceEnum"];
      /** @description Durée moyenne d'une consultation (HH:MM:SS) */
      consultation_duree?: string;
      affichage_service_status?: components["schemas"]["AffichageServiceStatusEnum"];
      report_hebdo?: boolean;
      impression_recto_verso?: boolean;
      preference_notification?: unknown;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      user: number;
      /** Format: uuid */
      ordonnance_default?: string | null;
    };
    ProfileUserSerialyzer: {
      readonly id: number;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      secretaire?: string;
      role?: components["schemas"]["RoleEnum"] | components["schemas"]["BlankEnum"];
      titre?:
        | components["schemas"]["ProfileUserSerialyzerTitreEnum"]
        | components["schemas"]["BlankEnum"];
      /** @description Modificateurs par défaut pour les actes CCAM: lettres séparées par une virgule. Ex: J,K  */
      modificateurs_default?: string | null;
      /** Format: int64 */
      rpps?: number | null;
      /** Format: date-time */
      annuaire_sante_verification?: string | null;
      /** Format: date-time */
      annuaire_sante_last_attempt?: string | null;
      entete_ordonnance?: string;
      /** Format: date-time */
      last_seen?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      user: number;
      /** Format: uuid */
      zkf_environnement_user?: string | null;
      /** Format: uuid */
      specialite_active?: string | null;
      /** Format: uuid */
      entete?: string | null;
      /** Format: uuid */
      conventionnement?: string | null;
      /** Format: uuid */
      activite_ccam?: string | null;
      praticiens_lies?: number[];
      zkf_environnement_lie?: string[];
      secretariat?: string[];
      specialite_profile: string[];
      service: string[];
      site: string[];
      groupe_facturation?: string[];
    };
    /**
     * @description * `Mr` - Monsieur
     *     * `Dr` - Docteur
     *     * `Mme` - Madame
     * @enum {string}
     */
    ProfileUserSerialyzerTitreEnum: "Mr" | "Dr" | "Mme";
    Protocole: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      libelle: string;
      description: string;
      /** @description en mois, séparés par un point virgule */
      frequence_rappel?: string | null;
      /** @description jour, semaine ou mois */
      temporalite_rappel: string;
      default_fields?: string | null;
      protocole_summary?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement_protocole: string;
      protocole_users: number[];
    };
    R_activite: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      cod_activ?: string | null;
      libelle?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    R_activite_modificateur: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      aa_code?: string | null;
      /** Format: date */
      aadt_modif?: string | null;
      modifi_cod?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    R_menu: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      cod_menu?: string | null;
      rang?: number | null;
      libelle?: string | null;
      cod_pere?: number | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    R_tb_23: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      cod_grille?: string | null;
      libelle?: string | null;
      definition?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    RelanceFacture: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      /** Format: date */
      date_relance?: string | null;
      note_relance?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_facture: string;
    };
    /**
     * @description * `PAIRE` - Paire
     *     * `IMPAIRE` - Impaire
     *     * `TOUTES` - Toutes
     * @enum {string}
     */
    RepetitionSemaineEnum: "PAIRE" | "IMPAIRE" | "TOUTES";
    /**
     * @description * `SEC` - Secretaire
     *     * `MED` - Medecin
     *     * `INF` - Infirmier
     * @enum {string}
     */
    RoleEnum: "SEC" | "MED" | "INF";
    Service: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_service: string;
      template_document?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      readonly zkf_environnement: string;
      /** Format: uuid */
      site_service: string;
      specialite_service: string[];
    };
    /**
     * @description * `MALE` - Homme
     *     * `FEMALE` - Femme
     * @enum {string}
     */
    SexeEnum: "MALE" | "FEMALE";
    Specialite: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      spe: string;
      spe_medecin: string;
      type_specialite: components["schemas"]["TypeSpecialiteEnum"];
      data_app: string;
      omop_code?: number | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    /**
     * @description * `unverified` - UNVERIFIED
     *     * `pending_validation` - PENDING_VALIDATION
     *     * `validated` - VALIDATED
     *     * `pending_deletion_validated` - PENDING_DELETION_VALIDATED
     *     * `pending_deletion_inactive` - PENDING_DELETION_INACTIVE
     *     * `pending_deletion_7day_warning_sent` - PENDING_DELETION_7DAY_WARNING_SENT
     *     * `deleted` - DELETED
     * @enum {string}
     */
    StatusEnum:
      | "unverified"
      | "pending_validation"
      | "validated"
      | "pending_deletion_validated"
      | "pending_deletion_inactive"
      | "pending_deletion_7day_warning_sent"
      | "deleted";
    /**
     * @description * `envoye` - Envoyé
     *     * `valide` - Validé
     *     * `attente_validation` - En attente de validation
     *     * `transcrit` - Transcrit (audio)
     *     * `non_fait` - Non fait
     *     * `non_statue` - Non statué
     * @enum {string}
     */
    StatutCourrierConsultationEnum:
      | "envoye"
      | "valide"
      | "attente_validation"
      | "transcrit"
      | "non_fait"
      | "non_statue";
    /**
     * @description * `envoye` - Envoyé
     *     * `valide` - Validé
     *     * `attente_validation` - En attente de validation
     *     * `non_fait` - Non fait
     *     * `non_statue` - Non statué
     * @enum {string}
     */
    StatutCrhEnum: "envoye" | "valide" | "attente_validation" | "non_fait" | "non_statue";
    Suivi: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      texte_suivi: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_patient: string;
      /** Format: uuid */
      zkf_hospitalisation: string;
    };
    Telephone: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      numero: string;
      categorie?:
        | (
            | components["schemas"]["CategorieEnum"]
            | components["schemas"]["BlankEnum"]
            | components["schemas"]["NullEnum"]
          )
        | null;
      editable?: boolean;
      /** @description Mode de communication préféré ? */
      preferred?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_contact?: string | null;
      /** Format: uuid */
      zkf_patient?: string | null;
    };
    /**
     * @description * `CONSULTATION` - Consultation
     *     * `HOSPITALISATION` - Hospitalisation
     *     * `EXAMEN_COMPLEMENTAIRE` - Examen Complementaire
     *     * `ORDONNANCE_TRAITEMENT` - Ordonnance Traitement
     *     * `BIOLOGIE` - Biologie
     *     * `AUTREZ` - Autre
     * @enum {string}
     */
    TypeCompteRenduEnum:
      | "CONSULTATION"
      | "HOSPITALISATION"
      | "EXAMEN_COMPLEMENTAIRE"
      | "ORDONNANCE_TRAITEMENT"
      | "BIOLOGIE"
      | "AUTREZ";
    /**
     * @description * `CHIRURGICAL` - Chirurgical
     *     * `MEDICAL` - Medical
     * @enum {string}
     */
    TypeSpecialiteEnum: "CHIRURGICAL" | "MEDICAL";
    /**
     * @description * `CONSULTATION` - Consultation
     *     * `OPERATION` - Operation
     * @enum {string}
     */
    TypeVacationEnum: "CONSULTATION" | "OPERATION";
    /**
     * @description * `urgence` - Urgence
     *     * `programme` - Programmé
     * @enum {string}
     */
    UrgenceEnum: "urgence" | "programme";
    User: {
      readonly id: number;
      readonly profile_id: string;
      readonly preferences_id: string;
      readonly default: string;
      readonly annuaire_sante_verification: string;
      readonly profile: components["schemas"]["ProfileUserSerialyzer"];
      readonly specialite_principale: string;
      readonly intervenant: string;
      /**
       * Dernière connexion
       * Format: date-time
       */
      last_login?: string | null;
      /**
       * Statut super-utilisateur
       * @description Précise que l’utilisateur possède toutes les permissions sans les assigner explicitement.
       */
      is_superuser?: boolean;
      /**
       * Nom d’utilisateur
       * @description Requis. 150 caractères maximum. Uniquement des lettres, nombres et les caractères « @ », « . », « + », « - » et « _ ».
       */
      username: string;
      /** Prénom */
      first_name?: string;
      /** Nom */
      last_name?: string;
      /**
       * Adresse électronique
       * Format: email
       */
      email?: string;
      /**
       * Statut équipe
       * @description Précise si l’utilisateur peut se connecter à ce site d'administration.
       */
      is_staff?: boolean;
      /**
       * Actif
       * @description Précise si l’utilisateur doit être considéré comme actif. Décochez ceci plutôt que de supprimer le compte.
       */
      is_active?: boolean;
      /**
       * Date d’inscription
       * Format: date-time
       */
      date_joined?: string;
      /**
       * Groupes
       * @description Les groupes dont fait partie cet utilisateur. Celui-ci obtient tous les droits de tous les groupes auxquels il appartient.
       */
      groups?: number[];
      /**
       * Permissions de l’utilisateur
       * @description Permissions spécifiques à cet utilisateur.
       */
      user_permissions?: number[];
    };
    UserEvent: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      notify_users?: boolean;
      notify_other_users?: string | null;
      event_title?: string;
      event_note?: string | null;
      /** Format: date-time */
      event_start: string;
      /** Format: date-time */
      event_end: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      event_category?: string | null;
      users?: number[];
    };
    UserEventCategory: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      name?: string;
      description: string;
      color?: string;
      duration?: string | null;
      allow_overlap?: boolean;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
    UserSites: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_site: string;
      adresse_site: string;
      zip_code_site: number;
      ville_site: string;
      telephone_site?: string | null;
      adeli_site?: string | null;
      operation_schedule_color?: string;
      consultation_schedule_color?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement_site: string;
    };
    UserSitesLieux: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_lieu: string;
      acces_lieu?: string;
      description_lieu?: string;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      /** Format: uuid */
      zkf_environnement: string;
      /** Format: uuid */
      zkf_site: string;
    };
    Vacation: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      type_vacation?: components["schemas"]["TypeVacationEnum"];
      /** @description Lundi:1 Dimanche:7 */
      jour_semaine: number;
      /** Format: time */
      debut_vacation: string;
      /** Format: time */
      fin_vacation: string;
      repetition_semaine?: components["schemas"]["RepetitionSemaineEnum"];
      /** Format: date */
      debut_periode: string;
      /** Format: date */
      fin_periode?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
      zkf_user: number;
      /** Format: uuid */
      site: string;
    };
    Ville: {
      /** Format: uuid */
      readonly id: string;
      /** Format: date-time */
      readonly created_on: string;
      /** Format: date-time */
      readonly modified_on: string | null;
      deleted?: boolean;
      /** Format: date-time */
      deleted_on?: string | null;
      nom_ville: string;
      code_postal: number;
      coordonnees_gps?: string | null;
      created_by?: number | null;
      modified_by?: number | null;
      deleted_by?: number | null;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  api_agenda_day_events_pdf_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": unknown;
        };
      };
    };
  };
  api_agenda_items_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AllCalendarItems"][];
        };
      };
    };
  };
  api_agenda_leave_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Absence"][];
        };
      };
    };
  };
  api_agenda_leave_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Absence"];
        "application/x-www-form-urlencoded": components["schemas"]["Absence"];
        "multipart/form-data": components["schemas"]["Absence"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Absence"];
        };
      };
    };
  };
  api_agenda_leave_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Absence"];
        };
      };
    };
  };
  api_agenda_leave_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Absence"];
        "application/x-www-form-urlencoded": components["schemas"]["Absence"];
        "multipart/form-data": components["schemas"]["Absence"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Absence"];
        };
      };
    };
  };
  api_agenda_leave_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_agenda_leave_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedAbsence"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedAbsence"];
        "multipart/form-data": components["schemas"]["PatchedAbsence"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Absence"];
        };
      };
    };
  };
  api_agenda_user_event_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedUserEventList"];
        };
      };
    };
  };
  api_agenda_user_event_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["UserEvent"];
        "application/x-www-form-urlencoded": components["schemas"]["UserEvent"];
        "multipart/form-data": components["schemas"]["UserEvent"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserEvent"];
        };
      };
    };
  };
  api_agenda_user_event_category_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserEventCategory"][];
        };
      };
    };
  };
  api_agenda_user_event_category_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["UserEventCategory"];
        "application/x-www-form-urlencoded": components["schemas"]["UserEventCategory"];
        "multipart/form-data": components["schemas"]["UserEventCategory"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserEventCategory"];
        };
      };
    };
  };
  api_agenda_user_event_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserEvent"];
        };
      };
    };
  };
  api_agenda_user_event_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["UserEvent"];
        "application/x-www-form-urlencoded": components["schemas"]["UserEvent"];
        "multipart/form-data": components["schemas"]["UserEvent"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserEvent"];
        };
      };
    };
  };
  api_agenda_user_event_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_agenda_user_event_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedUserEvent"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedUserEvent"];
        "multipart/form-data": components["schemas"]["PatchedUserEvent"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserEvent"];
        };
      };
    };
  };
  api_agenda_vacation_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Vacation"][];
        };
      };
    };
  };
  api_agenda_vacation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Vacation"];
        "application/x-www-form-urlencoded": components["schemas"]["Vacation"];
        "multipart/form-data": components["schemas"]["Vacation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Vacation"];
        };
      };
    };
  };
  api_agenda_vacation_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Vacation"];
        };
      };
    };
  };
  api_agenda_vacation_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Vacation"];
        "application/x-www-form-urlencoded": components["schemas"]["Vacation"];
        "multipart/form-data": components["schemas"]["Vacation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Vacation"];
        };
      };
    };
  };
  api_agenda_vacation_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_agenda_vacation_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedVacation"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedVacation"];
        "multipart/form-data": components["schemas"]["PatchedVacation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Vacation"];
        };
      };
    };
  };
  api_antecedent_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        patient_id: string;
        specialite: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_antecedent_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        patient_id: string;
        specialite: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_antecedent_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        patient_id: string;
        specialite: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_antecedent_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        patient_id: string;
        specialite: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_auditlog_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedAuditLogList"];
        };
      };
    };
  };
  api_auditlog_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AuditLog"];
        };
      };
    };
  };
  api_ccam_acte_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedActeList"];
        };
      };
    };
  };
  api_ccam_acte_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        acte: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_ccam_menu_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["R_menu"][];
        };
      };
    };
  };
  api_ccam_menu_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        menu_id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["R_menu"];
        };
      };
    };
  };
  api_ccam_modificateur_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_ccam_modificateur_list_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedR_activite_modificateurList"];
        };
      };
    };
  };
  api_ccam_modificateur_list_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["R_activite_modificateur"];
        "application/x-www-form-urlencoded": components["schemas"]["R_activite_modificateur"];
        "multipart/form-data": components["schemas"]["R_activite_modificateur"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["R_activite_modificateur"];
        };
      };
    };
  };
  api_ccam_r_activite_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["R_activite"][];
        };
      };
    };
  };
  api_ccam_r_activite_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["R_activite"];
        "application/x-www-form-urlencoded": components["schemas"]["R_activite"];
        "multipart/form-data": components["schemas"]["R_activite"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["R_activite"];
        };
      };
    };
  };
  api_ccam_r_tb_23_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedR_tb_23List"];
        };
      };
    };
  };
  api_ccam_r_tb_23_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["R_tb_23"];
        "application/x-www-form-urlencoded": components["schemas"]["R_tb_23"];
        "multipart/form-data": components["schemas"]["R_tb_23"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["R_tb_23"];
        };
      };
    };
  };
  api_ccam_search_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Acte"][];
        };
      };
    };
  };
  api_ccam_search_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["Acte"];
        "application/x-www-form-urlencoded": components["schemas"]["Acte"];
        "multipart/form-data": components["schemas"]["Acte"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Acte"];
        };
      };
    };
  };
  api_ccam_suggested_actes_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Acte"][];
        };
      };
    };
  };
  api_comptabilite_avoir_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedAvoirList"];
        };
      };
    };
  };
  api_comptabilite_avoir_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Avoir"];
        "application/x-www-form-urlencoded": components["schemas"]["Avoir"];
        "multipart/form-data": components["schemas"]["Avoir"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Avoir"];
        };
      };
    };
  };
  api_comptabilite_avoir_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Avoir"];
        };
      };
    };
  };
  api_comptabilite_avoir_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Avoir"];
        "application/x-www-form-urlencoded": components["schemas"]["Avoir"];
        "multipart/form-data": components["schemas"]["Avoir"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Avoir"];
        };
      };
    };
  };
  api_comptabilite_avoir_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_comptabilite_avoir_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedAvoir"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedAvoir"];
        "multipart/form-data": components["schemas"]["PatchedAvoir"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Avoir"];
        };
      };
    };
  };
  api_comptabilite_code_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OperationDevisCcam"][];
        };
      };
    };
  };
  api_comptabilite_code_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["OperationDevisCcam"];
        "application/x-www-form-urlencoded": components["schemas"]["OperationDevisCcam"];
        "multipart/form-data": components["schemas"]["OperationDevisCcam"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OperationDevisCcam"];
        };
      };
    };
  };
  api_comptabilite_code_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OperationDevisCcam"];
        };
      };
    };
  };
  api_comptabilite_code_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["OperationDevisCcam"];
        "application/x-www-form-urlencoded": components["schemas"]["OperationDevisCcam"];
        "multipart/form-data": components["schemas"]["OperationDevisCcam"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OperationDevisCcam"];
        };
      };
    };
  };
  api_comptabilite_code_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_comptabilite_code_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedOperationDevisCcam"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedOperationDevisCcam"];
        "multipart/form-data": components["schemas"]["PatchedOperationDevisCcam"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OperationDevisCcam"];
        };
      };
    };
  };
  api_comptabilite_devis_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedDevisList"];
        };
      };
    };
  };
  api_comptabilite_devis_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Devis"];
        "application/x-www-form-urlencoded": components["schemas"]["Devis"];
        "multipart/form-data": components["schemas"]["Devis"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Devis"];
        };
      };
    };
  };
  api_comptabilite_devis_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Devis"];
        };
      };
    };
  };
  api_comptabilite_devis_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Devis"];
        "application/x-www-form-urlencoded": components["schemas"]["Devis"];
        "multipart/form-data": components["schemas"]["Devis"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Devis"];
        };
      };
    };
  };
  api_comptabilite_devis_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_comptabilite_devis_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedDevis"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedDevis"];
        "multipart/form-data": components["schemas"]["PatchedDevis"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Devis"];
        };
      };
    };
  };
  api_comptabilite_facture_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedFactureList"];
        };
      };
    };
  };
  api_comptabilite_facture_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Facture"];
        "application/x-www-form-urlencoded": components["schemas"]["Facture"];
        "multipart/form-data": components["schemas"]["Facture"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Facture"];
        };
      };
    };
  };
  api_comptabilite_facture_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Facture"];
        };
      };
    };
  };
  api_comptabilite_facture_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Facture"];
        "application/x-www-form-urlencoded": components["schemas"]["Facture"];
        "multipart/form-data": components["schemas"]["Facture"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Facture"];
        };
      };
    };
  };
  api_comptabilite_facture_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_comptabilite_facture_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedFacture"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedFacture"];
        "multipart/form-data": components["schemas"]["PatchedFacture"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Facture"];
        };
      };
    };
  };
  api_comptabilite_groupe_facturation_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedGroupeFacturationList"];
        };
      };
    };
  };
  api_comptabilite_groupe_facturation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GroupeFacturation"];
        "application/x-www-form-urlencoded": components["schemas"]["GroupeFacturation"];
        "multipart/form-data": components["schemas"]["GroupeFacturation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GroupeFacturation"];
        };
      };
    };
  };
  api_comptabilite_groupe_facturation_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GroupeFacturation"];
        };
      };
    };
  };
  api_comptabilite_groupe_facturation_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GroupeFacturation"];
        "application/x-www-form-urlencoded": components["schemas"]["GroupeFacturation"];
        "multipart/form-data": components["schemas"]["GroupeFacturation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GroupeFacturation"];
        };
      };
    };
  };
  api_comptabilite_groupe_facturation_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_comptabilite_groupe_facturation_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedGroupeFacturation"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedGroupeFacturation"];
        "multipart/form-data": components["schemas"]["PatchedGroupeFacturation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GroupeFacturation"];
        };
      };
    };
  };
  api_comptabilite_relance_facture_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedRelanceFactureList"];
        };
      };
    };
  };
  api_comptabilite_relance_facture_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["RelanceFacture"];
        "application/x-www-form-urlencoded": components["schemas"]["RelanceFacture"];
        "multipart/form-data": components["schemas"]["RelanceFacture"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["RelanceFacture"];
        };
      };
    };
  };
  api_comptabilite_relance_facture_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["RelanceFacture"];
        };
      };
    };
  };
  api_comptabilite_relance_facture_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["RelanceFacture"];
        "application/x-www-form-urlencoded": components["schemas"]["RelanceFacture"];
        "multipart/form-data": components["schemas"]["RelanceFacture"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["RelanceFacture"];
        };
      };
    };
  };
  api_comptabilite_relance_facture_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_comptabilite_relance_facture_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedRelanceFacture"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedRelanceFacture"];
        "multipart/form-data": components["schemas"]["PatchedRelanceFacture"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["RelanceFacture"];
        };
      };
    };
  };
  api_compte_rendu_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedCompteRenduList"];
        };
      };
    };
  };
  api_compte_rendu_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["CompteRendu"];
        "application/x-www-form-urlencoded": components["schemas"]["CompteRendu"];
        "application/json": components["schemas"]["CompteRendu"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CompteRendu"];
        };
      };
    };
  };
  api_compte_rendu_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CompteRendu"];
        };
      };
    };
  };
  api_compte_rendu_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["CompteRendu"];
        "application/x-www-form-urlencoded": components["schemas"]["CompteRendu"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CompteRendu"];
        };
      };
    };
  };
  api_compte_rendu_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_compte_rendu_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "multipart/form-data": components["schemas"]["PatchedCompteRendu"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedCompteRendu"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CompteRendu"];
        };
      };
    };
  };
  api_compte_rendu_duplicate_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_consultation_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedConsultationList"];
        };
      };
    };
  };
  api_consultation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Consultation"];
        "application/x-www-form-urlencoded": components["schemas"]["Consultation"];
        "multipart/form-data": components["schemas"]["Consultation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Consultation"];
        };
      };
    };
  };
  api_consultation_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Consultation"];
        };
      };
    };
  };
  api_consultation_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Consultation"];
        "application/x-www-form-urlencoded": components["schemas"]["Consultation"];
        "multipart/form-data": components["schemas"]["Consultation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Consultation"];
        };
      };
    };
  };
  api_consultation_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_consultation_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedConsultation"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedConsultation"];
        "multipart/form-data": components["schemas"]["PatchedConsultation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Consultation"];
        };
      };
    };
  };
  api_consultation_consultation_type_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsultationType"][];
        };
      };
    };
  };
  api_consultation_consultation_type_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ConsultationType"];
        "application/x-www-form-urlencoded": components["schemas"]["ConsultationType"];
        "multipart/form-data": components["schemas"]["ConsultationType"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsultationType"];
        };
      };
    };
  };
  api_consultation_consultation_type_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsultationType"];
        };
      };
    };
  };
  api_consultation_consultation_type_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ConsultationType"];
        "application/x-www-form-urlencoded": components["schemas"]["ConsultationType"];
        "multipart/form-data": components["schemas"]["ConsultationType"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsultationType"];
        };
      };
    };
  };
  api_consultation_consultation_type_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_consultation_consultation_type_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedConsultationType"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedConsultationType"];
        "multipart/form-data": components["schemas"]["PatchedConsultationType"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsultationType"];
        };
      };
    };
  };
  api_consultation_generate_consultation_type_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_consultation_recent_consultations_list: {
    parameters: {
      query?: {
        /** @description Which field to use when ordering the results. */
        ordering?: string;
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedConsultationList"];
        };
      };
    };
  };
  api_consultation_suggested_consultations_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_contact_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contact"][];
        };
      };
    };
  };
  api_contact_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["Contact"];
        "application/x-www-form-urlencoded": components["schemas"]["Contact"];
        "multipart/form-data": components["schemas"]["Contact"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contact"];
        };
      };
    };
  };
  api_contact_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contact"];
        };
      };
    };
  };
  api_contact_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["Contact"];
        "application/x-www-form-urlencoded": components["schemas"]["Contact"];
        "multipart/form-data": components["schemas"]["Contact"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contact"];
        };
      };
    };
  };
  api_contact_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_contact_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedContact"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedContact"];
        "multipart/form-data": components["schemas"]["PatchedContact"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contact"];
        };
      };
    };
  };
  api_contact_adresse_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Adresse"][];
        };
      };
    };
  };
  api_contact_adresse_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Adresse"];
        "application/x-www-form-urlencoded": components["schemas"]["Adresse"];
        "multipart/form-data": components["schemas"]["Adresse"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Adresse"];
        };
      };
    };
  };
  api_contact_adresse_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Adresse"];
        };
      };
    };
  };
  api_contact_adresse_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Adresse"];
        "application/x-www-form-urlencoded": components["schemas"]["Adresse"];
        "multipart/form-data": components["schemas"]["Adresse"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Adresse"];
        };
      };
    };
  };
  api_contact_adresse_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_contact_adresse_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedAdresse"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedAdresse"];
        "multipart/form-data": components["schemas"]["PatchedAdresse"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Adresse"];
        };
      };
    };
  };
  api_contact_correspondant_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Correspondant"][];
        };
      };
    };
  };
  api_contact_correspondant_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["Correspondant"];
        "application/x-www-form-urlencoded": components["schemas"]["Correspondant"];
        "multipart/form-data": components["schemas"]["Correspondant"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Correspondant"];
        };
      };
    };
  };
  api_contact_correspondant_patient_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CorrespondantPatient"][];
        };
      };
    };
  };
  api_contact_correspondant_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Correspondant"];
        };
      };
    };
  };
  api_contact_correspondant_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["Correspondant"];
        "application/x-www-form-urlencoded": components["schemas"]["Correspondant"];
        "multipart/form-data": components["schemas"]["Correspondant"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Correspondant"];
        };
      };
    };
  };
  api_contact_correspondant_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_contact_correspondant_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedCorrespondant"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedCorrespondant"];
        "multipart/form-data": components["schemas"]["PatchedCorrespondant"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Correspondant"];
        };
      };
    };
  };
  api_contact_email_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Email"][];
        };
      };
    };
  };
  api_contact_email_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Email"];
        "application/x-www-form-urlencoded": components["schemas"]["Email"];
        "multipart/form-data": components["schemas"]["Email"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Email"];
        };
      };
    };
  };
  api_contact_email_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Email"];
        };
      };
    };
  };
  api_contact_email_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Email"];
        "application/x-www-form-urlencoded": components["schemas"]["Email"];
        "multipart/form-data": components["schemas"]["Email"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Email"];
        };
      };
    };
  };
  api_contact_email_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_contact_email_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedEmail"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedEmail"];
        "multipart/form-data": components["schemas"]["PatchedEmail"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Email"];
        };
      };
    };
  };
  api_contact_telephone_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Telephone"][];
        };
      };
    };
  };
  api_contact_telephone_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Telephone"];
        "application/x-www-form-urlencoded": components["schemas"]["Telephone"];
        "multipart/form-data": components["schemas"]["Telephone"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Telephone"];
        };
      };
    };
  };
  api_contact_telephone_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Telephone"];
        };
      };
    };
  };
  api_contact_telephone_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Telephone"];
        "application/x-www-form-urlencoded": components["schemas"]["Telephone"];
        "multipart/form-data": components["schemas"]["Telephone"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Telephone"];
        };
      };
    };
  };
  api_contact_telephone_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_contact_telephone_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedTelephone"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedTelephone"];
        "multipart/form-data": components["schemas"]["PatchedTelephone"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Telephone"];
        };
      };
    };
  };
  api_contact_ville_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Ville"][];
        };
      };
    };
  };
  api_contact_ville_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Ville"];
        "application/x-www-form-urlencoded": components["schemas"]["Ville"];
        "multipart/form-data": components["schemas"]["Ville"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Ville"];
        };
      };
    };
  };
  api_document_libre_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibre"][];
        };
      };
    };
  };
  api_document_libre_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["DocumentLibre"];
        "application/x-www-form-urlencoded": components["schemas"]["DocumentLibre"];
        "multipart/form-data": components["schemas"]["DocumentLibre"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibre"];
        };
      };
    };
  };
  api_document_libre_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibre"];
        };
      };
    };
  };
  api_document_libre_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["DocumentLibre"];
        "application/x-www-form-urlencoded": components["schemas"]["DocumentLibre"];
        "multipart/form-data": components["schemas"]["DocumentLibre"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibre"];
        };
      };
    };
  };
  api_document_libre_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_document_libre_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedDocumentLibre"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedDocumentLibre"];
        "multipart/form-data": components["schemas"]["PatchedDocumentLibre"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibre"];
        };
      };
    };
  };
  api_document_libre_template_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibreTemplate"][];
        };
      };
    };
  };
  api_document_libre_template_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["DocumentLibreTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["DocumentLibreTemplate"];
        "multipart/form-data": components["schemas"]["DocumentLibreTemplate"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibreTemplate"];
        };
      };
    };
  };
  api_document_libre_template_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibreTemplate"];
        };
      };
    };
  };
  api_document_libre_template_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["DocumentLibreTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["DocumentLibreTemplate"];
        "multipart/form-data": components["schemas"]["DocumentLibreTemplate"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibreTemplate"];
        };
      };
    };
  };
  api_document_libre_template_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_document_libre_template_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedDocumentLibreTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedDocumentLibreTemplate"];
        "multipart/form-data": components["schemas"]["PatchedDocumentLibreTemplate"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["DocumentLibreTemplate"];
        };
      };
    };
  };
  api_hospitalisation_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedHospitalisationList"];
        };
      };
    };
  };
  api_hospitalisation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Hospitalisation"];
        "application/x-www-form-urlencoded": components["schemas"]["Hospitalisation"];
        "multipart/form-data": components["schemas"]["Hospitalisation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Hospitalisation"];
        };
      };
    };
  };
  api_hospitalisation_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Hospitalisation"];
        };
      };
    };
  };
  api_hospitalisation_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Hospitalisation"];
        "application/x-www-form-urlencoded": components["schemas"]["Hospitalisation"];
        "multipart/form-data": components["schemas"]["Hospitalisation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Hospitalisation"];
        };
      };
    };
  };
  api_hospitalisation_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_hospitalisation_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedHospitalisation"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedHospitalisation"];
        "multipart/form-data": components["schemas"]["PatchedHospitalisation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Hospitalisation"];
        };
      };
    };
  };
  api_hospitalisation_generate_crh_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_hospitalisation_suivi_list: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Suivi"][];
        };
      };
    };
  };
  api_hospitalisation_suivi_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Suivi"];
        "application/x-www-form-urlencoded": components["schemas"]["Suivi"];
        "multipart/form-data": components["schemas"]["Suivi"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Suivi"];
        };
      };
    };
  };
  api_hospitalisation_suivi_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        suivi_pk: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Suivi"];
        };
      };
    };
  };
  api_hospitalisation_suivi_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        suivi_pk: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Suivi"];
        "application/x-www-form-urlencoded": components["schemas"]["Suivi"];
        "multipart/form-data": components["schemas"]["Suivi"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Suivi"];
        };
      };
    };
  };
  api_hospitalisation_suivi_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        suivi_pk: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_hospitalisation_suivi_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        suivi_pk: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedSuivi"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedSuivi"];
        "multipart/form-data": components["schemas"]["PatchedSuivi"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Suivi"];
        };
      };
    };
  };
  api_hospitalisation_create_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        zkf_patient: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_hospitalisation_consigne_sortie_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsignesSortie"][];
        };
      };
    };
  };
  api_hospitalisation_consigne_sortie_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ConsignesSortie"];
        "application/x-www-form-urlencoded": components["schemas"]["ConsignesSortie"];
        "multipart/form-data": components["schemas"]["ConsignesSortie"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsignesSortie"];
        };
      };
    };
  };
  api_hospitalisation_consigne_sortie_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsignesSortie"];
        };
      };
    };
  };
  api_hospitalisation_consigne_sortie_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ConsignesSortie"];
        "application/x-www-form-urlencoded": components["schemas"]["ConsignesSortie"];
        "multipart/form-data": components["schemas"]["ConsignesSortie"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsignesSortie"];
        };
      };
    };
  };
  api_hospitalisation_consigne_sortie_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_hospitalisation_consigne_sortie_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedConsignesSortie"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedConsignesSortie"];
        "multipart/form-data": components["schemas"]["PatchedConsignesSortie"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ConsignesSortie"];
        };
      };
    };
  };
  api_hospitalisation_generate_consigne_sortie_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_hospitalisation_service_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Hospitalisation"][];
        };
      };
    };
  };
  api_intervenant_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_media_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        url: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_notification_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedNotificationList"];
        };
      };
    };
  };
  api_notification_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Notification"];
        "application/x-www-form-urlencoded": components["schemas"]["Notification"];
        "multipart/form-data": components["schemas"]["Notification"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Notification"];
        };
      };
    };
  };
  api_notification_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Notification"];
        };
      };
    };
  };
  api_notification_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Notification"];
        "application/x-www-form-urlencoded": components["schemas"]["Notification"];
        "multipart/form-data": components["schemas"]["Notification"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Notification"];
        };
      };
    };
  };
  api_notification_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_notification_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedNotification"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedNotification"];
        "multipart/form-data": components["schemas"]["PatchedNotification"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Notification"];
        };
      };
    };
  };
  api_notification_read_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Operation"][];
        };
      };
    };
  };
  api_operation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Operation"];
        "application/x-www-form-urlencoded": components["schemas"]["Operation"];
        "multipart/form-data": components["schemas"]["Operation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Operation"];
        };
      };
    };
  };
  api_operation_templates_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_templates_retrieve_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        specialite: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_templates_retrieve_3: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        specialite: string;
        template_name: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_templates_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        specialite: string;
        template_name: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_templates_retrieve_4: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        specialite: string;
        template_name: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_templates_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        specialite: string;
        template_name: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_templates_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        specialite: string;
        template_name: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_templates_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
        specialite: string;
        template_name: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_generate_cro_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        cro_type_pk: string;
        operation_pk: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_peri_op_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        operation_pk: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Operation"];
        };
      };
    };
  };
  api_operation_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Operation"];
        "application/x-www-form-urlencoded": components["schemas"]["Operation"];
        "multipart/form-data": components["schemas"]["Operation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Operation"];
        };
      };
    };
  };
  api_operation_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedOperation"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedOperation"];
        "multipart/form-data": components["schemas"]["PatchedOperation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Operation"];
        };
      };
    };
  };
  api_operation_anesthesie_type_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Operation"][];
        };
      };
    };
  };
  api_operation_appareil_scopie_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["MatrielIrradiation"][];
        };
      };
    };
  };
  api_operation_appareil_scopie_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["MatrielIrradiation"];
        "application/x-www-form-urlencoded": components["schemas"]["MatrielIrradiation"];
        "multipart/form-data": components["schemas"]["MatrielIrradiation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["MatrielIrradiation"];
        };
      };
    };
  };
  api_operation_appareil_scopie_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["MatrielIrradiation"];
        };
      };
    };
  };
  api_operation_appareil_scopie_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["MatrielIrradiation"];
        "application/x-www-form-urlencoded": components["schemas"]["MatrielIrradiation"];
        "multipart/form-data": components["schemas"]["MatrielIrradiation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["MatrielIrradiation"];
        };
      };
    };
  };
  api_operation_appareil_scopie_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_appareil_scopie_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedMatrielIrradiation"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedMatrielIrradiation"];
        "multipart/form-data": components["schemas"]["PatchedMatrielIrradiation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["MatrielIrradiation"];
        };
      };
    };
  };
  api_operation_complications_post_op_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_complications_post_op_retrieve_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_complications_post_op_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_complications_post_op_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_complications_post_op_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_contrast_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contraste"][];
        };
      };
    };
  };
  api_operation_contrast_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Contraste"];
        "application/x-www-form-urlencoded": components["schemas"]["Contraste"];
        "multipart/form-data": components["schemas"]["Contraste"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contraste"];
        };
      };
    };
  };
  api_operation_contrast_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contraste"];
        };
      };
    };
  };
  api_operation_contrast_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Contraste"];
        "application/x-www-form-urlencoded": components["schemas"]["Contraste"];
        "multipart/form-data": components["schemas"]["Contraste"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contraste"];
        };
      };
    };
  };
  api_operation_contrast_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_contrast_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedContraste"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedContraste"];
        "multipart/form-data": components["schemas"]["PatchedContraste"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Contraste"];
        };
      };
    };
  };
  api_operation_cro_type_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CroType"][];
        };
      };
    };
  };
  api_operation_cro_type_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["CroType"];
        "application/x-www-form-urlencoded": components["schemas"]["CroType"];
        "multipart/form-data": components["schemas"]["CroType"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CroType"];
        };
      };
    };
  };
  api_operation_cro_type_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CroType"];
        };
      };
    };
  };
  api_operation_cro_type_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["CroType"];
        "application/x-www-form-urlencoded": components["schemas"]["CroType"];
        "multipart/form-data": components["schemas"]["CroType"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CroType"];
        };
      };
    };
  };
  api_operation_cro_type_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_cro_type_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedCroType"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedCroType"];
        "multipart/form-data": components["schemas"]["PatchedCroType"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CroType"];
        };
      };
    };
  };
  api_operation_irradiation_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Irradiation"][];
        };
      };
    };
  };
  api_operation_irradiation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["Irradiation"];
        "application/x-www-form-urlencoded": components["schemas"]["Irradiation"];
        "multipart/form-data": components["schemas"]["Irradiation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Irradiation"];
        };
      };
    };
  };
  api_operation_irradiation_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Irradiation"];
        };
      };
    };
  };
  api_operation_irradiation_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["Irradiation"];
        "application/x-www-form-urlencoded": components["schemas"]["Irradiation"];
        "multipart/form-data": components["schemas"]["Irradiation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Irradiation"];
        };
      };
    };
  };
  api_operation_irradiation_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_irradiation_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedIrradiation"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedIrradiation"];
        "multipart/form-data": components["schemas"]["PatchedIrradiation"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Irradiation"];
        };
      };
    };
  };
  api_operation_list_fields_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_operation_operation_model_fields_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_ordonnance_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedOrdonnanceList"];
        };
      };
    };
  };
  api_ordonnance_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Ordonnance"];
        "application/x-www-form-urlencoded": components["schemas"]["Ordonnance"];
        "multipart/form-data": components["schemas"]["Ordonnance"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Ordonnance"];
        };
      };
    };
  };
  api_ordonnance_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Ordonnance"];
        };
      };
    };
  };
  api_ordonnance_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Ordonnance"];
        "application/x-www-form-urlencoded": components["schemas"]["Ordonnance"];
        "multipart/form-data": components["schemas"]["Ordonnance"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Ordonnance"];
        };
      };
    };
  };
  api_ordonnance_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_ordonnance_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedOrdonnance"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedOrdonnance"];
        "multipart/form-data": components["schemas"]["PatchedOrdonnance"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Ordonnance"];
        };
      };
    };
  };
  api_ordonnance_categorie_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CategorieOrdonnance"][];
        };
      };
    };
  };
  api_ordonnance_categorie_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["CategorieOrdonnance"];
        "application/x-www-form-urlencoded": components["schemas"]["CategorieOrdonnance"];
        "multipart/form-data": components["schemas"]["CategorieOrdonnance"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CategorieOrdonnance"];
        };
      };
    };
  };
  api_ordonnance_template_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OrdonnanceTemplate"][];
        };
      };
    };
  };
  api_ordonnance_template_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["OrdonnanceTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["OrdonnanceTemplate"];
        "multipart/form-data": components["schemas"]["OrdonnanceTemplate"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OrdonnanceTemplate"];
        };
      };
    };
  };
  api_ordonnance_template_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OrdonnanceTemplate"];
        };
      };
    };
  };
  api_ordonnance_template_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["OrdonnanceTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["OrdonnanceTemplate"];
        "multipart/form-data": components["schemas"]["OrdonnanceTemplate"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OrdonnanceTemplate"];
        };
      };
    };
  };
  api_ordonnance_template_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_ordonnance_template_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedOrdonnanceTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedOrdonnanceTemplate"];
        "multipart/form-data": components["schemas"]["PatchedOrdonnanceTemplate"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["OrdonnanceTemplate"];
        };
      };
    };
  };
  api_patient_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedPatientList"];
        };
      };
    };
  };
  api_patient_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Patient"];
        "application/x-www-form-urlencoded": components["schemas"]["Patient"];
        "multipart/form-data": components["schemas"]["Patient"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Patient"];
        };
      };
    };
  };
  api_patient_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Patient"];
        };
      };
    };
  };
  api_patient_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Patient"];
        "application/x-www-form-urlencoded": components["schemas"]["Patient"];
        "multipart/form-data": components["schemas"]["Patient"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Patient"];
        };
      };
    };
  };
  api_patient_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_patient_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedPatient"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedPatient"];
        "multipart/form-data": components["schemas"]["PatchedPatient"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Patient"];
        };
      };
    };
  };
  api_patient_consultations_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedPatientList"];
        };
      };
    };
  };
  api_patient_events_list: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AllCalendarItems"][];
        };
      };
    };
  };
  api_patient_reset_ins_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_patient_verify_ins_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_patient_coverage_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CouvertureMaladie"][];
        };
      };
    };
  };
  api_patient_coverage_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["CouvertureMaladie"];
        "application/x-www-form-urlencoded": components["schemas"]["CouvertureMaladie"];
        "multipart/form-data": components["schemas"]["CouvertureMaladie"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["CouvertureMaladie"];
        };
      };
    };
  };
  api_patient_model_fields_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_patient_search_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedPatientList"];
        };
      };
    };
  };
  api_patient_search_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Patient"];
        "application/x-www-form-urlencoded": components["schemas"]["Patient"];
        "multipart/form-data": components["schemas"]["Patient"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Patient"];
        };
      };
    };
  };
  api_print_manager_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GestionnaireImpression"][];
        };
      };
    };
  };
  api_print_manager_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GestionnaireImpression"];
        "application/x-www-form-urlencoded": components["schemas"]["GestionnaireImpression"];
        "multipart/form-data": components["schemas"]["GestionnaireImpression"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GestionnaireImpression"];
        };
      };
    };
  };
  api_print_manager_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_print_manager_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GestionnaireImpression"];
        };
      };
    };
  };
  api_print_manager_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["GestionnaireImpression"];
        "application/x-www-form-urlencoded": components["schemas"]["GestionnaireImpression"];
        "multipart/form-data": components["schemas"]["GestionnaireImpression"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GestionnaireImpression"];
        };
      };
    };
  };
  api_print_manager_destroy_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_print_manager_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedGestionnaireImpression"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedGestionnaireImpression"];
        "multipart/form-data": components["schemas"]["PatchedGestionnaireImpression"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["GestionnaireImpression"];
        };
      };
    };
  };
  api_print_manager_compte_rendus_consultation_list: {
    parameters: {
      query?: {
        /** @description Number of results to return per page. */
        limit?: number;
        /** @description The initial index from which to return the results. */
        offset?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedExtendedConsultationList"];
        };
      };
    };
  };
  api_print_manager_compte_rendus_consultation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ExtendedConsultation"];
        "application/x-www-form-urlencoded": components["schemas"]["ExtendedConsultation"];
        "multipart/form-data": components["schemas"]["ExtendedConsultation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ExtendedConsultation"];
        };
      };
    };
  };
  api_print_manager_compte_rendus_hospitalisation_list: {
    parameters: {
      query?: {
        /** @description Number of results to return per page. */
        limit?: number;
        /** @description The initial index from which to return the results. */
        offset?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedExtendedHospitalisationList"];
        };
      };
    };
  };
  api_print_manager_compte_rendus_hospitalisation_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ExtendedHospitalisation"];
        "application/x-www-form-urlencoded": components["schemas"]["ExtendedHospitalisation"];
        "multipart/form-data": components["schemas"]["ExtendedHospitalisation"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ExtendedHospitalisation"];
        };
      };
    };
  };
  api_print_manager_print_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_print_manager_print_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_print_manager_print_selected_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_print_manager_print_selected_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_protocole_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Protocole"][];
        };
      };
    };
  };
  api_protocole_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Protocole"];
        "application/x-www-form-urlencoded": components["schemas"]["Protocole"];
        "multipart/form-data": components["schemas"]["Protocole"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Protocole"];
        };
      };
    };
  };
  api_protocole_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Protocole"];
        };
      };
    };
  };
  api_protocole_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Protocole"];
        "application/x-www-form-urlencoded": components["schemas"]["Protocole"];
        "multipart/form-data": components["schemas"]["Protocole"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Protocole"];
        };
      };
    };
  };
  api_protocole_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_protocole_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedProtocole"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedProtocole"];
        "multipart/form-data": components["schemas"]["PatchedProtocole"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Protocole"];
        };
      };
    };
  };
  api_protocole_answerformfield_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedAnswerFormFieldList"];
        };
      };
    };
  };
  api_protocole_answerformfield_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["AnswerFormField"];
        "application/x-www-form-urlencoded": components["schemas"]["AnswerFormField"];
        "multipart/form-data": components["schemas"]["AnswerFormField"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AnswerFormField"];
        };
      };
    };
  };
  api_protocole_answerformfield_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AnswerFormField"];
        };
      };
    };
  };
  api_protocole_answerformfield_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["AnswerFormField"];
        "application/x-www-form-urlencoded": components["schemas"]["AnswerFormField"];
        "multipart/form-data": components["schemas"]["AnswerFormField"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AnswerFormField"];
        };
      };
    };
  };
  api_protocole_answerformfield_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_protocole_answerformfield_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedAnswerFormField"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedAnswerFormField"];
        "multipart/form-data": components["schemas"]["PatchedAnswerFormField"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AnswerFormField"];
        };
      };
    };
  };
  api_protocole_form_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedFormList"];
        };
      };
    };
  };
  api_protocole_form_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Form"];
        "application/x-www-form-urlencoded": components["schemas"]["Form"];
        "multipart/form-data": components["schemas"]["Form"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Form"];
        };
      };
    };
  };
  api_protocole_form_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Form"];
        };
      };
    };
  };
  api_protocole_form_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Form"];
        "application/x-www-form-urlencoded": components["schemas"]["Form"];
        "multipart/form-data": components["schemas"]["Form"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Form"];
        };
      };
    };
  };
  api_protocole_form_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_protocole_form_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedForm"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedForm"];
        "multipart/form-data": components["schemas"]["PatchedForm"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Form"];
        };
      };
    };
  };
  api_protocole_formfield_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedFormFieldList"];
        };
      };
    };
  };
  api_protocole_formfield_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["FormField"];
        "application/x-www-form-urlencoded": components["schemas"]["FormField"];
        "multipart/form-data": components["schemas"]["FormField"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormField"];
        };
      };
    };
  };
  api_protocole_formfield_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormField"];
        };
      };
    };
  };
  api_protocole_formfield_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["FormField"];
        "application/x-www-form-urlencoded": components["schemas"]["FormField"];
        "multipart/form-data": components["schemas"]["FormField"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormField"];
        };
      };
    };
  };
  api_protocole_formfield_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_protocole_formfield_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedFormField"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedFormField"];
        "multipart/form-data": components["schemas"]["PatchedFormField"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormField"];
        };
      };
    };
  };
  api_protocole_formtemplate_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedFormTemplateList"];
        };
      };
    };
  };
  api_protocole_formtemplate_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["FormTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["FormTemplate"];
        "multipart/form-data": components["schemas"]["FormTemplate"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormTemplate"];
        };
      };
    };
  };
  api_protocole_formtemplates_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormTemplate"];
        };
      };
    };
  };
  api_protocole_formtemplates_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["FormTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["FormTemplate"];
        "multipart/form-data": components["schemas"]["FormTemplate"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormTemplate"];
        };
      };
    };
  };
  api_protocole_formtemplates_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_protocole_formtemplates_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedFormTemplate"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedFormTemplate"];
        "multipart/form-data": components["schemas"]["PatchedFormTemplate"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FormTemplate"];
        };
      };
    };
  };
  api_settings_model_fields_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_tag_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FullTag"][];
        };
      };
    };
  };
  api_tag_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["FullTag"];
        "application/x-www-form-urlencoded": components["schemas"]["FullTag"];
        "multipart/form-data": components["schemas"]["FullTag"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["FullTag"];
        };
      };
    };
  };
  api_token_auth_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/x-www-form-urlencoded": components["schemas"]["AuthToken"];
        "multipart/form-data": components["schemas"]["AuthToken"];
        "application/json": components["schemas"]["AuthToken"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AuthToken"];
        };
      };
    };
  };
  api_user_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
        /** @description Number of results to return per page. */
        page_size?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedUserList"];
        };
      };
    };
  };
  api_user_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["User"];
        "application/x-www-form-urlencoded": components["schemas"]["User"];
        "multipart/form-data": components["schemas"]["User"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["User"];
        };
      };
    };
  };
  api_user_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["User"];
        };
      };
    };
  };
  api_user_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["User"];
        "application/x-www-form-urlencoded": components["schemas"]["User"];
        "multipart/form-data": components["schemas"]["User"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["User"];
        };
      };
    };
  };
  api_user_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedUser"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedUser"];
        "multipart/form-data": components["schemas"]["PatchedUser"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["User"];
        };
      };
    };
  };
  api_user_intervenant_retrieve_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        user_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Intervenant"];
        };
      };
    };
  };
  api_user_intervenant_update_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        user_id: number;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Intervenant"];
        "application/x-www-form-urlencoded": components["schemas"]["Intervenant"];
        "multipart/form-data": components["schemas"]["Intervenant"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Intervenant"];
        };
      };
    };
  };
  api_user_intervenant_destroy_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        user_id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_intervenant_partial_update_2: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        user_id: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedIntervenant"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedIntervenant"];
        "multipart/form-data": components["schemas"]["PatchedIntervenant"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Intervenant"];
        };
      };
    };
  };
  api_user_entete_document_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["EnteteDocument"][];
        };
      };
    };
  };
  api_user_entete_document_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["EnteteDocument"];
        "application/x-www-form-urlencoded": components["schemas"]["EnteteDocument"];
        "multipart/form-data": components["schemas"]["EnteteDocument"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["EnteteDocument"];
        };
      };
    };
  };
  api_user_entete_document_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["EnteteDocument"];
        };
      };
    };
  };
  api_user_entete_document_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["EnteteDocument"];
        "application/x-www-form-urlencoded": components["schemas"]["EnteteDocument"];
        "multipart/form-data": components["schemas"]["EnteteDocument"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["EnteteDocument"];
        };
      };
    };
  };
  api_user_entete_document_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_entete_document_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedEnteteDocument"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedEnteteDocument"];
        "multipart/form-data": components["schemas"]["PatchedEnteteDocument"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["EnteteDocument"];
        };
      };
    };
  };
  api_user_groups_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Group"][];
        };
      };
    };
  };
  api_user_intervenant_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Intervenant"][];
        };
      };
    };
  };
  api_user_intervenant_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Intervenant"];
        };
      };
    };
  };
  api_user_intervenant_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Intervenant"];
        "application/x-www-form-urlencoded": components["schemas"]["Intervenant"];
        "multipart/form-data": components["schemas"]["Intervenant"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Intervenant"];
        };
      };
    };
  };
  api_user_intervenant_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_intervenant_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedIntervenant"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedIntervenant"];
        "multipart/form-data": components["schemas"]["PatchedIntervenant"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Intervenant"];
        };
      };
    };
  };
  api_user_intervenants_event_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_linked_users_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["User"][];
        };
      };
    };
  };
  api_user_me_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["User"];
        };
      };
    };
  };
  api_user_notification_choices_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_preferences_user_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PreferenceUserSerialyzer"];
        };
      };
    };
  };
  api_user_preferences_user_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["PreferenceUserSerialyzer"];
        "application/x-www-form-urlencoded": components["schemas"]["PreferenceUserSerialyzer"];
        "multipart/form-data": components["schemas"]["PreferenceUserSerialyzer"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PreferenceUserSerialyzer"];
        };
      };
    };
  };
  api_user_preferences_user_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedPreferenceUserSerialyzer"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedPreferenceUserSerialyzer"];
        "multipart/form-data": components["schemas"]["PatchedPreferenceUserSerialyzer"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PreferenceUserSerialyzer"];
        };
      };
    };
  };
  api_user_profile_user_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ProfileUserSerialyzer"];
        };
      };
    };
  };
  api_user_profile_user_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ProfileUserSerialyzer"];
        "application/x-www-form-urlencoded": components["schemas"]["ProfileUserSerialyzer"];
        "multipart/form-data": components["schemas"]["ProfileUserSerialyzer"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ProfileUserSerialyzer"];
        };
      };
    };
  };
  api_user_profile_user_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedProfileUserSerialyzer"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedProfileUserSerialyzer"];
        "multipart/form-data": components["schemas"]["PatchedProfileUserSerialyzer"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ProfileUserSerialyzer"];
        };
      };
    };
  };
  api_user_profile_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ProfileUserSerialyzer"];
        };
      };
    };
  };
  api_user_profile_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["ProfileUserSerialyzer"];
        "application/x-www-form-urlencoded": components["schemas"]["ProfileUserSerialyzer"];
        "multipart/form-data": components["schemas"]["ProfileUserSerialyzer"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ProfileUserSerialyzer"];
        };
      };
    };
  };
  api_user_profile_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: number;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedProfileUserSerialyzer"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedProfileUserSerialyzer"];
        "multipart/form-data": components["schemas"]["PatchedProfileUserSerialyzer"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["ProfileUserSerialyzer"];
        };
      };
    };
  };
  api_user_service_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Service"][];
        };
      };
    };
  };
  api_user_service_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Service"];
        };
      };
    };
  };
  api_user_service_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Service"];
        "application/x-www-form-urlencoded": components["schemas"]["Service"];
        "multipart/form-data": components["schemas"]["Service"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Service"];
        };
      };
    };
  };
  api_user_service_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_service_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedService"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedService"];
        "multipart/form-data": components["schemas"]["PatchedService"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Service"];
        };
      };
    };
  };
  api_user_site_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserSites"][];
        };
      };
    };
  };
  api_user_site_lieu_list: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        site_id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["UserSitesLieux"][];
        };
      };
    };
  };
  api_user_specialites_list: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Specialite"][];
        };
      };
    };
  };
  api_user_specialites_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Specialite"];
        };
      };
    };
  };
  api_user_specialites_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["Specialite"];
        "application/x-www-form-urlencoded": components["schemas"]["Specialite"];
        "multipart/form-data": components["schemas"]["Specialite"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Specialite"];
        };
      };
    };
  };
  api_user_specialites_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  api_user_specialites_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["PatchedSpecialite"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedSpecialite"];
        "multipart/form-data": components["schemas"]["PatchedSpecialite"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["Specialite"];
        };
      };
    };
  };
  api_user_users_lies_all_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  audio_recording_list: {
    parameters: {
      query?: {
        /** @description A page number within the paginated result set. */
        page?: number;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["PaginatedAudioRecordingList"];
        };
      };
    };
  };
  audio_recording_create: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["AudioRecording"];
        "application/x-www-form-urlencoded": components["schemas"]["AudioRecording"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AudioRecording"];
        };
      };
    };
  };
  audio_recording_retrieve: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A UUID string identifying this audio recording. */
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AudioRecording"];
        };
      };
    };
  };
  audio_recording_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A UUID string identifying this audio recording. */
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["AudioRecording"];
        "application/x-www-form-urlencoded": components["schemas"]["AudioRecording"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AudioRecording"];
        };
      };
    };
  };
  audio_recording_destroy: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A UUID string identifying this audio recording. */
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description No response body */
      204: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  audio_recording_partial_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A UUID string identifying this audio recording. */
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        "multipart/form-data": components["schemas"]["PatchedAudioRecording"];
        "application/x-www-form-urlencoded": components["schemas"]["PatchedAudioRecording"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AudioRecording"];
        };
      };
    };
  };
  audio_recording_correct_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        /** @description A UUID string identifying this audio recording. */
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["AudioRecording"];
        "application/x-www-form-urlencoded": components["schemas"]["AudioRecording"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AudioRecording"];
        };
      };
    };
  };
  audio_recording_update_audio_update: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["AudioRecording"];
        "application/x-www-form-urlencoded": components["schemas"]["AudioRecording"];
      };
    };
    responses: {
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AudioRecording"];
        };
      };
    };
  };
  audio_recording_upload_audio_create: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        patient_pk: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        "multipart/form-data": components["schemas"]["AudioRecording"];
        "application/x-www-form-urlencoded": components["schemas"]["AudioRecording"];
      };
    };
    responses: {
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          "application/json": components["schemas"]["AudioRecording"];
        };
      };
    };
  };
}
