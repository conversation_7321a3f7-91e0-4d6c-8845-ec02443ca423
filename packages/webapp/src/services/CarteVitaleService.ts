/**
 * Service for interacting with the Carte Vitale reader service
 * This service communicates with a local C program running on localhost:8082
 */

export interface CarteVitaleServiceResponse {
  success: boolean;
  data?: string; // XML data from carte vitale
  error?: string;
}

export interface CarteVitaleServiceStatus {
  available: boolean;
  error?: string;
}

export class CarteVitaleService {
  private static readonly BASE_URL = 'http://localhost:8082';
  private static readonly TIMEOUT = 5000; // 5 seconds timeout

  /**
   * Check if the carte vitale service is available
   */
  static async checkServiceAvailability(): Promise<CarteVitaleServiceStatus> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch(`${this.BASE_URL}/status`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return { available: true };
      } else {
        return { 
          available: false, 
          error: `Service responded with status ${response.status}` 
        };
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return { 
            available: false, 
            error: 'Service timeout - the carte vitale reader may not be running' 
          };
        }
        return { 
          available: false, 
          error: `Connection failed: ${error.message}` 
        };
      }
      return { 
        available: false, 
        error: 'Unknown error occurred while checking service availability' 
      };
    }
  }

  /**
   * Read carte vitale data from the service
   */
  static async readCarteVitale(): Promise<CarteVitaleServiceResponse> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT);

      const response = await fetch(`${this.BASE_URL}/read`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const xmlData = await response.text();
        return {
          success: true,
          data: xmlData,
        };
      } else {
        const errorText = await response.text();
        return {
          success: false,
          error: `Failed to read carte vitale: ${response.status} - ${errorText}`,
        };
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            success: false,
            error: 'Request timeout - please ensure the carte vitale is properly inserted and try again',
          };
        }
        return {
          success: false,
          error: `Connection failed: ${error.message}`,
        };
      }
      return {
        success: false,
        error: 'Unknown error occurred while reading carte vitale',
      };
    }
  }

  /**
   * Mock method for development/testing when the actual service is not available
   */
  static async mockReadCarteVitale(): Promise<CarteVitaleServiceResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock XML response - this will be replaced with actual sample data later
    const mockXml = `<?xml version="1.0" encoding="UTF-8"?>
<carteVitale>
  <beneficiaire>
    <nom>MARTIN</nom>
    <prenom>Jean</prenom>
    <dateNaissance>1985-03-15</dateNaissance>
    <sexe>M</sexe>
    <numeroSecu>1850315123456</numeroSecu>
  </beneficiaire>
  <ayantsDroit>
    <ayantDroit>
      <nom>MARTIN</nom>
      <prenom>Marie</prenom>
      <dateNaissance>1987-07-22</dateNaissance>
      <sexe>F</sexe>
      <lien>CONJOINT</lien>
    </ayantDroit>
    <ayantDroit>
      <nom>MARTIN</nom>
      <prenom>Lucas</prenom>
      <dateNaissance>2015-12-10</dateNaissance>
      <sexe>M</sexe>
      <lien>ENFANT</lien>
    </ayantDroit>
  </ayantsDroit>
</carteVitale>`;

    return {
      success: true,
      data: mockXml,
    };
  }
}
