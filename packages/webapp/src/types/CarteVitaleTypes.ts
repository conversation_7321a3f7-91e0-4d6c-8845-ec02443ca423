import { SexeEnum } from "../models/types";

/**
 * Raw carte vitale data structure as parsed from XML
 */
export interface CarteVitaleRawData {
  carteVitale: {
    beneficiaire: CarteVitaleBeneficiaire;
    ayantsDroit?: {
      ayantDroit: CarteVitaleAyantDroit | CarteVitaleAyantDroit[];
    };
  };
}

export interface CarteVitaleBeneficiaire {
  nom: string;
  prenom: string;
  dateNaissance: string; // Format: YYYY-MM-DD
  sexe: 'M' | 'F';
  numeroSecu?: string;
  lieuNaissance?: string;
  codePostalNaissance?: string;
  villeNaissance?: string;
}

export interface CarteVitaleAyantDroit {
  nom: string;
  prenom: string;
  dateNaissance: string; // Format: YYYY-MM-DD
  sexe: 'M' | 'F';
  lien: 'CONJOINT' | 'ENFANT' | 'AUTRE';
  numeroSecu?: string;
}

/**
 * Processed carte vitale data ready for patient creation
 */
export interface CarteVitalePatientData {
  nom: string;
  prenom: string;
  sexe: SexeEnum;
  dob: string; // ISO date format
  nom_naissance?: string;
  birth_place_code?: string;
  numeroSecu?: string;
  source: 'BENEFICIAIRE' | 'AYANT_DROIT';
  lien?: string; // For ayants droit
}

/**
 * Complete carte vitale information with all persons
 */
export interface CarteVitaleInfo {
  beneficiaire: CarteVitalePatientData;
  ayantsDroit: CarteVitalePatientData[];
  totalPersons: number;
}

/**
 * Patient matching result
 */
export interface PatientMatchResult {
  patientData: CarteVitalePatientData;
  existingPatient?: {
    id: string;
    nom: string;
    prenom: string;
    dob: string;
    matchScore: number; // 0-100, higher is better match
    matchReasons: string[]; // What matched (name, dob, etc.)
  };
  action: 'CREATE' | 'UPDATE' | 'CONFIRM';
}

/**
 * Carte vitale processing result
 */
export interface CarteVitaleProcessingResult {
  success: boolean;
  carteVitaleInfo?: CarteVitaleInfo;
  matches?: PatientMatchResult[];
  error?: string;
}

/**
 * User selection for patient creation/update
 */
export interface CarteVitaleUserSelection {
  selectedPatients: CarteVitalePatientData[];
  actions: {
    patientData: CarteVitalePatientData;
    action: 'CREATE' | 'UPDATE';
    existingPatientId?: string;
  }[];
}
