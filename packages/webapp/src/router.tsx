import { createBrowserRouter } from "react-router-dom";

import App from "./App";
import CalendarPage from "./pages/Calendar/CalendarPage";
import DocumentDetailPage from "./pages/Documents/DocumentDetailsPage";
import DocumentsPage from "./pages/Documents/DocumentsPage";
import Landing from "./pages/Landing";
import OrdonnancesList from "./pages/OrdonnancesPage/OrdonnancesList";
import ConsultationPage from "./pages/PatientPage/Consultation/ConsultationPage";
import CreateConsultation from "./pages/PatientPage/Consultation/CreateConsultation";
import EditHistory from "./pages/PatientPage/History/EditHistory";
import HospitalisationPage from "./pages/PatientPage/Hospitalisation/HospitalisationPage";
import ListOfPatients from "./pages/PatientPage/ListOfPatients";
import CreateOperation from "./pages/PatientPage/Operation/CreateOperation";
import OperationPage from "./pages/PatientPage/Operation/OperationPage";
import PatientEditPage from "./pages/PatientPage/PatientEditPage";
import PatientPage from "./pages/PatientPage/PatientPage";
import AdminPage from "./pages/Preferences/admin/AdminPage";
import PreferencePage from "./pages/Preferences/PreferencePage";
import ServicePage from "./pages/Service/ServicePage";
import SpeechToTextPage from "./pages/SpeechToText/SpeechToTextDrawer";
import TranscriptionHistoryPage from "./pages/SpeechToText/TranscriptionHistoryPage";
import DailyStats from "./pages/Stats/DailyStats";
import DatesIntervalStatistics from "./pages/Stats/DatesIntervalStatistics";
import QueryStatistics from "./pages/Stats/QueryStatistics";
import StatsIndex from "./pages/Stats/StatsIndex";

// Create a wrapper component that includes the providers
const AppWithProviders = () => {
  return <App />;
};

export const router = createBrowserRouter([
  {
    path: "/",
    element: <AppWithProviders />,
    errorElement: <div>404</div>,
    children: [
      {
        path: "",
        element: <Landing />,
        id: "landing-page",
      },
      {
        path: "patient",
        element: <ListOfPatients />,
        id: "patient-list",
      },
      {
        path: "patient/:id",
        element: <PatientPage />,
        id: "patient-edit-by-id",
      },
      {
        path: "patient/:id/edit",
        element: <PatientEditPage />,
        id: "patient-view-by-id",
      },
      {
        path: "calendar",
        element: <CalendarPage />,
        id: "calendar-page",
      },
      {
        path: "service",
        element: <ServicePage />,
        id: "service",
      },
      {
        path: "consultation/create",
        element: <CreateConsultation />,
        id: "consultation-create",
      },
      {
        path: "consultation/:id",
        element: <ConsultationPage />,
        id: "consultation-view",
      },
      {
        path: "operation/create",
        element: <CreateOperation />,
        id: "operation-create",
      },
      {
        path: "operation/:operation_id",
        element: <OperationPage />,
        id: "operation-view",
      },
      {
        path: "patient/:patient_id/medical-history/edit",
        element: <EditHistory />,
        id: "edit-history-of-patient",
      },
      {
        path: "hospitalisation/:hospitalisation_id",
        element: <HospitalisationPage />,
        id: "hospitalisation-view",
      },
      {
        path: "hospitalisation/:hospitalisation_id/create-operation",
        element: <CreateOperation />,
        id: "hospitalisation-create-operation",
      },
      {
        path: "preferences",
        element: <PreferencePage />,
        id: "preferences-page",
      },
      {
        path: "preferences/admin",
        element: <AdminPage />,
        id: "admin-page",
      },
      {
        path: "documents",
        element: <DocumentsPage />,
        id: "documents-page",
      },
      {
        path: "patient/:id/documents",
        element: <DocumentsPage />,
        id: "patient-documents-page",
      },
      {
        path: "document/:documentId",
        element: <DocumentDetailPage />,
        id: "document-detail-page",
      },
      {
        path: "ordonnances/:patientId",
        element: <OrdonnancesList />,
        id: "ordonnances-list",
      },
      {
        path: "speech-to-text",
        element: <SpeechToTextPage />,
        id: "speech-to-text-page",
      },
      {
        path: "speech-to-text/history",
        element: <TranscriptionHistoryPage />,
        id: "speech-to-text-history-page",
      },
      {
        path: "stats",
        element: <StatsIndex />,
        id: "stats-index-page",
      },
      {
        path: "stats/daily",
        element: <DailyStats />,
        id: "daily-stats-page",
      },
      {
        path: "stats/query",
        element: <QueryStatistics />,
        id: "query-stats-page",
      },
      {
        path: "stats/intervals",
        element: <DatesIntervalStatistics />,
        id: "intervals-stats-page",
      },
    ],
  },
]);
