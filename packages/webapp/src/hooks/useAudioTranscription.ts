// Hook pour la logique de transcription audio
import { useState } from "react";

import { useApi } from "../contexts/ApiContext";

export default function useAudioTranscription() {
  const [loading, setLoading] = useState(false);
  const [transcription, setTranscription] = useState("");
  const [formattedText, setFormattedText] = useState("");
  const apiClient = useApi();

  const transcribe = async (audioBlob: Blob) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("audio", audioBlob, "recording.wav");
      // Appelle la méthode centralisée de l'ApiClient
      const response = await apiClient.transcribeAudio(formData);
      setTranscription(response.transcription || "");
      setFormattedText(response.formatted_text || "");
    } catch (e) {
      setTranscription("Erreur lors de la transcription");
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    transcription,
    formattedText,
    transcribe,
    setTranscription,
    setFormattedText,
  };
}
