// Hook pour la logique de sauvegarde du document
import { useState } from "react";

export default function useDocumentSave(api: any) {
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const saveDocument = async (patient: any, formattedText: string) => {
    setSaving(true);
    setError(null);
    setSuccess(false);
    if (!patient || !patient.id) {
      setError("Patient obligatoire");
      setSaving(false);
      return;
    }
    if (!formattedText) {
      setError("Texte vide");
      setSaving(false);
      return;
    }
    try {
      // Remplace api.saveDocument par la méthode réelle de ton ApiClient
      await api.saveDocument({ patientId: patient.id, text: formattedText });
      setSuccess(true);
    } catch (e) {
      setError("Erreur lors de la sauvegarde");
    } finally {
      setSaving(false);
    }
  };

  return {
    saving,
    error,
    success,
    saveDocument,
  };
}
