import { useState } from "react";

import { useApi } from "../contexts/ApiContext";

interface UseAudioUploadResult {
  uploadAudio: (
    audioBlob: Blob,
    patientId?: string,
    hospitalisationId?: string,
    consultationId?: string,
    title?: string,
    auteur?: string
  ) => Promise<boolean>;
  loading: boolean;
  error: string | null;
}

// Custom hook to handle uploading audio recordings
export function useAudioUpload(): UseAudioUploadResult {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const apiClient = useApi();

  /**
   * Uploads an audio recording with optional patient, hospitalisation, consultation, title, and author.
   * @param audioBlob The audio file blob
   * @param patientId The patient ID
   * @param hospitalisationId The hospitalisation ID
   * @param consultationId The consultation ID
   * @param title The title of the audio recording
   * @param auteur The ID of the author (authenticated user)
   */
  const uploadAudio = async (
    audioBlob: Blob,
    patientId?: string,
    hospitalisationId?: string,
    consultationId?: string,
    title?: string,
    auteur?: string
  ) => {
    setLoading(true);
    setError(null);
    try {
      await apiClient.createAudioRecording(
        audioBlob,
        patientId,
        hospitalisationId,
        consultationId,
        title,
        auteur
      );
      setLoading(false);
      return true;
    } catch (e: any) {
      setError(e.message || "Erreur lors de l'enregistrement audio");
      setLoading(false);
      return false;
    }
  };

  return { uploadAudio, loading, error };
}
