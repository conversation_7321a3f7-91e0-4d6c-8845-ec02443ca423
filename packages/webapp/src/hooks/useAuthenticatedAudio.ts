import React from "react";

import { useApi } from "../contexts/ApiContext";

// Adjust path based on actual ApiContext location

interface UseAuthenticatedAudioReturn {
  blobUrl: string | null;
  isLoading: boolean;
  error: string | null;
  loadAudioForUrl: (url: string | null) => void; // Function to explicitly trigger loading a new URL
}

export const useAuthenticatedAudio = (): UseAuthenticatedAudioReturn => {
  const api = useApi();
  const [targetUrl, setTargetUrl] = React.useState<string | null>(null);
  const [blobUrl, setBlobUrl] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  // Keep track of the created blob URL to revoke it later
  const blobUrlRef = React.useRef<string | null>(null);

  // Effect for fetching and creating blob URL with retry logic
  React.useEffect(() => {
    if (!targetUrl) {
      return;
    }

    let didCancel = false;
    const MAX_RETRIES = 3;
    const RETRY_DELAY_MS = 1000;

    const fetchAudioWithRetries = async (attempt: number) => {
      // On the first attempt, clear any previous state
      if (attempt === 1) {
        setIsLoading(true);
        setError(null);
        if (blobUrlRef.current) {
          URL.revokeObjectURL(blobUrlRef.current);
          blobUrlRef.current = null;
          setBlobUrl(null);
        }
      }

      try {
        const fetchedBlob = await api.getMedia(targetUrl);
        if (!didCancel) {
          const newBlobUrl = URL.createObjectURL(fetchedBlob);
          blobUrlRef.current = newBlobUrl;
          setBlobUrl(newBlobUrl);
          setIsLoading(false); // Success!
        }
      } catch (e) {
        if (didCancel) return;

        console.error(`Error fetching audio (attempt ${attempt} of ${MAX_RETRIES}):`, e);

        if (attempt < MAX_RETRIES) {
          // If retries are left, try again after a delay
          setTimeout(() => {
            if (!didCancel) {
              fetchAudioWithRetries(attempt + 1);
            }
          }, RETRY_DELAY_MS * attempt); // Increase delay for subsequent retries
        } else {
          // Max retries reached, set final error state
          setError('Failed to load audio after multiple attempts.');
          setIsLoading(false);
        }
      }
    };

    fetchAudioWithRetries(1); // Start the fetching process

    return () => {
      didCancel = true;
    };
  }, [targetUrl, api]);

  // Effect for cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (blobUrlRef.current) {
        URL.revokeObjectURL(blobUrlRef.current);
      }
    };
  }, []);

  const loadAudioForUrl = React.useCallback((url: string | null) => {
    // If the URL is the same, don't re-trigger
    setTargetUrl(prev => (prev === url ? prev : url));
  }, []);

  return { blobUrl, isLoading, error, loadAudioForUrl };
};
