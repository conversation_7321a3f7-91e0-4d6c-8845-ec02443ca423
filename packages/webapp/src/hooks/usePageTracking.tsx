import { useEffect } from "react";
import { useLocation } from "react-router-dom";

import { useCurrentPatient } from "../contexts/CurrentPatientContext";
import { useAddToHistory } from "../contexts/NavigationHistoryContext";

// Type pour les options de configuration
interface PageTrackingOptions {
  title: string;
  icon: "patient" | "consultation" | "hospitalisation" | "ordonnance" | "operation";
  includePatient?: boolean;
}

/**
 * Hook pour suivre les pages visitées et les ajouter à l'historique de navigation
 * @param options Options de configuration pour le suivi de page
 */
export const usePageTracking = (options: PageTrackingOptions) => {
  const location = useLocation();
  // const params = useParams(); // Removed unused variable
  const { currentPatient } = useCurrentPatient();
  const addToHistory = useAddToHistory();

  useEffect(() => {
    // Récupérer les données actuelles
    const patientName = currentPatient
      ? `${currentPatient.nom.toUpperCase()} ${currentPatient.prenom}`
      : undefined;

    // Création de l'entrée d'historique
    const historyEntry = {
      path: location.pathname,
      title: options.title,
      icon: options.icon,
      patientName: options.includePatient ? patientName : undefined,
    };

    // Ajouter à l'historique
    addToHistory(historyEntry);
  }, [location.pathname, currentPatient, options, addToHistory]);
};

/**
 * Exemple d'utilisation :
 *
 * // Dans un composant patient
 * usePageTracking({
 *   title: 'Fiche patient',
 *   icon: 'patient',
 *   includePatient: true
 * });
 *
 * // Dans un composant consultation
 * usePageTracking({
 *   title: 'Consultation du ' + format(consultation.date, 'dd/MM/yyyy'),
 *   icon: 'consultation',
 *   includePatient: true
 * });
 */
