import dayjs from "dayjs";
import React, { use<PERSON><PERSON>back, useMemo } from "react";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  DeleteForever,
  Drafts,
  Error,
  Flag,
  GroupRemove,
  LocalHospital,
  Markunread,
  People,
  Warning,
} from "@mui/icons-material";
import FilterAlt from "@mui/icons-material/FilterAlt";
import Info from "@mui/icons-material/Info";
import Mask from "@mui/icons-material/Masks";
import NewReleases from "@mui/icons-material/NewReleases";
import Notifications from "@mui/icons-material/Notifications";
import { Tooltip } from "@mui/joy";
import Box from "@mui/joy/Box";
import DialogContent from "@mui/joy/DialogContent";
import DialogTitle from "@mui/joy/DialogTitle";
import ModalClose from "@mui/joy/ModalClose";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { NfTypeEnum, Notification } from "../../models/types";
import { router } from "../../router";
import PreviewFile from "../File/PreviewFile";
import { NotificationTypes } from "./Notifications";

export default function NotificationsDrawer({
  notifications,
  statusFilter,
  onChangeFilter,
  refresh,
  onClose,
  selectedNotificationId,
  onSelectNotification,
}: {
  notifications: Notification[];
  statusFilter: NfTypeEnum[];
  onChangeFilter: (filter: NfTypeEnum[]) => void;
  refresh: () => void;
  onClose: () => void;
  selectedNotificationId: string | null;
  onSelectNotification: (id: string | null) => void;
}) {
  const api = useApi();
  const { show } = useSnackbar();
  // const [selected, setSelected] = useState<Notification>();
  const selected = useMemo(() => {
    return notifications.find((n) => n.id === selectedNotificationId) || null;
  }, [notifications, selectedNotificationId]);
  const [file, setFile] = React.useState<File>();

  // React.useEffect(() => {
  //   if (selected) {
  //     setselected(notifications.find((n) => n.id === selected.id));
  //   }
  // }, [notifications, selected]);

  React.useEffect(() => {
    if (selected?.doc_link) {
      api
        .getMedia(selected.doc_link)
        .then((blob) => setFile(new File([blob], "doc", { type: blob.type })))
        .catch(() => show("Erreur lors du chargement du fichier", "danger"));
    } else {
      setFile(undefined);
    }
  }, [api, selected, show]);

  const handleFilterChange = (newValue: NfTypeEnum[]) => {
    onChangeFilter(newValue.length ? newValue : NotificationTypes.map((type) => type.value));
  };

  const handleFlag = useCallback(
    (notification: Notification) => {
      api
        .flagNotification(notification.id, !notification.flagged)
        .then(() => refresh())
        .catch(() => show("Erreur lors du flag de la notification", "danger"));
    },
    [api, refresh, show]
  );

  const handleMark = useCallback(
    (notification: Notification) => {
      api
        .markNotification(notification.id, !notification.read)
        .then(() => refresh())
        .catch(() => show("Erreur lors du marquage de la notification", "danger"));
    },
    [api, refresh, show]
  );

  const handleDelete = useCallback(
    (notification: Notification, all = false) => {
      api
        .deleteNotification(notification.id, all)
        .then(() => refresh())
        .catch(() => show("Erreur lors de la suppression de la notification", "danger"));
    },
    [api, refresh, show]
  );

  const handleDeleteAllRead = () => {
    api
      .deleteAllReadNotifications(notifications.filter((n) => n.read).map((n) => n.id))
      .then(() => refresh())
      .catch(() => show("Erreur lors de la suppression des notifications lues", "danger"));
  };

  const handleAllRead = () => {
    api
      .updateReadNotifications(notifications.filter((n) => !n.read).map((n) => n.id))
      .then(() => refresh())
      .catch(() => show("Erreur lors de la mise a lu des notifications lues", "danger"));
  };

  const getColorAndIcon = (notification: Notification) => {
    let color: "neutral" | "primary" | "success" | "warning" | "danger" = "neutral";
    let icon = <NewReleases width={"24px"} height={"24px"} color="info" />;
    switch (notification.nf_type) {
      case "cr_consultation":
        color = "primary";
        icon = <People width={"24px"} height={"24px"} color="primary" />;
        break;
      case "cr_operation":
        color = "success";
        icon = <Mask width={"24px"} height={"24px"} color="success" />;
        break;
      case "cr_hospitalisation":
        color = "warning";
        icon = <LocalHospital width={"24px"} height={"24px"} color="warning" />;
        break;
    }

    return { color, icon };
  };

  const renderActions = useCallback(
    (notification: Notification) => (
      <Stack direction={"row"} gap={1}>
        <Tooltip title={notification.flagged ? "Deflagguer" : "Flagguer"}>
          <Flag
            onClick={(e) => {
              e.stopPropagation();
              handleFlag(notification);
            }}
            sx={(theme) =>
              notification.flagged ? { fill: (theme.palette as any).danger.solidBg } : {}
            }
          />
        </Tooltip>
        {notification.read ? (
          <Tooltip title={"Marquer comme non lu"}>
            <Drafts
              onClick={(e) => {
                e.stopPropagation();
                handleMark(notification);
              }}
            />
          </Tooltip>
        ) : (
          <Tooltip title={"Marquer comme lu"}>
            <Markunread
              onClick={(e) => {
                e.stopPropagation();
                handleMark(notification);
              }}
            />
          </Tooltip>
        )}
        <Tooltip title={"Supprimer"}>
          <DeleteForever
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(notification);
            }}
            sx={(theme) => ({
              fill: (theme.palette as any).danger.solidBg,
            })}
          />
        </Tooltip>
        <Tooltip title={"Supprimer pour tous"}>
          <GroupRemove
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(notification, true);
            }}
            sx={(theme) => ({
              fill: (theme.palette as any).danger.solidBg,
            })}
          />
        </Tooltip>
      </Stack>
    ),
    [handleDelete, handleFlag, handleMark]
  );

  const renderNotification = (notification: Notification) => {
    const { color, icon } = getColorAndIcon(notification);
    const isSelected = selectedNotificationId === notification.id;

    return (
      <Sheet
        key={notification.id}
        color={color}
        variant="soft"
        sx={{
          p: 1,
          borderRadius: "md",
          border: isSelected ? "2px solid #FFA726" : "1px solid transparent",
          backgroundColor: isSelected ? "rgba(255, 167, 38, 0.1)" : "transparent",
          opacity: notification.read ? 0.7 : 1,
          cursor: "pointer",
          transition: "all 0.2s ease-in-out",
        }}
        onClick={() => {
          onSelectNotification(notification.id);
          if (!notification.read) {
            handleMark(notification);
          }
        }}
      >
        <Stack direction={"row"} alignItems={"center"} gap={1} flex={1}>
          {notification.level === "INFO" && (
            <Stack sx={(theme) => ({ fill: theme.palette.primary.solidBg })}>
              <Info sx={{ width: "24px", height: "24px", fill: "inherit" }} />
            </Stack>
          )}
          {notification.level === "WARNING" && (
            <Stack sx={(theme) => ({ fill: theme.palette.warning.solidBg })}>
              <Warning width={"24px"} height={"24px"} color="warning" />
            </Stack>
          )}
          {notification.level === "SUCCESS" && (
            <Stack sx={(theme) => ({ fill: theme.palette.success.solidBg })}>
              <CheckCircle width={"36px"} height={"36px"} color="success" />
            </Stack>
          )}
          {notification.level === "ERROR" && (
            <Stack sx={(theme) => ({ fill: theme.palette.danger.solidBg })}>
              <Error width={"36px"} height={"36px"} color="error" />
            </Stack>
          )}
          <Stack direction="column" flex={1}>
            <Stack flex={1}>
              <Stack direction="row" justifyContent={"space-between"}>
                <Typography level="title-md" startDecorator={icon}>
                  {notification.title}
                </Typography>
                {renderActions(notification)}
              </Stack>
              <Typography level={"body-sm"}>{notification.verb}</Typography>
            </Stack>
            <Stack direction="row" justifyContent="flex-end">
              <Typography level="body-xs">
                {
                  // if today, show time, else show date and time
                  dayjs(notification.created_on).isSame(dayjs(), "day")
                    ? dayjs(notification.created_on).format("HH:mm")
                    : dayjs(notification.created_on).format("DD MMM YYYY — HH:mm")
                }
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      </Sheet>
    );
  };

  const navigateToPage = useCallback(() => {
    if (!selected) return;

    switch (selected.nf_type) {
      case "cr_consultation":
        if (selected.obj_object_id) {
          router.navigate(`/consultation/${selected.obj_object_id}`);
          onClose();
        }
        return;
      case "cr_hospitalisation":
        if (selected.obj_object_id) {
          router.navigate(`/hospitalisation/${selected.obj_object_id}`);
          onClose();
        }
        return;
      case "cr_operation":
        if (selected.obj_object_id) {
          router.navigate(`/operation/${selected.obj_object_id}`);
          onClose();
        }
        return;
      // START - Ajout pour les notifications audio
      case "audio_deletion_scheduled_validated":
      case "audio_deletion_scheduled_inactive":
      case "audio_deleted":
        if (selected.obj_object_id) {
          // Redirige vers la page de consultation associée à l'audio
          router.navigate(`/consultation/${selected.target_object_id}`);
          onClose();
        }
        return;
      // END - Ajout pour les notifications audio
      case "cr_correspondant":
      case "patient_added_in_protocol":
        if (selected.target_object_id) {
          router.navigate(`/patient/${selected.target_object_id}`);
          onClose();
        }
        return;
      // TODO redirection vers la page protocol
      // case "user_added_in_protocol":
      //   router.navigate(`/protocol/${selected.target_object_id}`);
      //   onClose();
      //   return;
    }
  }, [onClose, selected]);

  const renderWindow = useMemo(() => {
    if (!selected) return null;

    const { icon } = getColorAndIcon(selected);
    return (
      <Box
        sx={{
          flex: 1,
          height: "calc(100vh - 32px)",
          maxWidth: "calc(100% - 516px)",
        }}
      >
        <Sheet
          sx={{
            borderRadius: "lg",
            height: "calc(100vh - 28px)",
            overflow: "hidden",
          }}
        >
          <Stack gap={1} height={"100%"}>
            <Stack direction={"row"} justifyContent={"space-between"}>
              <Typography
                level={"title-md"}
                startDecorator={icon}
                onClick={navigateToPage}
                sx={{ cursor: "pointer" }}
              >
                {selected.title}
              </Typography>
              {renderActions(selected)}
            </Stack>
            <Typography level={"body-sm"}>{selected.description}</Typography>
            {selected.recipients?.length > 0 && (
              <Stack direction={"row"} alignItems={"center"} gap={1}>
                <Typography level={"title-md"}>Reçu par : </Typography>
                <Typography level={"body-sm"}>
                  {selected.recipients.map((r) => `${r.first_name} ${r.last_name}`).join(", ")}
                </Typography>
              </Stack>
            )}
            {file && <PreviewFile file={file} />}
          </Stack>
        </Sheet>
      </Box>
    );
  }, [file, navigateToPage, renderActions, selected]);

  return (
    <Stack direction="row" gap={2} sx={{ width: "100vw", p: 2 }}>
      <Box sx={{ minWidth: "500px", maxWidth: "500px" }}>
        <Sheet sx={{ borderRadius: "lg" }}>
          <DialogTitle sx={{ mb: 2 }}>
            <Typography level="title-lg" startDecorator={<Notifications />}>
              Notifications
            </Typography>
            <ModalClose />
          </DialogTitle>
          <DialogContent>
            <Stack gap={1}>
              <Stack direction={"row"} justifyContent={"space-between"}>
                <Stack direction="row" spacing={1} alignItems="center" width={"90%"}>
                  <Typography level="body-sm" minWidth={"95px"} sx={{ mr: 1 }}>
                    <FilterAlt fontSize="small" sx={{ verticalAlign: "middle", mr: 0.5 }} />
                    Filtrer par:
                  </Typography>
                  <Select
                    value={statusFilter}
                    onChange={(e, v) => handleFilterChange(v)}
                    multiple
                    renderValue={(selectedOptions) =>
                      selectedOptions.length === NotificationTypes.length
                        ? "Tout"
                        : selectedOptions.map((o) => o.label).join(", ")
                    }
                  >
                    {NotificationTypes.map((type) => (
                      <Option value={type.value}>{type.display}</Option>
                    ))}
                  </Select>
                </Stack>
                <Stack direction="row" alignItems="center" gap={1}>
                  <Tooltip title={"Mettre à lues toutes les notifications"}>
                    <Markunread
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAllRead();
                      }}
                    />
                  </Tooltip>
                  <Tooltip title={"Supprimer toutes les notifications lues"}>
                    <DeleteForever
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAllRead();
                      }}
                      sx={(theme) => ({
                        fill: (theme.palette as any).danger.solidBg,
                      })}
                    />
                  </Tooltip>
                </Stack>
              </Stack>
              <Stack gap={1} height={"calc(100vh - 144px)"} overflow={"scroll"}>
                {notifications.length > 0 ? (
                  notifications.map(renderNotification)
                ) : (
                  <Typography level="body-md">Aucune notification</Typography>
                )}
              </Stack>
            </Stack>
          </DialogContent>
        </Sheet>
      </Box>
      {renderWindow}
    </Stack>
  );
}
