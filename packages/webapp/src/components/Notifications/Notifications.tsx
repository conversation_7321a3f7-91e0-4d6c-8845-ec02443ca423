import { keyframes } from "@emotion/react";
import React, { useCallback } from "react";

import NotificationsIcon from "@mui/icons-material/Notifications";
import NotificationsActiveIcon from "@mui/icons-material/NotificationsActive";
import Badge from "@mui/joy/Badge";
import Box from "@mui/joy/Box";
import Drawer from "@mui/joy/Drawer";

import { useApi } from "../../contexts/ApiContext";
import { useInterval } from "../../hooks/useInterval";
import { NfTypeEnum, Notification } from "../../models/types";
import NotificationsDrawer from "./NotificationsDrawer";

const bellAnimation = keyframes`
  0% {
    transform: translate(0, 0);
  }
  10% {
    transform: translate(10px, 0);
  }
  20% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(5px, 0);
  }
  30% {
    transform: translate(0, 0);
  }
  32.5% {
    transform: translate(3px, 0);
  }
  35% {
    transform: translate(0, 0);
  }
    36% {
    transform: translate(2px, 0);
  }
  37% {
    transform: translate(0, 0);
  }
`;

export const NotificationTypes: { value: NfTypeEnum; display: string }[] = [
  { value: "cr_consultation", display: "CR Consultation" },
  { value: "cr_operation", display: "CR Opération" },
  { value: "cr_hospitalisation", display: "CR Hospitalisation" },
  { value: "user_added_to_staff", display: "User ajouté au staff" },
  { value: "cr_correspondant", display: "CR Correspondant" },
  {
    value: "patient_added_in_protocol",
    display: "Patient ajouté à un protocol",
  },
  { value: "user_added_in_protocol", display: "User ajouté à un protocol" },
  {
    value: "audio_deletion_scheduled_validated",
    display: "Avertissement Suppression Audio (Validée)",
  },
  {
    value: "audio_deletion_scheduled_inactive",
    display: "Avertissement Suppression Audio (Inactivité)",
  },
  { value: "audio_deleted", display: "Confirmation Suppression Audio" },
  { value: "audio_deletion_cancelled", display: "Annulation Suppression Audio" },
];

export default function Notifications() {
  const api = useApi();

  const [open, setOpen] = React.useState(false);
  const [notifications, setNotifications] = React.useState<Notification[]>([]);
  const [count, setCount] = React.useState(0);
  const [statusFilter, setStatusFilter] = React.useState<NfTypeEnum[]>(
    NotificationTypes.map((type) => type.value)
  );
  const [selectedNotificationId, setSelectedNotificationId] = React.useState<string | null>(null);

  const handleRefresh = React.useCallback(() => {
    if (api) {
      api.getNotifications(statusFilter).then((res) => {
        setNotifications(res.results);
        setCount(res.count);
      });
    }
  }, [api, statusFilter]);

  React.useEffect(() => {
    handleRefresh();
  }, [handleRefresh]);

  // Refresh every 5 min
  useInterval(() => {
    handleRefresh();
  }, 300000);

  const onClose = useCallback(() => setOpen(false), []);

  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <Badge
      badgeContent={count}
      color="warning"
      size="sm"
      sx={{
        animation: notifications.length > 0 ? `${bellAnimation} 2s infinite` : "initial",
        "&:hover": { animation: "initial" },
      }}
    >
      <Drawer
        size="md"
        variant="plain"
        open={open}
        onClose={onClose}
        slotProps={{
          content: {
            sx: {
              bgcolor: "transparent",
              boxShadow: "none",
              width: "100vw",
            },
          },
        }}
      >
        <NotificationsDrawer
          notifications={notifications}
          statusFilter={statusFilter}
          onChangeFilter={(newFilter) => {
            setStatusFilter(newFilter);
            setSelectedNotificationId(null); // Reset selection on filter change
          }}
          refresh={handleRefresh}
          onClose={onClose}
          selectedNotificationId={selectedNotificationId}
          onSelectNotification={setSelectedNotificationId}
        />
      </Drawer>

      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          transition: "transform 0.2s ease, opacity 0.2s ease",
          "&:hover": {
            transform: "translateY(-2px)",
            opacity: 0.9,
          },
        }}
        onClick={handleOpen}
      >
        {notifications.length > 0 ? (
          <NotificationsActiveIcon sx={{ color: "white", fontSize: "20px" }} />
        ) : (
          <NotificationsIcon sx={{ color: "white", fontSize: "20px" }} />
        )}
      </Box>
    </Badge>
  );
}
