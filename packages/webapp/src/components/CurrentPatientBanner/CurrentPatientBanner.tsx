import React from "react";
import { useNavigate } from "react-router-dom";

import BookmarkIcon from "@mui/icons-material/Bookmark";
import CakeIcon from "@mui/icons-material/Cake";
import CheckmarkIcon from "@mui/icons-material/CheckCircleOutline";
import CheckListIcon from "@mui/icons-material/ChecklistRtl";
import ClearIcon from "@mui/icons-material/Clear";
import FemaleIcon from "@mui/icons-material/Female";
import InfoIcon from "@mui/icons-material/Info";
import MaleIcon from "@mui/icons-material/Male";
import PeopleIcon from "@mui/icons-material/People";
import RestartAltOutlined from "@mui/icons-material/RestartAltOutlined";
import SearchIcon from "@mui/icons-material/Search";
import VerifiedUserOutlined from "@mui/icons-material/VerifiedUserOutlined";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Chip, { ChipProps } from "@mui/joy/Chip";
import Divider from "@mui/joy/Divider";
import IconButton from "@mui/joy/IconButton";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import Stack from "@mui/joy/Stack";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { Patient } from "../../models/types";
import { formatDate } from "../../utils/utils";
import PatientContactListModal from "../Contact/PatientContactListModal";
import PatientInfoEditModal from "../Modals/PatientInfoEditModal";
import HistoryIconComponent from "../Navigation/HistoryIcon";
import PatientSearchAutocomplete from "../PatientSearch/PatientSearchAutocomplete";
import CompteRenduModal from "./modals/CompteRenduModal";

interface CurrentPatientBannerProps {
  patient?: Patient | null;
  handleResetIns?: () => Promise<void>;
  handleVerifyIns?: () => Promise<void>;
  isResetting?: boolean;
  isVerifying?: boolean;
  insStatusInfo?: { text: string; icon: React.ReactElement; color: any };
  insTooltipTitle?: string;
}

export default function CurrentPatientBanner({
  patient: propPatient,
  handleResetIns,
  handleVerifyIns,
  isResetting = false,
  isVerifying = false,
  insStatusInfo,
  insTooltipTitle,
}: CurrentPatientBannerProps) {
  const { currentPatient, setCurrentPatient } = useCurrentPatient();
  const patient = propPatient ?? currentPatient;
  const navigate = useNavigate();

  const [openedModal, setOpenedModal] = React.useState<
    "info" | "contacts" | "comptes-rendus-correspondants" | "rappels" | null
  >(null);
  const [insModalOpen, setInsModalOpen] = React.useState<boolean>(false);

  // --- Determine INS Display State ---
  const insExists = Boolean(patient?.ins);
  const isIdentityVerified = patient?.checked_identity && patient?.checked_identity !== "NONE";
  const isInsProvisional = Boolean(patient?.ins_oid?.endsWith(".9"));
  const isLookupSuccess = patient?.ins_lookup_status === "SUCCESS";

  let chipColor: ChipProps["color"] = insStatusInfo?.color || "neutral";
  let chipIcon: React.ReactElement | null = insStatusInfo?.icon || null;
  let chipText = insStatusInfo?.text || "Statut INS inconnu";
  let modalText = insTooltipTitle || "Détails non disponibles";

  const isQualified = insExists && isIdentityVerified && isLookupSuccess;
  const isRetrievedNotVerified = insExists && !isIdentityVerified && isLookupSuccess;

  if (isQualified) {
    chipColor = "success";
    chipIcon = <CheckmarkIcon color="success" fontSize="small" />;
    chipText = patient.ins!;
    modalText = "INS Qualifié";
  } else if (isRetrievedNotVerified) {
    chipColor = "warning";
    chipIcon = null;
    chipText = patient.ins!;
    modalText = `INS récupéré avec succès.\nIdentité patient non vérifiée.`;
  } else {
    chipColor = insStatusInfo?.color || "danger";
    chipIcon = insStatusInfo?.icon || null;
    chipText = insStatusInfo?.text || "Erreur ou Inconnu";
    modalText = insTooltipTitle || "Détails non disponibles";
  }

  const handleInsClick = () => {
    if (handleResetIns && handleVerifyIns && insStatusInfo && insTooltipTitle) {
      setInsModalOpen(true);
    }
  };

  const handleInsClose = () => {
    setInsModalOpen(false);
  };

  // Function to clear the current patient
  const handleClearPatient = () => {
    if (setCurrentPatient) {
      setCurrentPatient(null);
    }
  };

  const getGenderIcon = (gender?: string) => {
    if (gender === "MALE") return <MaleIcon fontSize="small" sx={{ color: "white" }} />;
    if (gender === "FEMALE") return <FemaleIcon fontSize="small" sx={{ color: "white" }} />;
    return null;
  };

  const getGenderLabel = (gender?: string) => {
    if (gender === "MALE") return "Homme";
    if (gender === "FEMALE") return "Femme";
    return "";
  };

  const getAge = (dob?: string) => {
    if (!dob) return "";
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    return age;
  };

  return (
    <Box position="relative" width="100%" height="50px" zIndex={1000}>
      <Box
        sx={{
          position: "fixed",
          width: "calc(100% - 96px)",
          height: "50px",
          left: "96px",
          top: 0,
          px: 2,
          zIndex: 1000,
          backgroundColor: (theme) => theme.palette.primary[500],
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* Left side - Search input and history icon */}
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <PatientSearchAutocomplete
            navigateOnSelect
            width="350px"
            sx={{
              backgroundColor: (theme) => theme.palette.primary[300],
              borderColor: (theme) => theme.palette.primary[200],
              color: "white",
              "& .MuiInput-input": {
                color: "white",
              },
              "& .MuiInput-placeholder": {
                color: "rgba(255, 255, 255, 0.7)",
              },
            }}
            startDecorator={<SearchIcon sx={{ color: "white" }} />}
          />
          {/* Utilisation du composant HistoryIcon */}
          <React.Suspense fallback={null}>
            <HistoryIconComponent />
          </React.Suspense>
        </Box>

        {/* Right side - Current Patient or No Patient message */}
        <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>
          {patient ? (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                height: "38px",
                backgroundColor: (theme) => theme.palette.primary[400],
                borderRadius: "5px",
                border: "1px solid rgba(255, 255, 255, 0.3)",
                boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.15)",
                position: "relative",
                pl: 3.5,
                pr: "2px",
              }}
            >
              {/* Bookmark clip icon hovering above the patient box */}
              <BookmarkIcon
                sx={{
                  position: "absolute",
                  top: "-8px",
                  left: "0",
                  fontSize: "28px",
                  zIndex: 1,
                  filter: "drop-shadow(0px 1px 1px rgba(0,0,0,0.3))",
                  color: "white",
                }}
              />

              {/* Patient name with ellipsis for overflow */}
              <Stack
                direction="row"
                alignItems="center"
                spacing={0.5}
                onClick={() => navigate(`/patient/${patient?.id}`)}
                sx={{ cursor: "pointer" }}
              >
                <Typography
                  level="title-sm"
                  sx={{
                    color: "white",
                    maxWidth: "120px",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    fontWeight: "bold",
                  }}
                >
                  {patient?.nom.toUpperCase()}
                </Typography>
                <Typography
                  level="title-sm"
                  sx={{
                    color: "white",
                    maxWidth: "120px",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {patient?.prenom}
                </Typography>
              </Stack>

              {/* Patient information */}
              <Divider
                orientation="vertical"
                sx={{
                  mx: 1,
                  height: "26px",
                  borderColor: "rgba(255,255,255,0.3)",
                }}
              />

              <Stack direction="row" gap={1} alignItems="center">
                <Chip
                  variant="soft"
                  color="primary"
                  size="sm"
                  startDecorator={getGenderIcon(patient?.sexe)}
                  sx={{
                    backgroundColor: "rgba(255,255,255,0.15)",
                    borderColor: "rgba(255,255,255,0.2)",
                    border: "1px solid",
                    fontWeight: "500",
                  }}
                >
                  <Typography level="body-sm" sx={{ color: "white" }}>
                    {getGenderLabel(patient?.sexe)}
                  </Typography>
                </Chip>

                <Chip
                  variant="soft"
                  color="primary"
                  size="sm"
                  startDecorator={<CakeIcon fontSize="small" sx={{ color: "white" }} />}
                  sx={{
                    backgroundColor: "rgba(255,255,255,0.15)",
                    borderColor: "rgba(255,255,255,0.2)",
                    border: "1px solid",
                    fontWeight: "500",
                  }}
                >
                  <Typography level="body-sm" sx={{ color: "white" }}>
                    {formatDate(patient?.dob)} ({getAge(patient?.dob)} ans)
                  </Typography>
                </Chip>

                {insStatusInfo && insTooltipTitle && (
                  <Box
                    onClick={handleInsClick}
                    sx={{
                      cursor: handleResetIns && handleVerifyIns ? "pointer" : "default",
                    }}
                  >
                    <Chip
                      variant="soft"
                      size="sm"
                      color="primary"
                      sx={{
                        borderColor: "rgba(255,255,255,0.2)",
                        border: "1px solid",
                        fontWeight: "500",
                      }}
                      startDecorator={
                        <Typography level="body-xs" sx={{ color: "white", fontStyle: "italic" }}>
                          INS
                        </Typography>
                      }
                    >
                      <Typography
                        level="body-sm"
                        sx={{ color: "white", ml: 0.5 }}
                        startDecorator={chipIcon}
                        data-testid="patient-ins-number"
                      >
                        {chipText}
                        {insExists && isInsProvisional && (
                          <Chip
                            size="sm"
                            color="warning"
                            sx={{
                              ml: 1,
                              fontSize: "0.7rem",
                              fontWeight: "bold",
                            }}
                          >
                            provisoire
                          </Chip>
                        )}
                      </Typography>
                    </Chip>
                  </Box>
                )}
              </Stack>

              <Divider
                orientation="vertical"
                sx={{
                  mx: 1,
                  height: "26px",
                  borderColor: "rgba(255,255,255,0.3)",
                }}
              />

              {/* Action buttons - plain white style */}
              <Stack direction="row" gap={1} alignItems="center">
                <Tooltip title="Informations patient" placement="bottom">
                  <IconButton
                    size="sm"
                    variant="plain"
                    onClick={() => setOpenedModal("info")}
                    sx={{
                      "&:hover": {
                        backgroundColor: "rgba(255,255,255,0.15)",
                      },
                    }}
                  >
                    <InfoIcon sx={{ fontSize: "20px", color: "white" }} />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Comptes rendus" placement="bottom">
                  <IconButton
                    size="sm"
                    variant="plain"
                    onClick={() => setOpenedModal("comptes-rendus-correspondants")}
                    sx={{
                      "&:hover": {
                        backgroundColor: "rgba(255,255,255,0.15)",
                      },
                    }}
                  >
                    <CheckListIcon sx={{ fontSize: "20px", color: "white" }} />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Contacts" placement="bottom">
                  <IconButton
                    size="sm"
                    variant="plain"
                    onClick={() => setOpenedModal("contacts")}
                    sx={{
                      "&:hover": {
                        backgroundColor: "rgba(255,255,255,0.15)",
                      },
                    }}
                  >
                    <PeopleIcon sx={{ fontSize: "20px", color: "white" }} />
                  </IconButton>
                </Tooltip>

                {/* Clear current patient button */}
                <Tooltip title="Fermer patient actuel" placement="bottom">
                  <IconButton
                    size="sm"
                    variant="outlined"
                    color="danger"
                    onClick={handleClearPatient}
                    sx={{
                      color: "white",
                      borderColor: "rgba(255,255,255,0.5)",
                      ml: 1,
                    }}
                  >
                    <ClearIcon sx={{ fontSize: "20px" }} />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Box>
          ) : (
            <Chip
              variant="soft"
              color="neutral"
              size="md"
              sx={{
                mr: 2,
                backgroundColor: "rgba(255,255,255,0.15)",
                color: "white",
                border: "1px solid rgba(255,255,255,0.2)",
              }}
              startDecorator={
                <BookmarkIcon sx={{ opacity: 0.8, fontSize: "20px", color: "white" }} />
              }
            >
              <Typography level="body-sm" sx={{ color: "white", fontWeight: "500" }}>
                Aucun patient sélectionné
              </Typography>
            </Chip>
          )}
        </Box>
      </Box>

      {handleResetIns && handleVerifyIns && insStatusInfo && insTooltipTitle && (
        <Modal open={insModalOpen} onClose={handleInsClose}>
          <ModalDialog size="sm" variant="outlined">
            <ModalClose aria-label="Close" />
            <Typography level="title-md" mb={1}>
              Statut INS
            </Typography>

            {patient?.ins ? (
              <>
                <Stack direction="column" spacing={0.5} sx={{ mb: 1.5 }}>
                  <Typography level="body-sm" fontWeight="bold">
                    INS Complet
                  </Typography>
                  <Typography level="body-sm">{patient?.ins}</Typography>
                </Stack>

                <Stack direction="column" spacing={0.5} sx={{ mb: 1.5 }}>
                  <Typography level="body-sm" fontWeight="bold">
                    Clé de contrôle
                  </Typography>
                  <Typography level="body-sm">
                    {patient?.ins?.includes("-") ? patient?.ins?.split("-")[1] : "Non disponible"}
                  </Typography>
                </Stack>

                <Stack direction="column" spacing={0.5} sx={{ mb: 1.5 }}>
                  <Typography level="body-sm" fontWeight="bold">
                    OID
                  </Typography>
                  <Typography level="body-sm">
                    {patient.ins_oid
                      ? patient.ins_oid.startsWith("urn:oid:")
                        ? patient.ins_oid.substring(8)
                        : patient.ins_oid
                      : "Non disponible"}
                    {patient.ins_oid && (
                      <>
                        {" "}
                        (
                        {patient.ins_oid?.endsWith(".8")
                          ? "NIR"
                          : patient.ins_oid?.endsWith(".9")
                            ? "NIA"
                            : "Inconnu"}
                        )
                      </>
                    )}
                    {patient.ins_oid?.endsWith(".9") && (
                      <Chip
                        size="sm"
                        variant="soft"
                        color="warning"
                        sx={{ ml: 1, fontSize: "0.7rem" }}
                      >
                        provisoire
                      </Chip>
                    )}
                  </Typography>
                </Stack>
              </>
            ) : (
              <Stack direction="column" spacing={0.5} sx={{ mb: 1.5 }}>
                <Typography level="body-sm">Aucun INS disponible pour ce patient.</Typography>
              </Stack>
            )}

            <Chip variant="soft" color={chipColor} startDecorator={chipIcon} sx={{ mb: 1.5 }}>
              {modalText}
            </Chip>
            <Divider sx={{ my: 1.5 }} />
            <Stack direction="column" spacing={1} alignItems="stretch">
              <Button
                onClick={() => {
                  handleVerifyIns();
                  handleInsClose();
                }}
                startDecorator={<VerifiedUserOutlined />}
                variant="outlined"
                color="primary"
                size="sm"
                loading={isVerifying}
                disabled={!patient?.ins}
              >
                Vérifier INS
              </Button>
              <Button
                onClick={() => {
                  handleResetIns();
                  handleInsClose();
                }}
                startDecorator={<RestartAltOutlined />}
                variant="outlined"
                color="warning"
                size="sm"
                loading={isResetting}
              >
                Réinitialiser Statut
              </Button>
            </Stack>
          </ModalDialog>
        </Modal>
      )}

      {currentPatient &&
        !!openedModal &&
        (openedModal === "contacts" ? (
          <PatientContactListModal
            patient={currentPatient}
            open={openedModal === "contacts"}
            onClose={() => setOpenedModal(null)}
          />
        ) : openedModal === "info" ? (
          <PatientInfoEditModal
            patient={currentPatient}
            open
            onClose={() => setOpenedModal(null)}
          />
        ) : (
          openedModal === "comptes-rendus-correspondants" && (
            <CompteRenduModal patient={currentPatient} open onClose={() => setOpenedModal(null)} />
          )
        ))}
    </Box>
  );
}
