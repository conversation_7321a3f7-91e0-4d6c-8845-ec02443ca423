import React from "react";

import { <PERSON><PERSON><PERSON><PERSON>, PersonAdd, Warning } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Card,
  Chip,
  DialogTitle,
  Divider,
  Modal,
  ModalClose,
  ModalDialog,
  ModalOverflow,
  Stack,
  Typography,
} from "@mui/joy";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { Patient } from "../../models/types";
import { CarteVitaleInfo, CarteVitalePatientData } from "../../types/CarteVitaleTypes";
import { formatDate, ISODateToAge } from "../../utils/utils";

interface CarteVitaleModalProps {
  open: boolean;
  onClose: () => void;
  carteVitaleInfo: CarteVitaleInfo | null;
  onPatientCreated?: (patient: Patient) => void;
  onPatientUpdated?: (patient: Patient) => void;
}

export function CarteVitaleModal({
  open,
  onClose,
  carteVitaleInfo,
  onPatientCreated,
  onPatientUpdated,
}: CarteVitaleModalProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [loading, setLoading] = React.useState(false);
  const [selectedPatients, setSelectedPatients] = React.useState<CarteVitalePatientData[]>([]);
  const [creationErrors, setCreationErrors] = React.useState<{ [key: string]: string }>({});

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (open && carteVitaleInfo) {
      setSelectedPatients([carteVitaleInfo.beneficiaire]);
      setCreationErrors({});
    } else {
      setSelectedPatients([]);
      setCreationErrors({});
    }
  }, [open, carteVitaleInfo]);

  const handlePatientToggle = (patientData: CarteVitalePatientData) => {
    setSelectedPatients((prev) => {
      const isSelected = prev.some(
        (p) =>
          p.nom === patientData.nom && p.prenom === patientData.prenom && p.dob === patientData.dob
      );

      if (isSelected) {
        return prev.filter(
          (p) =>
            !(
              p.nom === patientData.nom &&
              p.prenom === patientData.prenom &&
              p.dob === patientData.dob
            )
        );
      } else {
        return [...prev, patientData];
      }
    });
  };

  const handleCreatePatients = async () => {
    if (!api || selectedPatients.length === 0) return;

    setLoading(true);
    setCreationErrors({});

    try {
      const createdPatients: Patient[] = [];
      const errors: { [key: string]: string } = {};

      for (const patientData of selectedPatients) {
        try {
          // Convert CarteVitalePatientData to PatchedPatient format
          const patientPayload = {
            nom: patientData.nom,
            nom_naissance: patientData.nom_naissance || patientData.nom,
            prenom: patientData.prenom,
            sexe: patientData.sexe,
            dob: patientData.dob,
            birth_place_code: patientData.birth_place_code || "",
          };

          const createdPatient = await api.createPatient(patientPayload);
          createdPatients.push(createdPatient);
        } catch (error: any) {
          const patientKey = `${patientData.prenom} ${patientData.nom}`;

          if (error instanceof Error && (error.cause as any)?.error === "PATIENT_ALREADY_EXISTS") {
            const existingPatient = (error.cause as any).patient;
            errors[patientKey] =
              `Patient déjà existant: ${existingPatient.prenom} ${existingPatient.nom} (${existingPatient.dob})`;
          } else {
            errors[patientKey] = "Erreur lors de la création du patient";
          }
        }
      }

      // Update errors state
      setCreationErrors(errors);

      // Notify success for created patients
      if (createdPatients.length > 0) {
        if (createdPatients.length === 1) {
          snackbar.show(
            `Patient ${createdPatients[0].nom} ${createdPatients[0].prenom} créé avec succès`,
            "success"
          );
          onPatientCreated?.(createdPatients[0]);
        } else {
          snackbar.show(`${createdPatients.length} patients créés avec succès`, "success");
          // For multiple patients, call onPatientCreated for the first one (beneficiaire)
          onPatientCreated?.(createdPatients[0]);
        }
      }

      // If all patients were created successfully, close the modal
      if (Object.keys(errors).length === 0) {
        onClose();
      }
    } catch (error) {
      console.error("Error creating patients:", error);
      snackbar.show("Erreur lors de la création des patients", "danger");
    } finally {
      setLoading(false);
    }
  };

  const renderPatientCard = (patientData: CarteVitalePatientData) => {
    const isSelected = selectedPatients.some(
      (p) =>
        p.nom === patientData.nom && p.prenom === patientData.prenom && p.dob === patientData.dob
    );

    return (
      <Card
        key={`${patientData.nom}-${patientData.prenom}-${patientData.dob}`}
        variant={isSelected ? "soft" : "outlined"}
        color={isSelected ? "primary" : "neutral"}
        sx={{
          cursor: "pointer",
          transition: "all 0.2s",
          "&:hover": {
            boxShadow: "md",
          },
        }}
        onClick={() => handlePatientToggle(patientData)}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack spacing={1}>
            <Typography level="title-md">
              {patientData.prenom} {patientData.nom}
            </Typography>
            <Stack direction="row" spacing={1}>
              <Chip size="sm" variant="outlined">
                {patientData.sexe === "MALE" ? "Homme" : "Femme"}
              </Chip>
              <Chip size="sm" variant="outlined">
                {formatDate(patientData.dob)} ({ISODateToAge(patientData.dob)})
              </Chip>
              {patientData.source === "BENEFICIAIRE" && (
                <Chip size="sm" color="primary">
                  Bénéficiaire
                </Chip>
              )}
              {patientData.source === "AYANT_DROIT" && (
                <Chip size="sm" color="warning">
                  {patientData.lien}
                </Chip>
              )}
            </Stack>
            {patientData.numeroSecu && (
              <Typography level="body-sm" color="neutral">
                N° Sécu: {patientData.numeroSecu}
              </Typography>
            )}
          </Stack>
          {isSelected && <CheckCircle color="primary" />}
        </Stack>
      </Card>
    );
  };

  if (!carteVitaleInfo) {
    return null;
  }

  const allPatients = [carteVitaleInfo.beneficiaire, ...carteVitaleInfo.ayantsDroit];

  return (
    <Modal open={open} onClose={onClose}>
      <ModalOverflow>
        <ModalDialog sx={{ width: "600px", maxHeight: "80vh" }}>
          <ModalClose />
          <DialogTitle>Informations Carte Vitale</DialogTitle>

          <Stack spacing={3}>
            <Alert color="success" startDecorator={<CheckCircle />}>
              Carte vitale lue avec succès ! {carteVitaleInfo.totalPersons} personne(s) trouvée(s).
            </Alert>

            <Box>
              <Typography level="title-sm" sx={{ mb: 2 }}>
                Sélectionnez les patients à créer :
              </Typography>
              <Stack spacing={2}>{allPatients.map(renderPatientCard)}</Stack>
            </Box>

            {Object.keys(creationErrors).length > 0 && (
              <Box>
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Erreurs de création :
                </Typography>
                <Stack spacing={2}>
                  {Object.entries(creationErrors).map(([patientKey, errorMessage]) => (
                    <Alert key={patientKey} color="warning" startDecorator={<Warning />}>
                      <Stack spacing={1}>
                        <Typography level="title-sm">{patientKey}</Typography>
                        <Typography level="body-sm">{errorMessage}</Typography>
                      </Stack>
                    </Alert>
                  ))}
                </Stack>
              </Box>
            )}

            <Divider />

            <Stack direction="row" spacing={2} justifyContent="flex-end">
              <Button variant="plain" onClick={onClose} disabled={loading}>
                Annuler
              </Button>
              <Button
                startDecorator={<PersonAdd />}
                onClick={handleCreatePatients}
                disabled={selectedPatients.length === 0 || loading}
                loading={loading}
              >
                Créer{" "}
                {selectedPatients.length > 1 ? `${selectedPatients.length} patients` : "le patient"}
              </Button>
            </Stack>
          </Stack>
        </ModalDialog>
      </ModalOverflow>
    </Modal>
  );
}
