import React from "react";

import { Check<PERSON><PERSON><PERSON>, PersonAdd, Warning } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Card,
  Chip,
  DialogTitle,
  Divider,
  Modal,
  ModalClose,
  ModalDialog,
  ModalOverflow,
  Stack,
  Typography,
} from "@mui/joy";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { Patient } from "../../models/types";
import {
  CarteVitaleInfo,
  CarteVitalePatientData,
  PatientMatchResult,
} from "../../types/CarteVitaleTypes";
import { findPatientMatches } from "../../utils/PatientMatcher";
import { formatDate, ISODateToAge } from "../../utils/utils";

interface CarteVitaleModalProps {
  open: boolean;
  onClose: () => void;
  carteVitaleInfo: CarteVitaleInfo | null;
  onPatientCreated?: (patient: Patient) => void;
  onPatientUpdated?: (patient: Patient) => void;
}

export function CarteVitaleModal({
  open,
  onClose,
  carteVitaleInfo,
  onPatientCreated,
  onPatientUpdated,
}: CarteVitaleModalProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [loading, setLoading] = React.useState(false);
  const [selectedPatients, setSelectedPatients] = React.useState<CarteVitalePatientData[]>([]);
  const [matches, setMatches] = React.useState<PatientMatchResult[]>([]);

  // Reset state when modal opens/closes and perform patient matching
  React.useEffect(() => {
    if (open && carteVitaleInfo && api) {
      setSelectedPatients([carteVitaleInfo.beneficiaire]);

      // Perform patient matching
      const performMatching = async () => {
        try {
          const allPatients = [carteVitaleInfo.beneficiaire, ...carteVitaleInfo.ayantsDroit];
          const matchResults = await findPatientMatches(allPatients, (query) =>
            api.searchPatients(query)
          );
          setMatches(matchResults);
        } catch (error) {
          console.error("Error performing patient matching:", error);
          setMatches([]);
        }
      };

      performMatching();
    } else {
      setSelectedPatients([]);
      setMatches([]);
    }
  }, [open, carteVitaleInfo, api]);

  const handlePatientToggle = (patientData: CarteVitalePatientData) => {
    setSelectedPatients((prev) => {
      const isSelected = prev.some(
        (p) =>
          p.nom === patientData.nom && p.prenom === patientData.prenom && p.dob === patientData.dob
      );

      if (isSelected) {
        return prev.filter(
          (p) =>
            !(
              p.nom === patientData.nom &&
              p.prenom === patientData.prenom &&
              p.dob === patientData.dob
            )
        );
      } else {
        return [...prev, patientData];
      }
    });
  };

  const handleCreatePatients = async () => {
    if (!api || selectedPatients.length === 0) return;

    setLoading(true);
    try {
      const createdPatients: Patient[] = [];

      for (const patientData of selectedPatients) {
        // Convert CarteVitalePatientData to PatchedPatient format
        const patientPayload = {
          nom: patientData.nom,
          nom_naissance: patientData.nom_naissance || patientData.nom,
          prenom: patientData.prenom,
          sexe: patientData.sexe,
          dob: patientData.dob,
          birth_place_code: patientData.birth_place_code || "",
        };

        const createdPatient = await api.createPatient(patientPayload);
        createdPatients.push(createdPatient);
      }

      // Notify success
      if (createdPatients.length === 1) {
        snackbar.show(
          `Patient ${createdPatients[0].nom} ${createdPatients[0].prenom} créé avec succès`,
          "success"
        );
        onPatientCreated?.(createdPatients[0]);
      } else {
        snackbar.show(`${createdPatients.length} patients créés avec succès`, "success");
        // For multiple patients, call onPatientCreated for the first one (beneficiaire)
        if (createdPatients.length > 0) {
          onPatientCreated?.(createdPatients[0]);
        }
      }

      onClose();
    } catch (error) {
      console.error("Error creating patients:", error);
      snackbar.show("Erreur lors de la création des patients", "danger");
    } finally {
      setLoading(false);
    }
  };

  const renderPatientCard = (patientData: CarteVitalePatientData) => {
    const isSelected = selectedPatients.some(
      (p) =>
        p.nom === patientData.nom && p.prenom === patientData.prenom && p.dob === patientData.dob
    );

    return (
      <Card
        key={`${patientData.nom}-${patientData.prenom}-${patientData.dob}`}
        variant={isSelected ? "soft" : "outlined"}
        color={isSelected ? "primary" : "neutral"}
        sx={{
          cursor: "pointer",
          transition: "all 0.2s",
          "&:hover": {
            boxShadow: "md",
          },
        }}
        onClick={() => handlePatientToggle(patientData)}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack spacing={1}>
            <Typography level="title-md">
              {patientData.prenom} {patientData.nom}
            </Typography>
            <Stack direction="row" spacing={1}>
              <Chip size="sm" variant="outlined">
                {patientData.sexe === "MALE" ? "Homme" : "Femme"}
              </Chip>
              <Chip size="sm" variant="outlined">
                {formatDate(patientData.dob)} ({ISODateToAge(patientData.dob)})
              </Chip>
              {patientData.source === "BENEFICIAIRE" && (
                <Chip size="sm" color="primary">
                  Bénéficiaire
                </Chip>
              )}
              {patientData.source === "AYANT_DROIT" && (
                <Chip size="sm" color="warning">
                  {patientData.lien}
                </Chip>
              )}
            </Stack>
            {patientData.numeroSecu && (
              <Typography level="body-sm" color="neutral">
                N° Sécu: {patientData.numeroSecu}
              </Typography>
            )}
          </Stack>
          {isSelected && <CheckCircle color="primary" />}
        </Stack>
      </Card>
    );
  };

  if (!carteVitaleInfo) {
    return null;
  }

  const allPatients = [carteVitaleInfo.beneficiaire, ...carteVitaleInfo.ayantsDroit];

  return (
    <Modal open={open} onClose={onClose}>
      <ModalOverflow>
        <ModalDialog sx={{ width: "600px", maxHeight: "80vh" }}>
          <ModalClose />
          <DialogTitle>Informations Carte Vitale</DialogTitle>

          <Stack spacing={3}>
            <Alert color="success" startDecorator={<CheckCircle />}>
              Carte vitale lue avec succès ! {carteVitaleInfo.totalPersons} personne(s) trouvée(s).
            </Alert>

            <Box>
              <Typography level="title-sm" sx={{ mb: 2 }}>
                Sélectionnez les patients à créer :
              </Typography>
              <Stack spacing={2}>{allPatients.map(renderPatientCard)}</Stack>
            </Box>

            {matches.some((match) => match.existingPatient) && (
              <Box>
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Patients similaires trouvés :
                </Typography>
                <Alert color="warning" startDecorator={<Warning />} sx={{ mb: 2 }}>
                  Des patients similaires ont été trouvés dans la base de données. Vérifiez s'il
                  s'agit des mêmes personnes avant de créer de nouveaux patients.
                </Alert>
                <Stack spacing={2}>
                  {matches
                    .filter((match) => match.existingPatient)
                    .map((match, index) => (
                      <Card key={index} variant="outlined" color="warning">
                        <Stack spacing={1}>
                          <Typography level="title-sm">
                            Carte vitale: {match.patientData.prenom} {match.patientData.nom}
                          </Typography>
                          <Typography level="body-sm" color="warning">
                            Patient existant similaire ({match.existingPatient!.matchScore}% de
                            correspondance):
                          </Typography>
                          <Typography level="body-sm">
                            {match.existingPatient!.prenom} {match.existingPatient!.nom} -{" "}
                            {formatDate(match.existingPatient!.dob)}
                          </Typography>
                          <Typography level="body-xs" color="neutral">
                            Correspondances: {match.existingPatient!.matchReasons.join(", ")}
                          </Typography>
                          <Button
                            size="sm"
                            variant="outlined"
                            color="warning"
                            onClick={() =>
                              window.open(`/patient/${match.existingPatient!.id}`, "_blank")
                            }
                          >
                            Voir le patient existant
                          </Button>
                        </Stack>
                      </Card>
                    ))}
                </Stack>
              </Box>
            )}

            <Divider />

            <Stack direction="row" spacing={2} justifyContent="flex-end">
              <Button variant="plain" onClick={onClose} disabled={loading}>
                Annuler
              </Button>
              <Button
                startDecorator={<PersonAdd />}
                onClick={handleCreatePatients}
                disabled={selectedPatients.length === 0 || loading}
                loading={loading}
              >
                Créer{" "}
                {selectedPatients.length > 1 ? `${selectedPatients.length} patients` : "le patient"}
              </Button>
            </Stack>
          </Stack>
        </ModalDialog>
      </ModalOverflow>
    </Modal>
  );
}
