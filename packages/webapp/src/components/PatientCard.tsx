import moment from "moment";
import React from "react";

import { Cake, Female, InfoOutlined, Male, People, WarningAmber } from "@mui/icons-material";
import ExitToAppRoundedIcon from "@mui/icons-material/ExitToAppRounded";
import PersonIcon from "@mui/icons-material/Person";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { useApi } from "../contexts/ApiContext";
import { useSnackbar } from "../contexts/SnackbarContext";
import { isUser } from "../models/type_guards";
import { Hospitalisation, Patient } from "../models/types";
import ContactInfo from "../pages/PatientPage/Administratif/ContactInfo";
import GeneralInfo from "../pages/PatientPage/Administratif/GeneralInfo";
import { router } from "../router";
import { formatDate, formatNameAndSurname, ISODateToAge, isToday } from "../utils/utils";
import PatientContactListModal from "./Contact/PatientContactListModal";
import CurrentPatientManager from "./CurrentPatientManager";
import ModalComponent from "./Modals/Modal";

interface PatientCardProps {
  patient?: Patient | null;
  width?: string;
  hospitalisation?: Hospitalisation;
  onLeave?: (leaveDate: string | undefined) => void;
  hideButtons?: boolean;
}

export default function PatientCard({
  patient,
  width = "300px",
  hospitalisation,
  hideButtons = false,
  onLeave,
}: PatientCardProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [open, setOpen] = React.useState(false);
  const [leave, setLeave] = React.useState<string>();
  const [openInfo, setOpenInfo] = React.useState(false);
  const [openContact, setOpenContact] = React.useState(false);

  const updateSortieDate = async () => {
    if (!hospitalisation) return;

    try {
      await api.patchHospitalisation({
        id: hospitalisation.id,
        sortie_date: leave,
      });
      onLeave && onLeave(leave);
      snackbar.show("Date de sortie mise a jour", "success");
    } catch (e) {
      snackbar.show("Erreur lors de la mise a jour de la Date de sortie", "danger");
    }
  };

  if (!patient)
    return (
      <Sheet sx={{ px: 2, borderRadius: "sm", width }} variant="soft">
        <CurrentPatientManager />
      </Sheet>
    );

  let color: "neutral" | "primary" | "warning" | "success" = "neutral";
  if (hospitalisation?.hospitalisation_ambulatoire) color = "primary";
  else if (hospitalisation?.entree_date && isToday(hospitalisation.entree_date)) color = "success";
  else if (hospitalisation?.sortie_date && isToday(hospitalisation.sortie_date)) color = "warning";

  return (
    <>
      <Sheet
        sx={{
          p: 2,
          borderRadius: "sm",
          width,
          transition: "all 0.2s ease-in-out",
          "&:hover": {
            transform: "translateY(-2px)",
            boxShadow: "sm",
          },
        }}
        variant="soft"
        color={color}
      >
        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
            <Stack direction="row" spacing={2} alignItems="center">
              <PersonIcon />
              <Stack>
                <Typography
                  level="title-md"
                  sx={{
                    cursor: "pointer",
                    "&:hover": {
                      textDecoration: "underline",
                    },
                  }}
                  onClick={() => router.navigate(`/patient/${patient.id}`)}
                >
                  {formatNameAndSurname(patient.nom, patient.prenom)}
                  {patient.nom_naissance &&
                    patient.nom_naissance !== patient.nom &&
                    " né " + patient.nom_naissance.toUpperCase()}
                </Typography>
                <Stack direction="row" spacing={0.5} alignItems="center">
                  <Chip
                    size="sm"
                    variant="outlined"
                    startDecorator={patient.sexe === "MALE" ? <Male /> : <Female />}
                  >
                    {patient.sexe === "MALE" ? "Homme" : "Femme"}
                  </Chip>
                  <Chip size="sm" variant="outlined" startDecorator={<Cake />}>
                    {new Date(patient.dob).toLocaleDateString("fr")}
                    {" " + ISODateToAge(patient.dob)}
                  </Chip>
                  {patient.etat && patient.etat === "DCD" && (
                    <Chip
                      size="sm"
                      variant="outlined"
                      color="warning"
                      startDecorator={<WarningAmber />}
                    >
                      Décédé
                    </Chip>
                  )}
                </Stack>
              </Stack>
            </Stack>
            {!hideButtons && (
              <Stack direction="row" spacing={1}>
                <IconButton
                  size="sm"
                  variant="outlined"
                  onClick={() => setOpenInfo(true)}
                  sx={{ cursor: "pointer" }}
                >
                  <InfoOutlined />
                </IconButton>
                <IconButton
                  size="sm"
                  variant="outlined"
                  onClick={() => setOpenContact(true)}
                  sx={{ cursor: "pointer" }}
                >
                  <People />
                </IconButton>
              </Stack>
            )}
          </Stack>

          {hospitalisation && (
            <Stack spacing={1}>
              <Typography
                color="warning"
                level="title-sm"
                sx={{
                  cursor: "pointer",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
                onClick={() => router.navigate(`/hospitalisation/${hospitalisation.id}`)}
                data-cy="hospitalisation-motif"
              >
                {hospitalisation.motif_hospitalisation}
              </Typography>
              <Stack direction="row" spacing={2} alignItems="center">
                <Chip size="sm" variant="outlined" color="success">
                  Entrée: {formatDate(hospitalisation.entree_date)}
                </Chip>
                {hospitalisation.sortie_date && (
                  <Chip size="sm" variant="outlined" color="warning">
                    Sortie: {formatDate(hospitalisation.sortie_date)}
                  </Chip>
                )}
              </Stack>
              <Stack direction="row" spacing={1} alignItems="center" justifyContent="flex-end">
                {hospitalisation.medecin_referent.map(
                  (m) =>
                    isUser(m) && (
                      <Chip key={m.id} size="sm" variant="soft" color="neutral">
                        Dr {m.last_name}
                      </Chip>
                    )
                )}
                <Tooltip title="Définir la date de sortie">
                  <IconButton
                    variant="plain"
                    onClick={() => {
                      setLeave(
                        hospitalisation?.sortie_date ||
                          hospitalisation?.entree_date ||
                          moment().format("YYYY-MM-DD")
                      );
                      setOpen(true);
                    }}
                  >
                    <ExitToAppRoundedIcon sx={{ fill: "red" }} />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Stack>
          )}
        </Stack>
      </Sheet>

      <Modal open={open} onClose={() => setOpen(false)}>
        <ModalDialog>
          <Typography level="title-md">Date de sortie</Typography>
          <Input type="date" value={leave} onChange={(e) => setLeave(e.target.value)} />
          <Box
            sx={{
              mt: 1,
              display: "flex",
              gap: 1,
              flexDirection: { xs: "column", sm: "row-reverse" },
            }}
          >
            <Button
              variant="solid"
              color="primary"
              onClick={() => {
                setOpen(false);
                updateSortieDate();
              }}
            >
              Confirmer
            </Button>
            <Button variant="plain" color="neutral" onClick={() => setOpen(false)}>
              Annuler
            </Button>
          </Box>
        </ModalDialog>
      </Modal>

      <ModalComponent
        open={openInfo}
        title="Détails administratifs"
        onClose={() => setOpenInfo(false)}
        closeLabel="Fermer"
      >
        <Stack direction="row" gap={2}>
          <GeneralInfo patient={patient} />
          <ContactInfo patient={patient} />
        </Stack>
      </ModalComponent>

      <PatientContactListModal
        patient={patient}
        open={openContact}
        onClose={() => setOpenContact(false)}
      />
    </>
  );
}
