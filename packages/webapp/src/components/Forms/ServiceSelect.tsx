import React from "react";

import { Place } from "@mui/icons-material";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { Service } from "../../models/custom";
import { UserSites } from "../../models/types";

interface ServiceSelectProps {
  value: Service["id"] | null;
  onSelect: (value: Service) => void;
  width?: string;
}

export default function ServiceSelect({ value, onSelect, width = "auto" }: ServiceSelectProps) {
  const api = useApi();

  const [services, setServices] = React.useState<Service[]>([]);

  // Utiliser useRef pour éviter les boucles de rendu
  const hasLoadedRef = React.useRef(false);
  const autoSelectRef = React.useRef(false);

  React.useEffect(() => {
    // Ne charger les services qu'une seule fois
    if (hasLoadedRef.current) return;

    const fetch = async () => {
      hasLoadedRef.current = true;
      const res = await api.listUserServices(true);
      setServices(res);

      // Marquer pour auto-sélection si un seul service est disponible
      if (res.length === 1 && !value) {
        autoSelectRef.current = true;
      }
    };
    fetch();
  }, [api, value]); // Pas de dépendance pour éviter les appels répétés

  // Séparer l'auto-sélection du chargement pour éviter les boucles
  React.useEffect(() => {
    if (autoSelectRef.current && services.length === 1 && !value) {
      autoSelectRef.current = false;
      onSelect(services[0]);
    }
  }, [services, value, onSelect]);

  return (
    <Select
      value={value}
      slotProps={{
        listbox: { sx: { width: "100%", maxWidth: "100%" } },
        button: { sx: { width: "100%", minWidth: 0, maxWidth: "100%" } },
      }}
      sx={{
        width: "100%",
        minWidth: 0,
        maxWidth: "100%",
        overflow: "hidden",
      }}
      onChange={(e, newService) => {
        if (newService) {
          onSelect(services.find((s) => s.id === newService) as Service);
        }
      }}
    >
      <Option
        value={null}
        sx={{
          opacity: 0.5,
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
        }}
      >
        Non renseigné
      </Option>
      {services.map((service) => (
        <Option
          key={service.id}
          value={service.id}
          sx={{
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            maxWidth: "100%",
          }}
        >
          <Stack
            gap={0.5}
            sx={{
              width: "100%",
              overflow: "hidden",
              maxWidth: "100%",
            }}
          >
            <Typography
              level="body-xs"
              startDecorator={<Place sx={{ fontSize: "12px" }} />}
              noWrap
              overflow="hidden"
              textOverflow="ellipsis"
              maxWidth="100%"
            >
              {(service.site_service as unknown as UserSites).nom_site}
            </Typography>
            <Typography noWrap overflow="hidden" textOverflow="ellipsis" maxWidth="100%">
              {service.nom_service}
            </Typography>
          </Stack>
        </Option>
      ))}
    </Select>
  );
}
