import React from "react";

import CancelOutlined from "@mui/icons-material/CancelOutlined";
import GroupIcon from "@mui/icons-material/Group";
import Checkbox from "@mui/joy/Checkbox";
import Chip from "@mui/joy/Chip";
import Divider from "@mui/joy/Divider";
import Stack from "@mui/joy/Stack";
import Tooltip from "@mui/joy/Tooltip";

import { useApi } from "../../contexts/ApiContext";
import { useDebounce } from "../../hooks/useDebounce";
import { User } from "../../models/types";

interface DoctorSelectChipsProps {
  value?: number | number[];
  onSelect: (doctorId: number | number[]) => void;
  multiple?: boolean;
}

export default function DoctorSelectChips({ value, onSelect, multiple }: DoctorSelectChipsProps) {
  const api = useApi();

  const [selectedDr, setSelectedDr] = React.useState<number | number[] | null>(
    multiple ? ((value || []) as number[]) : (value as number)
  );
  const debouncedSelectedDr = useDebounce(selectedDr, 500);
  const [linkedUsers, setLinkedUsers] = React.useState<User[]>([]);

  React.useEffect(() => {
    if (value !== undefined) {
      setSelectedDr(multiple ? ((value || []) as number[]) : (value as number));
    }
  }, [value, multiple]);

  React.useEffect(() => {
    const fetchLinkedUsers = async () => {
      const users = await api?.getLinkedUsers();
      setLinkedUsers(users || []);
    };
    fetchLinkedUsers();
  }, [api]);

  const areAllSelected = React.useMemo(() => {
    if (!Array.isArray(selectedDr) || linkedUsers.length === 0) return false;
    return linkedUsers.every((user) => selectedDr.includes(user.id));
  }, [selectedDr, linkedUsers]);

  React.useEffect(() => {
    if (debouncedSelectedDr !== null && debouncedSelectedDr !== undefined) {
      const isEqual = multiple
        ? Array.isArray(value) &&
          Array.isArray(debouncedSelectedDr) &&
          value.length === debouncedSelectedDr.length &&
          value.every((id) => debouncedSelectedDr.includes(id))
        : value === debouncedSelectedDr;

      if (!isEqual) {
        onSelect(debouncedSelectedDr);
      }
    }
  }, [debouncedSelectedDr, onSelect, value, multiple]);

  return (
    <Stack direction="row" gap={1} alignItems="center">
      {multiple && (
        <>
          <Tooltip title={areAllSelected ? "Désélectionner tous" : "Sélectionner tous"}>
            <Chip
              variant="outlined"
              color={areAllSelected ? "neutral" : "primary"}
              sx={{
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  bgcolor: areAllSelected ? "primary.softHoverBg" : "neutral.softHoverBg",
                  transform: "scale(1.02)",
                },
                width: "32px",
                justifyContent: "center",
                lineHeight: 1,
              }}
              onClick={() => {
                const newValue = areAllSelected ? [] : linkedUsers.map((user) => user.id);
                setSelectedDr(newValue);
              }}
            >
              {areAllSelected ? <CancelOutlined /> : <GroupIcon />}
            </Chip>
          </Tooltip>
          <Divider orientation="vertical" />
        </>
      )}
      {linkedUsers.map((user) => {
        const checked = Array.isArray(selectedDr)
          ? selectedDr.includes(user.id)
          : selectedDr === user.id;
        return (
          <Chip
            key={user.last_name?.toUpperCase() + " " + user.first_name}
            variant="soft"
            color={checked ? "primary" : "neutral"}
            sx={{
              bgcolor: checked ? "primary.softBg" : "neutral.softBg",
              transition: "all 0.2s ease-in-out",
              "&:hover": {
                bgcolor: checked ? "primary.softHoverBg" : "neutral.softHoverBg",
                transform: "scale(1.02)",
              },
            }}
          >
            <Checkbox
              variant="outlined"
              color={checked ? "primary" : "neutral"}
              disableIcon
              overlay
              label={user.last_name?.toUpperCase() + " " + user.first_name}
              checked={checked}
              onChange={() => {
                const newValue = multiple
                  ? checked
                    ? (selectedDr as number[]).filter((id) => id !== user.id)
                    : [...(selectedDr as number[]), user.id]
                  : user.id;
                setSelectedDr(newValue);
              }}
            />
          </Chip>
        );
      })}
    </Stack>
  );
}
