import dayjs from "dayjs";
import React from "react";

import { Chip } from "@mui/joy";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { Operation, Patient } from "../../models/types";
import { formatDate } from "../../utils/utils";

interface OperationSelectProps {
  patientId: Patient["id"];
  value: string[];
  onChange: (operations: string[]) => void;
}

export default function OperationSelect({ patientId, value, onChange }: OperationSelectProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [operations, setOperations] = React.useState<Operation[]>([]);

  React.useEffect(() => {
    const fetchOperations = async () => {
      try {
        if (patientId) {
          const operations = await api.getOperationsByPatient(patientId);
          setOperations(operations);
        } else {
          setOperations([]);
        }
      } catch (error) {
        snackbar.show("Erreur lors de la récupération des opérations du patient", "danger");
      }
    };
    fetchOperations();
  }, [patientId, api, snackbar]);

  return (
    <Select
      multiple
      value={value}
      onChange={(e, newValue) => {
        onChange(newValue as string[]);
      }}
      renderValue={(selected) => (
        <Stack direction="row" gap={0.5} flexWrap="wrap">
          {selected.map((option) => {
            const operation = operations.find((op) => op.id === option.value);
            return (
              <Chip
                size="sm"
                key={option.value}
                sx={{ maxWidth: "300px", textOverflow: "ellipsis" }}
              >
                {formatDate(operation?.operation_date)} — {operation?.titre_operation}
              </Chip>
            );
          })}
        </Stack>
      )}
    >
      {operations.length > 0 ? (
        operations.map((operation) => (
          <Option key={operation.id} value={operation.id}>
            <Stack direction="row" gap={1} alignItems="center">
              <Typography level="body-sm">
                {dayjs(operation.operation_date).format("DD/MM/YYYY")}
              </Typography>
              <Typography level="body-sm">{operation.titre_operation}</Typography>
            </Stack>
          </Option>
        ))
      ) : (
        <Option disabled value="">
          Aucune opération trouvée pour le patient
        </Option>
      )}
    </Select>
  );
}
