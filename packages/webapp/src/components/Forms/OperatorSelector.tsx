import React, { useState } from "react";

import Autocomplete from "@mui/joy/Autocomplete";

import { useApi } from "../../contexts/ApiContext";
import { useUser } from "../../contexts/UserContext";
import { isUser } from "../../models/type_guards";
import { Intervenant } from "../../models/types";

interface OperatorSelectorProps {
  onSelect: (value: number[]) => void;
  exludeOperators?: number[];
  includeCurrentUser?: boolean;
}

export default function OperatorSelector({
  onSelect,
  exludeOperators,
  includeCurrentUser = true,
}: OperatorSelectorProps) {
  const api = useApi();
  const user = useUser();
  const [operators, setOperators] = useState<Intervenant[]>([]);
  const [selectedOperators, setSelectedOperators] = React.useState<Intervenant[]>([]);

  React.useEffect(() => {
    api.getUserIntervenantsEvents().then((res) => {
      const ope = res.Operateur.filter(
        (o) => !exludeOperators?.includes(isUser(o.zkf_user) ? o.zkf_user.id : 0)
      );
      setOperators(ope);
      // includeCurrentUser && setSelectedOperators(ope.filter((o) => o.zkf_user === user.user?.id));
      if (includeCurrentUser && user.user?.id) {
        setSelectedOperators(
          ope.filter((o) => (isUser(o.zkf_user) ? o.zkf_user.id === user.user?.id : false))
        );
      }
    });
  }, [api, exludeOperators, includeCurrentUser, user.user?.id]);

  return (
    <Autocomplete
      sx={{ flex: 1 }}
      value={selectedOperators}
      multiple
      disableCloseOnSelect
      onChange={(e, newValue) => {
        onSelect(
          newValue
            .map((o) => (isUser(o.zkf_user) ? o.zkf_user.id : 0))
            .filter((id): id is number => typeof id === "number")
        );
        setSelectedOperators(newValue || []);
      }}
      disabled={operators.length === 0}
      options={operators}
      getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
      isOptionEqualToValue={(option, value) => option.id === value.id}
    />
  );
}
