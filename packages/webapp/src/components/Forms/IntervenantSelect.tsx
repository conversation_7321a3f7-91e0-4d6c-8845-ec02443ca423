import React from "react";

import { Autocomplete } from "@mui/joy";

import { useApi } from "../../contexts/ApiContext";
import { useAuthenticatedUser } from "../../contexts/UserContext";
import { Intervenant } from "../../models/types";

interface IntervenantSelectProps {
  value: Intervenant["id"][];
  onChange: (value: Intervenant["id"][]) => void;
  initValueWithUser?: boolean;
  type?: "Operateur" | "Anesthesiste" | "Aide Operatoire";
}

export default function IntervenantSelect({
  value,
  onChange,
  initValueWithUser = false,
  type = "Operateur",
}: IntervenantSelectProps) {
  const api = useApi();
  const { user } = useAuthenticatedUser();

  const [intervenants, setIntervenants] = React.useState<Intervenant[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);

  React.useEffect(() => {
    const fetch = async () => {
      try {
        setLoading(true);
        console.log(`Fetching intervenants for type: ${type}`);

        // IMPORTANT: Méthode 1 - Récupérer tous les intervenants globalement
        const allIntervenants = await api.listUserIntervenants();
        console.log("All intervenants (for debugging):", allIntervenants);

        // IMPORTANT: Méthode 2 - Récupérer les intervenants par événements (méthode préférée)
        const intervenantsEvent = await api.getUserIntervenantsEvents();
        console.log("Fetched intervenants by events:", intervenantsEvent);

        // Choisir la source de données à utiliser
        let dataSource = intervenantsEvent;

        // Si les données d'événements sont vides ou ne contiennent pas le type demandé
        if (!dataSource || !dataSource[type] || dataSource[type].length === 0) {
          console.warn(`No ${type} found in events, trying all intervenants`);
          // Essayer d'utiliser tous les intervenants s'ils sont disponibles
          if (allIntervenants && allIntervenants.length > 0) {
            console.log("Using all intervenants as fallback");
            // Créer une structure similaire à intervenantsEvent
            const tempSource: any = {};
            tempSource[type] = allIntervenants;
            dataSource = tempSource;
          }
        }

        if (dataSource && dataSource[type] && dataSource[type].length > 0) {
          console.log(`Setting ${dataSource[type].length} ${type}(s)`);
          setIntervenants(dataSource[type]);
        } else {
          console.warn(`No ${type} found in any data source, using empty array`);
          setIntervenants([]);
        }
      } catch (error) {
        console.error(`Erreur lors de la récupération des ${type}:`, error);

        // Essayer une requête alternative en cas d'erreur
        try {
          console.log("Trying alternative API method to get intervenants");
          const alternativeData = await api.listUserIntervenants();
          if (alternativeData && alternativeData.length > 0) {
            console.log("Got intervenants from alternative source");
            setIntervenants(alternativeData);
          } else {
            console.warn("Alternative source returned no data");
            setIntervenants([]);
          }
        } catch (fallbackError) {
          console.error("All fallback methods failed:", fallbackError);
          setIntervenants([]);
        }
      } finally {
        setLoading(false);
      }
    };

    // Force fetch on component mount or type change
    fetch();
  }, [type, api]); // Dépendre uniquement du type pour éviter les rechargements en boucle

  // Définition de l'intervenant utilisateur par défaut
  const initializedRef = React.useRef(false);
  const userRef = React.useRef(user);

  React.useEffect(() => {
    // Ne définir la valeur par défaut qu'une seule fois
    if (
      initValueWithUser &&
      intervenants.length > 0 &&
      !initializedRef.current &&
      value.length === 0
    ) {
      initializedRef.current = true;
      const userIntervenant = intervenants.find((i) => i.zkf_user === userRef.current?.id);
      if (userIntervenant) {
        onChange([userIntervenant.id]);
      }
    }
  }, [initValueWithUser, intervenants, value.length, onChange]);

  // Force la mise à jour du composant quand value change
  React.useEffect(() => {
    // Ce useEffect vide force le rendu du composant quand value change
  }, [value]);

  // Si aucun intervenant n'est disponible, afficher un message d'erreur
  if (intervenants.length === 0 && !loading) {
    console.warn(`No ${type} available in the component`);
  }

  // Filtrer les valeurs sélectionnées pour ne garder que celles qui existent dans les intervenants
  const selectedValues = React.useMemo(() => {
    if (!value || value.length === 0 || intervenants.length === 0) {
      return [];
    }
    try {
      // S'assurer que tous les intervenants ont un ID valide
      const validIntervenants = intervenants.filter((i) => !!i && !!i.id);
      const existingValues = validIntervenants.filter((s) => value.includes(s.id));
      console.log(
        `Found ${existingValues.length} selected ${type}(s) in intervenants list of ${validIntervenants.length} items`
      );
      return existingValues;
    } catch (err) {
      console.error("Error filtering selected values:", err);
      return [];
    }
  }, [value, intervenants, type]);

  return (
    <Autocomplete
      multiple
      loading={loading}
      value={selectedValues}
      onChange={(e, newInter) => {
        console.log("Selected in component:", newInter);
        // S'assurer que chaque intervenant a un ID avant d'appeler onChange
        const validIntervenants = newInter.filter((i) => !!i.id);
        onChange(validIntervenants.map((s) => s.id));
      }}
      options={intervenants}
      getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
      isOptionEqualToValue={(option, value) => option.id === value?.id}
      filterSelectedOptions
      key={`${type}-${value.join("-")}`} // Force le rendu quand value ou type change
      clearOnBlur={false} // Éviter de vider les sélections au clic en dehors
      autoHighlight // Aide à la sélection rapide
      openOnFocus // Ouvre le menu au focus
      noOptionsText={`Aucun ${type} disponible`}
      loadingText="Chargement..."
      size="sm"
    />
  );
}
