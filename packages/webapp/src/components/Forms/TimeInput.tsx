import dayjs from "dayjs";

import AccessTimeIcon from "@mui/icons-material/AccessTime";
import Button from "@mui/joy/Button";
import Input from "@mui/joy/Input";
import Stack from "@mui/joy/Stack";
import { ColorPaletteProp } from "@mui/joy/styles/types/colorSystem";
import { SxProps } from "@mui/joy/styles/types/theme";

interface TimeInputProps {
  value: string;
  color?: ColorPaletteProp;
  onChange: (value: string) => void;
  sx?: SxProps;
  startDecorator?: React.ReactNode;
  showIncrementButtons?: boolean;
}

export default function TimeInput({
  value,
  color = "neutral",
  onChange,
  sx,
  startDecorator,
  showIncrementButtons = false,
}: TimeInputProps) {
  // Fonction pour ajouter 5 minutes à l'heure de fin
  const addFiveMinutes = (timeValue: string) => {
    if (!timeValue) return timeValue;

    const [hours, minutes] = timeValue.split(":").map(Number);
    let newMinutes = minutes + 5;
    let newHours = hours;

    if (newMinutes >= 60) {
      newMinutes -= 60;
      newHours = (newHours + 1) % 24;
    }

    const formattedHours = newHours.toString().padStart(2, "0");
    const formattedMinutes = newMinutes.toString().padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  // Fonction pour soustraire 5 minutes à l'heure de fin
  const subtractFiveMinutes = (timeValue: string) => {
    if (!timeValue) return timeValue;

    const [hours, minutes] = timeValue.split(":").map(Number);
    let newMinutes = minutes - 5;
    let newHours = hours;

    if (newMinutes < 0) {
      newMinutes += 60;
      newHours = (newHours - 1 + 24) % 24;
    }

    const formattedHours = newHours.toString().padStart(2, "0");
    const formattedMinutes = newMinutes.toString().padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  // Fonction pour définir l'heure actuelle
  const setCurrentTime = () => {
    const now = dayjs();
    return now.format("HH:mm");
  };

  return (
    <Stack direction="row" alignItems="center">
      <Stack direction="row" sx={{ flex: 1, position: "relative" }}>
        <Input
          color={color}
          type="time"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          startDecorator={startDecorator}
          sx={{
            ...sx,
            flex: 1,
            ...(showIncrementButtons && {
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
            }),
          }}
        />
        <Button
          size="sm"
          variant="plain"
          color="neutral"
          onClick={() => onChange(setCurrentTime())}
          sx={{
            position: "absolute",
            right: showIncrementButtons ? "36px" : "36px",
            top: "50%",
            transform: "translateY(-50%)",
            minWidth: "26px",
            padding: "2px",
            margin: 0,
            minHeight: "0 !important",
          }}
        >
          <AccessTimeIcon sx={{ fontSize: "1rem" }} />
        </Button>
      </Stack>
      {showIncrementButtons && (
        <Stack alignItems="center">
          <Button
            color={color}
            onClick={() => onChange(addFiveMinutes(value))}
            variant="outlined"
            size="sm"
            sx={{
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
              borderBottomRightRadius: 0,
              borderLeft: "none",
              padding: 0,
              margin: 0,
              height: "18px !important",
              minHeight: "0 !important",
              width: "18px",
            }}
          >
            +
          </Button>
          <Button
            color={color}
            onClick={() => onChange(subtractFiveMinutes(value))}
            variant="outlined"
            size="sm"
            sx={{
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
              borderTopRightRadius: 0,
              borderLeft: "none",
              borderTop: "none",
              padding: 0,
              margin: 0,
              height: "18px !important",
              width: "18px",
              minHeight: "0 !important",
              // apply to this button only the same boxshadow as joy inputs but opacity 0.5
              boxShadow: (theme) =>
                `0 0 1px 0px ${theme.colorSchemes.light.palette.neutral.outlinedBorder}`,
            }}
          >
            -
          </Button>
        </Stack>
      )}
    </Stack>
  );
}
