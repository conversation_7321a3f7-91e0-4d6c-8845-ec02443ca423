import React from "react";

import { PersonAdd } from "@mui/icons-material";
import Autocomplete from "@mui/joy/Autocomplete";
import Button from "@mui/joy/Button";
import DialogTitle from "@mui/joy/DialogTitle";
import ListItem from "@mui/joy/ListItem";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalOverflow from "@mui/joy/ModalOverflow";
import Typography from "@mui/joy/Typography";

import { CreatePatient } from "./CreatePatient";
import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { Patient } from "../../models/types";

interface PatientSelectorProps {
  value: string;
  onSelect: (value: Patient | null) => void;
  disabled?: boolean;
  onCreatePatient?: () => void;
}

export default function PatientSelector({
  value,
  onSelect,
  disabled = false,
  onCreatePatient,
}: PatientSelectorProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const [query, setQuery] = React.useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = React.useState(query);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [patients, setPatients] = React.useState<Patient[]>([]);
  const [openCreatePatientModal, setOpenCreatePatientModal] = React.useState<boolean>(false);
  
  React.useEffect(() => {
    api.getPatient(value).then((res) => {
      setPatients([res]);
    });
  }, [api, value]);

  React.useEffect(() => {
    if (value && patients.length === 1) return;

    setLoading(true);
    if (query === "" || query === " ") {
      setLoading(false);
      setPatients([]);
      return;
    }

    // Set a timeout to update the debounced query after 300ms
    const handler = setTimeout(() => {
      setDebouncedQuery(query);
    }, 500);

    // Clear the timeout if query changes (cleanup function)
    return () => {
      clearTimeout(handler);
    };
  }, [patients.length, query, value]); // Only re-run the effect if query changes

  // Effect to perform the search action when debounced query changes
  React.useEffect(() => {
    if (debouncedQuery) {
      const fetchResults = async () => {
        try {
          setLoading(true);
          const response = await api?.searchPatients(debouncedQuery);
          setPatients(response.results || []);
          setLoading(false);
        } catch (error) {
          console.error("Error fetching search results:", error);
          setLoading(false);
        }
      };

      // Call the async function
      fetchResults();
    }
  }, [debouncedQuery, api]);

  const handleCreatePatient = () => {
    setOpenCreatePatientModal(true);
  };

  const handlePatientCreated = (newPatient: Patient) => {
    setOpenCreatePatientModal(false);
    onSelect(newPatient);
    snackbar.show(`Patient ${newPatient.nom} ${newPatient.prenom} créé avec succès`, "success");
  };

  return (
    <>
      <Autocomplete
      placeholder={"Rechercher un patient"}
      options={patients}
      getOptionLabel={(option: Patient) => (option ? `${option.nom} ${option.prenom}` : "")}
      getOptionKey={(option) => option.id}
      onChange={(_, newValue) => onSelect(newValue)}
      value={patients.find((c) => c.id === value) || null}
      onInputChange={(e, value) => e && setQuery(value)}
      isOptionEqualToValue={(option, value) => option.id === value.id}
      sx={{ flex: 1 }}
      loading={loading}
      slotProps={{ listbox: { sx: { zIndex: 3000 } } }}
      disabled={disabled}
      renderOption={(props, option) => (
        <ListItem {...props}>
          <Typography>{option.nom} {option.prenom}</Typography>
        </ListItem>
      )}
      renderGroup={(params) => (
        <div>
          {params.children}
          <ListItem sx={{ justifyContent: "center", p: 1, borderTop: "1px solid", borderColor: "divider" }}>
            <Button
              onClick={handleCreatePatient}
              startDecorator={<PersonAdd />}
              variant="soft"
              color="primary"
              size="sm"
              disabled={disabled}
              sx={{ width: "100%" }}
            >
              Ajouter un patient
            </Button>
          </ListItem>
        </div>
      )}
      noOptionsText={
        <div>
          <Typography level="body-sm" sx={{ p: 1, textAlign: "center" }}>
            {query === "" ? "Taper du texte pour commencer la recherche" : "Aucun patient trouvé"}
          </Typography>
          <ListItem sx={{ justifyContent: "center", p: 1 }}>
            <Button
              onClick={handleCreatePatient}
              startDecorator={<PersonAdd />}
              variant="soft"
              color="primary"
              size="sm"
              disabled={disabled}
            >
              Ajouter un patient
            </Button>
          </ListItem>
        </div>
      }
    />
    <Modal open={openCreatePatientModal} onClose={() => setOpenCreatePatientModal(false)}>
      <ModalOverflow>
        <ModalDialog sx={{ width: "400px" }}>
          <ModalClose />
          <DialogTitle>Ajouter un nouveau patient</DialogTitle>
          <CreatePatient 
            onSuccess={handlePatientCreated}
            disableRedirect={true}
          />
        </ModalDialog>
      </ModalOverflow>
    </Modal>
    </>
  );
}
