import dayjs from "dayjs";
import "dayjs/locale/fr";
import weekOfYear from "dayjs/plugin/weekOfYear";
import React from "react";
import { Calendar, dayjsLocalizer, SlotInfo, View } from "react-big-calendar";
import withDragAndDrop, { EventInteractionArgs } from "react-big-calendar/lib/addons/dragAndDrop";
import DefaultDateHeader from "react-big-calendar/lib/DateHeader";

import Box from "@mui/joy/Box";
import MenuItem from "@mui/joy/MenuItem";
import MenuList from "@mui/joy/MenuList";
import Stack from "@mui/joy/Stack";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { ExtendedCalendarEvent } from "../../models/custom";
import { calendarEventPropGetter } from "../../models/functions";
import { CalendarItem } from "../../models/types";
import "./calendar-component.scss";

dayjs.locale("fr");
dayjs.extend(weekOfYear);
const localizer = dayjsLocalizer(dayjs);
const DnDCalendar = withDragAndDrop(Calendar);

interface CalendarComponentProps {
  selectedDoctors: number[];
  onEventClick?: (event: ExtendedCalendarEvent) => void;
  onSlotSelect?: (slotInfo: SlotInfo, vacation?: CalendarItem) => void;
  onEventDrop?: (event: EventInteractionArgs<ExtendedCalendarEvent>) => void;
  onEventResize?: (event: EventInteractionArgs<ExtendedCalendarEvent>) => void;
  onCreateEvent?: () => void;
  width?: string;
  height?: string;
  refresh?: number;
}

export default function CalendarComponent({
  selectedDoctors,
  onEventClick,
  onSlotSelect,
  onEventDrop,
  onEventResize,
  onCreateEvent,
  width = "100%",
  height = "100%",
  refresh,
}: CalendarComponentProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const [menuPosition, setMenuPosition] = React.useState<{
    x: number;
    y: number;
  } | null>(null);

  const [rawItems, setRawItems] = React.useState<CalendarItem[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [events, setEvents] = React.useState<ExtendedCalendarEvent[]>([]);
  const [backGroundEvents, setBackGroundEvents] = React.useState<ExtendedCalendarEvent[]>([]);
  const [view, setView] = React.useState<View>("week");
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const [isWeek, setIsWeek] = React.useState<boolean>(false);
  const [resources, setResources] = React.useState<any[]>([]);
  const [range, setRange] = React.useState<
    | Date[]
    | {
        start: Date;
        end: Date;
      }
  >(() => {
    if (view === "week") {
      return Array.from({ length: 7 }, (_, i) => dayjs().startOf("week").add(i, "day").toDate());
    } else if (view === "month") {
      return Array.from({ length: 31 }, (_, i) => dayjs().startOf("month").add(i, "day").toDate());
    }
    return [];
  });

  const contextMenuFromPoint = React.useCallback((x: number, y: number) => {
    const underlyingElement = document.elementsFromPoint(x, y);
    // Select slot under position and propagate context menu click on it
    const slotContainer = underlyingElement?.filter((el) => el.classList.contains("rbc-time-slot"));
    const clickEvent = new MouseEvent("contextmenu", {
      bubbles: true,
      cancelable: true,
      clientX: x,
      clientY: y,
      view: window,
    });
    slotContainer?.[0]?.dispatchEvent(clickEvent);
  }, []);

  React.useEffect(() => {
    const elements = document.querySelectorAll<HTMLElement>(".rbc-events-container");

    const handleContextMenu = (event: MouseEvent) => {
      event.preventDefault();
      contextMenuFromPoint(event.clientX, event.clientY);
    };

    elements.forEach((element) => {
      element.addEventListener("contextmenu", handleContextMenu);
    });

    return () => {
      elements.forEach((element) => {
        element.removeEventListener("contextmenu", handleContextMenu);
      });
    };
  }, [contextMenuFromPoint]);

  const customTimeSlotWrapper = React.useCallback((props: any) => {
    return React.cloneElement(props.children, {
      style: { zIndex: 1 },
      onContextMenu: (e: MouseEvent) => {
        e.preventDefault();
        // Sauvegarde la date et l'heure du clic droit
        const slotInfo = props.value;
        if (slotInfo) {
          // Émet un événement personnalisé avec les informations de date/heure
          const event = new CustomEvent("calendarContextMenu", {
            detail: {
              date: slotInfo,
              x: e.clientX,
              y: e.clientY,
            },
          });
          document.dispatchEvent(event);
        }
        setMenuPosition({ x: e.clientX, y: e.clientY });
      },
    });
  }, []);

  const handleCloseMenu = React.useCallback(() => {
    setMenuPosition(null);
  }, []);

  React.useEffect(() => {
    let start: number;
    let end: number;
    if (Array.isArray(range)) {
      if (range.length === 1) {
        start = Number(range[0].getTime().toString().slice(0, -3));
        end = Number(range[0].getTime().toString().slice(0, -3)) + 86399;
      } else {
        start = Number(range[0].getTime().toString().slice(0, -3));
        end = Number(range[range.length - 1].getTime().toString().slice(0, -3));
      }
    } else {
      start = Number(range.start.getTime().toString().slice(0, -3));
      end = Number(range.end.getTime().toString().slice(0, -3));
    }

    try {
      api?.getCalendarItems(start, end, selectedDoctors).then((items: CalendarItem[]) => {
        setRawItems(items);

        const newEvents = items
          .filter(
            (item) =>
              item.type !== "vacation" && item.zkf_user !== null && item.type !== "jourferie"
          )
          .map((item: CalendarItem) => ({
            id: item.id,
            title: item.title,
            start: new Date(item.start * 1000),
            end: new Date(item.end * 1000),
            zkf_user: item.zkf_user,
            background_color: item.background_color,
            text_color: item.text_color,
            item_type: item.type,
            resource: item.zkf_user,
            all_day: item.all_day,
          })) as ExtendedCalendarEvent[];

        setEvents(newEvents);

        const newBackGroundEvents = items
          .filter((item) => item.type === "vacation" || item.type === "jourferie")
          .map((item: CalendarItem) => ({
            id: item.id,
            title: item.title,
            start: new Date(item.start * 1000),
            end: new Date(item.end * 1000),
            zkf_user: item.zkf_user,
            background_color: item.background_color,
            item_type: item.type,
            resource: item.zkf_user.toString(),
            all_day: item.all_day,
          })) as ExtendedCalendarEvent[];
        setBackGroundEvents(newBackGroundEvents);

        if (view === "day") {
          const uniqueUsers = Array.from(new Set(newEvents.map((e) => e.resource))).filter(
            (id) => id && id !== "auth.User.None"
          );
          const doctorResources = uniqueUsers.map((id) => ({
            id: id,
            title: `Docteur ${id}`,
          }));
          setResources(doctorResources);
        } else {
          setResources([]);
        }

        setLoading(false);
      });
    } catch (error) {
      snackbar.show("Erreur lors de la récupération des événements", "danger");
      setLoading(false);
    }
  }, [api, selectedDoctors, range, view, refresh, snackbar]);

  const handleOnNavigate = React.useCallback(
    (date: Date) => {
      let newRange: Date[] = [];
      if (view === "day") {
        newRange = [date];
      } else if (view === "week") {
        newRange = Array.from({ length: 7 }, (_, i) =>
          dayjs(date).startOf("week").add(i, "day").toDate()
        );
      } else if (view === "month") {
        newRange = Array.from({ length: 31 }, (_, i) =>
          dayjs(date).startOf("month").add(i, "day").toDate()
        );
      }
      setRange(newRange);
      setCurrentDate(date);
    },
    [view]
  );

  const scrollToTime = React.useCallback(() => {
    const now = new Date();
    now.setHours(now.getHours() - 1);
    return now;
  }, []);

  const DateHeader = (props: any) => {
    const isFirstDayOfWeek = props.date.getDay() === 1;
    const weekNumber = isFirstDayOfWeek ? dayjs(props.date).week() : 0;

    return (
      <Stack direction={"row-reverse"} justifyContent={"space-between"} p={0.5}>
        <DefaultDateHeader {...props} />
        {isFirstDayOfWeek && (
          <button
            className={"rbc-button-link"}
            onClick={(e) => {
              setIsWeek(true);
              setTimeout(() => props.onDrillDown(e), 0);
            }}
          >
            Week {weekNumber}
          </button>
        )}
      </Stack>
    );
  };

  const checkSlotOverlapWithBackgroundEvents = (
    slotInfo: SlotInfo
  ): ExtendedCalendarEvent | undefined => {
    const slotStart = slotInfo.start.getTime();
    const slotEnd = slotInfo.end.getTime();

    return backGroundEvents.find((bgEvent) => {
      const bgStart = bgEvent.start!.getTime();
      const bgEnd = bgEvent.end!.getTime();
      return (
        (slotStart >= bgStart && slotStart < bgEnd) || // Slot start is within background event
        (slotEnd > bgStart && slotEnd <= bgEnd) || // Slot end is within background event
        (slotStart <= bgStart && slotEnd >= bgEnd) // Slot completely encompasses background event
      );
    });
  };

  const handleSlotSelect = (slotInfo: SlotInfo) => {
    const overlappingEvent = checkSlotOverlapWithBackgroundEvents(slotInfo);
    if (overlappingEvent && overlappingEvent.item_type === "vacation") {
      const vacation = rawItems.find((bgEvent) => bgEvent.id === overlappingEvent.id);
      onSlotSelect?.(slotInfo, vacation);
    } else {
      onSlotSelect?.(slotInfo);
    }
  };

  return (
    <Box
      sx={{
        opacity: loading ? 0.5 : 1,
        pointerEvents: loading ? "none" : "auto",
        position: "relative",
        width: width,
        height: height,
      }}
    >
      <MenuList
        sx={{
          position: "fixed",
          top: menuPosition?.y || 0,
          left: menuPosition?.x || 0,
          visibility: menuPosition ? "visible" : "hidden",
          zIndex: 11,
        }}
      >
        <MenuItem
          onClick={() => {
            onCreateEvent?.();
            handleCloseMenu();
          }}
        >
          Creer un evenement
        </MenuItem>
      </MenuList>
      <DnDCalendar
        localizer={localizer}
        events={events}
        backgroundEvents={backGroundEvents}
        dayLayoutAlgorithm={"no-overlap"}
        selectable
        showAllEvents
        allDayMaxRows={3}
        style={{ height: "100%", width: "100%" }}
        formats={{
          dayFormat: "dddd DD/MM",
          timeGutterFormat: "HH:mm",
          dayHeaderFormat: "dddd DD/MM",
        }}
        onSelectSlot={handleSlotSelect}
        view={view}
        views={["day", "week", "work_week", "month", "agenda"]}
        onDrillDown={(date, view) => {
          setView(isWeek ? "week" : view);
          setCurrentDate(date);
          setIsWeek(false);
        }}
        onRangeChange={setRange}
        onView={setView}
        onSelectEvent={onEventClick}
        step={15}
        timeslots={2}
        scrollToTime={scrollToTime()}
        date={currentDate}
        onNavigate={handleOnNavigate}
        eventPropGetter={calendarEventPropGetter}
        onEventDrop={onEventDrop}
        onEventResize={onEventResize}
        components={{
          timeSlotWrapper: customTimeSlotWrapper,
          month: {
            dateHeader: DateHeader,
          },
        }}
        culture="fr-FR"
        resources={view === "day" ? resources : undefined}
        resourceIdAccessor={(resource: any) => resource.id}
        resourceTitleAccessor={(resource: any) => resource.title}
        defaultView={view}
        resizable={view !== "day"}
      />
    </Box>
  );
}
