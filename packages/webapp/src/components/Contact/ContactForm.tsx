import React from "react";

import Delete from "@mui/icons-material/Delete";
import Box from "@mui/joy/Box";
import Checkbox from "@mui/joy/Checkbox";
import Input from "@mui/joy/Input";
import Option from "@mui/joy/Option";
import Radio from "@mui/joy/Radio";
import RadioGroup from "@mui/joy/RadioGroup";
import Select from "@mui/joy/Select";
import Stack from "@mui/joy/Stack";
import Tab from "@mui/joy/Tab";
import TabList from "@mui/joy/TabList";
import Tabs from "@mui/joy/Tabs";
import Textarea from "@mui/joy/Textarea";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { isSpecialite } from "../../models/type_guards";
import {
  Adresse,
  Contact,
  ContactTitreEnum,
  Email,
  Patient,
  SexeEnum,
  Specialite,
  Telephone,
} from "../../models/types";
import {
  emptyAdresse,
  emptyEmail,
  emptyTelephone,
  panelRender,
} from "../ContactPoint/ContactPoint";
import ModalComponent from "../Modals/Modal";

// Custom event for contact set to inactive
const CONTACT_INACTIVE_EVENT = "contactInactiveEvent";

interface ContactFormProps {
  open: boolean;
  onClose: () => void;
  onSave?: (contact: Contact) => void;
  contact?: Contact;
  patient: Patient | undefined;
}

export default function ContactForm({ open, onClose, contact, patient, onSave }: ContactFormProps) {
  const api = useApi();

  const [loading, setLoading] = React.useState<boolean>(false);
  const [specialites, setSpecialites] = React.useState<Specialite[]>([]);
  const [nom, setNom] = React.useState<string>(contact?.nom || "");
  const [prenom, setPrenom] = React.useState<string>(contact?.prenom || "");
  const [sexe, setSexe] = React.useState<SexeEnum>((contact?.sexe as SexeEnum) || "");
  const [structure, setStructure] = React.useState<string>(contact?.structure || "");
  const [comment, setComment] = React.useState<string>(contact?.comment || "");
  const [titre, setTitre] = React.useState<ContactTitreEnum>(contact?.titre || "Docteur");
  const [selectedSpecialites, setSelectedSpecialites] = React.useState<Specialite[]>(
    specialites.filter((spe) => contact?.specialite?.includes(spe.id)) || []
  );
  const [emails, setEmails] = React.useState<Email[]>([{ ...emptyEmail }]);
  const [telephones, setTelephones] = React.useState<Telephone[]>([{ ...emptyTelephone }]);
  const [adresses, setAdresses] = React.useState<Adresse[]>([{ ...emptyAdresse }]);
  const [isActive, setIsActive] = React.useState<boolean>(
    contact?.id ? contact.en_activite || false : true
  );

  const width = "100px";

  React.useEffect(() => {
    api.listUserSpecialites().then((res) => {
      setSpecialites(res);
      // contact.specialite is an array of Specialite objects
      // we need to find the specialites in res that have the same id as contact.specialite
      if (
        contact?.specialite &&
        Array.isArray(contact.specialite) &&
        contact.specialite.length > 0 &&
        res.length > 0 &&
        isSpecialite(contact.specialite[0])
      ) {
        // Map each speciality from contact to the matching one from res
        const matchedSpecialites = (contact.specialite as unknown as Specialite[])
          .map((spe) => res.find((s) => s.id === spe.id))
          .filter((spec): spec is Specialite => !!spec); // Filter out undefined values

        setSelectedSpecialites(matchedSpecialites);
      } else if (contact?.specialite && Array.isArray(contact.specialite)) {
        // otherwise, we have an array of strings (ids)
        const matchedSpecialites = contact.specialite
          .map((id) => res.find((s) => s.id === id))
          .filter((spec): spec is Specialite => !!spec); // Filter out undefined values

        setSelectedSpecialites(matchedSpecialites);
      }
    });
    if (contact?.id) {
      api
        .listEmailsByContact(contact.id)
        .then((res) => setEmails(res.length > 0 ? res : [{ ...emptyEmail }]));
      api
        .listPhonesByContact(contact.id)
        .then((res) => setTelephones(res.length > 0 ? res : [{ ...emptyTelephone }]));
      api
        .listAddressesByContact(contact.id)
        .then((res) => setAdresses(res.length > 0 ? res : [{ ...emptyAdresse }]));
    }
  }, [api, contact]);

  const addEmail = () => setEmails([...emails, { ...emptyEmail }]);
  const addTelephone = () => setTelephones([...telephones, { ...emptyTelephone }]);
  const addAdresse = () => setAdresses([...adresses, { ...emptyAdresse }]);

  const specialitesValue = selectedSpecialites?.map((selectSpe) => selectSpe.spe).join(", ");

  const updateEmails = (index: number, field: string, value: string | boolean) =>
    setEmails((prevData) =>
      prevData.map((data, i) => (index !== i ? data : { ...data, [field]: value }))
    );

  const updateTelephones = (index: number, field: string, value: string | boolean) =>
    setTelephones((prevData) =>
      prevData.map((data, i) => (index !== i ? data : { ...data, [field]: value }))
    );

  const updateAdresses = async (index: number, field: string, value: string | boolean) => {
    const newData = { [field]: value };
    if (field === "code_postal" && (value as string).length === 5) {
      const villes = await api.searchVilleByZipCode(value as string);
      if (villes.length === 1) {
        newData.ville = villes[0].nom_ville;
      }
    }
    setAdresses((prevData) =>
      prevData.map((data, i) => (index !== i ? data : { ...data, ...newData }))
    );
  };

  const deleteEmail = (index: number) => {
    if (emails.length > 1) {
      setEmails(emails.filter((e, i) => i !== index));
    } else {
      setEmails([{ ...emptyEmail }]);
    }
  };

  const deleteTelephone = (index: number) => {
    if (telephones.length > 1) {
      setTelephones(telephones.filter((e, i) => i !== index));
    } else {
      setTelephones([{ ...emptyTelephone }]);
    }
  };

  const deleteAdresse = (index: number) => {
    if (adresses.length > 1) {
      setAdresses(adresses.filter((e, i) => i !== index));
    } else {
      setAdresses([{ ...emptyAdresse }]);
    }
  };

  const container = (
    title: string,
    handleDelete: () => void,
    isFavori: boolean,
    handleFavori: () => void,
    content: React.ReactNode
  ) => (
    <Box
      key={title}
      padding={2}
      style={{
        position: "relative",
        border: "1px solid #ccc",
        borderRadius: "8px",
      }}
    >
      <Typography
        sx={{
          position: "absolute",
          top: "-10px",
          left: "10px",
          backgroundColor: "white",
          padding: "0 8px",
          fontSize: "12px",
        }}
        endDecorator={<Delete style={{ fill: "red", cursor: "pointer" }} onClick={handleDelete} />}
      >
        {title}
      </Typography>
      <Typography
        sx={{
          position: "absolute",
          top: "-10px",
          right: "10px",
          backgroundColor: "white",
          padding: "0 8px",
          fontSize: "12px",
        }}
        endDecorator={<Checkbox checked={isFavori} onClick={handleFavori} />}
      >
        Favori
      </Typography>
      {content}
    </Box>
  );

  const emailRender = (email: Email, index: number) =>
    container(
      `Email ${index + 1}`,
      () => deleteEmail(index),
      email.preferred || false,
      () => updateEmails(index, "preferred", !email.preferred),
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center">
          <Typography sx={{ width }}>Categorie</Typography>
          <Input
            value={email.categorie || ""}
            onChange={(e) => updateEmails(index, "categorie", e.target.value)}
            style={{ flex: 1 }}
          />
        </Stack>
        <Stack direction="row" alignItems="center">
          <Typography sx={{ width }}>Email</Typography>
          <Input
            value={email.email || ""}
            onChange={(e) => updateEmails(index, "email", e.target.value)}
            style={{ flex: 1 }}
          />
        </Stack>
      </Stack>
    );

  const telephoneRender = (telephone: Telephone, index: number) =>
    container(
      `Telephone ${index + 1}`,
      () => deleteTelephone(index),
      telephone.preferred || false,
      () => updateTelephones(index, "preferred", !telephone.preferred),
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center">
          <Typography sx={{ width }}>Categorie</Typography>
          <Select
            value={telephone.categorie || ""}
            onChange={(e, newValue) => updateTelephones(index, "categorie", newValue || "")}
            style={{ width: "200px" }}
          >
            <Option value="PORTABLE">Portable</Option>
            <Option value="PROFESSIONNEL">Professionnel</Option>
            <Option value="DOMICILE">Domicile</Option>
            <Option value="FAX">Fax</Option>
            <Option value="AUTRE">Autre</Option>
          </Select>
        </Stack>
        <Stack direction="row" alignItems="center">
          <Typography sx={{ width }}>Numero</Typography>
          <Input
            value={telephone.numero || ""}
            onChange={(e) => {
              if (/^\d{0,10}$/.test(e.target.value)) {
                updateTelephones(index, "numero", e.target.value);
              }
            }}
            style={{ flex: 1 }}
          />
        </Stack>
      </Stack>
    );

  const adresseRender = (adresse: Adresse, index: number) =>
    container(
      `Adresse ${index + 1}`,
      () => deleteAdresse(index),
      adresse.preferred || false,
      () => updateAdresses(index, "preferred", !adresse.preferred),
      <Stack spacing={2}>
        <Stack direction="row" spacing={2}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography width="100px">No de rue</Typography>
            <Input
              value={adresse.numero_rue || ""}
              onChange={(e) => {
                if (/^\d{0,5}$/.test(e.target.value)) {
                  updateAdresses(index, "numero_rue", e.target.value);
                }
              }}
              style={{ flex: 1, width: "75px" }}
            />
          </Stack>
          <Stack direction="row" alignItems="center" spacing={1} flex={1}>
            <Typography>Rue</Typography>
            <Input
              value={adresse.rue || ""}
              onChange={(e) => updateAdresses(index, "rue", e.target.value)}
              style={{ flex: 1 }}
            />
          </Stack>
        </Stack>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Typography width="100px">Complement</Typography>
          <Input
            placeholder="Complement d'adresse"
            value={adresse.complement_adresse || ""}
            onChange={(e) => updateAdresses(index, "complement_adresse", e.target.value)}
            style={{ flex: 1 }}
          />
        </Stack>
        <Stack direction="row" spacing={2}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography width="100px">Code Postal</Typography>
            <Input
              value={adresse.code_postal || ""}
              onChange={(e) => {
                if (/^\d{0,5}$/.test(e.target.value)) {
                  updateAdresses(index, "code_postal", e.target.value);
                }
              }}
              style={{ flex: 1, width: "75px" }}
            />
          </Stack>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography>Ville</Typography>
            <Input
              value={adresse.ville || ""}
              onChange={(e) => updateAdresses(index, "ville", e.target.value)}
              style={{ flex: 1 }}
            />
          </Stack>
        </Stack>
      </Stack>
    );

  const handleSaveCorrespondant = async () => {
    setLoading(true);
    let result: Contact;
    try {
      if (contact?.id) {
        result = await api.updateContact({
          id: contact.id,
          nom,
          prenom,
          sexe,
          comment,
          structure,
          titre,
          en_activite: isActive,
          specialite: selectedSpecialites.map((spe) => spe.id),
          email: emails.filter((email) => email.email),
          telephone: telephones.filter((tel) => tel.numero),
          adresse: adresses.filter(
            (adresse) => adresse.numero_rue || adresse.rue || adresse.code_postal || adresse.ville
          ),
        });

        // If the contact was set to inactive, dispatch the custom event
        if (contact.en_activite && !isActive) {
          const event = new CustomEvent(CONTACT_INACTIVE_EVENT);
          window.dispatchEvent(event);
        }
      } else {
        result = await api.createContact({
          nom,
          prenom,
          sexe,
          comment,
          structure,
          titre,
          en_activite: isActive,
          specialite: selectedSpecialites.map((spe) => spe.id),
          email: emails.filter((email) => email.email),
          telephone: telephones.filter((tel) => tel.numero),
          adresse: adresses.filter(
            (adresse) => adresse.numero_rue || adresse.rue || adresse.code_postal || adresse.ville
          ),
        });
        if (patient) {
          // Create a new CorrespondantPatient object
          const newCorrespondant = {
            contact: result.id,
            send_report: true,
            referral_source: false,
          };

          // Map existing correspondants to the correct format if needed
          const existingCorrespondants = (patient.correspondants || []).map((corr) => {
            // If corr is a string (just the ID), convert it to the proper format
            if (typeof corr === "string") {
              return {
                contact: corr,
                send_report: true,
                referral_source: false,
              };
            }
            return corr;
          });

          await api.linkContactToPatient(patient.id, [...existingCorrespondants, newCorrespondant]);
        }
      }
      onSave && onSave(result);
      onClose();
    } catch (e) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ModalComponent
      open={open}
      onClose={onClose}
      title={`${contact?.id ? "Modifier" : "Créer"} un contact`}
      style={{ minWidth: "80%", minHeight: "60%" }}
      validateLabel={contact?.id ? "Modifier" : "Créer"}
      onValidate={handleSaveCorrespondant}
      canValidate={(!!nom && !!prenom) || !!structure}
      isLoading={loading}
      patient={patient}
    >
      <Stack direction="row" width="100%" spacing={3} marginTop={2}>
        <Stack spacing={2} width="45%">
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>Nom</Typography>
            <Input
              value={nom}
              onChange={(e) => setNom(e.target.value)}
              error={!nom && !structure}
              style={{ flex: 1 }}
            />
          </Stack>
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>Prenom</Typography>
            <Input
              value={prenom}
              onChange={(e) => setPrenom(e.target.value)}
              error={!prenom && !structure}
              style={{ flex: 1 }}
            />
          </Stack>
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>Sexe</Typography>
            <RadioGroup
              orientation="horizontal"
              value={sexe}
              onChange={(e) => setSexe(e.target.value as SexeEnum)}
            >
              <Radio value="MALE" label="Homme" />
              <Radio value="FEMALE" label="Femme" />
            </RadioGroup>
          </Stack>
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>Structure</Typography>
            <Input
              value={structure}
              onChange={(e) => setStructure(e.target.value)}
              error={!structure && !nom && !prenom}
              style={{ flex: 1 }}
            />
          </Stack>
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>Titre</Typography>
            <Select
              value={titre}
              onChange={(e, newValue) => setTitre(newValue || "Docteur")}
              style={{ flex: 1 }}
            >
              <Option value="Docteur">Docteur</Option>
              <Option value="Professeur">Professeur</Option>
            </Select>
          </Stack>
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>Specialites</Typography>
            <Tooltip title={specialitesValue}>
              <Select
                multiple
                renderValue={() => <Typography noWrap>{specialitesValue}</Typography>}
                value={selectedSpecialites}
                onChange={(e, newValue) => setSelectedSpecialites(newValue || [])}
                style={{ flex: 1 }}
              >
                {specialites.map((spe) => (
                  <Option key={spe.id} value={spe}>
                    {spe.spe}
                  </Option>
                ))}
              </Select>
            </Tooltip>
          </Stack>
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>Comment</Typography>
            <Textarea
              minRows={5}
              maxRows={5}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              style={{ flex: 1 }}
            />
          </Stack>
          <Stack direction="row" alignItems="center">
            <Typography sx={{ width }}>En activite</Typography>
            <Checkbox checked={isActive} onChange={() => setIsActive(!isActive)} />
          </Stack>
        </Stack>
        <Tabs defaultValue={0} style={{ flex: 1, backgroundColor: "inherit" }}>
          <TabList tabFlex={1}>
            <Tab>Emails</Tab>
            <Tab>Téléphones</Tab>
            <Tab>Adresses</Tab>
          </TabList>
          {panelRender(
            0,
            addEmail,
            emails.map((email, index) => emailRender(email, index))
          )}
          {panelRender(
            1,
            addTelephone,
            telephones.map((tel, index) => telephoneRender(tel, index))
          )}
          {panelRender(
            2,
            addAdresse,
            adresses.map((adresse, index) => adresseRender(adresse, index))
          )}
        </Tabs>
      </Stack>
    </ModalComponent>
  );
}
