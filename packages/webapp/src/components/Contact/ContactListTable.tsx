import React from "react";

import Add from "@mui/icons-material/Add";
import Delete from "@mui/icons-material/Delete";
import Edit from "@mui/icons-material/Edit";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import CircularProgress from "@mui/joy/CircularProgress";
import IconButton from "@mui/joy/IconButton";
import Skeleton from "@mui/joy/Skeleton";
import Stack from "@mui/joy/Stack";
import Switch from "@mui/joy/Switch";
import Table from "@mui/joy/Table";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { isSpecialite } from "../../models/type_guards";
import { Adresse, Contact, CorrespondantPatient, Patient, Specialite } from "../../models/types";
import ContactSelector from "../Forms/ContactSelector";
import ModalComponent from "../Modals/Modal";
import ContactModalForm from "./ContactForm";

// Custom events for contact changes
const CONTACT_ACTIVITY_CHANGE_EVENT = "contactActivityChange";
const CONTACT_INACTIVE_EVENT = "contactInactiveEvent";

interface ContactListTableProps {
  title: string;
  patient: Patient;
  filterInactive?: boolean;
  showAddButton?: boolean;
}

type CorrespondantPatientWithContact = Omit<CorrespondantPatient, "contact"> & {
  contact: Contact;
};

// Cette fonction mappe un CorrespondantPatientWithContact vers le format attendu par le backend
const mapContactToCorrespondant = (cp: CorrespondantPatientWithContact): CorrespondantPatient => {
  // Forcer l'exactitude des valeurs booléennes en utilisant une comparaison stricte
  // Cela garantit que les valeurs restent des booléens cohérents
  const send_report = cp.send_report === true;
  const referral_source = cp.referral_source === true;

  // Utiliser les variables converties pour être sûr d'envoyer des booléens valides
  return {
    contact: cp.contact.id,
    send_report: send_report,
    referral_source: referral_source,
  };
};

const getContactAddress = (a: Adresse): React.ReactNode => {
  return (
    <p>
      {a.titre && (
        <>
          <b>{a.titre}</b>
          <br />
        </>
      )}
      {a.numero_rue + " " + a.rue}
      <br />
      {a.structure && a.structure + <br />}
      {a.code_postal + " " + a.ville}
    </p>
  );
};

const ContactListTable: React.FC<ContactListTableProps> = ({
  title,
  patient,
  filterInactive = false,
  showAddButton = false,
}) => {
  const api = useApi();
  const snackbar = useSnackbar();

  const [initialLoading, setInitialLoading] = React.useState<boolean>(true);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [contacts, setContacts] = React.useState<CorrespondantPatientWithContact[]>([]);
  const [openEditContact, setOpenEditContact] = React.useState<boolean>(false);
  const [openAddContact, setOpenAddContact] = React.useState<boolean>(false);
  const [selectedContact, setSelectedContact] = React.useState<Contact | null>(null);
  const [adresses, setAdresses] = React.useState<{ [key: string]: Adresse[] }>({});

  const fetchContactsByPatient = React.useCallback(
    async (showLoadingState = true) => {
      try {
        // Afficher l'état de chargement uniquement lors du chargement initial
        // ou lorsque c'est explicitement demandé (et pas pour les rafraîchissements silencieux)
        if (initialLoading && showLoadingState) {
          setLoading(true);
        }

        const res = await api.listContactsByPatient(patient.id, 1);

        // Maintenant le backend retourne directement des objets CorrespondantPatient
        const correspondantPatients: CorrespondantPatientWithContact[] = res.map(
          (correspondant: any) => {
            // Récupération du contact depuis le champ contact du CorrespondantPatient
            const contact = correspondant.contact;

            // Forcer les valeurs exactement comme elles sont dans le backend
            // Utiliser strict === false/true pour s'assurer de la cohérence des booléens
            const sendReport = correspondant.send_report === false ? false : true;
            const referralSource = correspondant.referral_source === true ? true : false;

            const correspondantWithContact = {
              contact: contact,
              send_report: sendReport,
              referral_source: referralSource,
            };

            return correspondantWithContact;
          }
        );

        // Maintenant on garde les objets CorrespondantPatientWithContact au lieu de juste extraire le contact
        setContacts(
          filterInactive
            ? correspondantPatients.filter((c) => c.contact.en_activite === false)
            : correspondantPatients.filter((c) => c.contact.en_activite !== false)
        );

        // Chargement des adresses en arrière-plan sans blocage de l'interface
        Promise.all(
          correspondantPatients.map(async (c) => {
            const adresses = await api.getContactAddresses(c.contact.id);
            return { [c.contact.id]: adresses };
          })
        )
          .then((addressResults) => {
            setAdresses(Object.assign({}, ...addressResults));
          })
          .catch((error) => {
            console.error("Erreur lors du chargement des adresses:", error);
            // Ne pas afficher d'erreur à l'utilisateur pour les adresses, ce n'est pas critique
          });
      } catch (error) {
        console.error(error);
        snackbar.show("Erreur lors de la récupération des contacts", "danger");
      } finally {
        setInitialLoading(false);
        if (showLoadingState) {
          setLoading(false);
        }
      }
    },
    [api, patient.id, filterInactive, snackbar, initialLoading]
  );

  React.useEffect(() => {
    fetchContactsByPatient(true);
  }, [fetchContactsByPatient]);

  // Listen for contact activity change events
  React.useEffect(() => {
    const handleContactActivityChange = () => {
      fetchContactsByPatient(true);
    };

    window.addEventListener(CONTACT_ACTIVITY_CHANGE_EVENT, handleContactActivityChange);

    return () => {
      window.removeEventListener(CONTACT_ACTIVITY_CHANGE_EVENT, handleContactActivityChange);
    };
  }, [fetchContactsByPatient]);

  const handleEdit = (correspondant: CorrespondantPatientWithContact) => {
    const contactWithAddresses = {
      ...correspondant.contact,
      adresse: adresses[correspondant.contact.id] || [],
    };
    setSelectedContact(contactWithAddresses);
    setOpenEditContact(true);
  };

  const onCloseContact = () => {
    setSelectedContact(null);
    setOpenEditContact(false);
    setOpenAddContact(false);
  };

  const handleDelete = async (id: Contact["id"]) => {
    // Optimistically update the UI
    const updatedContacts = contacts.filter((c) => c.contact.id !== id);
    setContacts(updatedContacts);

    try {
      await api.linkContactToPatient(patient.id, updatedContacts.map(mapContactToCorrespondant));
      snackbar.show("Le contact a été supprimé", "success");
    } catch (error) {
      console.error(error);
      snackbar.show("Erreur lors de la suppression du contact", "danger");
      // Revert the optimistic update in case of error
      await fetchContactsByPatient(false);
    }
  };

  const handleAddContact = async () => {
    if (!selectedContact) return;

    // Create a new CorrespondantPatientWithContact
    const newCorrespondant: CorrespondantPatientWithContact = {
      contact: selectedContact,
      send_report: true,
      referral_source: false,
    };

    // Optimistically update the UI
    const updatedContacts = [...contacts, newCorrespondant];
    setContacts(updatedContacts);

    try {
      // Préparer tous les correspondants (existants + nouveau)
      const correspondantParams = updatedContacts.map(mapContactToCorrespondant);

      // Appel à l'API avec PATCH /patient/uuid
      await api.linkContactToPatient(patient.id, correspondantParams);

      // Rafraîchir les contacts après l'ajout
      await fetchContactsByPatient(false);

      snackbar.show("Le contact a été ajouté", "success");
      onCloseContact();
    } catch (error) {
      console.error("Erreur lors de l'ajout du contact:", error);
      snackbar.show("Erreur lors de l'ajout du contact", "danger");
      // Revert the optimistic update in case of error
      setContacts(contacts);
    }
  };

  const handleChangeSendReport = async (correspondant: CorrespondantPatientWithContact) => {
    // Stocker la valeur actuelle à l'extérieur du bloc try pour qu'elle soit accessible dans le catch
    const originalValue = correspondant.send_report === true;

    try {
      // Inverser la valeur booléenne
      const newSendReport = !originalValue;

      // Mise à jour optimiste de l'UI (mais sans rechargement du state complet)
      // Modification directe pour éviter un rafraîchissement complet
      correspondant.send_report = newSendReport;

      // Préparation des données pour l'API sans modifier l'état React
      const correspondantParams = contacts.map((cp) => mapContactToCorrespondant(cp));

      // Appel API sans montrer l'indicateur de chargement
      await api.linkContactToPatient(patient.id, correspondantParams);

      // Notification à l'utilisateur
      if (newSendReport) {
        snackbar.show(
          correspondant.contact.nom?.toUpperCase() +
            " " +
            correspondant.contact.prenom +
            " est ajouté comme destinataire",
          "success"
        );
      } else {
        snackbar.show(
          correspondant.contact.nom?.toUpperCase() +
            " " +
            correspondant.contact.prenom +
            " a été retiré comme destinataire",
          "success"
        );
      }

      // Force une mise à jour de l'interface sans modifier les données
      // Cette technique permet de forcer le rendu sans changer les objets
      setContacts((prevContacts) => [...prevContacts]);
    } catch (error) {
      console.error(error);
      snackbar.show("Erreur lors de la modification du contact", "danger");

      // Annuler le changement optimiste
      correspondant.send_report = originalValue;
      // Force une mise à jour de l'interface
      setContacts((prevContacts) => [...prevContacts]);
    }
  };

  const handleChangeReferralSource = async (correspondant: CorrespondantPatientWithContact) => {
    // Stocker la valeur actuelle à l'extérieur du bloc try pour qu'elle soit accessible dans le catch
    const originalValue = correspondant.referral_source === true;

    try {
      // Inverser la valeur booléenne
      const newReferralSource = !originalValue;

      // Mise à jour optimiste de l'UI (mais sans rechargement du state complet)
      // Modification directe pour éviter un rafraîchissement complet
      correspondant.referral_source = newReferralSource;

      // Préparation des données pour l'API sans modifier l'état React
      const correspondantParams = contacts.map((cp) => mapContactToCorrespondant(cp));

      // Appel API sans montrer l'indicateur de chargement
      await api.linkContactToPatient(patient.id, correspondantParams);

      // Notification à l'utilisateur
      if (newReferralSource) {
        snackbar.show(
          correspondant.contact.nom?.toUpperCase() +
            " " +
            correspondant.contact.prenom +
            " est défini comme médecin adresseur",
          "success"
        );
      } else {
        snackbar.show(
          correspondant.contact.nom?.toUpperCase() +
            " " +
            correspondant.contact.prenom +
            " n'est plus défini comme médecin adresseur",
          "success"
        );
      }

      // Force une mise à jour de l'interface sans modifier les données
      // Cette technique permet de forcer le rendu sans changer les objets
      setContacts((prevContacts) => [...prevContacts]);
    } catch (error) {
      console.error(error);
      snackbar.show("Erreur lors de la modification du contact", "danger");

      // Annuler le changement optimiste
      correspondant.referral_source = originalValue;
      // Force une mise à jour de l'interface
      setContacts((prevContacts) => [...prevContacts]);
    }
  };

  // This handler will be called when a contact is saved in the edit modal
  const handleContactSave = (updatedContact: Contact) => {
    // Dispatch event to notify all ContactListTable components about the activity change
    const event = new CustomEvent(CONTACT_ACTIVITY_CHANGE_EVENT);
    window.dispatchEvent(event);

    // If the contact was set to inactive, also dispatch the inactive event
    if (selectedContact && selectedContact.en_activite && !updatedContact.en_activite) {
      const inactiveEvent = new CustomEvent(CONTACT_INACTIVE_EVENT);
      window.dispatchEvent(inactiveEvent);
    }

    // Close the modal
    onCloseContact();
  };

  return (
    <>
      {contacts.length === 0 && !filterInactive ? (
        <Stack sx={{ height: 400 }} spacing={2} alignItems="center" justifyContent="center">
          <Typography level="title-md">Aucun contact trouvé pour ce patient.</Typography>
          <Typography level="body-md">
            Ajoutez un premier contact au patient en cliquant sur le bouton ci-dessous.
          </Typography>
          <Button onClick={() => setOpenAddContact(true)} startDecorator={<Add />}>
            Ajouter un contact
          </Button>
        </Stack>
      ) : (
        <>
          <Stack spacing={2} sx={{ position: "relative" }}>
            {loading && (
              <Box
                sx={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  backgroundColor: "transparent", // Fond transparent
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 10,
                }}
              >
                <CircularProgress size="sm" />
              </Box>
            )}
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography level="title-md">{title}</Typography>
              {showAddButton && (
                <Button
                  onClick={() => setOpenAddContact(true)}
                  startDecorator={<Add />}
                  data-cy={"add-contact-button"}
                >
                  Ajouter Contact
                </Button>
              )}
            </Stack>
            {initialLoading ? (
              <Stack spacing={1}>
                {Array.from({ length: 10 }).map((_, index) => (
                  <Box key={index} sx={{ width: "100%", height: 30, position: "relative" }}>
                    <Skeleton animation="wave" sx={{ width: "100%", height: 30 }} />
                  </Box>
                ))}
              </Stack>
            ) : (
              contacts.length > 0 && (
                <Table sx={{ borderRadius: "sm" }} variant="outlined">
                  <thead>
                    <tr>
                      <th>Nom</th>
                      <th>Spécialités</th>
                      <th>Villes</th>
                      {!filterInactive && <th>Ajouter comme destinataire</th>}
                      {!filterInactive && <th>Médecin adresseur</th>}
                      <th style={{ width: "150px" }} />
                    </tr>
                  </thead>
                  <tbody>
                    {contacts.map((correspondant) => (
                      <tr key={correspondant.contact.id}>
                        <td>
                          <Box
                            component="a"
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              handleEdit(correspondant);
                            }}
                            sx={{
                              color: "primary.main",
                              textDecoration: "none",
                              "&:hover": {
                                textDecoration: "underline",
                              },
                            }}
                          >
                            {correspondant.contact.titre && correspondant.contact.titre + " "}
                            {correspondant.contact.nom?.toUpperCase() +
                              " " +
                              correspondant.contact.prenom}
                          </Box>
                        </td>
                        <td>
                          <Stack direction="row" spacing={0.5} flexWrap="wrap">
                            {correspondant.contact.specialite &&
                              correspondant.contact.specialite.every(isSpecialite) &&
                              (correspondant.contact.specialite as unknown as Specialite[]).map(
                                (s) => {
                                  return (
                                    <Chip key={s.spe} size="sm" variant="outlined">
                                      {s.spe}
                                    </Chip>
                                  );
                                }
                              )}
                          </Stack>
                        </td>
                        <td>
                          <Stack direction="row" spacing={0.5} flexWrap="wrap">
                            {adresses[correspondant.contact.id]?.map((a) => (
                              <Tooltip
                                key={a.id}
                                title={getContactAddress(a)}
                                placement="top"
                                arrow
                              >
                                <Chip size="sm" variant="outlined">
                                  {a.code_postal +
                                    " " +
                                    a.ville[0].toUpperCase() +
                                    a.ville.slice(1).toLowerCase()}
                                </Chip>
                              </Tooltip>
                            ))}
                          </Stack>
                        </td>
                        {!filterInactive && (
                          <td>
                            <Switch
                              startDecorator={<Typography level="body-xs">Non</Typography>}
                              endDecorator={<Typography level="body-xs">Oui</Typography>}
                              checked={correspondant.send_report === true}
                              onChange={() => {
                                try {
                                  handleChangeSendReport(correspondant);
                                } catch (error) {
                                  console.error("❌ ERREUR dans onChange:", error);
                                }
                              }}
                            />
                          </td>
                        )}
                        {!filterInactive && (
                          <td>
                            <Switch
                              startDecorator={<Typography level="body-xs">Non</Typography>}
                              endDecorator={<Typography level="body-xs">Oui</Typography>}
                              checked={correspondant.referral_source === true}
                              onChange={() => {
                                try {
                                  handleChangeReferralSource(correspondant);
                                } catch (error) {
                                  console.error("❌ ERREUR dans onChange:", error);
                                }
                              }}
                            />
                          </td>
                        )}
                        <td>
                          <Stack direction="row" alignItems="center" justifyContent="flex-end">
                            <IconButton size="sm" onClick={() => handleEdit(correspondant)}>
                              <Edit />
                            </IconButton>
                            <IconButton
                              size="sm"
                              onClick={() => handleDelete(correspondant.contact.id)}
                            >
                              <Delete />
                            </IconButton>
                          </Stack>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )
            )}
          </Stack>
        </>
      )}

      {openEditContact && selectedContact && (
        <ContactModalForm
          contact={selectedContact}
          open={openEditContact}
          onClose={onCloseContact}
          patient={patient}
          onSave={handleContactSave}
        />
      )}

      {openAddContact && (
        <ModalComponent
          open={openAddContact}
          onClose={onCloseContact}
          patient={patient}
          title={"Ajouter un contact"}
          validateLabel={"Ajouter"}
          onValidate={handleAddContact}
          canValidate={
            !!selectedContact && !contacts.some((c) => c.contact.id === selectedContact.id)
          }
        >
          <Stack gap={2} pt={4} pb={4} width={"100%"} data-cy={"add-contact-modal"}>
            <Typography level={"body-sm"}>
              Sélectionner un contact existant ou créer un nouveau
            </Typography>
            <ContactSelector
              value={selectedContact?.id || ""}
              onSelect={(contact, isNew) => {
                if (isNew && contact) {
                  // Create a new CorrespondantPatientWithContact with the new contact
                  const newCorrespondant: CorrespondantPatientWithContact = {
                    contact: contact,
                    send_report: true,
                    referral_source: false,
                  };
                  setContacts((prevState) => [...prevState, newCorrespondant]);
                  onCloseContact();
                } else {
                  // Vérifier si le contact est déjà dans la liste avant de le sélectionner
                  if (contact && !contacts.some((c) => c.contact.id === contact.id)) {
                    setSelectedContact(contact);
                  } else if (contact) {
                    // Notifier l'utilisateur mais laisser le contact sélectionné pour éviter le clignotement
                    setSelectedContact(contact);
                    snackbar.show(
                      "Ce contact est déjà dans la liste des contacts du patient",
                      "warning"
                    );
                  }
                }
              }}
              patient={patient}
            />
            {/* Message déplacé vers une notification snackbar pour éviter le clignotement */}
          </Stack>
        </ModalComponent>
      )}
    </>
  );
};

export default ContactListTable;
