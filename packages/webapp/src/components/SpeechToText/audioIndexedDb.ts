// Utilitaires IndexedDB pour l'audio
// Permet de sauvegarder, relire et effacer des chunks audio temporairement côté navigateur

const DB_NAME = "audio-recorder-db";
const STORE_NAME = "audio-chunks";
const DB_VERSION = 1;

export function openAudioDb(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const req = indexedDB.open(DB_NAME, DB_VERSION);
    req.onerror = () => reject(req.error);
    req.onsuccess = () => resolve(req.result);
    req.onupgradeneeded = () => {
      const db = req.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        db.createObjectStore(STORE_NAME, { keyPath: "key", autoIncrement: true });
      }
    };
  });
}

export async function saveChunkToDb(recordingId: string, chunk: Blob, index: number) {
  const db = await openAudioDb();
  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);
  await new Promise<void>((resolve, reject) => {
    const req = store.put({ key: `${recordingId}_${index}`, recordingId, index, chunk });
    req.onsuccess = () => resolve();
    req.onerror = () => reject(req.error);
  });
  db.close();
}

export async function getChunksFromDb(recordingId: string): Promise<Blob[]> {
  const db = await openAudioDb();
  const tx = db.transaction(STORE_NAME, "readonly");
  const store = tx.objectStore(STORE_NAME);
  const req = store.openCursor();
  const chunks: { index: number; chunk: Blob }[] = [];
  return new Promise((resolve, reject) => {
    req.onerror = () => reject(req.error);
    req.onsuccess = (event) => {
      const cursor = req.result;
      if (cursor) {
        const value = cursor.value;
        if (value.recordingId === recordingId) {
          chunks.push({ index: value.index, chunk: value.chunk });
        }
        cursor.continue();
      } else {
        db.close();
        resolve(chunks.sort((a, b) => a.index - b.index).map((c) => c.chunk));
      }
    };
  });
}

export async function clearChunksFromDb(recordingId: string) {
  const db = await openAudioDb();
  const tx = db.transaction(STORE_NAME, "readwrite");
  const store = tx.objectStore(STORE_NAME);
  const req = store.openCursor();
  return new Promise<void>((resolve, reject) => {
    req.onerror = () => reject(req.error);
    req.onsuccess = () => {
      const cursor = req.result;
      if (cursor) {
        const value = cursor.value;
        if (value.recordingId === recordingId) {
          cursor.delete();
        }
        cursor.continue();
      } else {
        db.close();
        resolve();
      }
    };
  });
}

// Retourne la liste des recordingId présents dans IndexedDB
export async function listRecordingIdsInDb(): Promise<string[]> {
  const db = await openAudioDb();
  const tx = db.transaction(STORE_NAME, "readonly");
  const store = tx.objectStore(STORE_NAME);
  const req = store.openCursor();
  const ids = new Set<string>();
  return new Promise((resolve, reject) => {
    req.onerror = () => reject(req.error);
    req.onsuccess = () => {
      const cursor = req.result;
      if (cursor) {
        const value = cursor.value;
        if (value.recordingId) {
          ids.add(value.recordingId);
        }
        cursor.continue();
      } else {
        db.close();
        resolve(Array.from(ids));
      }
    };
  });
}
