import React, { useRef } from "react";

import FileUploadIcon from "@mui/icons-material/FileUpload";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";

interface AudioFileUploadProps {
  onAudioSelected: (blob: Blob) => void;
}

const AudioFileUpload: React.FC<AudioFileUploadProps> = ({ onAudioSelected }) => {
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files && e.target.files[0];
    if (file) {
      onAudioSelected(file);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        width: "100%",
      }}
    >
      <input
        ref={inputRef}
        type="file"
        accept="audio/*"
        style={{ display: "none" }}
        onChange={handleFileChange}
      />
      <Button
        onClick={() => inputRef.current?.click()}
        color="primary"
        variant="solid"
        startDecorator={<FileUploadIcon />}
      >
        Choisir un fichier audio
      </Button>
    </Box>
  );
};

export default AudioFileUpload;
