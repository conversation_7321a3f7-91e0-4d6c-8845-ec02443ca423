import React from "react";

import Button, { ButtonProps } from "@mui/joy/Button";
import Tooltip from "@mui/joy/Tooltip";

import { useAudioUpload } from "../../hooks/useAudioUpload";

interface SaveAudioButtonProps {
  audioBlob: Blob | null;
  patient: any;
  title?: string;
  consultationId?: string | null;
  auteur?: string;
  onSuccess?: () => void;
  sx?: ButtonProps["sx"];
}

// SaveAudioButton allows saving an audio recording with optional title and consultation link
// It ensures a valid patient and patient.id are provided before uploading the audio recording.
const SaveAudioButton: React.FC<SaveAudioButtonProps> = ({
  audioBlob,
  patient,
  title,
  consultationId,
  auteur,
  onSuccess,
  sx,
}) => {
  const { uploadAudio, loading } = useAudioUpload();

  // Check for missing requirements
  const missingRequirements = React.useMemo(() => {
    const missing: string[] = [];
    if (!patient || !patient.id) {
      missing.push("Patient");
    }
    if (!consultationId) {
      missing.push("Consultation associée");
    }
    if (!audioBlob) {
      missing.push("Enregistrement audio");
    }
    return missing;
  }, [patient, consultationId, audioBlob]);

  const isDisabled = missingRequirements.length > 0 || loading;

  const tooltipText = React.useMemo(() => {
    if (missingRequirements.length > 0) {
      return `Éléments manquants: ${missingRequirements.join(", ")}`;
    }
    if (loading) {
      return "Sauvegarde en cours...";
    }
    return "Sauvegarder l'enregistrement";
  }, [missingRequirements, loading]);

  // Handles the click event for saving audio
  const handleClick = async () => {
    if (!audioBlob) return;
    // Ensure patient and patient.id are defined before sending to the server
    // This prevents sending an undefined patient ID and helps debugging
    if (!patient || !patient.id) {
      alert("Please select a patient before saving the audio.");
      return;
    }
    if (!consultationId) {
      alert("Please select a consultation before saving the audio.");
      return;
    }
    // Pass title and consultationId to uploadAudio
    // Ensure consultationId is string or undefined, not null
    const success = await uploadAudio(
      audioBlob,
      patient.id,
      undefined,
      consultationId || undefined,
      title,
      auteur
    );
    if (success) {
      if (onSuccess) onSuccess();
    } else {
      alert("Erreur lors de l'enregistrement audio");
    }
  };

  return (
    <Tooltip title={tooltipText} placement="top">
      <span>
        {" "}
        {/* Wrapper span pour que le tooltip fonctionne avec un bouton désactivé */}
        <Button
          size="sm"
          variant="solid"
          color="primary"
          onClick={handleClick}
          disabled={isDisabled}
          loading={loading}
          sx={sx}
        >
          Sauvegarder
        </Button>
      </span>
    </Tooltip>
  );
};

export default SaveAudioButton;
