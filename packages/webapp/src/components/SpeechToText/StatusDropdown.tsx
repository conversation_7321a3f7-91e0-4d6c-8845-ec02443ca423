import * as React from "react";

import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";

export type AudioStatus =
  | "unverified"
  | "pending_validation"
  | "validated"
  | "pending_deletion_validated"
  | "pending_deletion_inactive"
  | "pending_deletion_7day_warning_sent"
  | "deleted";

interface StatusDropdownProps {
  value: AudioStatus;
  onChange: (value: AudioStatus) => void;
  disabled?: boolean;
}

// English labels for each status
const statusLabels: Record<AudioStatus, string> = {
  unverified: "Unverified",
  pending_validation: "Pending Validation",
  validated: "Validated",
  pending_deletion_validated: "Pending Deletion (Validated)",
  pending_deletion_inactive: "Pending Deletion (Inactive)",
  pending_deletion_7day_warning_sent: "Deletion Warning Sent",
  deleted: "Deleted",
};

const StatusDropdown: React.FC<StatusDropdownProps> = ({ value, onChange, disabled }) => (
  <Select
    value={value}
    onChange={(_, newValue) => {
      if (newValue) onChange(newValue as AudioStatus);
    }}
    size="sm"
    variant="outlined"
    sx={{ minWidth: 180 }}
    disabled={disabled}
  >
    {Object.entries(statusLabels).map(([key, label]) => (
      <Option key={key} value={key}>
        {label}
      </Option>
    ))}
  </Select>
);

export default StatusDropdown;
