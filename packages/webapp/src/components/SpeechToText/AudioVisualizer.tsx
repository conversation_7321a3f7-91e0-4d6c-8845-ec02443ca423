import React, { useEffect, useRef, useState } from "react";

import Box from "@mui/joy/Box";

interface AudioVisualizerProps {
  analyserNode: AnalyserNode | null;
  isPaused: boolean;
  barCount?: number; // Ce sera moins pertinent si la largeur est dynamique
  barColor?: string;
  barWidth?: number;
  gap?: number;
  visualizerHeight?: number;
  centralLineColor?: string;
}

const AudioVisualizer: React.FC<AudioVisualizerProps> = ({
  analyserNode,
  isPaused,
  barColor = "#888888", // Couleur grise neutre
  barWidth = 3, // Largeur de barre un peu réduite
  gap = 2,
  visualizerHeight = 60, // Hauteur augmentée
  centralLineColor = "#555555", // Ligne centrale un peu plus foncée
}) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const [canvasWidth, setCanvasWidth] = useState(300); // Largeur initiale, sera mise à jour

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeObserver = new ResizeObserver((entries) => {
      if (entries[0]) {
        setCanvasWidth(entries[0].contentRect.width);
      }
    });

    if (canvas.parentElement) {
      resizeObserver.observe(canvas.parentElement);
      setCanvasWidth(canvas.parentElement.clientWidth);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    if (analyserNode) {
      analyserNode.fftSize = 256; // Ou 512 pour plus de résolution si besoin
      dataArrayRef.current = new Uint8Array(analyserNode.frequencyBinCount);
    }

    const draw = () => {
      if (!canvasRef.current || !analyserNode || !dataArrayRef.current) {
        if (!isPaused) animationFrameIdRef.current = requestAnimationFrame(draw);
        return;
      }

      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      const dataArray = dataArrayRef.current;

      if (!ctx) {
        if (!isPaused) animationFrameIdRef.current = requestAnimationFrame(draw);
        return;
      }

      // Ajuster la taille du canvas ici aussi si elle change dynamiquement
      canvas.width = canvasWidth;
      canvas.height = visualizerHeight;

      const centerY = canvas.height / 2;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Dessiner la ligne centrale
      ctx.fillStyle = centralLineColor;
      ctx.fillRect(0, centerY - 0.5, canvas.width, 1); // Ligne de 1px d'épaisseur

      if (isPaused) {
        // Si en pause, on a déjà dessiné la ligne centrale, on arrête l'animation
        if (animationFrameIdRef.current) {
          cancelAnimationFrame(animationFrameIdRef.current);
          animationFrameIdRef.current = null;
        }
        return;
      }

      analyserNode.getByteFrequencyData(dataArray);

      const bufferLength = analyserNode.frequencyBinCount;
      const totalBarVisualWidth = barWidth + gap;
      const numVisibleBars = Math.floor(canvas.width / totalBarVisualWidth);
      const step = Math.max(1, Math.floor(bufferLength / numVisibleBars));
      let x = 0;

      for (let i = 0; i < numVisibleBars; i++) {
        let barValueSum = 0;
        for (let j = 0; j < step; j++) {
          barValueSum += dataArray[i * step + j] || 0;
        }
        const avgBarValue = barValueSum / step;
        // L'amplitude est la hauteur totale de la barre (moitié au-dessus, moitié en dessous du centre)
        const barAmplitude = (avgBarValue / 255.0) * (canvas.height * 0.9); // 0.9 pour ne pas toucher les bords haut/bas

        ctx.fillStyle = barColor;
        // Dessiner la barre symétriquement autour de la ligne centrale
        ctx.fillRect(x, centerY - barAmplitude / 2, barWidth, barAmplitude);
        x += totalBarVisualWidth;
      }
      animationFrameIdRef.current = requestAnimationFrame(draw);
    };

    if (!isPaused && analyserNode) {
      draw();
    } else {
      // Si en pause au montage initial ou si analyserNode devient null, on s'assure de dessiner la ligne centrale
      // et d'arrêter toute animation potentielle.
      draw(); // Appel pour dessiner la ligne centrale si en pause
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
    }

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
    };
  }, [
    analyserNode,
    isPaused,
    barColor,
    barWidth,
    gap,
    visualizerHeight,
    centralLineColor,
    canvasWidth,
  ]);

  return (
    <Box
      sx={{
        width: "100%",
        height: visualizerHeight,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        my: 1,
        overflow: "hidden",
      }}
    >
      <canvas ref={canvasRef} />
    </Box>
  );
};

export default AudioVisualizer;
