// Composant pour afficher les infos du document et sélectionner le patient
import React from "react";

import Card from "@mui/joy/Card";
import Typography from "@mui/joy/Typography";

import PatientSelector from "../../components/Forms/PatientSelector";

interface DocumentInfoProps {
  patient: any;
  onPatientChange: (patient: any) => void;
  disabled?: boolean;
}

const DocumentInfo: React.FC<DocumentInfoProps> = ({
  patient,
  onPatientChange,
  disabled = false,
}) => {
  return (
    <Card variant="outlined" sx={{ my: 2 }}>
      <Typography level="title-md">Informations du document</Typography>
      <PatientSelector value={patient?.id || ""} onSelect={onPatientChange} disabled={disabled} />
    </Card>
  );
};

export default DocumentInfo;
