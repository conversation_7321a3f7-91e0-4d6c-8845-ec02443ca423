// packages/webapp/src/components/SpeechToText/AudioTranscriptionList.tsx
import React from "react";

// Adjust path if necessary

// Added MicIcon import
import AddIcon from "@mui/icons-material/Add";
import ArrowBack from "@mui/icons-material/ArrowBack";
// Import PlayDisabledIcon
import MicIcon from "@mui/icons-material/Mic";
import PauseIcon from "@mui/icons-material/Pause";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PlayDisabledIcon from "@mui/icons-material/PlayDisabled";
// For the plus icon
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import CircularProgress from "@mui/joy/CircularProgress";
import List from "@mui/joy/List";
import ListItem from "@mui/joy/ListItem";
import ListItemButton from "@mui/joy/ListItemButton";
// Added Button import

import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useAuthenticatedAudio } from "../../hooks/useAuthenticatedAudio";
import { AudioRecording } from "../../models/types";

interface AudioTranscriptionListProps {
  patientId: string;
  consultationId: string;
  onTranscriptionSelect: (transcription: string) => void;
  onOpenDictationDrawer?: () => void;
  isDictateButtonDisabled?: boolean;
  refreshKey?: number; // To trigger re-fetch
}

const AudioTranscriptionList: React.FC<AudioTranscriptionListProps> = ({
  patientId,
  consultationId,
  onTranscriptionSelect,
  onOpenDictationDrawer,
  isDictateButtonDisabled = false, // Default to false if not provided
  refreshKey,
}: AudioTranscriptionListProps) => {
  const api = useApi();
  const [audioItems, setAudioItems] = React.useState<AudioRecording[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);
  const [currentPlayingId, setCurrentPlayingId] = React.useState<string | null>(null);
  const {
    blobUrl: authenticatedAudioSrc,
    isLoading: audioLoading,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    error: audioError,
    loadAudioForUrl,
  } = useAuthenticatedAudio();
  const [isPlaying, setIsPlaying] = React.useState<boolean>(false);
  const audioRef = React.useRef<HTMLAudioElement>(null);

  React.useEffect(() => {
    if (!patientId || !consultationId) {
      setLoading(false);
      setAudioItems([]);
      setError("Patient ou consultation non spécifié.");
      return;
    }

    const fetchAudios = async () => {
      setLoading(true);
      setError(null);
      try {
        // S'assurer que patientId est une chaîne avant de l'envoyer, sinon undefined
        const patientIdToSend = typeof patientId === "string" ? patientId : undefined;

        // Passer undefined comme deuxième argument pour hospitalisationId,
        // et consultationId en troisième argument.
        const response = await api.listAudioRecordings(patientIdToSend, undefined, consultationId);

        let rawItems: AudioRecording[] = [];
        if (Array.isArray(response)) {
          rawItems = response;
        } else if (response && response.results) {
          rawItems = response.results;
        }

        // Filtrer les items où la transcription est une chaîne vide
        const filteredItems = rawItems.filter((item) => item.transcription !== "");

        // Trier les items par date de création croissante
        filteredItems.sort((a, b) => {
          if (!a.created_at || !b.created_at) return 0;
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        });

        setAudioItems(filteredItems);
      } catch (err) {
        console.error("Failed to fetch audio transcriptions:", err);
        setError("Erreur lors du chargement des audios.");
        setAudioItems([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAudios();
  }, [api, patientId, consultationId, refreshKey]);

  React.useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const audioElement = audioRef.current;
    // Effect to play audio once the authenticated blob URL is ready
    if (authenticatedAudioSrc && audioRef.current && currentPlayingId) {
      // Check if the blobUrl is for the currentPlayingId to avoid race conditions if user clicks rapidly
      // This check might need refinement if loadAudioForUrl doesn't immediately clear old blobUrl
      audioRef.current.src = authenticatedAudioSrc;
      audioRef.current
        .play()
        .then(() => setIsPlaying(true))
        .catch((e) => {
          console.error("Error playing audio:", e);
          setIsPlaying(false);
          // Optionally, display an error to the user related to playback
        });
    } else if (!authenticatedAudioSrc && audioRef.current && currentPlayingId && !audioLoading) {
      // If blobUrl became null (e.g. error in hook, or new URL is null)
      // and we were trying to play something, stop it.
      audioRef.current.pause();
      if (audioRef.current.src) {
        // Check if src exists before trying to clear it
        audioRef.current.src = ""; // Explicitly clear the source
      }
      setIsPlaying(false);
    }
  }, [authenticatedAudioSrc, currentPlayingId, audioLoading]);

  // Effect to handle audio ending
  React.useEffect(() => {
    const audioElement = audioRef.current;
    const handleAudioEnded = () => {
      setIsPlaying(false);
      // Optionally, reset currentPlayingId if you want the item to not appear 'selected' after finishing
      // setCurrentPlayingId(null);
    };

    if (audioElement) {
      audioElement.addEventListener("ended", handleAudioEnded);
      return () => {
        audioElement.removeEventListener("ended", handleAudioEnded);
      };
    }
  }, [currentPlayingId]); // Re-run if currentPlayingId changes, to attach to new audio source if needed

  // Common header content
  const headerContent = (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        mb: 1,
        flexShrink: 0,
        width: "100%",
      }}
    >
      <Typography level="title-md">Audio(s) lié(s)</Typography>
      {onOpenDictationDrawer && (
        <Tooltip title="Ajouter un Audio" variant="outlined" size="sm">
          <Button
            variant="outlined"
            color="neutral"
            size="sm"
            onClick={onOpenDictationDrawer}
            disabled={isDictateButtonDisabled}
            sx={{ minWidth: "auto", px: 0.5 }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <MicIcon sx={{ fontSize: "1.25rem" }} />
              <AddIcon sx={{ fontSize: "1.25rem" }} />
            </Box>
          </Button>
        </Tooltip>
      )}
    </Box>
  );

  if (loading) {
    return <CircularProgress size="sm" sx={{ display: "block", margin: "auto" }} />;
  }

  if (error) {
    return (
      <Typography color="danger" level="body-sm">
        {error}
      </Typography>
    );
  }

  if (audioItems.length === 0) {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          alignItems: "center",
          py: 1,
        }}
      >
        {headerContent}
        <Typography level="body-sm" sx={{ mt: 2 }}>
          Aucun audio pour cette consultation.
        </Typography>
      </Box>
    );
  }

  const formatAudioTitle = (item: AudioRecording, index: number): string => {
    let dateString = "";
    if (item.created_at) {
      const date = new Date(item.created_at);
      const formattedDate = `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1).toString().padStart(2, "0")}/${date.getFullYear()}`;
      const formattedTime = `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
      dateString = `${formattedDate} ${formattedTime}`;
    }

    let titlePrefix = `${index + 1}. `;

    if (item.title) {
      return dateString
        ? `${titlePrefix}${item.title} - ${dateString}`
        : `${titlePrefix}${item.title}`;
    }

    if (dateString) {
      return `${titlePrefix}${dateString}`;
    }

    return `${titlePrefix}Audio #${item.id}`; // Fallback
  };

  const handlePlayPause = (itemId: string, fileUrl: string) => {
    if (audioRef.current) {
      if (currentPlayingId === itemId) {
        // Clicked on the currently playing/paused item
        if (isPlaying) {
          audioRef.current.pause();
          setIsPlaying(false);
        } else {
          audioRef.current.play();
          setIsPlaying(true);
        }
      } else {
        // Clicked on a new item
        if (audioRef.current.src) {
          // Check if there's any src to begin with
          audioRef.current.pause(); // Pause the old one
          audioRef.current.src = ""; // Explicitly clear the source to prevent any lingering playback/loading
        }
        setCurrentPlayingId(itemId);
        loadAudioForUrl(fileUrl); // Trigger authenticated load for the new URL the audio
        // Play will be called in the useEffect that listens to authenticatedAudioSrc
      }
    }
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100%", width: "100%" }}>
      {headerContent}
      <List
        variant="outlined"
        sx={{
          borderRadius: "sm",
          overflow: "auto", // Pour le défilement vertical
          flexGrow: 1,
          width: "100%", // Assurer que la liste prend la largeur contrainte
        }}
      >
        {audioItems.map((item, index) => {
          const textToUse = item.transcription_corrected || item.transcription;
          return (
            <ListItem key={item.id}>
              <ListItemButton
                disabled={!textToUse}
                onClick={() => textToUse && onTranscriptionSelect(textToUse)}
                sx={{
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between", // Pousse le groupe play/spinner à droite
                  py: 0.5, // Réduction du padding vertical
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    flexGrow: 1,
                    overflow: "hidden",
                    mr: 1,
                    minWidth: 0,
                  }}
                >
                  <ArrowBack
                    sx={{
                      mr: 0.75, // Marge réduite
                      color: item.transcription == null ? "action.disabled" : "inherit",
                      fontSize: "1.1rem", // Taille de l'icône réduite
                    }}
                  />
                  <Typography
                    level="body-xs" // Taille de police réduite
                    sx={{
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      maxWidth: "100%", // Assurer que le texte ne dépasse pas son conteneur
                    }}
                    title={formatAudioTitle(item, index)}
                  >
                    {formatAudioTitle(item, index)}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  {!item.file ? (
                    <PlayDisabledIcon
                      sx={{
                        fontSize: "1.1rem",
                        color: "action.disabled",
                        cursor: "default", // Make it clear it's not clickable
                      }}
                    />
                  ) : item.id === currentPlayingId && isPlaying ? (
                    <PauseIcon
                      sx={{
                        cursor: "pointer",
                        fontSize: "1.1rem",
                        color: "primary.main", // Or any color indicating active state
                      }}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent ListItemButton click
                        handlePlayPause(item.id, item.file);
                      }}
                    />
                  ) : (
                    <PlayArrowIcon
                      sx={{
                        cursor: "pointer",
                        fontSize: "1.1rem",
                        color: "action.active",
                      }}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent ListItemButton click
                        handlePlayPause(item.id, item.file);
                      }}
                    />
                  )}

                  {/* Spinner for transcription loading, shown only if not disabled and not currently playing this item */}
                  {item.transcription == null &&
                    item.file &&
                    !(item.id === currentPlayingId && isPlaying) && (
                      <CircularProgress size="sm" sx={{ ml: 0.75 }} />
                    )}
                </Box>
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
      <audio ref={audioRef} /> {/* Hidden audio player */}
    </Box>
  );
};

export default AudioTranscriptionList;
