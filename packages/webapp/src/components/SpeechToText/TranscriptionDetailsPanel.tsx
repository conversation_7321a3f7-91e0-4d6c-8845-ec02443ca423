import React, { useState } from "react";

import Edit from "@mui/icons-material/Edit";
import Save from "@mui/icons-material/Save";
// Use @mui/material for all layout and input components for consistency with the rest of the app and to avoid missing components.
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import Snackbar from "@mui/joy/Snackbar";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { AudioRecording } from "../../models/types";
import PatientCard from "../PatientCard";
import AudioPlayer from "./AudioPlayer";
import StatusDropdown, { AudioStatus } from "./StatusDropdown";
import TranscribedTextDisplay from "./TranscribedTextDisplay";
import TranscribedTextEditor from "./TranscribedTextEditor";
import ExpandRawTranscription from "./TranscriptionDetailsPanel.ExpandRawTranscription";

interface TranscriptionDetailsPanelProps {
  recording: AudioRecording | null;
  onSaveCorrection: (id: string, corrected: string) => void;
  onStatusChange?: (id: string, newStatus: string) => void;
}

const TranscriptionDetailsPanel: React.FC<TranscriptionDetailsPanelProps> = ({
  recording,
  onSaveCorrection,
  onStatusChange,
}) => {
  const api = useApi();
  const [status, setStatus] = useState<AudioStatus>(
    (recording?.status as AudioStatus) || "unverified"
  );
  const [statusLoading, setStatusLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    color: "success" | "danger";
  }>({ open: false, message: "", color: "success" });

  React.useEffect(() => {
    setStatus((recording?.status as AudioStatus) || "unverified");
  }, [recording]);

  // Handle status change and API update
  const handleStatusChange = async (newStatus: AudioStatus) => {
    if (!recording || newStatus === status) return;
    setStatusLoading(true);
    try {
      await api.updateAudioRecording(recording.id, { status: newStatus });
      setStatus(newStatus);
      setSnackbar({ open: true, message: "Status updated successfully", color: "success" });
    } catch (e) {
      setSnackbar({ open: true, message: "Failed to update status", color: "danger" });
    } finally {
      setStatusLoading(false);
    }
  };

  // Use both snake_case and camelCase for compatibility with backend and frontend naming
  // Use type assertion to access both snake_case and camelCase for compatibility with backend/frontend naming
  const getCorrectedTranscription = (rec: AudioRecording | null | undefined) =>
    rec?.transcription_corrected ?? (rec as any)?.correctedTranscription ?? "";
  // NOTE: The 'as any' is required to avoid TypeScript errors if the property is not in the type definition.

  const [editedCorrection, setEditedCorrection] = useState(getCorrectedTranscription(recording));
  const [editing, setEditing] = useState(false);

  React.useEffect(() => {
    setEditedCorrection(getCorrectedTranscription(recording));
    setEditing(false);
  }, [recording]);

  // State for expanding/collapsing the raw transcription (must be called unconditionally)
  const [showRaw, setShowRaw] = React.useState(false);

  if (!recording) {
    // Show a message if no transcription is selected
    return <Typography level="title-lg">No transcription selected.</Typography>;
  }

  // Display audio, then transcribed text (read-only), then corrected text (editable)
  return (
    <Box sx={{ width: "100%", height: "100%" }}>
      <Card
        variant="outlined"
        sx={{
          width: "100%",
          height: "100%",
          p: 3,
          pr: 2,
          borderRadius: 4,
          boxShadow: "md",
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
      >
        {/* Show PatientCard if patient exists, otherwise show N/A */}
        {recording.patient ? (
          <PatientCard patient={recording.patient} width="100%" />
        ) : (
          <Typography level="body-md" sx={{ mb: 0.5 }}>
            Patient: N/A
          </Typography>
        )}
        {/* Consultation motif and date, fallback to N/A if missing */}
        <Typography level="body-md" sx={{ mb: 2 }}>
          Consultation :{" "}
          {recording.consultation
            ? `${recording.consultation.motif_consultation || "N/A"}${recording.consultation.consultation_date ? " (" + new Date(recording.consultation.consultation_date).toLocaleDateString("fr-FR") + ")" : ""}`
            : "N/A"}
        </Typography>

        {/* Audio player for the recording */}
        <AudioPlayer src={recording.file || ""} />

        {/* Expand/collapse arrow for raw transcription */}
        <ExpandRawTranscription
          expanded={showRaw}
          onToggle={() => setShowRaw((v) => !v)}
          label="Afficher la transcription brute"
        />
        {showRaw && (
          <TranscribedTextDisplay
            text={recording.transcription || ""}
            label="Transcription brute"
          />
        )}

        {/* Status dropdown and edit/save/cancel buttons above the text area, on the same row */}
        <TranscribedTextEditor
          text={editedCorrection}
          label="Transcription"
          onSave={(newText) => onSaveCorrection(recording.id, newText)}
          editing={editing}
          setEditing={setEditing}
          setValue={setEditedCorrection}
          actionsLeft={
            <StatusDropdown
              value={recording?.status || "unverified"}
              onChange={(status) => {
                if (recording && onStatusChange) {
                  onStatusChange(recording.id, status);
                }
                handleStatusChange(status);
              }}
              disabled={statusLoading}
            />
          }
          actionsRight={
            !editing ? (
              <Button
                variant="plain"
                color="neutral"
                size="sm"
                onClick={() => setEditing(true)}
                startDecorator={<Edit />}
              >
                Modifier
              </Button>
            ) : (
              <Box sx={{ display: "flex", gap: 1 }}>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => {
                    setEditing(false);
                    onSaveCorrection(recording.id, editedCorrection);
                  }}
                  startDecorator={<Save />}
                >
                  Enregistrer
                </Button>
                <Button
                  variant="outlined"
                  color="neutral"
                  onClick={() => {
                    setEditedCorrection(recording.transcription_corrected || "");
                    setEditing(false);
                  }}
                >
                  Annuler
                </Button>
              </Box>
            )
          }
        />
        <Snackbar
          open={snackbar.open}
          autoHideDuration={2500}
          color={snackbar.color}
          onClose={() => setSnackbar((s) => ({ ...s, open: false }))}
        >
          {snackbar.message}
        </Snackbar>
      </Card>
    </Box>
  );
};

export default TranscriptionDetailsPanel;
