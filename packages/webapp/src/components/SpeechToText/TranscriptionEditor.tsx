// Composant pour éditer la transcription
import React from "react";

import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import Typography from "@mui/joy/Typography";

import SaveAudioButton from "./SaveAudioButton";

interface TranscriptionEditorProps {
  transcription: string;
  formattedText: string;
  setFormattedText: (text: string) => void;
  audioBlob: Blob | null;
  patient: any;
  onAudioSaved?: () => void;
}

const TranscriptionEditor: React.FC<TranscriptionEditorProps> = ({
  transcription,
  formattedText,
  setFormattedText,
  audioBlob,
  patient,
  onAudioSaved,
}) => {
  return (
    <Card variant="outlined" sx={{ my: 2 }}>
      <Typography level="title-md">Transcription</Typography>
      <Typography level="body-sm" sx={{ mt: 1 }}>
        Transcription brute
      </Typography>
      <textarea
        value={transcription}
        readOnly
        rows={4}
        style={{ width: "100%", marginBottom: 8 }}
      />
      <Typography level="body-sm">Texte formaté (éditable)</Typography>
      <textarea
        value={formattedText}
        onChange={(e) => setFormattedText(e.target.value)}
        rows={6}
        style={{ width: "100%", marginBottom: 8 }}
      />
      <div style={{ display: "flex", justifyContent: "flex-end", gap: 8 }}>
        <Button size="sm" variant="outlined" color="neutral" disabled>
          Annuler
        </Button>
        <SaveAudioButton audioBlob={audioBlob} patient={patient} onSuccess={onAudioSaved} />
      </div>
    </Card>
  );
};

export default TranscriptionEditor;
