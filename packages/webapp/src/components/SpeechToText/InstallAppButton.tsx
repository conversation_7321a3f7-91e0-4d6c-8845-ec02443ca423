// Bouton pour installer l'application en PWA
import React from "react";

import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";

const InstallAppButton: React.FC = () => {
  // Placeholder logique installation PWA
  const handleInstall = () => {
    alert("Installation PWA non implémentée dans ce mockup.");
  };
  return (
    <Card variant="outlined" sx={{ my: 2, p: 2, display: "flex", justifyContent: "center" }}>
      <Button onClick={handleInstall} color="success" variant="soft">
        Installer l'application (PWA)
      </Button>
    </Card>
  );
};

export default InstallAppButton;
