import React from "react";

import Box from "@mui/joy/Box";
import Textarea from "@mui/joy/Textarea";

interface TranscribedTextEditorProps {
  text: string;
  label?: string;
  onSave: (newText: string) => void;
  actionsLeft?: React.ReactNode; // Left-aligned actions (e.g., dropdown)
  actionsRight?: React.ReactNode; // Right-aligned actions (e.g., edit/save/cancel)
  editing: boolean;
  setEditing: (v: boolean) => void;
  setValue: (v: string) => void;
}

/**
 * Allows editing of the corrected transcription text.
 */
const TranscribedTextEditor: React.FC<TranscribedTextEditorProps> = ({
  text,
  label,
  onSave,
  actionsLeft,
  actionsRight,
  editing,
  setEditing,
  setValue,
}) => {
  // Sync local value when the prop 'text' changes (e.g. when switching recordings)
  // Only leave editing mode if a new recording is loaded while editing
  // Prevent initial empty value from overwriting the corrected transcription when first clicking after reload
  const hasSetValue = React.useRef(false);
  React.useEffect(() => {
    // Only set value if not already set, or if text is non-empty
    if (!hasSetValue.current || text) {
      setValue(text || "");
      hasSetValue.current = true;
    }
    // If editing and the text changes (i.e., new recording), exit edit mode
    // This prevents exiting edit mode when simply typing
    // (Do not call setEditing(false) unconditionally)
  }, [text, setValue]);

  return (
    <Box sx={{ mb: 2 }}>
      {/* Row with label on top, dropdown at left, actions at right */}
      <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 0.5 }}>
        {actionsLeft}
        {actionsRight}
      </Box>
      <Textarea
        minRows={3}
        value={text}
        onChange={(e) => setValue(e.target.value)}
        disabled={!editing}
        sx={{ mb: 1, borderRadius: 2 }}
      />
    </Box>
  );
};

export default TranscribedTextEditor;
