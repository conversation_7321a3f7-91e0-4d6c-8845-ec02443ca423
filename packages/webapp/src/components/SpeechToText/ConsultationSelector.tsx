import React, { useEffect, useState } from "react";

import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";

import { useApi } from "../../contexts/ApiContext";

// Use the API context for correct base URL and auth

interface ConsultationSelectorProps {
  patientId: string | null;
  value: string | null;
  onChange: (consultationId: string | null) => void;
  disabled?: boolean;
}

const ConsultationSelector: React.FC<ConsultationSelectorProps> = ({
  patientId,
  value,
  onChange,
  disabled = false,
}) => {
  const [consultations, setConsultations] = useState<any[]>([]); // TODO: Remplacer any par le type Consultation approprié
  const [loading, setLoading] = useState(false);

  // console.log(
  //   "[ConsultationSelector] Props: patientId:",
  //   patientId,
  //   "value (initialConsultationId):",
  //   value,
  //   "disabled:",
  //   disabled
  // );

  const api = useApi(); // Always use the ApiContext to get the correct ApiClient instance

  // Effect 1: <PERSON>les fetching a specific consultation when disabled and value is provided.
  useEffect(() => {
    if (disabled && value) {
      // console.log(
      //   `[ConsultationSelector] Disabled & Locked Mode Effect. Value: ${value}, Current Consultations: ${consultations.length > 0 && consultations[0] ? consultations[0].id : "none"}`
      // );
      // If the correct single consultation is already loaded, do nothing.
      if (
        consultations &&
        consultations.length === 1 &&
        consultations[0] &&
        consultations[0].id === value
      ) {
        // console.log(
        //   `[ConsultationSelector] Disabled & Locked Mode: Consultation ${value} is already loaded. Skipping fetch.`
        // );
        // Ensure loading is false if we are certain no fetch is needed for this path.
        // If setLoading(true) is only called before an actual fetch, this might not be needed.
        // However, to be safe if other effects might set it true:
        setLoading(false);
        return;
      }

      // console.log(
      //   `[ConsultationSelector] Disabled & Locked Mode: Attempting to fetch details for consultation ${value}.`
      // );
      setLoading(true);
      api
        .getConsultation(value)
        .then((consultation) => {
          // console.log(
          //   "[ConsultationSelector] Disabled & Locked Mode: Fetched specific consultation:",
          //   consultation
          // );
          setConsultations(consultation ? [consultation] : []);
          if (!consultation) {
            console.warn(
              `[ConsultationSelector] Disabled & Locked Mode: Consultation ID "${value}" not found.`
            );
          }
        })
        .catch((error) => {
          console.error(
            `[ConsultationSelector] Disabled & Locked Mode: Error fetching consultation ${value}:`,
            error
          );
          setConsultations([]);
        })
        .finally(() => setLoading(false));
    }
  }, [api, value, disabled, consultations]); // Dependencies for disabled & locked mode. `patientId` is intentionally omitted.

  // Effect 2: Handles fetching list of consultations for enabled mode, or disabled without a specific value.
  useEffect(() => {
    // This effect should only run if we are NOT in the 'disabled and value-locked' state handled by Effect 1.
    if (!disabled || !value) {
      // console.log(
      //   `[ConsultationSelector] Enabled/List Mode Effect. PatientId: ${patientId}, Disabled: ${disabled}, Value: ${value}`
      // );
      setLoading(true);
      let fetchConsultationsPromise;
      if (patientId) {
        // console.log(
        //   `[ConsultationSelector] Enabled/List Mode: Fetching consultations for patientId: ${patientId}`
        // );
        fetchConsultationsPromise = api.listConsultation(patientId);
      } else {
        // return;
        // console.log("[ConsultationSelector] Enabled/List Mode: No patientId, fetching recent consultations.");
        fetchConsultationsPromise = api.listRecentConsultations();
      }

      fetchConsultationsPromise
        .then((response) => {
          const fetchedConsultations = response.results || [];
          // console.log(
          //   "[ConsultationSelector] Enabled/List Mode: Consultations fetched:",
          //   fetchedConsultations
          // );
          setConsultations(fetchedConsultations);
          // Warning if a 'value' is present (e.g. from a previous selection) but not in the new list when enabled.
          if (
            value &&
            !disabled &&
            fetchedConsultations.length > 0 &&
            !fetchedConsultations.some((c) => c.id === value)
          ) {
            console.warn(
              `[ConsultationSelector] Enabled/List Mode: Current value "${value}" not found in newly fetched consultations for patient ${patientId}. Selection might reset.`
            );
          }
        })
        .catch((error) => {
          console.error(
            "[ConsultationSelector] Enabled/List Mode: Error fetching consultations:",
            error
          );
          setConsultations([]);
        })
        .finally(() => setLoading(false));
    }
  }, [api, patientId, disabled, value]); // Dependencies for enabled/list mode. `consultations` state is not a direct dependency here.

  // // Log pour voir si la valeur correspond à une option
  // useEffect(() => {
  //   if (consultations.length > 0 && value) {
  //     const selectedConsultation = consultations.find((c) => c.id === value);
  //     if (selectedConsultation) {
  //       // console.log(
  //       //   "[ConsultationSelector] Initial value matches a loaded consultation:",
  //       //   selectedConsultation
  //       // );
  //     } else {
  //       console.log("[ConsultationSelector] Initial value does NOT match any loaded consultation.");
  //     }
  //   }
  // }, [consultations, value]);

  return (
    <Select
      value={value} // La valeur du Select doit être l'ID de la consultation
      onChange={(_, newValue) => onChange(newValue as string | null)}
      placeholder={loading ? "Chargement..." : "Sélectionner une consultation"}
      disabled={disabled || loading} // Supprimer la condition !patientId
      sx={{ minWidth: 220 }}
      slotProps={{ listbox: { sx: { zIndex: 3000 } } }}
    >
      <Option value="">Aucune consultation</Option>
      {consultations.map((c) => {
        // Format the consultation date for better readability
        let dateStr = c.consultation_date ? new Date(c.consultation_date).toLocaleDateString() : "";
        // Display motif_consultation and date
        // S'assurer que c.patient_details ou un équivalent est disponible si on veut afficher le nom du patient
        const patientName = c.patient_details
          ? `${c.patient_details.nom} ${c.patient_details.prenom}`
          : "";
        const displayLabel = [patientName, dateStr, c.motif_consultation]
          .filter(Boolean)
          .join(" — ");

        return (
          <Option key={c.id} value={c.id}>
            {displayLabel || "Consultation sans détails"}
          </Option>
        );
      })}
    </Select>
  );
};

export default ConsultationSelector;
