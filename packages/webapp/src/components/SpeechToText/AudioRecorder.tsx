import React, { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import CloseIcon from "@mui/icons-material/Close";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import MicIcon from "@mui/icons-material/Mic";
import PauseIcon from "@mui/icons-material/Pause";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import StopIcon from "@mui/icons-material/Stop";
import { IconButton, Stack } from "@mui/joy";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import Typography from "@mui/joy/Typography";

import AudioFileUpload from "./AudioFileUpload";
import { clearChunksFromDb, getChunksFromDb, saveChunkToDb } from "./audioIndexedDb";
import AudioVisualizer from "./AudioVisualizer";

interface AudioRecorderProps {
  onAudioRecorded: (blob: Blob | null) => void;
  resetKey?: number;
  initialBlob?: Blob | null;
  resumeInfo?: {
    recordingId: string;
    lastChunkIndex: number;
    blob: Blob;
  } | null;
  patient?: any;
  consultationId?: string | null;
}

// AudioRecorder allows the user to either record audio or upload an audio file
const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onAudioRecorded,
  resetKey,
  initialBlob,
  resumeInfo,
  patient,
  consultationId,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [, setRecordingId] = useState<string | null>(null);
  const [isUploadMode, setIsUploadMode] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunkIndexRef = useRef<number>(0);
  const stoppingRef = useRef(false);
  const blobReadyRef = useRef(false);
  const recordingIdRef = useRef<string | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);

  // Refs pour l'API Web Audio
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);

  const [audioSrcUrl, setAudioSrcUrl] = useState<string | null>(null);

  useEffect(() => {}, [audioBlob]);

  useEffect(() => {
    // (Optionnel) proposer de reprendre un enregistrement interrompu
    // getChunksFromDb(...)
  }, []);

  useEffect(() => {
    if (resetKey !== undefined) {
      reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [resetKey]);

  useEffect(() => {
    if (initialBlob) {
      setAudioBlob(initialBlob);
    }
  }, [initialBlob]);

  useEffect(() => {
    if (resumeInfo) {
      recordingIdRef.current = resumeInfo.recordingId;
      chunkIndexRef.current = resumeInfo.lastChunkIndex + 1; // On continue après le dernier chunk
      setAudioBlob(resumeInfo.blob);
    }
  }, [resumeInfo]);

  useEffect(() => {
    if (audioBlob) {
      const objectUrl = URL.createObjectURL(audioBlob);
      setAudioSrcUrl(objectUrl);
      return () => {
        URL.revokeObjectURL(objectUrl);
        setAudioSrcUrl(null);
      };
    } else {
      setAudioSrcUrl(null);
    }
  }, [audioBlob]);

  const setupAudioContext = async (stream: MediaStream) => {
    if (!audioContextRef.current) {
      const context = new (window.AudioContext || (window as any).webkitAudioContext)();
      audioContextRef.current = context;
      analyserRef.current = context.createAnalyser();
      analyserRef.current.fftSize = 256; // Peut être ajusté
      dataArrayRef.current = new Uint8Array(analyserRef.current.frequencyBinCount);
      sourceRef.current = context.createMediaStreamSource(stream);
      sourceRef.current.connect(analyserRef.current);
      // Ne pas connecter analyserRef.current à context.destination si MediaRecorder gère déjà le flux
      // ou si vous ne voulez pas entendre le son en direct via les haut-parleurs (ce qui peut causer du larsen).
      // MediaRecorder fonctionne directement avec le MediaStream.
    }
    if (audioContextRef.current?.state === "suspended") {
      await audioContextRef.current.resume();
    }
  };

  const cleanupAudioContext = () => {
    sourceRef.current?.disconnect();
    // analyserRef.current?.disconnect(); // Pas nécessaire si source est déconnectée
    if (audioContextRef.current && audioContextRef.current.state !== "closed") {
      audioContextRef.current
        .close()
        .catch((e) => console.error("Erreur lors de la fermeture de l'AudioContext", e));
    }
    audioContextRef.current = null;
    analyserRef.current = null;
    sourceRef.current = null;
    dataArrayRef.current = null;
  };

  const startRecording = async () => {
    if (isRecording) return;
    setError(null);
    setAudioBlob(null); // Réinitialiser le blob précédent
    blobReadyRef.current = false;
    if (!recordingIdRef.current) {
      recordingIdRef.current = uuidv4();
    }
    // Ensure recordingIdRef.current is not null before calling clearChunksFromDb
    if (recordingIdRef.current) {
      await clearChunksFromDb(recordingIdRef.current); // Nettoyer les anciens chunks pour ce nouvel enregistrement
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = stream;
      await setupAudioContext(stream); // Initialiser Web Audio API

      const options = { mimeType: أفضل_نوع_mime_متوفر() }; // Helper à définir
      const recorder = new MediaRecorder(stream, options);
      mediaRecorderRef.current = recorder;
      recorder.ondataavailable = async (e) => {
        if (e.data.size > 0 && recordingIdRef.current) {
          await saveChunkToDb(recordingIdRef.current, e.data, chunkIndexRef.current++);
        }
        // When stopping, assemble the blob using the type of the first chunk if available
        if (stoppingRef.current && recordingIdRef.current && !blobReadyRef.current) {
          const chunks = await getChunksFromDb(recordingIdRef.current);
          // Use the MIME type of the first chunk if available for the final blob
          const mimeType = chunks.length > 0 ? chunks[0].type : "";
          const blob = mimeType ? new Blob(chunks, { type: mimeType }) : new Blob(chunks);
          setAudioBlob(blob);
          onAudioRecorded(blob);
          stoppingRef.current = false;
          blobReadyRef.current = true;
        }
      };
      recorder.onstop = async () => {
        if (stoppingRef.current && recordingIdRef.current && !blobReadyRef.current) {
          const chunks: Blob[] = await getChunksFromDb(recordingIdRef.current);
          if (chunks.length > 0) {
            const firstChunkMimeType = chunks[0].type;
            const finalBlob = new Blob(chunks, {
              type: firstChunkMimeType || أفضل_نوع_mime_متوفر(),
            });
            setAudioBlob(finalBlob);
            onAudioRecorded(finalBlob);
            blobReadyRef.current = true;
          } else {
            setAudioBlob(null); // Assurer que audioBlob est null si pas de chunks
          }
        }

        // Nettoyage après l'arrêt complet
        if (mediaStreamRef.current) {
          mediaStreamRef.current.getTracks().forEach((track) => track.stop());
          mediaStreamRef.current = null;
        }
        cleanupAudioContext();

        setIsRecording(false);
        setIsPaused(false);
        if (timerRef.current) clearInterval(timerRef.current);
        stoppingRef.current = false; // Réinitialiser pour le prochain cycle d'enregistrement
      };
      recorder.onpause = () => {
        setIsPaused(true);
        if (timerRef.current) clearInterval(timerRef.current);
      };
      recorder.onresume = () => {
        setIsPaused(false);
        timerRef.current = setInterval(() => {
          setRecordingTime((t) => t + 1);
        }, 1000);
      };
      recorder.start(1000); // chunk toutes les secondes
      setIsRecording(true);
      setIsPaused(false);
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime((t) => t + 1);
      }, 1000);
    } catch (e) {
      setError("Impossible d'accéder au micro");
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      stoppingRef.current = true; // S'assurer que stoppingRef est true AVANT d'appeler stop()
      mediaRecorderRef.current.stop(); // ondataavailable et onstop seront appelés
    }
  };

  const pauseRecording = () => {
    mediaRecorderRef.current?.pause();
  };

  const resumeRecording = () => {
    mediaRecorderRef.current?.resume();
  };

  const reset = async () => {
    stopRecording(); // S'assurer que l'enregistrement est arrêté
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach((track) => track.stop());
      mediaStreamRef.current = null;
    }
    mediaRecorderRef.current = null;
    cleanupAudioContext(); // Nettoyer Web Audio API
    setAudioBlob(null);
    setIsRecording(false);
    setIsPaused(false);
    setRecordingTime(0);
    setError(null);
    setRecordingId(null);
    chunkIndexRef.current = 0;
    blobReadyRef.current = false;
    if (recordingIdRef.current) {
      clearChunksFromDb(recordingIdRef.current);
    }
  };

  // Handle file upload mode: set audioBlob and notify parent
  const handleAudioFileSelected = (file: Blob) => {
    setAudioBlob(file);
    onAudioRecorded(file);
  };

  const أفضل_نوع_mime_متوفر = () => {
    if (MediaRecorder.isTypeSupported("audio/webm;codecs=opus")) {
      return "audio/webm;codecs=opus";
    }
    if (MediaRecorder.isTypeSupported("audio/ogg;codecs=opus")) {
      return "audio/ogg;codecs=opus";
    }
    if (MediaRecorder.isTypeSupported("audio/webm")) {
      return "audio/webm";
    }
    if (MediaRecorder.isTypeSupported("audio/ogg")) {
      return "audio/ogg";
    }
    return "audio/wav"; // Fallback, mais WAV n'est pas toujours bien supporté pour l'enregistrement
  };

  return (
    <Card variant="outlined">
      <Typography level="title-md">Enregistrement audio</Typography>

      {!isUploadMode ? (
        // Recording interface
        <Stack gap={1} justifyContent="center">
          {error && (
            <Typography color="danger" sx={{ mb: 1 }}>
              {error}
            </Typography>
          )}
          <Stack spacing={1} alignItems="center" justifyContent="center">
            {!isRecording && !audioBlob && (
              <Button
                size="lg"
                onClick={startRecording}
                color="primary"
                startDecorator={<MicIcon />}
                disabled={!patient || !consultationId}
                title={
                  !patient && !consultationId
                    ? "Veuillez sélectionner un patient et une consultation associée"
                    : !patient
                      ? "Veuillez sélectionner un patient"
                      : !consultationId
                        ? "Veuillez sélectionner une consultation associée"
                        : "Lancer l'enregistrement"
                }
              >
                Lancer l'enregistrement
              </Button>
            )}
            {isRecording && (
              <Stack direction="row" spacing={1} alignItems="center">
                <IconButton
                  onClick={isPaused ? resumeRecording : pauseRecording}
                  color="neutral"
                  variant="plain"
                >
                  {isPaused ? <PlayArrowIcon /> : <PauseIcon />}
                </IconButton>
                <Button onClick={stopRecording} color="neutral" variant="plain">
                  <StopIcon />
                </Button>
              </Stack>
            )}
            {isRecording && (
              <Stack direction="row" spacing={1} alignItems="center">
                <AudioVisualizer analyserNode={analyserRef.current} isPaused={isPaused} />
                <Typography
                  component="div"
                  level="body-sm"
                  sx={{ minWidth: 50, display: "flex", alignItems: "center" }}
                >
                  {isRecording && (
                    <>
                      <FiberManualRecordIcon sx={{ color: "red", fontSize: "0.8rem", mr: 0.5 }} />{" "}
                      {`${recordingTime}s`}
                    </>
                  )}
                </Typography>
              </Stack>
            )}
          </Stack>
          {!audioBlob && !isRecording && (
            <Button
              onClick={() => setIsUploadMode(true)}
              startDecorator={<FileUploadIcon />}
              variant="plain"
              color="neutral"
              sx={{ mt: 2 }}
            >
              Téléverser un fichier à la place
            </Button>
          )}
        </Stack>
      ) : (
        // Upload interface
        <Box sx={{ p: 1.5 }}>
          {error && (
            <Typography color="danger" sx={{ mb: 1 }}>
              {error}
            </Typography>
          )}
          <Box
            sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}
          >
            <Typography level="title-sm">Téléverser un fichier audio</Typography>
            <Button
              onClick={() => setIsUploadMode(false)}
              variant="plain"
              color="neutral"
              startDecorator={<CloseIcon />}
            >
              Annuler
            </Button>
          </Box>
          <AudioFileUpload onAudioSelected={handleAudioFileSelected} />
        </Box>
      )}

      {audioBlob && audioSrcUrl && (
        <Box sx={{ mt: 2, p: 1.5 }}>
          <Typography level="title-sm" sx={{ mb: 1 }}>
            Audio enregistré :
          </Typography>
          <audio src={audioSrcUrl} controls style={{ width: "100%" }} />
          <Box sx={{ mt: 1, display: "flex", justifyContent: "center" }}>
            <Button
              onClick={reset}
              size="sm"
              color="neutral"
              variant="plain"
              startDecorator={<RestartAltIcon />}
            >
              Réinitialiser l'enregistrement
            </Button>
          </Box>
        </Box>
      )}
    </Card>
  );
};

export default AudioRecorder;
