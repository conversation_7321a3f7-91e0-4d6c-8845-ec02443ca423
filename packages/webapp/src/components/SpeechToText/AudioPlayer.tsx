import React from "react";

import Box from "@mui/joy/Box";
import Typography from "@mui/joy/Typography";

import { useAuthenticatedAudio } from "../../hooks/useAuthenticatedAudio";

// Adjust path if necessary

interface AudioPlayerProps {
  src: string; // This will be the direct URL to the media file
}

/**
 * AudioPlayer displays an audio player for the provided audio file URL.
 */
const AudioPlayer: React.FC<AudioPlayerProps> = ({ src: directUrl }) => {
  const { blobUrl: audioBlobUrl, isLoading, error, loadAudioForUrl } = useAuthenticatedAudio();

  React.useEffect(() => {
    // When the directUrl prop changes, tell the hook to load it.
    loadAudioForUrl(directUrl);
  }, [directUrl, loadAudioForUrl]);

  if (isLoading) {
    return <Typography>Loading audio...</Typography>;
  }

  if (error) {
    return <Typography color="danger">{error}</Typography>;
  }

  if (!audioBlobUrl) {
    // This can be shown if directUrl is initially null/empty or if loading failed and cleared the URL.
    return <Typography color="warning">No audio to play or audio failed to load.</Typography>;
  }

  return (
    <Box sx={{ mb: 2, display: "flex", justifyContent: "center" }}>
      <audio controls style={{ width: "100%", maxWidth: 480 }} src={audioBlobUrl} />
    </Box>
  );
};

export default AudioPlayer;
