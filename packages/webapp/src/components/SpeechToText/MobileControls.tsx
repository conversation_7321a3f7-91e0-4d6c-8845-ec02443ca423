// Contrôles mobiles pour l'enregistrement audio
import React from "react";

import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import { useTheme } from "@mui/joy/styles";
import Typography from "@mui/joy/Typography";
import useMediaQuery from "@mui/material/useMediaQuery";

// On peut supprimer ce composant ou le rendre visible uniquement sur mobile
const MobileControls: React.FC = () => {
  const theme = useTheme();
  // Utilise la breakpoint mobile de MUI ("sm" ou 600px)
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  if (!isMobile) return null;
  // if needed buton for mobile
  return (
    <Card
      variant="outlined"
      sx={{ my: 2, p: 2, display: "flex", flexDirection: "column", alignItems: "center" }}
    >
      <Typography level="body-sm" sx={{ color: "#888", mb: 1 }}>
        (Contrôles mobiles ici)
      </Typography>
      <Button size="sm" variant="soft" color="primary" disabled>
        Action mobile
      </Button>
    </Card>
  );
};

export default MobileControls;
