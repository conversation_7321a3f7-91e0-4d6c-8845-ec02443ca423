import React from "react";

import Box from "@mui/joy/Box";
import CircularProgress from "@mui/joy/CircularProgress";
import Typography from "@mui/joy/Typography";

interface TranscribedTextDisplayProps {
  text: string;
  label?: string;
  loading?: boolean; // If true, show spinner and 'Transcription en cours...'
}

/**
 * Displays the transcribed text (read-only).
 * If loading is true and no text is available, shows a spinner and 'Transcription en cours...'.
 */
const TranscribedTextDisplay: React.FC<TranscribedTextDisplayProps> = ({
  text,
  label,
  loading,
}) => (
  <Box sx={{ mb: 2 }}>
    {label && (
      <Typography level="body-sm" sx={{ mb: 0.5 }}>
        {label}
      </Typography>
    )}
    <Typography
      variant="outlined"
      sx={{
        p: 1,
        borderRadius: 2,
        bgcolor: "background.level1",
        whiteSpace: "pre-wrap",
        display: "flex",
        alignItems: "center",
        minHeight: 40,
      }}
    >
      {loading && !text ? (
        <>
          <CircularProgress size="sm" sx={{ mr: 1 }} />
          Transcription en cours...
        </>
      ) : (
        text || "No transcription available."
      )}
    </Typography>
  </Box>
);

export default TranscribedTextDisplay;
