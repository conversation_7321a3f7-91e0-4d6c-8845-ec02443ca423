import React from "react";

import Divider from "@mui/joy/Divider";
// Use @mui/material for List components because @mui/joy does not provide ListItemText or ListItemButton.
import List from "@mui/joy/List";
import ListItemButton from "@mui/joy/ListItemButton";
import Typography from "@mui/joy/Typography";

import { AudioRecording } from "../../models/types";

interface TranscriptionHistoryListProps {
  recordings: AudioRecording[];
  selectedId: string | null;
  onSelect: (id: string) => void;
}

// Sort recordings by created_at descending before rendering
const TranscriptionHistoryList: React.FC<
  TranscriptionHistoryListProps & { isLoading?: boolean }
> = ({ recordings, selectedId, onSelect, isLoading }) => {
  const sortedRecordings = React.useMemo(() => {
    return [...recordings].sort((a, b) => {
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
      return dateB - dateA;
    });
  }, [recordings]);

  return (
    <List sx={{ width: "100%", maxWidth: 360, bgcolor: "background.body" }}>
      {/* Section title */}
      <Typography level="h4" sx={{ p: 2 }}>
        Historique
      </Typography>
      {/* Empty state */}
      {recordings.length === 0 && <Typography sx={{ p: 2 }}>Aucune transcription.</Typography>}
      {isLoading && <Typography sx={{ p: 2, textAlign: "center" }}>Chargement...</Typography>}
      {/* List of recordings */}
      {sortedRecordings.map((rec) => [
        <ListItemButton
          key={rec.id}
          selected={rec.id === selectedId}
          onClick={() => onSelect(rec.id)}
          sx={{ display: "block", flexDirection: "column", alignItems: "flex-start" }}
        >
          {/* Title */}
          <Typography level="title-md" sx={{ fontWeight: "bold" }}>
            {rec.title || "Sans titre"}
          </Typography>
          {/* Patient's full name (first and last), fallback to N/A if missing */}
          <Typography level="body-sm">
            Patient:{" "}
            {rec.patient
              ? `${rec.patient.prenom || ""} ${rec.patient.nom || ""}`.trim() || "N/A"
              : "N/A"}
          </Typography>
          {/* Consultation motif and date, fallback to N/A if missing */}
          <Typography level="body-sm">
            Consultation:{" "}
            {rec.consultation
              ? `${rec.consultation.motif_consultation || "N/A"}${rec.consultation.consultation_date ? " (" + new Date(rec.consultation.consultation_date).toLocaleDateString() + ")" : ""}`
              : "N/A"}
          </Typography>
        </ListItemButton>,
        <Divider key={rec.id + "-divider"} />,
      ])}
    </List>
  );
};

export default TranscriptionHistoryList;
