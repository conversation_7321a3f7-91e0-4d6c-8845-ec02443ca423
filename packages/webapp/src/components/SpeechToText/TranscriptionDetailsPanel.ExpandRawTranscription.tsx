import React from "react";

import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import Box from "@mui/joy/Box";
import IconButton from "@mui/joy/IconButton";
import Typography from "@mui/joy/Typography";

interface ExpandRawTranscriptionProps {
  expanded: boolean;
  onToggle: () => void;
  label: string;
}

/**
 * Displays a label with a clickable arrow icon to expand/collapse content.
 */
const ExpandRawTranscription: React.FC<ExpandRawTranscriptionProps> = ({
  expanded,
  onToggle,
  label,
}) => (
  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
    <IconButton
      size="sm"
      variant="plain"
      onClick={onToggle}
      sx={{ mr: 1, transition: "transform 0.2s", transform: expanded ? "rotate(90deg)" : "none" }}
      aria-label={expanded ? "Réduire" : "Afficher"}
    >
      <KeyboardArrowRight />
    </IconButton>
    <Typography level="body-md">{label}</Typography>
  </Box>
);

export default ExpandRawTranscription;
