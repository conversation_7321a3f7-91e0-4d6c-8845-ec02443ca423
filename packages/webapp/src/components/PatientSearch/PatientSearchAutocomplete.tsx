import React from "react";

import { PersonAdd } from "@mui/icons-material";
import PersonSearchIcon from "@mui/icons-material/PersonSearch";
import SearchIcon from "@mui/icons-material/Search";
import Autocomplete from "@mui/joy/Autocomplete";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import CircularProgress from "@mui/joy/CircularProgress";
import DialogTitle from "@mui/joy/DialogTitle";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalOverflow from "@mui/joy/ModalOverflow";
import Stack from "@mui/joy/Stack";
import { SxProps } from "@mui/joy/styles/types/theme";
import Typography from "@mui/joy/Typography";

import { CreatePatient } from "../Forms/CreatePatient";
import { useApi } from "../../contexts/ApiContext";
import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { useDebounce } from "../../hooks/useDebounce";
import { Patient } from "../../models/types";
import { router } from "../../router";
import { formatDate, ISODateToAge, sexeToGender } from "../../utils/utils";

interface PatientSearchAutocompleteProps {
  onSelectPatient?: (patient: Patient) => void;
  navigateOnSelect?: boolean;
  placeholder?: string;
  width?: string | number;
  sx?: SxProps;
  startDecorator?: React.ReactNode;
}

export default function PatientSearchAutocomplete({
  onSelectPatient,
  navigateOnSelect = false,
  placeholder = "Rechercher un patient",
  width = "350px",
  sx,
  startDecorator,
}: PatientSearchAutocompleteProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const { setCurrentPatient } = useCurrentPatient();
  const [query, setQuery] = React.useState<string>("");
  const [results, setResults] = React.useState<Patient[]>([]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [inputValue, setInputValue] = React.useState<string>("");
  const [openCreatePatientModal, setOpenCreatePatientModal] = React.useState<boolean>(false);

  const debouncedQuery = useDebounce(query, 500);

  React.useEffect(() => {
    if (debouncedQuery) {
      const fetchResults = async () => {
        try {
          setLoading(true);
          const response = await api?.searchPatients(debouncedQuery);
          setResults(response?.results || []);
          setLoading(false);
        } catch (error) {
          console.error("Error fetching search results:", error);
          snackbar.show("Erreur lors de la recherche de patients", "danger");
          setLoading(false);
        }
      };
      fetchResults();
    } else {
      setResults([]);
    }
  }, [debouncedQuery, api, snackbar]);

  const handleSelectPatient = (patient: Patient | null) => {
    if (!patient) return;

    setCurrentPatient(patient);
    if (onSelectPatient) {
      onSelectPatient(patient);
    }
    if (navigateOnSelect) {
      router.navigate("/patient/" + patient.id);
    }
  };

  const handleCreatePatient = () => {
    setOpenCreatePatientModal(true);
  };

  const handlePatientCreated = (newPatient: Patient) => {
    setOpenCreatePatientModal(false);
    handleSelectPatient(newPatient);
    snackbar.show(`Patient ${newPatient.nom} ${newPatient.prenom} créé avec succès`, "success");
  };

  function getAge(dob: string | undefined): string {
    if (!dob) return "";
    // only return the age in years
    return ISODateToAge(dob).split(" ")[0];
  }

  return (
    <Box sx={{ width, position: "relative" }}>
      <Autocomplete
        placeholder={placeholder}
        options={results}
        getOptionLabel={(option: Patient) => `${option.nom.toUpperCase()} ${option.prenom}`}
        getOptionKey={(option) => option.id}
        onChange={(event, newValue) => handleSelectPatient(newValue)}
        onInputChange={(event, value) => {
          setInputValue(value);
          setQuery(value);
        }}
        inputValue={inputValue}
        loading={loading}
        startDecorator={startDecorator}
        filterOptions={(options, state) => {
          // Always return all options from the API, since filtering is done on the backend
          return options;
        }}
        loadingText={
          <Stack
            gap={2}
            alignItems="center"
            justifyContent="center"
            sx={{
              width: "100%",
              height: "180px",
            }}
          >
            <CircularProgress size="sm" />
            <Typography level="body-sm">Recherche en cours...</Typography>
          </Stack>
        }
        sx={{
          ...sx,
        }}
        renderOption={(props, option) => (
          <li
            {...props}
            style={{
              padding: "8px",
            }}
          >
            <Stack
              direction="row"
              spacing={1}
              alignItems="center"
              justifyContent="space-between"
              sx={{
                "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.1)" },
                cursor: "pointer",
              }}
            >
              <Typography level="body-sm">
                {option.nom.toUpperCase()} {option.prenom}
              </Typography>
              <Typography level="body-xs" sx={{ opacity: 0.7 }}>
                {sexeToGender(option.sexe)} — {formatDate(option.dob)} ({getAge(option.dob)})
              </Typography>
            </Stack>
          </li>
        )}
        noOptionsText={
          query && !loading ? (
            <Stack
              gap={1}
              alignItems="center"
              sx={{ width: "100%", height: "180px" }}
              justifyContent="center"
            >
              <SearchIcon sx={{ fontSize: "36px" }} />
              <Typography level="title-md">Aucun patient trouvé</Typography>
              <Button
                size="sm"
                variant="soft"
                color="primary"
                startDecorator={<PersonAdd />}
                onClick={handleCreatePatient}
              >
                Ajouter un patient
              </Button>
            </Stack>
          ) : (
            <Stack
              gap={1}
              alignItems="center"
              sx={{ width: "100%", height: "180px" }}
              justifyContent="center"
            >
              <PersonSearchIcon sx={{ fontSize: "36px" }} />
              <Typography level="body-sm" textAlign="center">
                Tapez du texte pour commencer la recherche
              </Typography>
              <Button
                size="sm"
                variant="soft"
                color="primary"
                startDecorator={<PersonAdd />}
                onClick={handleCreatePatient}
              >
                Ajouter un patient
              </Button>
            </Stack>
          )
        }
      />
      <Modal open={openCreatePatientModal} onClose={() => setOpenCreatePatientModal(false)}>
        <ModalOverflow>
          <ModalDialog sx={{ width: "400px" }}>
            <ModalClose />
            <DialogTitle>Ajouter un nouveau patient</DialogTitle>
            <CreatePatient 
              onSuccess={handlePatientCreated}
              disableRedirect={true}
            />
          </ModalDialog>
        </ModalOverflow>
      </Modal>
    </Box>
  );
}
