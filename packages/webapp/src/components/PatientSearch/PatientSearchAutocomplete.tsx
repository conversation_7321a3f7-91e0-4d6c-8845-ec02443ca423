import React from "react";

import { CreditCard, PersonAdd } from "@mui/icons-material";
import PersonSearchIcon from "@mui/icons-material/PersonSearch";
import SearchIcon from "@mui/icons-material/Search";
import Autocomplete from "@mui/joy/Autocomplete";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import CircularProgress from "@mui/joy/CircularProgress";
import DialogTitle from "@mui/joy/DialogTitle";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalOverflow from "@mui/joy/ModalOverflow";
import Stack from "@mui/joy/Stack";
import { SxProps } from "@mui/joy/styles/types/theme";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { useDebounce } from "../../hooks/useDebounce";
import { Patient } from "../../models/types";
import { router } from "../../router";
import { CarteVitaleService } from "../../services/CarteVitaleService";
import { CarteVitaleInfo } from "../../types/CarteVitaleTypes";
import { processCarteVitaleData } from "../../utils/CarteVitaleParser";
import { formatDate, ISODateToAge, sexeToGender } from "../../utils/utils";
import { CarteVitaleModal } from "../CarteVitale/CarteVitaleModal";
import { CreatePatient } from "../Forms/CreatePatient";

interface PatientSearchAutocompleteProps {
  onSelectPatient?: (patient: Patient) => void;
  navigateOnSelect?: boolean;
  placeholder?: string;
  width?: string | number;
  sx?: SxProps;
  startDecorator?: React.ReactNode;
}

export default function PatientSearchAutocomplete({
  onSelectPatient,
  navigateOnSelect = false,
  placeholder = "Rechercher un patient",
  width = "350px",
  sx,
  startDecorator,
}: PatientSearchAutocompleteProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const { setCurrentPatient } = useCurrentPatient();
  const [query, setQuery] = React.useState<string>("");
  const [results, setResults] = React.useState<Patient[]>([]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [inputValue, setInputValue] = React.useState<string>("");
  const [openCreatePatientModal, setOpenCreatePatientModal] = React.useState<boolean>(false);
  const [openCarteVitaleModal, setOpenCarteVitaleModal] = React.useState(false);
  const [carteVitaleInfo, setCarteVitaleInfo] = React.useState<CarteVitaleInfo | null>(null);
  const [carteVitaleLoading, setCarteVitaleLoading] = React.useState(false);
  const [carteVitaleAvailable, setCarteVitaleAvailable] = React.useState<boolean | null>(null);

  const debouncedQuery = useDebounce(query, 500);

  React.useEffect(() => {
    if (debouncedQuery) {
      const fetchResults = async () => {
        try {
          setLoading(true);
          const response = await api?.searchPatients(debouncedQuery);
          setResults(response?.results || []);
          setLoading(false);
        } catch (error) {
          console.error("Error fetching search results:", error);
          snackbar.show("Erreur lors de la recherche de patients", "danger");
          setLoading(false);
        }
      };
      fetchResults();
    } else {
      setResults([]);
    }
  }, [debouncedQuery, api, snackbar]);

  const handleSelectPatient = (patient: Patient | null) => {
    if (!patient) return;

    setCurrentPatient(patient);
    if (onSelectPatient) {
      onSelectPatient(patient);
    }
    if (navigateOnSelect) {
      router.navigate("/patient/" + patient.id);
    }
  };

  const handleCreatePatient = () => {
    setOpenCreatePatientModal(true);
  };

  const handlePatientCreated = (newPatient: Patient) => {
    setOpenCreatePatientModal(false);
    handleSelectPatient(newPatient);
    snackbar.show(`Patient ${newPatient.nom} ${newPatient.prenom} créé avec succès`, "success");
  };

  function getAge(dob: string | undefined): string {
    if (!dob) return "";
    // only return the age in years
    return ISODateToAge(dob).split(" ")[0];
  }

  // Check carte vitale service availability on component mount
  React.useEffect(() => {
    const checkCarteVitaleService = async () => {
      try {
        const status = await CarteVitaleService.checkServiceAvailability();
        setCarteVitaleAvailable(status.available);
      } catch (error) {
        setCarteVitaleAvailable(false);
      }
    };

    checkCarteVitaleService();
  }, []);

  const handleReadCarteVitale = async () => {
    setCarteVitaleLoading(true);
    try {
      // For development, use mock data. In production, use the real service
      const isDevelopment = process.env.NODE_ENV === "development";
      const response = isDevelopment
        ? await CarteVitaleService.mockReadCarteVitale()
        : await CarteVitaleService.readCarteVitale();

      if (response.success && response.data) {
        const processingResult = processCarteVitaleData(response.data);

        if (processingResult.success && processingResult.carteVitaleInfo) {
          setCarteVitaleInfo(processingResult.carteVitaleInfo);
          setOpenCarteVitaleModal(true);
        } else {
          snackbar.show(
            processingResult.error || "Erreur lors du traitement des données de la carte vitale",
            "danger"
          );
        }
      } else {
        snackbar.show(response.error || "Erreur lors de la lecture de la carte vitale", "danger");
      }
    } catch (error) {
      console.error("Error reading carte vitale:", error);
      snackbar.show("Erreur lors de la lecture de la carte vitale", "danger");
    } finally {
      setCarteVitaleLoading(false);
    }
  };

  const handleCarteVitalePatientCreated = (patient: Patient) => {
    setOpenCarteVitaleModal(false);
    setCarteVitaleInfo(null);
    handleSelectPatient(patient);
    snackbar.show(`Patient créé depuis la carte vitale`, "success");
  };

  return (
    <Box sx={{ width, position: "relative" }}>
      <Autocomplete
        placeholder={placeholder}
        options={results}
        getOptionLabel={(option: Patient) => `${option.nom.toUpperCase()} ${option.prenom}`}
        getOptionKey={(option) => option.id}
        onChange={(event, newValue) => handleSelectPatient(newValue)}
        onInputChange={(event, value) => {
          setInputValue(value);
          setQuery(value);
        }}
        inputValue={inputValue}
        loading={loading}
        startDecorator={startDecorator}
        filterOptions={(options, state) => {
          // Always return all options from the API, since filtering is done on the backend
          return options;
        }}
        loadingText={
          <Stack
            gap={2}
            alignItems="center"
            justifyContent="center"
            sx={{
              width: "100%",
              height: "180px",
            }}
          >
            <CircularProgress size="sm" />
            <Typography level="body-sm">Recherche en cours...</Typography>
          </Stack>
        }
        sx={{
          ...sx,
        }}
        renderOption={(props, option) => (
          <li
            {...props}
            style={{
              padding: "8px",
            }}
          >
            <Stack
              direction="row"
              spacing={1}
              alignItems="center"
              justifyContent="space-between"
              sx={{
                "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.1)" },
                cursor: "pointer",
              }}
            >
              <Typography level="body-sm">
                {option.nom.toUpperCase()} {option.prenom}
              </Typography>
              <Typography level="body-xs" sx={{ opacity: 0.7 }}>
                {sexeToGender(option.sexe)} — {formatDate(option.dob)} ({getAge(option.dob)})
              </Typography>
            </Stack>
          </li>
        )}
        noOptionsText={
          query && !loading ? (
            <Stack
              gap={1}
              alignItems="center"
              sx={{ width: "100%", height: "180px" }}
              justifyContent="center"
            >
              <SearchIcon sx={{ fontSize: "36px" }} />
              <Typography level="title-md">Aucun patient trouvé</Typography>
              <Stack direction="row" spacing={1}>
                <Tooltip
                  title={
                    carteVitaleAvailable === null
                      ? "Vérification du service en cours..."
                      : carteVitaleAvailable
                        ? "Service carte vitale disponible"
                        : "Service carte vitale non disponible"
                  }
                >
                  <Button
                    size="sm"
                    variant="outlined"
                    color="primary"
                    startDecorator={<CreditCard />}
                    onClick={handleReadCarteVitale}
                    disabled={carteVitaleAvailable === false || carteVitaleLoading}
                    loading={carteVitaleLoading}
                  >
                    Lire carte vitale
                  </Button>
                </Tooltip>
                <Button
                  size="sm"
                  variant="soft"
                  color="primary"
                  startDecorator={<PersonAdd />}
                  onClick={handleCreatePatient}
                >
                  Ajouter un patient
                </Button>
              </Stack>
            </Stack>
          ) : (
            <Stack
              gap={1}
              alignItems="center"
              sx={{ width: "100%", height: "180px" }}
              justifyContent="center"
            >
              <PersonSearchIcon sx={{ fontSize: "36px" }} />
              <Typography level="body-sm" textAlign="center">
                Tapez du texte pour commencer la recherche
              </Typography>
              <Stack direction="row" spacing={1}>
                <Tooltip
                  title={
                    carteVitaleAvailable === null
                      ? "Vérification du service en cours..."
                      : carteVitaleAvailable
                        ? "Service carte vitale disponible"
                        : "Service carte vitale non disponible"
                  }
                >
                  <Button
                    size="sm"
                    variant="outlined"
                    color="primary"
                    startDecorator={<CreditCard />}
                    onClick={handleReadCarteVitale}
                    disabled={carteVitaleAvailable === false || carteVitaleLoading}
                    loading={carteVitaleLoading}
                  >
                    Lire carte vitale
                  </Button>
                </Tooltip>
                <Button
                  size="sm"
                  variant="soft"
                  color="primary"
                  startDecorator={<PersonAdd />}
                  onClick={handleCreatePatient}
                >
                  Ajouter un patient
                </Button>
              </Stack>
            </Stack>
          )
        }
      />
      <Modal open={openCreatePatientModal} onClose={() => setOpenCreatePatientModal(false)}>
        <ModalOverflow>
          <ModalDialog sx={{ width: "400px" }}>
            <ModalClose />
            <DialogTitle>Ajouter un nouveau patient</DialogTitle>
            <CreatePatient onSuccess={handlePatientCreated} disableRedirect={true} />
          </ModalDialog>
        </ModalOverflow>
      </Modal>

      <CarteVitaleModal
        open={openCarteVitaleModal}
        onClose={() => {
          setOpenCarteVitaleModal(false);
          setCarteVitaleInfo(null);
        }}
        carteVitaleInfo={carteVitaleInfo}
        onPatientCreated={handleCarteVitalePatientCreated}
      />
    </Box>
  );
}
