import React, { useCallback } from "react";

import { List } from "@mui/icons-material";
import Delete from "@mui/icons-material/Delete";
import FileOpenSharp from "@mui/icons-material/FileOpenSharp";
import Help from "@mui/icons-material/Help";
import Person from "@mui/icons-material/Person";
import Print from "@mui/icons-material/Print";
import Alert from "@mui/joy/Alert";
import Box from "@mui/joy/Box";
import Checkbox from "@mui/joy/Checkbox";
import CircularProgress from "@mui/joy/CircularProgress";
import DialogContent from "@mui/joy/DialogContent";
import DialogTitle from "@mui/joy/DialogTitle";
import Divider from "@mui/joy/Divider";
import IconButton from "@mui/joy/IconButton";
import ModalClose from "@mui/joy/ModalClose";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Tab from "@mui/joy/Tab";
import TabList from "@mui/joy/TabList";
import Tabs from "@mui/joy/Tabs";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { usePrintManager } from "../../contexts/PrintManagerContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { useUser } from "../../contexts/UserContext";
import { PrintManagerFile } from "../../models/custom";
import { Consultation, Hospitalisation } from "../../models/types";
import PDFPreviewAndPrint from "../PDF/PDFPreview";
import CompteRenduList from "./CompteRendu/CompteRenduList";
import CompteRenduWindow from "./CompteRendu/CompteRenduWindow";

interface PrintManagerDrawerProps {
  isOpen?: boolean;
  closePrintManager?: () => void;
}

export default function PrintManagerDrawer({ isOpen, closePrintManager }: PrintManagerDrawerProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const { preferences, setPreferences } = useUser();
  const { files, removeFromPrintManager } = usePrintManager();

  const [fileBlobURL, setFileBlobURL] = React.useState<string | null>(null);
  const [selected, setSelected] = React.useState<PrintManagerFile[]>([]);
  const [groupedByPatient, setGroupedByPatient] = React.useState<
    Record<string, PrintManagerFile[]>
  >({});
  const [activeTab, setActiveTab] = React.useState(0);
  const [crCount, setCrCount] = React.useState(0);
  const [selectedCr, setSelectedCr] = React.useState<Consultation | Hospitalisation | null>(null);
  const [lastCrhRefresh, setLastCrhRefresh] = React.useState<number>(0);

  // Ref to hold the latest selectedCr to avoid it being a direct dependency of refreshCrList's useCallback
  const selectedCrRef = React.useRef(selectedCr);
  React.useEffect(() => {
    selectedCrRef.current = selectedCr;
  }, [selectedCr]);

  const refreshCrList = useCallback(async () => {
    setLastCrhRefresh(Date.now()); // Always update timestamp for other listeners

    // Check if selectedCr is a Consultation and can be re-fetched.
    // A more robust type guard (e.g., checking a unique 'type' field or using an 'isConsultation' utility)
    // would be preferable if 'motif_consultation' is not exclusive to Consultation.
    // However, this will primarily be triggered by CourrierTab's onSave, where selectedCr is known to be a Consultation.
    const currentSelectedCr = selectedCrRef.current; // Read from ref
    if (
      api &&
      currentSelectedCr &&
      "motif_consultation" in currentSelectedCr &&
      currentSelectedCr.id
    ) {
      try {
        const freshConsultation = await api.getConsultation(currentSelectedCr.id, 1); // Assuming '1' is for detailed fetch

        if (freshConsultation) {
          setSelectedCr(freshConsultation); // Update the selected CR with fresh data
        } else {
          // This case might mean the consultation was deleted or no longer accessible
          // snackbar.show("La consultation sélectionnée n'a pas pu être rafraîchie.", "warning");
          // Optionally, set selectedCr to null or handle as an error
          // setSelectedCr(null);
        }
      } catch (error) {
        console.error("Failed to refresh consultation data:", error);
        // snackbar.show("Erreur lors du rafraîchissement de la consultation.", "danger");
      }
    }
  }, [api, setSelectedCr]); // Removed snackbar & selectedCr, using selectedCrRef.current via closure

  React.useEffect(() => {
    if (!isOpen) {
      setSelected([]);
      setFileBlobURL(null);
    } else {
      if (activeTab === 1) {
        refreshCrList();
      }
    }
  }, [activeTab, isOpen, refreshCrList]);

  const handleSelectAll = () => {
    if (selected.length === files.length) {
      setSelected([]);
    } else {
      setSelected(files);
    }
  };

  const handleSelectAllForPatient = (patient_name: string) => () => {
    // if all files for the patient are selected, deselect all
    // otherwise, select all files for the patient
    if (isAllCheckedForPatient(patient_name)) {
      setSelected(selected.filter((file) => file.zkf_patient !== patient_name));
    } else {
      setSelected([
        ...selected,
        ...groupedByPatient[patient_name].filter((file) => !selected.includes(file)),
      ]);
    }
  };

  const isAllCheckedForPatient = (zkf_patient: string) => {
    return groupedByPatient[zkf_patient].every((file) => selected.includes(file));
  };

  const handleRectoVersoChange = async (checked: boolean) => {
    try {
      const res = await api.updatePreferencesUser({
        id: preferences?.id,
        impression_recto_verso: checked,
      });

      setPreferences(res);
    } catch (error) {
      console.error("Failed to update preferences:", error);
      snackbar.show("Echec de la modification des settings utilisateur", "danger");
    }
  };

  const handleRemoveSelected = (uuids?: string[]) => {
    // call the api to remove the file from the print manager, then refresh the queue
    // if all files are selected, call without uuid to remove all
    if (uuids) {
      removeFromPrintManager(uuids);
    } else {
      removeFromPrintManager();
    }
    // remove the blob from the fileBlobURL
    setFileBlobURL(null);
    setSelected([]);
  };

  const handleSelect = (file: PrintManagerFile) => {
    if (selected.includes(file)) {
      setSelected(selected.filter((f) => f !== file));
    } else {
      setSelected([...selected, file]);
    }
  };

  React.useEffect(() => {
    const grouped = files.reduce(
      (acc, file) => {
        if (!acc[file.zkf_patient]) {
          acc[file.zkf_patient] = [];
        }
        acc[file.zkf_patient].push(file);
        return acc;
      },
      {} as Record<string, PrintManagerFile[]>
    );
    setGroupedByPatient(grouped);
  }, [files]);

  // debounce on selected files change and call updatePreview
  React.useEffect(() => {
    if (selected.length > 0) {
      const updatePreview = async () => {
        setFileBlobURL(null);
        const blob = await api?.printManagerPrint(
          selected.map((file) => file.id),
          preferences?.impression_recto_verso
        );
        // open the print dialog with the blob
        if (blob) {
          // download the blob
          const url = window.URL.createObjectURL(blob);
          setFileBlobURL(url);
        }
      };
      updatePreview();
    } else {
      setFileBlobURL(null);
    }
  }, [selected, api, preferences?.impression_recto_verso]);

  const handlePrint = async () => {
    // clear the queue
    setSelected([]);
    setFileBlobURL(null);
    // clear all selected files from the print manager
    removeFromPrintManager(selected.map((file) => file.id));
  };

  React.useEffect(() => {
    return () => {
      // Clear selection when component unmounts (drawer closes)
      setSelected([]);
      setFileBlobURL(null);
    };
  }, []);

  // Function to handle selecting a compte rendu without refreshing the list
  const handleSelectCr = React.useCallback((doc: Consultation | Hospitalisation | null) => {
    setSelectedCr(doc);
    // We don't update lastCrhRefresh here, which prevents the list from reloading
  }, []);

  const handleTotalCountChange = React.useCallback((count: number) => {
    setCrCount(count);
  }, []);

  return (
    <Stack direction="row" gap={2} sx={{ width: "100vw", p: 2 }}>
      <Box sx={{ width: "510px" }}>
        <Sheet sx={{ height: "calc(100vh - 32px)", borderRadius: "lg" }}>
          <DialogTitle>
            <Typography level="title-lg" startDecorator={<Print />}>
              Gestionnaire d'impression
            </Typography>
            <ModalClose />
          </DialogTitle>
          <Tabs
            value={activeTab}
            onChange={(e, value) => setActiveTab(value as number)}
            sx={{
              borderTop: "none",
              mt: 2,
              mx: -2,
              background: "transparent",
              "& *": {
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderRadius: 0,
              },
              "& .MuiTab-root": {
                flex: 1,
                lineHeight: 1,
                borderRadius: 0,
              },
            }}
          >
            <TabList>
              <Tab>
                <List />
                &nbsp;Documents&nbsp;
                <Typography
                  component="span"
                  sx={{
                    bgcolor: "neutral.softBg",
                    px: 1,
                    py: 0.2,
                    borderRadius: "sm",
                    fontSize: "xs",
                  }}
                >
                  {files.length}
                </Typography>
              </Tab>
              <Tab>
                <FileOpenSharp />
                &nbsp;Comptes rendus&nbsp;
                <Typography
                  component="span"
                  sx={{
                    bgcolor: "neutral.softBg",
                    px: 1,
                    py: 0.2,
                    borderRadius: "sm",
                    fontSize: "xs",
                  }}
                >
                  {crCount}
                </Typography>
              </Tab>
            </TabList>
          </Tabs>
          <DialogContent sx={{ height: "calc(100vh - 144px)", overflowY: "scroll" }}>
            {activeTab === 0 ? (
              <Stack
                direction="column"
                gap={1}
                sx={{
                  height: "100%",
                }}
              >
                {groupedByPatient && files.length > 0 ? (
                  <Stack sx={{ height: "100%" }} flex={1} direction="column">
                    <Stack direction="row" justifyContent="space-between" sx={{ py: 1, mt: 1 }}>
                      <Typography
                        level="body-sm"
                        startDecorator={<Checkbox onChange={() => handleSelectAll()} />}
                        sx={{ pl: 1 }}
                      >
                        Tout selectionner
                      </Typography>
                      {selected.length > 0 && (
                        <Typography
                          level="body-sm"
                          fontWeight="bold"
                          sx={{
                            pl: 1,
                            opacity: 0.8,
                            "&:hover": {
                              cursor: "pointer",
                              opacity: 1,
                              textDecoration: "underline",
                            },
                          }}
                          color="danger"
                          onClick={() => handleRemoveSelected(selected.map((file) => file.id))}
                          startDecorator={<Delete sx={{ fontSize: "16px", mr: "-4px" }} />}
                        >
                          Supprimer
                        </Typography>
                      )}
                    </Stack>
                    <Divider />
                    <Box
                      sx={{
                        flex: 1,
                        overflowY: "auto",
                        my: 1,
                      }}
                    >
                      {Object.keys(groupedByPatient).map((zkf_patient) => {
                        const name = groupedByPatient[zkf_patient][0].patient;
                        return (
                          <Sheet
                            variant="outlined"
                            sx={{ p: 1, mb: 1, borderRadius: "sm" }}
                            key={name}
                          >
                            <Stack direction="row" gap={1}>
                              <Box>
                                <Checkbox
                                  checked={isAllCheckedForPatient(zkf_patient)}
                                  indeterminate={
                                    // if some files are selected for the patient, but not all
                                    selected.some((file) => file.zkf_patient === zkf_patient) &&
                                    !isAllCheckedForPatient(zkf_patient)
                                  }
                                  onChange={handleSelectAllForPatient(zkf_patient)}
                                />
                              </Box>
                              <Box flex={1}>
                                <Typography
                                  level="body-md"
                                  startDecorator={<Person />}
                                  fontWeight="bold"
                                >
                                  {name}
                                </Typography>
                                <Stack
                                  sx={{ mt: 1 }}
                                  direction="column"
                                  gap={1}
                                  alignItems="flex-start"
                                >
                                  {
                                    // loop through groupedByPatient[patientId] and render each file
                                    groupedByPatient[zkf_patient].map((file) => (
                                      <Stack direction="row" gap={0.5} key={file.id}>
                                        <Stack direction="row" gap={0.5}>
                                          <Checkbox
                                            size="sm"
                                            checked={selected.includes(file)}
                                            onChange={() => handleSelect(file)}
                                          />
                                          <FileOpenSharp fontSize="small" />{" "}
                                        </Stack>
                                        <Typography level="body-sm" lineHeight="100%">
                                          {file.titre_document}
                                        </Typography>
                                        <IconButton
                                          size="sm"
                                          color="neutral"
                                          variant="soft"
                                          onClick={() => handleRemoveSelected([file.id])}
                                        >
                                          <Delete fontSize="small" />
                                        </IconButton>
                                      </Stack>
                                    ))
                                  }
                                </Stack>
                              </Box>
                            </Stack>
                          </Sheet>
                        );
                      })}
                    </Box>
                    <Divider />
                    <Stack direction="row" gap={1} justifyContent="flex-end" sx={{ pt: 1 }}>
                      <Typography
                        level="body-sm"
                        startDecorator={
                          <Checkbox
                            checked={preferences?.impression_recto_verso}
                            onChange={() =>
                              handleRectoVersoChange(!preferences?.impression_recto_verso)
                            }
                          />
                        }
                        onClick={() => handleRectoVersoChange(!preferences?.impression_recto_verso)}
                      >
                        Recto-verso
                      </Typography>
                    </Stack>
                  </Stack>
                ) : (
                  <Stack
                    gap={1}
                    sx={{
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    flex={1}
                  >
                    <Typography level="title-md">Pas de fichier à imprimer</Typography>
                    <Alert startDecorator={<Help />} sx={{ fontWeight: 400, textAlign: "justify" }}>
                      Vous pouvez des fichiers à imprimer en cliquant sur le bouton "ajout au
                      gestionnaire d'impression" sur les fichiers que vous souhaitez imprimer.
                    </Alert>
                  </Stack>
                )}
              </Stack>
            ) : (
              <CompteRenduList
                onSelectCr={handleSelectCr}
                onTotalCountChange={handleTotalCountChange}
                lastRefresh={lastCrhRefresh}
              />
            )}
          </DialogContent>
        </Sheet>
      </Box>

      <Box sx={{ flex: 1, height: "calc(100vh - 32px)" }}>
        <Sheet
          sx={{
            borderRadius: "lg",
            height: "calc(100vh - 32px)",
            overflow: "hidden",
          }}
        >
          {activeTab === 0 ? (
            selected.length > 0 ? (
              fileBlobURL ? (
                <PDFPreviewAndPrint fileUrl={fileBlobURL} onPrint={handlePrint} />
              ) : (
                <Box
                  sx={{
                    flex: 1,
                    height: "calc(100vh - 32px)",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    opacity: 0.8,
                  }}
                >
                  <Stack gap={2} alignItems="center">
                    <CircularProgress />
                    <Typography level="body-md">Chargement de l'aperçu...</Typography>
                  </Stack>
                </Box>
              )
            ) : (
              <Stack
                sx={{ flex: 1, height: "100%" }}
                alignItems="center"
                justifyContent="center"
                gap={2}
              >
                <Print color="primary" sx={{ fontSize: "6rem" }} />
                <Typography level="h4">Aucun document sélectionné</Typography>
                <Typography level="body-md">
                  Vous pouvez selectionner un document dans la liste de gauche.
                </Typography>
              </Stack>
            )
          ) : (
            <CompteRenduWindow
              document={selectedCr}
              closePrintManager={closePrintManager}
              refreshList={refreshCrList}
              dataTimestamp={lastCrhRefresh}
            />
          )}
        </Sheet>
      </Box>
    </Stack>
  );
}
