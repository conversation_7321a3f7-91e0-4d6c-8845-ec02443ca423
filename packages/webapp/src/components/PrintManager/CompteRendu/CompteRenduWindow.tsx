import React, { CSSProperties } from "react";

import { HourglassEmpty, People, Print, Save } from "@mui/icons-material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import Close from "@mui/icons-material/Close";
import DocumentScanner from "@mui/icons-material/DocumentScannerOutlined";
import EditIcon from "@mui/icons-material/Edit";
import LocalHospitalIcon from "@mui/icons-material/LocalHospital";
import SendIcon from "@mui/icons-material/Send";
import { Tooltip } from "@mui/joy";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Checkbox from "@mui/joy/Checkbox";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../../contexts/ApiContext";
import { useSnackbar } from "../../../contexts/SnackbarContext";
import { PrintData } from "../../../models/custom";
import { isConsultation, isHospitalisation } from "../../../models/type_guards";
import {
  Consultation,
  Hospitalisation,
  Operation,
  PatchedConsultation,
  StatutCourrierConsultationEnum,
  StatutCrhEnum,
} from "../../../models/types";
import CourrierTab from "../../../pages/PatientPage/Consultation/CourrierTab";
import CRHModal from "../../../pages/PatientPage/Hospitalisation/Panels/HospitalisationSortiePanel/CRHModal";
import { router } from "../../../router";
import PDFPreviewAndPrint, { PDFPreviewAndPrintRef } from "../../PDF/PDFPreview";

interface CompteRenduWindowProps {
  document: Consultation | Hospitalisation | null;
  refreshList: () => void;
  closePrintManager?: () => void;
  dataTimestamp?: number;
}

export default function CompteRenduWindow({
  document,
  refreshList,
  closePrintManager,
  dataTimestamp,
}: CompteRenduWindowProps) {
  const api = useApi();
  const { show } = useSnackbar();

  // Memoize the document to prevent unnecessary re-renders
  const [isEditing, setIsEditing] = React.useState(false);
  const [courrierConsultation, setCourrierConsultation] = React.useState<string | null>(null);
  const [basPageConsultation, setBasPageConsultation] = React.useState<string | null>(null);
  const [crhContent, setCrhContent] = React.useState<string>("");
  const [fileBlobURL, setFileBlobURL] = React.useState<string>("");
  const [isPrinting, setIsPrinting] = React.useState<boolean>(false);
  const [pdfRef, setPdfRef] = React.useState<PDFPreviewAndPrintRef | null>(null);
  const [openValidateTooltip, setOpenValidateTooltip] = React.useState<boolean>(false);
  const [withOrdonnances, setWithOrdonnances] = React.useState<boolean>(false);
  const [withOperations, setWithOperations] = React.useState<boolean>(false);
  // State to track the ID of the currently loaded document
  const [currentDocumentId, setCurrentDocumentId] = React.useState<string | number | null>(null);

  React.useEffect(() => {
    // This effect handles re-initialization when the document ID actually changes
    if (document && document.id !== currentDocumentId) {
      setCurrentDocumentId(document.id);
      setFileBlobURL(""); // Reset preview URL
      setIsPrinting(false); // Reset printing state
      setPdfRef(null); // Reset PDF ref
      setWithOrdonnances(false); // Reset options
      setWithOperations(false); // Reset options

      // Initialize isEditing based on the status of the NEW document
      setIsEditing(
        (isHospitalisation(document) && document.statut_crh === "non_fait") ||
          (isConsultation(document) && document.statut_courrier_consultation === "non_fait")
      );

      // Reset content based on the new document type
      if (isConsultation(document)) {
        setCourrierConsultation(document.courrier_consultation ?? "");
        setBasPageConsultation(document.courrier_consultation_bas_page ?? "");
      }
      if (isHospitalisation(document)) {
        setCrhContent(document.crh ?? "");
      }
    }
  }, [document, currentDocumentId]);

  const fetchPdfBlob = React.useCallback(async () => {
    if (!document) return;

    const docs: PrintData[] = [
      {
        type_document: isConsultation(document) ? "courrier_consultation" : "crh",
        id: document.id,
        model_app_datas: document.model_app_datas,
      },
    ];

    if (withOrdonnances) {
      const ordonnances = await api.getOrdonnances(
        isConsultation(document) ? document.id : undefined,
        isHospitalisation(document) ? document.id : undefined
      );
      ordonnances.results.forEach((o) =>
        docs.push({
          type_document: "ordonnance",
          id: o.id,
          model_app_datas: o.model_app_datas,
        })
      );
    }

    if (withOperations && isHospitalisation(document)) {
      (document.operations as unknown as Operation[])?.forEach((o) =>
        docs.push({
          type_document: "cro",
          id: o.id,
          model_app_datas: {
            model: "operation",
            app: "operation",
          } as unknown as string,
        })
      );
    }

    const blob = await api.printManagerPrintAll(docs);
    setFileBlobURL(window.URL.createObjectURL(blob));
  }, [api, document, withOperations, withOrdonnances]);

  // useEffect specifically for fetching/updating the PDF blob when relevant dependencies change
  React.useEffect(() => {
    if (document) {
      // Ensure document exists before trying to fetch PDF
      fetchPdfBlob();
    }
  }, [document, fetchPdfBlob, withOrdonnances, withOperations]);

  React.useEffect(() => {
    if (isPrinting && !!pdfRef) {
      setTimeout(() => {
        pdfRef.print();
      }, 100);
    }
  }, [isPrinting, pdfRef]);

  const renderPdf = React.useMemo(() => {
    if (!!fileBlobURL) {
      const style: CSSProperties = isEditing
        ? { visibility: "hidden", height: 0 }
        : { height: "100%" };
      return (
        <div style={style}>
          <PDFPreviewAndPrint fileUrl={fileBlobURL} withPrint={false} ref={setPdfRef} />
        </div>
      );
    }
    return null;
  }, [fileBlobURL, isEditing]);

  const updateHospitalisationStatus = async (
    successMsg: string,
    status: StatutCrhEnum,
    refresh = true
  ) => {
    if (isHospitalisation(document)) {
      const newHospitalisation: Partial<Hospitalisation> = {
        id: document.id,
        statut_crh: status,
      };
      try {
        await api.updateHospitalisation(newHospitalisation);
        if (refresh) {
          refreshList();
          show(successMsg, "success");
        }
      } catch (error) {
        show("Erreur lors de de changement de statut compte rendu", "danger");
      }
    }
  };

  const updateHospitalisationContent = async (successMsg: string, refresh = true) => {
    if (isHospitalisation(document)) {
      const newHospitalisation: Partial<Hospitalisation> = {
        id: document.id,
        crh: crhContent,
      };
      try {
        await api.updateHospitalisation(newHospitalisation);
        if (refresh) {
          refreshList();
          show(successMsg, "success");
        }
      } catch (error) {
        show("Erreur lors de l'enregistrement du compte rendu", "danger");
      }
    }
  };

  const updateConsultationStatus = async (
    successMsg: string,
    status: StatutCourrierConsultationEnum,
    refresh = true
  ) => {
    if (isConsultation(document)) {
      try {
        const newConsult: PatchedConsultation = {
          id: document.id,
          statut_courrier_consultation: status,
        };
        await api.updateConsultation(newConsult);
        if (refresh) {
          refreshList();
          show(successMsg, "success");
        }
      } catch (error) {
        show("Erreur lors du changement de statut compte rendu", "danger");
      }
    }
  };

  const updateConsultationContent = async (successMsg: string, refresh = true) => {
    if (isConsultation(document)) {
      try {
        const newConsult: PatchedConsultation = {
          id: document.id,
          courrier_consultation: courrierConsultation ?? undefined,
          courrier_consultation_bas_page: basPageConsultation ?? undefined,
        };
        await api.updateConsultation(newConsult);
        if (refresh) {
          refreshList();
          show(successMsg, "success");
        }
      } catch (error) {
        console.error("Error updating consultation:", error);
        show("Erreur lors de l'enregistrement du compte rendu", "danger");
      }
    }
  };

  const updateStatusFunction = isConsultation(document!)
    ? updateConsultationStatus
    : updateHospitalisationStatus;

  const updateContentFunction = isConsultation(document!)
    ? updateConsultationContent
    : updateHospitalisationContent;

  const handleNoStatus = async () => {
    if (isEditing) {
      // Ensure content is saved before changing status
      await updateContentFunction("Compte rendu enregistré", false); // Save without full refresh if status change will trigger it
    }
    await updateStatusFunction("Compte rendu non statué", "non_statue");
    setIsEditing(false); // Exit edit mode after action
  };

  const handleSave = async (refresh = true) => {
    await updateContentFunction("Compte rendu enregistré", refresh);
    setIsEditing(false);
  };

  const handleWaitingValidate = async () => {
    if (isEditing) {
      // Ensure content is saved before changing status
      await updateContentFunction("Compte rendu enregistré", false); // Save without full refresh if status change will trigger it
    }
    await updateStatusFunction("Compte rendu en attente de validation", "attente_validation");
    setIsEditing(false); // Exit edit mode after action
  };

  const handleValidate = async () => {
    if (isEditing) {
      // Ensure content is saved before changing status
      await updateContentFunction("Compte rendu enregistré", false); // Save without full refresh if status change will trigger it
    }
    await updateStatusFunction("Compte rendu validé", "valide");
    setIsEditing(false); // Exit edit mode after action
  };

  const handleSend = async () => {
    if (isEditing) {
      // Ensure content is saved before changing status
      await updateContentFunction("Compte rendu enregistré", false); // Save without full refresh if status change will trigger it
    }
    await updateStatusFunction("Compte rendu envoyé", "envoye");
    setIsEditing(false); // Exit edit mode after action
  };

  const handleSwitchToEdit = () => {
    setIsEditing(true);
  };

  // The main guard for the document prop
  if (!document) {
    return (
      <Stack sx={{ flex: 1, height: "100%" }} alignItems="center" justifyContent="center" gap={2}>
        <DocumentScanner color="primary" sx={{ fontSize: "6rem" }} />
        <Typography level="h4">Aucun document sélectionné</Typography>
        <Typography level="body-md">
          Vous pouvez sélectionner un document dans la liste de gauche.
        </Typography>
      </Stack>
    );
  }

  // Define constants that depend on 'document' only AFTER the null check
  const status = isConsultation(document)
    ? document.statut_courrier_consultation
    : document.statut_crh;

  const canValidate = isConsultation(document)
    ? true // Assuming consultations can always be validated if actions are available
    : !!document.sortie_date && !!document.zkf_redacteur;

  const renderButtons = () => {
    return (
      <Stack flex={1} direction={"row"}>
        <Button variant="plain" color="neutral" startDecorator={<Close />} onClick={handleNoStatus}>
          Non statué
        </Button>
        <Stack flex={1} direction={"row"} justifyContent={"flex-end"} gap={1} alignItems="center">
          {status === "valide" && (
            <Checkbox
              checked={withOrdonnances}
              onChange={() => setWithOrdonnances(!withOrdonnances)}
              label={"Ordonnances"}
            />
          )}
          {status === "valide" &&
            isHospitalisation(document!) &&
            (document!.operations as unknown as Operation[])?.length && (
              <Checkbox
                checked={withOperations}
                onChange={() => setWithOperations(!withOperations)}
                label={"Opérations"}
              />
            )}
          {isEditing && (
            <Button
              variant="plain"
              color="primary"
              startDecorator={<Save />}
              onClick={() => handleSave(true)}
            >
              Enregistrer
            </Button>
          )}
          {!isEditing && (
            <Button
              variant="plain"
              color="primary"
              startDecorator={<EditIcon />}
              onClick={handleSwitchToEdit}
            >
              Modifier
            </Button>
          )}
          {(status === "non_fait" || status === "transcrit") && (
            <Button
              variant="solid"
              color="success"
              startDecorator={<HourglassEmpty />}
              onClick={handleWaitingValidate}
            >
              En attente validation
            </Button>
          )}
          {(status === "attente_validation" || status === "non_fait" || status === "transcrit") && (
            <Tooltip
              title={
                !canValidate
                  ? "La date de sortie et le rédacteur doivent être renseignées pour pouvoir valider le CRH"
                  : ""
              }
              placement="top"
              open={openValidateTooltip}
              arrow
              sx={{
                zIndex: 10000,
              }}
            >
              <Box
                onMouseEnter={() => setOpenValidateTooltip(true)}
                onMouseLeave={() => setOpenValidateTooltip(false)}
              >
                <Button
                  variant="solid"
                  color="success"
                  startDecorator={<CheckCircleIcon />}
                  onClick={handleValidate}
                  disabled={!canValidate}
                >
                  Valider
                </Button>
              </Box>
            </Tooltip>
          )}
          {status === "valide" && (
            <Button
              variant="solid"
              color="success"
              startDecorator={<Print />}
              onClick={async () => {
                if (
                  !isEditing ||
                  (isConsultation(document!) && // Added non-null assertion for clarity, though guarded
                    document!.courrier_consultation === courrierConsultation &&
                    document!.courrier_consultation_bas_page === basPageConsultation) ||
                  (isHospitalisation(document!) && document!.crh === crhContent) // Added non-null assertion
                ) {
                  pdfRef?.print();
                } else {
                  try {
                    await handleSave(false);
                    await fetchPdfBlob();
                    setIsPrinting(true);
                    setPdfRef(null);
                  } catch (error) {}
                }
              }}
            >
              Print
            </Button>
          )}
          {status === "valide" && (
            <Button
              variant="solid"
              color="success"
              startDecorator={<SendIcon />}
              onClick={handleSend}
            >
              Statut envoyé
            </Button>
          )}
        </Stack>
      </Stack>
    );
  };

  const renderConsultation = () => {
    if (!isConsultation(document)) return null;

    return (
      <Stack gap={2} height={"100%"}>
        <Typography
          level="h4"
          sx={{
            maxLines: 1,
            overflow: "hidden",
            textOverflow: "ellipsis",
            cursor: "pointer",
          }}
          startDecorator={<People color="primary" />}
          onClick={() => {
            router.navigate(`/consultation/${document.id}`);
            closePrintManager && closePrintManager();
          }}
        >
          {document.motif_consultation}
        </Typography>
        <Box sx={{ height: "calc(100% - 46px)" }}>
          {isEditing && (
            <CourrierTab
              consultation={document}
              isEditMode={true}
              withHeader={false}
              onCourrierChange={(courrier, basPage) => {
                setCourrierConsultation(courrier);
                setBasPageConsultation(basPage);
              }}
              dataTimestamp={dataTimestamp}
              onSave={refreshList}
            />
          )}
          {renderPdf}
        </Box>
      </Stack>
    );
  };

  const renderHospitalisation = () => {
    if (!isHospitalisation(document)) return null;
    return (
      <Stack gap={2} height={"100%"}>
        <Typography
          level="h4"
          sx={{
            maxLines: 1,
            overflow: "hidden",
            textOverflow: "ellipsis",
            cursor: "pointer",
          }}
          startDecorator={<LocalHospitalIcon color="warning" />}
          onClick={() => {
            router.navigate(`/hospitalisation/${document.id}`);
            closePrintManager && closePrintManager();
          }}
        >
          {document.motif_hospitalisation}
        </Typography>
        <Box sx={{ height: "calc(100% - 46px)" }}>
          {isEditing && (
            <CRHModal
              key={document.id}
              initialMode="edit"
              onCrhChange={setCrhContent}
              hospitalisation={document}
              hideButtons
            />
          )}
          {renderPdf}
        </Box>
      </Stack>
    );
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100%" }}>
      <Box sx={{ flex: 1, overflow: "auto" }}>
        {isConsultation(document) ? renderConsultation() : renderHospitalisation()}
      </Box>

      <Sheet
        variant="outlined"
        sx={{
          p: 0,
          px: 2,
          pt: 2,
          mx: -2,
          borderRadius: 0,
          borderLeft: 0,
          borderRight: 0,
          borderBottom: 0,
          display: "flex",
          gap: 0.5,
        }}
      >
        {renderButtons()}
      </Sheet>
    </Box>
  );
}
