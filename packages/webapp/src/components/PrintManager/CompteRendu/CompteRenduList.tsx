import dayjs from "dayjs";
import React, { useCallback } from "react";

import FilterAlt from "@mui/icons-material/FilterAlt";
import Person from "@mui/icons-material/Person";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import Sheet from "@mui/joy/Sheet";
import Skeleton from "@mui/joy/Skeleton";
import Stack from "@mui/joy/Stack";
import ToggleButtonGroup from "@mui/joy/ToggleButtonGroup";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../../contexts/ApiContext";
import { useSnackbar } from "../../../contexts/SnackbarContext";
import { isConsultation, isHospitalisation, isPatient, isUser } from "../../../models/type_guards";
import { Consultation, Hospitalisation } from "../../../models/types";

export default function CompteRenduList({
  onSelectCr,
  onTotalCountChange,
  lastRefresh,
}: {
  onSelectCr: (cr: Consultation | Hospitalisation | null) => void;
  onTotalCountChange: (count: number) => void;
  lastRefresh?: number;
}) {
  const api = useApi();
  const { show } = useSnackbar();

  const [selectedCr, setSelectedCr] = React.useState<string | undefined>(undefined);
  const [loading, setLoading] = React.useState(false);
  const [list, setList] = React.useState<(Consultation | Hospitalisation)[]>([]);
  const [updatingList, setUpdatingList] = React.useState(false);
  const [lastLocalRefresh, setLastLocalRefresh] = React.useState<number>(0);
  const [statusFilter, setStatusFilter] = React.useState<string>("");

  const handleSelectCr = useCallback(
    (cr: Consultation | Hospitalisation | null) => {
      setSelectedCr(cr?.id);
      onSelectCr(cr);
    },
    [onSelectCr]
  );

  const fetchCr = React.useCallback(async () => {
    try {
      const consultations = await api.listComptesRendusConsultation(
        10,
        0,
        statusFilter || undefined
      );
      const crh = await api.listComptesRendusHospitalisation(10, 0, statusFilter || undefined);
      const list = [...consultations.results, ...crh.results];
      // order list by date
      const totalCount = consultations.count + crh.count;
      onTotalCountChange(totalCount);
      // sort by date consutlation or date d'entrée d'hospit
      setList(
        list.sort((a, b) => {
          const dateA = isConsultation(a)
            ? dayjs(a.consultation_date)
            : isHospitalisation(a)
              ? dayjs(a.entree_date || a.sortie_date)
              : dayjs(0);

          const dateB = isConsultation(b)
            ? dayjs(b.consultation_date)
            : isHospitalisation(b)
              ? dayjs(b.entree_date || b.sortie_date)
              : dayjs(0);

          return dateB.diff(dateA); // décroissant
        })
      );
    } catch (error) {
      show("Erreur lors de la récupération des comptes rendus", "danger");
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [api, statusFilter, onTotalCountChange, show]);

  React.useEffect(() => {
    setLoading(true);
    fetchCr();
  }, [api, fetchCr]);

  // silently refresh list when lastRefresh is updated
  React.useEffect(() => {
    const refreshList = async () => {
      setUpdatingList(true);
      await fetchCr();
      setUpdatingList(false);
    };
    if (lastRefresh && lastRefresh > lastLocalRefresh) {
      setLastLocalRefresh(lastRefresh);
      refreshList();
    }
  }, [lastRefresh, fetchCr, lastLocalRefresh]);

  React.useEffect(() => {
    handleSelectCr(list.find((cr) => cr.id === selectedCr) || null);
  }, [handleSelectCr, list, selectedCr]);

  const handleFilterChange = (event: React.MouseEvent<HTMLElement>, newValue: string | null) => {
    setStatusFilter(newValue || "");
  };

  const filterButtonSx = { fontSize: "0.75rem", padding: "2px 8px" };

  return (
    <Stack gap={1} sx={{ mt: 2 }}>
      <Stack direction="row" spacing={1} alignItems="center">
        <Typography level="body-sm" sx={{ mr: 1 }}>
          <FilterAlt fontSize="small" sx={{ verticalAlign: "middle", mr: 0.5 }} />
          Filtrer par:
        </Typography>
        <ToggleButtonGroup value={statusFilter} onChange={handleFilterChange} size="sm">
          <Button
            value={""}
            variant={statusFilter === "" ? "solid" : "outlined"}
            sx={filterButtonSx}
          >
            Tout
          </Button>
          <Button
            value="non_fait"
            variant={statusFilter === "non_fait" ? "solid" : "outlined"}
            sx={filterButtonSx}
          >
            À faire
          </Button>
          <Button
            value="transcrit"
            variant={statusFilter === "transcrit" ? "solid" : "outlined"}
            sx={filterButtonSx}
          >
            Transcrit
          </Button>
          <Button
            value="attente_validation"
            variant={statusFilter === "attente_validation" ? "solid" : "outlined"}
            sx={filterButtonSx}
          >
            À valider
          </Button>
          <Button
            value="valide"
            variant={statusFilter === "valide" ? "solid" : "outlined"}
            sx={filterButtonSx}
          >
            À envoyer
          </Button>
        </ToggleButtonGroup>
      </Stack>

      {loading ? (
        <Stack gap={1}>
          {Array.from({ length: 10 }).map((_, index) => (
            <Box key={index} sx={{ width: "100%", height: 50, position: "relative" }}>
              <Skeleton animation="wave" sx={{ width: "100%", height: 50 }} />
            </Box>
          ))}
        </Stack>
      ) : (
        list.map((cr) => (
          <Sheet
            key={cr.id}
            sx={{
              p: 1,
              cursor: "pointer",
              backgroundColor: selectedCr === cr.id ? "primary.100" : "background.surface",
              borderLeftWidth: selectedCr === cr.id ? 2 : 1,
              opacity: updatingList ? 0.5 : 1,
              pointerEvents: updatingList ? "none" : "auto",
            }}
            variant="outlined"
            onClick={() => handleSelectCr(cr)}
          >
            <Stack gap={1}>
              <Stack direction="row" gap={1} justifyContent="space-between">
                {isConsultation(cr) ? (
                  <Chip size="sm" color="primary" variant="outlined">
                    Consultation
                  </Chip>
                ) : isHospitalisation(cr) ? (
                  <Chip size="sm" color="warning" variant="outlined">
                    Hospitalisation
                  </Chip>
                ) : (
                  <Chip size="sm" color="warning" variant="outlined">
                    Autre
                  </Chip>
                )}
                <Stack direction="row" gap={1}>
                  {isConsultation(cr) &&
                    cr.statut_courrier_consultation === "attente_validation" && (
                      <Chip size="sm" color="warning" variant="soft">
                        À valider
                      </Chip>
                    )}
                  {isConsultation(cr) && cr.statut_courrier_consultation === "valide" && (
                    <Chip size="sm" color="success" variant="soft">
                      À envoyer
                    </Chip>
                  )}
                  {isConsultation(cr) && cr.statut_courrier_consultation === "non_fait" && (
                    <Chip size="sm" color="danger" variant="soft">
                      À faire
                    </Chip>
                  )}
                  {isConsultation(cr) && cr.statut_courrier_consultation === "transcrit" && (
                    <Chip size="sm" color="primary" variant="soft">
                      Transcrit
                    </Chip>
                  )}
                  {isHospitalisation(cr) && cr.statut_crh === "attente_validation" && (
                    <Chip size="sm" color="warning" variant="soft">
                      À valider
                    </Chip>
                  )}
                  {isHospitalisation(cr) && cr.statut_crh === "valide" && (
                    <Chip size="sm" color="success" variant="soft">
                      À envoyer
                    </Chip>
                  )}
                  {isHospitalisation(cr) && cr.statut_crh === "non_fait" && (
                    <Chip size="sm" color="danger" variant="soft">
                      À faire
                    </Chip>
                  )}
                  {/* {isHospitalisation(cr) && cr.statut_crh === "transcrit" && (
                    <Chip size="sm" color="primary" variant="soft">
                      Transcrit
                    </Chip>
                  )} */}
                  <Chip size="sm" color="neutral" variant="outlined" startDecorator={<Person />}>
                    {isPatient(cr.zkf_patient)
                      ? cr.zkf_patient.nom.toUpperCase() +
                        " " +
                        cr.zkf_patient.prenom.charAt(0).toUpperCase() +
                        cr.zkf_patient.prenom.slice(1).toLowerCase()
                      : "???"}
                  </Chip>
                </Stack>
              </Stack>
              <Typography level="title-md">
                {isConsultation(cr)
                  ? `${cr.motif_consultation} - ${dayjs(cr.consultation_date).format("dddd D MMMM")}`
                  : isHospitalisation(cr)
                    ? `${cr.motif_hospitalisation} ${dayjs(cr.entree_date).format("D/MM/YYYY")} - ${dayjs(cr.sortie_date).format("D/MM/YYYY")}`
                    : "???"}
              </Typography>
              <Typography level="body-sm">
                {isUser(cr.zkf_redacteur)
                  ? `Rédigé par: ${cr.zkf_redacteur.last_name} ${cr.zkf_redacteur.first_name}`
                  : "Auteur inconnu"}
              </Typography>
            </Stack>
          </Sheet>
        ))
      )}
    </Stack>
  );
}
