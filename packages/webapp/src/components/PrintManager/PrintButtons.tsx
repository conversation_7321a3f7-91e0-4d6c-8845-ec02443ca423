import React from "react";

import CheckCircle from "@mui/icons-material/CheckCircle";
import Print from "@mui/icons-material/Print";
import Button, { ButtonProps } from "@mui/joy/Button";
import CircularProgress from "@mui/joy/CircularProgress";
import IconButton from "@mui/joy/IconButton";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import PrinterAddIcon from "../../assets/icons/PrinterAddIcon";
import { useApi } from "../../contexts/ApiContext";
import { usePrintManager } from "../../contexts/PrintManagerContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { PrintData } from "../../models/custom";
import ModalComponent from "../Modals/Modal";
import PDFPreviewAndPrint from "../PDF/PDFPreview";

interface PrintButtonsProps {
  files: PrintData[];
  onPrint?: () => void;
  onAddToPrintManager?: () => void;
  beforePrint?: () => Promise<PrintData[]>;
  variant?: ButtonProps["variant"];
  color?: ButtonProps["color"];
  withText?: boolean;
  disabled?: boolean;
  size?: ButtonProps["size"];
  onlyPrintNow?: boolean;
}

export default function PrintButtons({
  files,
  onPrint,
  onAddToPrintManager,
  beforePrint,
  variant = "outlined",
  color = "neutral",
  withText = false,
  disabled = false,
  size = "md",
  onlyPrintNow = false,
}: PrintButtonsProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const { addToPrintManager } = usePrintManager();

  const [printModalOpen, setPrintModalOpen] = React.useState(false);
  const [pdfUrl, setPdfUrl] = React.useState<string | null>(null);
  const [loadingAddToPrintManager, setLoadingAddToPrintManager] = React.useState(false);
  const [printManagerAdded, setPrintManagerAdded] = React.useState(false);
  const [isLoadingPdf, setIsLoadingPdf] = React.useState(false);
  const printRequestInProgress = React.useRef(false);
  const printTimeoutRef = React.useRef<number | null>(null);

  // Cleanup function for any pending timeouts
  React.useEffect(() => {
    return () => {
      if (printTimeoutRef.current !== null) {
        clearTimeout(printTimeoutRef.current);
      }
    };
  }, []);

  const openPrintModal = async () => {
    // Prevent duplicate calls while a request is in progress
    if (printRequestInProgress.current) return;

    try {
      printRequestInProgress.current = true;
      setPrintModalOpen(true);
      setIsLoadingPdf(true);

      // Process files only after modal is opened
      // Store the timeout ID for potential cleanup and use a longer timeout
      // to ensure the modal transition is complete before loading the PDF
      printTimeoutRef.current = window.setTimeout(async () => {
        try {
          const filesToPrint = beforePrint ? await beforePrint() : files;
          const pdfBlob = await api.printManagerPrintAll(filesToPrint);

          // Garder le loader affiché pendant un court moment supplémentaire
          // pour assurer une transition fluide
          const objectUrl = window.URL.createObjectURL(pdfBlob);
          setPdfUrl(objectUrl);

          // Laisser un petit délai avant de désactiver l'état de chargement
          // pour permettre au PDF de se rendre et éviter le clignotement
          setTimeout(() => {
            setIsLoadingPdf(false);
          }, 500);
        } catch (error) {
          console.error(error);
          snackbar.show("Erreur lors de la récupération du document.", "danger");
          setPrintModalOpen(false);
          setIsLoadingPdf(false); // En cas d'erreur, réinitialiser l'état de chargement
        } finally {
          // Ne pas désactiver isLoadingPdf ici car c'est géré par le setTimeout
          printRequestInProgress.current = false;
          printTimeoutRef.current = null;
        }
      }, 100); // Réduit à 100ms pour ne pas trop retarder le chargement tout en laissant la modale s'ouvrir
    } catch (error) {
      console.error(error);
      snackbar.show("Erreur lors de l'ouverture du modal.", "danger");
      setPrintModalOpen(false);
      setIsLoadingPdf(false);
      printRequestInProgress.current = false;
    }
  };

  const handlePrint = async () => {
    onPrint?.();
  };

  const handleAddToPrintManager = async () => {
    setLoadingAddToPrintManager(true);
    setPrintManagerAdded(false);
    try {
      const filesToAdd = beforePrint ? await beforePrint() : files;

      filesToAdd.forEach((file) => addToPrintManager(file.type_document, file.id));
      setPrintManagerAdded(true);
      onAddToPrintManager?.();
    } catch (error) {
      console.error(error);
      snackbar.show("Erreur lors de l'ajout au gestionnaire d'impression.", "danger");
    } finally {
      setLoadingAddToPrintManager(false);
    }
  };

  React.useEffect(() => {
    if (printManagerAdded) {
      setTimeout(() => {
        setPrintManagerAdded(false);
      }, 2000);
    }
  }, [printManagerAdded]);

  return (
    <>
      <Stack direction="row" gap={1} alignItems="center">
        {withText ? (
          <>
            <Button
              variant={variant}
              color={color}
              startDecorator={
                isLoadingPdf || printRequestInProgress.current ? (
                  <CircularProgress size="sm" sx={{ transform: "scale(0.8)" }} />
                ) : (
                  <Print />
                )
              }
              size={size}
              disabled={disabled || isLoadingPdf || printRequestInProgress.current}
              onClick={openPrintModal}
              data-cy="ordonnance-print-button"
            >
              {isLoadingPdf || printRequestInProgress.current ? "Chargement..." : "Imprimer"}
            </Button>
            {!onlyPrintNow && (
              <Button
                variant={variant}
                color={color}
                startDecorator={
                  loadingAddToPrintManager ? (
                    <CircularProgress size="sm" sx={{ transform: "scale(0.8)" }} />
                  ) : printManagerAdded ? (
                    <CheckCircle color="success" sx={{ opacity: 0.5 }} />
                  ) : (
                    <PrinterAddIcon />
                  )
                }
                disabled={disabled || loadingAddToPrintManager || printManagerAdded}
                size={size}
                onClick={handleAddToPrintManager}
                data-cy="ordonnance-add-to-print-manager-button"
                sx={{
                  minHeight: "38px",
                  width: "100%",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                  whiteSpace: "nowrap",
                }}
              >
                Ajouter au gest. d'impression
              </Button>
            )}
          </>
        ) : (
          <>
            <IconButton
              sx={{
                minWidth: size === "md" ? "38px !important" : undefined,
              }}
              variant={variant}
              color={color}
              onClick={openPrintModal}
              size={size}
              disabled={disabled || isLoadingPdf || printRequestInProgress.current}
            >
              {isLoadingPdf || printRequestInProgress.current ? (
                <CircularProgress size="sm" />
              ) : (
                <Print />
              )}
            </IconButton>
            {!onlyPrintNow && (
              <IconButton
                sx={{
                  minWidth: size === "md" ? "38px !important" : undefined,
                }}
                variant={variant}
                color={color}
                onClick={handleAddToPrintManager}
                size={size}
                disabled={disabled || loadingAddToPrintManager || printManagerAdded}
              >
                {loadingAddToPrintManager ? (
                  <CircularProgress size="sm" />
                ) : printManagerAdded ? (
                  <CheckCircle color="success" sx={{ opacity: 0.5 }} />
                ) : (
                  <PrinterAddIcon />
                )}
              </IconButton>
            )}
          </>
        )}
      </Stack>
      <ModalComponent
        open={printModalOpen}
        onClose={() => {
          // Fermer d'abord la modale
          setPrintModalOpen(false);
          setIsLoadingPdf(false); // En cas d'erreur, réinitialiser l'état de chargement
          // Nettoyage simple et fiable
          setTimeout(() => {
            if (pdfUrl) {
              URL.revokeObjectURL(pdfUrl);
              setPdfUrl(null);
            }
          }, 100);
        }}
        title="Imprimer"
        style={{
          minWidth: "60%",
          height: "100%",
          opacity: pdfUrl ? 1 : 0.95, // Légère transparence pendant le chargement
          transition: "opacity 0.3s ease", // Transition douce
        }}
        withCloseButton={false}
      >
        <div
          style={{
            flex: 1,
            height: "100%",
            position: "relative", // Position relative pour le positionnement absolu des enfants
          }}
        >
          {/* Conteneur pour le PDF avec transition d'opacité */}
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: pdfUrl ? 1 : 0,
              transition: "opacity 0.5s ease-in-out",
              zIndex: pdfUrl ? 2 : 1,
              overflow: "hidden",
            }}
          >
            {pdfUrl && (
              <PDFPreviewAndPrint
                fileUrl={pdfUrl}
                withPrint
                theme="light"
                height="100%"
                onPrint={handlePrint}
              />
            )}
          </div>

          {/* L'indicateur de chargement reste visible jusqu'à ce que le PDF soit prêt */}
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: isLoadingPdf ? 1 : 0,
              transition: "opacity 0.5s ease-in-out",
              zIndex: isLoadingPdf ? 2 : 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Stack gap={2} alignItems="center">
              <CircularProgress />
              <Typography level="body-lg">Génération du document en cours...</Typography>
            </Stack>
          </div>
        </div>
      </ModalComponent>
    </>
  );
}
