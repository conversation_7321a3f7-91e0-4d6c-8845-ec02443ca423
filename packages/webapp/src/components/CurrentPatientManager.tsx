import React from "react";

import CakeIcon from "@mui/icons-material/Cake";
import FemaleIcon from "@mui/icons-material/Female";
import MaleIcon from "@mui/icons-material/Male";
import Person from "@mui/icons-material/Person";
import SearchIcon from "@mui/icons-material/Search";
import Warning from "@mui/icons-material/Warning";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import DialogTitle from "@mui/joy/DialogTitle";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import { useCurrentPatient } from "../contexts/CurrentPatientContext";
import { router } from "../router";
import { ISODateToAge } from "../utils/utils";
import { CreatePatient } from "./Forms/CreatePatient";
import PatientSearchAutocomplete from "./PatientSearch/PatientSearchAutocomplete";

interface CurrentPatientManagerProps {
  searchOnly?: boolean;
  forcedCurrentPatient?: boolean;
}

export default function CurrentPatientManager({
  searchOnly = false,
  forcedCurrentPatient = false,
}: CurrentPatientManagerProps) {
  const [openCreatePatientModal, setOpenCreatePatientModal] = React.useState(false);
  const { currentPatient, setCurrentPatient } = useCurrentPatient();

  const getGenderIcon = (gender?: string) => {
    if (gender === "MALE") return <MaleIcon fontSize="small" />;
    if (gender === "FEMALE") return <FemaleIcon fontSize="small" />;
    return null;
  };

  const getGenderLabel = (gender?: string) => {
    if (gender === "MALE") return "Homme";
    if (gender === "FEMALE") return "Femme";
    return "";
  };

  return (
    <Stack direction="column" gap={2}>
      {currentPatient ? (
        <>
          {/* Header with title and change patient button */}
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography level="title-lg" startDecorator={<Person />}>
              {currentPatient.nom.toUpperCase() + " " + currentPatient.prenom}
              {currentPatient.nom_naissance &&
                currentPatient.nom_naissance !== currentPatient.nom && (
                  <span style={{ fontStyle: "italic", fontWeight: "normal" }}>
                    &nbsp;né{currentPatient.sexe === "FEMALE" ? "e" : ""}&nbsp;
                    {currentPatient.nom_naissance}
                  </span>
                )}
            </Typography>
            {!forcedCurrentPatient && (
              <Button
                variant="plain"
                color="neutral"
                startDecorator={<SearchIcon />}
                onClick={() => setCurrentPatient(null)}
                size="sm"
              >
                Changer de patient
              </Button>
            )}
          </Stack>

          {/* Patient information */}
          <Stack direction="row" gap={1} flexWrap="wrap">
            <Chip
              variant="soft"
              color="primary"
              startDecorator={getGenderIcon(currentPatient.sexe)}
            >
              <Typography level="body-sm">{getGenderLabel(currentPatient.sexe)}</Typography>
            </Chip>

            <Chip variant="soft" color="primary" startDecorator={<CakeIcon />}>
              <Typography level="body-sm">
                {new Date(currentPatient.dob).toLocaleDateString("fr")} (
                {ISODateToAge(currentPatient.dob)})
              </Typography>
            </Chip>

            {currentPatient.ins && (
              <Chip variant="soft" color="primary" startDecorator="INS">
                <Typography level="body-sm">{currentPatient.ins}</Typography>
              </Chip>
            )}
          </Stack>
        </>
      ) : (
        <>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography
              level="title-md"
              startDecorator={<Warning color="warning" />}
              color="warning"
            >
              Sélectionner un patient
            </Typography>
          </Stack>

          <PatientSearchAutocomplete
            onSelectPatient={(patient) => {
              setCurrentPatient(patient);
              searchOnly && router.navigate("/patient/" + patient.id);
            }}
            width="100%"
            startDecorator={<SearchIcon />}
          />
        </>
      )}

      {/* Create Patient Modal */}
      <Modal open={openCreatePatientModal} onClose={() => setOpenCreatePatientModal(false)}>
        <ModalDialog sx={{ width: "400px" }}>
          <ModalClose />
          <DialogTitle>Ajouter un nouveau patient</DialogTitle>
          <CreatePatient />
        </ModalDialog>
      </Modal>
    </Stack>
  );
}
