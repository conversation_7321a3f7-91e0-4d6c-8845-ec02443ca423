import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import React from "react";

import "./MultiRootEditor.css";

interface MultiRootEditorProps {
  indicationData: string;
  croData: string;
  onIndicationChange: (value: string) => void;
  onCroChange: (value: string) => void;
  indicationHeight?: string;
  croHeight?: string;
}

// Cette implémentation utilise deux éditeurs distincts mais avec une configuration
// et un style cohérents pour simuler un éditeur multi-root
const MultiRootEditor: React.FC<MultiRootEditorProps> = ({
  indicationData,
  croData,
  onIndicationChange,
  onCroChange,
  indicationHeight = "150px",
  croHeight = "450px",
}) => {
  // Configuration commune pour les deux éditeurs
  const editorConfig = {
    toolbar: {
      items: [
        "heading",
        "|",
        "bold",
        "italic",
        "link",
        "bulletedList",
        "numberedList",
        "|",
        "indent",
        "outdent",
        "|",
        "blockQuote",
        "insertTable",
        "undo",
        "redo",
      ],
    },
    placeholder: "Saisir le texte ici...",
  };

  // Pour s'assurer que les deux éditeurs partagent la même apparence
  const indicationStyle = { height: indicationHeight };
  const croStyle = { height: croHeight };

  return (
    <div className="multi-root-editor-container">
      <div className="editor-section">
        <div className="editor-label">Indication opératoire</div>
        <div className="editor-wrapper" style={indicationStyle}>
          <CKEditor
            editor={ClassicEditor}
            data={indicationData}
            onChange={(event, editor) => {
              const data = editor.getData();
              onIndicationChange(data);
            }}
            config={editorConfig}
          />
        </div>
      </div>

      <div className="editor-section">
        <div className="editor-label">Compte rendu opératoire</div>
        <div className="editor-wrapper" style={croStyle}>
          <CKEditor
            editor={ClassicEditor}
            data={croData}
            onChange={(event, editor) => {
              const data = editor.getData();
              onCroChange(data);
            }}
            config={editorConfig}
          />
        </div>
      </div>
    </div>
  );
};

export default MultiRootEditor;
