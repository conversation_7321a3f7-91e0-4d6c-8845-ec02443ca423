.multi-root-editor-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.editor-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.editor-label {
  font-weight: 600;
  font-size: 1rem;
  color: var(--joy-palette-text-primary, #333);
  margin-bottom: 0.25rem;
}

.editor-wrapper {
  overflow: hidden;
  border-radius: 8px;
}

/* Ensure both CKEditor instances have the same styling */
.multi-root-editor-container .ck.ck-editor {
  border-radius: 8px;
  overflow: hidden;
}

.multi-root-editor-container .ck.ck-editor__editable_inline {
  border-radius: 0 0 8px 8px !important;
  padding: 0.75rem;
  overflow-y: auto;
}

.multi-root-editor-container .ck.ck-toolbar {
  border-radius: 8px 8px 0 0 !important;
}

/* Add a visual distinction between the two editors */
.multi-root-editor-container .editor-section:first-child .ck.ck-toolbar {
  background: linear-gradient(to right, #f0f8ff, #e6f2ff);
}

.multi-root-editor-container .editor-section:last-child .ck.ck-toolbar {
  background: linear-gradient(to right, #fff5f5, #ffe6e6);
}

/* Add a slight delay for toolbar appearance to make the UI feel more responsive */
.multi-root-editor-container .ck.ck-toolbar {
  transition: all 0.2s ease;
}

/* Adjust styling for consistent heights */
.multi-root-editor-container .ck.ck-editor__main {
  min-height: 100px;
}

/* Custom scrollbar for editor areas */
.multi-root-editor-container .ck.ck-editor__editable_inline::-webkit-scrollbar {
  width: 8px;
}

.multi-root-editor-container .ck.ck-editor__editable_inline::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 8px;
}

.multi-root-editor-container .ck.ck-editor__editable_inline::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 8px;
}

.multi-root-editor-container .ck.ck-editor__editable_inline::-webkit-scrollbar-thumb:hover {
  background: #999;
}
