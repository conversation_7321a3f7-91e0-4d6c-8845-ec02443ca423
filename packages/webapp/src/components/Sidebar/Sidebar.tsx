import React, { ReactElement, useState } from "react";
import { useLocation } from "react-router-dom";

import BarChartIcon from "@mui/icons-material/BarChart";
import BugReportIcon from "@mui/icons-material/BugReport";
import CalendarMonth from "@mui/icons-material/CalendarMonth";
import DescriptionIcon from "@mui/icons-material/Description";
import HomeRoundedIcon from "@mui/icons-material/HomeRounded";
import LocalHospital from "@mui/icons-material/LocalHospital";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import Person from "@mui/icons-material/Person";
import Avatar from "@mui/joy/Avatar";
import Box from "@mui/joy/Box";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import MeiflowLogoMWhite from "../../assets/img/meiflow-m-white.png";
import { router } from "../../router";
import DebugMenuModal from "../DebugMenu/DebugMenuModal";
import { EnvGate } from "../EnvGate";
import Notifications from "../Notifications/Notifications";
import PrintManager from "../PrintManager/PrintManager";

interface MenuIconProps {
  icon: ReactElement;
  label: string;
  to: string;
  active?: boolean;
  secondaryLinks?: Array<{ label: string; to: string }>;
}

const MenuIcon = ({ icon, label, to, active, secondaryLinks }: MenuIconProps) => {
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });

  const handleClick = (event: React.MouseEvent) => {
    // Clic gauche normal - navigation vers le lien principal
    if (event.button === 0) {
      router.navigate(to);
    }
  };

  const handleContextMenu = (event: React.MouseEvent) => {
    // Clic droit - afficher le menu contextuel avec tous les liens
    if (secondaryLinks && secondaryLinks.length > 0) {
      event.preventDefault();
      setContextMenuPosition({ x: event.clientX, y: event.clientY });
      setContextMenuOpen(true);
    }
  };

  const handleMenuClose = () => {
    setContextMenuOpen(false);
  };

  const handleSecondaryClick = (secondaryTo: string) => {
    router.navigate(secondaryTo);
    handleMenuClose();
  };

  const handleMainClick = () => {
    router.navigate(to);
    handleMenuClose();
  };

  return (
    <>
      <Box
        role="menuitem"
        onClick={handleClick}
        onContextMenu={handleContextMenu}
        sx={{
          position: "relative",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          flexDirection: "column",
          p: 1,
          py: 2,
          cursor: "pointer",
          "&:hover": {
            backgroundColor: (theme) => theme.palette.primary[400],
          },
          backgroundColor: active ? (theme) => theme.palette.primary[400] : "transparent",
          overflow: "hidden",
          "&:before": {
            content: '""',
            position: "absolute",
            right: -6,
            top: "calc(50% - 6px)",
            bottom: 0,
            width: "12px",
            height: "12px",
            backgroundColor: active ? "white" : "transparent",
            borderRadius: "50%",
          },
        }}
      >
        <Stack direction="row" alignItems="center" gap={0.5}>
          {React.cloneElement(icon, { sx: { color: "white", fontSize: "20px" } })}
          {secondaryLinks && secondaryLinks.length > 0 && (
            <MoreVertIcon sx={{ color: "white", fontSize: "12px", opacity: 0.7 }} />
          )}
        </Stack>
        <Typography
          level="body-xs"
          sx={{
            color: "white",
            fontWeight: secondaryLinks && secondaryLinks.length > 0 ? "bold" : "normal",
          }}
        >
          {label}
        </Typography>
      </Box>

      {/* Menu contextuel positionné à la souris */}
      {secondaryLinks && secondaryLinks.length > 0 && contextMenuOpen && (
        <>
          {/* Overlay invisible pour fermer le menu quand on clique ailleurs */}
          <Box
            sx={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 999,
            }}
            onClick={handleMenuClose}
          />
          <Box
            sx={{
              position: "fixed",
              left: contextMenuPosition.x,
              top: contextMenuPosition.y,
              zIndex: 1000,
              backgroundColor: "background.popup",
              border: "1px solid",
              borderColor: "divider",
              borderRadius: "8px",
              boxShadow: "md",
              minWidth: "160px",
              py: 1,
            }}
          >
            <Box
              onClick={handleMainClick}
              sx={{
                px: 2,
                py: 1,
                cursor: "pointer",
                fontWeight: "bold",
                "&:hover": {
                  backgroundColor: "background.level1",
                },
              }}
            >
              <Typography level="body-sm">{label} (principal)</Typography>
            </Box>
            {secondaryLinks.map((link, index) => (
              <Box
                key={index}
                onClick={() => handleSecondaryClick(link.to)}
                sx={{
                  px: 2,
                  py: 1,
                  cursor: "pointer",
                  "&:hover": {
                    backgroundColor: "background.level1",
                  },
                }}
              >
                <Typography level="body-sm">{link.label}</Typography>
              </Box>
            ))}
          </Box>
        </>
      )}
    </>
  );
};

export default function Sidebar() {
  const { pathname } = useLocation();
  const [debugOpen, setDebugOpen] = useState(false);

  return (
    <Box
      sx={{
        position: "fixed",
        width: "96px",
        minWidth: "96px",
        maxWidth: "96px",
        flexShrink: 0,
        backgroundColor: (theme) => theme.palette.primary[500],
        color: "white",
        borderColor: (theme) => theme.palette.primary[600],
        height: "100vh",
        transition: "all 0.2s ease-in-out",
        zIndex: 100,
        py: 2,
      }}
      data-cy="sidebar"
    >
      <Stack sx={{ height: "100%" }} direction="column" justifyContent="space-between">
        <div>
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <img src={MeiflowLogoMWhite} alt="Meiflow" width="40" loading="lazy" />
          </Box>

          <Stack
            direction="column"
            sx={{
              mt: 2,
            }}
          >
            <MenuIcon icon={<HomeRoundedIcon />} label="Accueil" to="/" active={pathname === "/"} />
            <MenuIcon
              icon={<BarChartIcon />}
              label="Statistiques"
              to="/stats"
              active={pathname.startsWith("/stats")}
              secondaryLinks={[
                { label: "Stats quotidiennes", to: "/stats/daily" },
                { label: "Comparaison périodes", to: "/stats/intervals" },
                { label: "Recherche & Export", to: "/stats/query" },
              ]}
            />
            <MenuIcon
              icon={<LocalHospital />}
              label="Services"
              to="/service"
              active={pathname === "/service"}
            />
            <MenuIcon
              icon={<CalendarMonth />}
              label="Agenda"
              to="/calendar"
              active={pathname === "/calendar"}
            />
            <MenuIcon
              icon={<DescriptionIcon />}
              label="Documents"
              to="/documents"
              active={pathname === "/documents"}
            />
          </Stack>
        </div>
        <Stack direction="column" gap={2} alignItems={"center"} sx={{ width: "100%" }}>
          <Box>
            <Notifications />
          </Box>
          <Box>
            <PrintManager />
          </Box>

          <Stack direction="row" gap={1} sx={{ width: "100%" }}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                transition: "transform 0.2s ease, opacity 0.2s ease",
                "&:hover": {
                  transform: "translateY(-2px)",
                  opacity: 0.9,
                },
                width: "100%",
              }}
              onClick={() => router.navigate("/preferences")}
            >
              <Avatar
                size="sm"
                sx={{
                  bgcolor: "white",
                  "& svg": {
                    color: (theme) => theme.palette.primary[500],
                    fontSize: "20px",
                  },
                }}
              >
                <Person />
              </Avatar>
            </Box>
          </Stack>
          <EnvGate visibleEnvs={["dev"]}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                transition: "transform 0.2s ease, opacity 0.2s ease",
                "&:hover": { transform: "translateY(-2px)", opacity: 0.9 },
                width: "100%",
              }}
              onClick={() => setDebugOpen(true)}
            >
              <BugReportIcon sx={{ color: "red", fontSize: "20px" }} />
            </Box>
          </EnvGate>
        </Stack>
      </Stack>
      <DebugMenuModal open={debugOpen} onClose={() => setDebugOpen(false)} />
    </Box>
  );
}
