import dayjs from "dayjs";
import React from "react";

import FormatListBulletedIcon from "@mui/icons-material/FormatListBulleted";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import CircularProgress from "@mui/joy/CircularProgress";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Stack from "@mui/joy/Stack";
import { useTheme } from "@mui/joy/styles";
import Typography from "@mui/joy/Typography";
import useMediaQuery from "@mui/material/useMediaQuery";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { useDebounce } from "../../hooks/useDebounce";
import { User } from "../../models/types";
import ModalComponent from "../Modals/Modal";
import PDFPreviewAndPrint from "../PDF/PDFPreview";

type TypesOfEvents = "consultation" | "operation" | "user_event";
const namesOfEvents: Record<TypesOfEvents, string> = {
  consultation: "Consultation",
  operation: "Opération",
  user_event: "Événement utilisateur",
};

interface TodayEventsModalAndButtonProps {
  date: string;
  selectedDr: User["id"][];
}

export default function TodayEventsModalAndButton({
  date,
  selectedDr,
}: TodayEventsModalAndButtonProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [openModal, setOpenModal] = React.useState(false);
  const [pdfBlob, setPdfBlob] = React.useState<string | null>(null);
  const [types, setTypes] = React.useState<TypesOfEvents[]>(["consultation", "operation"]);
  const debouncedTypes = useDebounce(types, 500);
  const [periodicity, setPeriodicity] = React.useState<"am" | "pm" | "all">("all");
  const debouncedPeriodicity = useDebounce(periodicity, 500);
  const [loading, setLoading] = React.useState(true);

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  React.useEffect(() => {
    const fetchPdfBlob = async () => {
      if (!openModal) return; // don't fetch if modal is not open
      try {
        setLoading(true);
        const blob = await api.getEventsListPDF(
          dayjs(date).startOf("day").format("YYYY-MM-DD"),
          dayjs(date).endOf("day").format("YYYY-MM-DD"),
          selectedDr,
          debouncedTypes,
          debouncedPeriodicity
        );
        setPdfBlob(window.URL.createObjectURL(blob));
      } catch (error: any) {
        console.error(error);
        snackbar.show(
          "Un problème est survenu lors de l'impression de la liste des événements",
          "danger"
        );
      } finally {
        setLoading(false);
      }
    };
    fetchPdfBlob();
  }, [api, date, selectedDr, snackbar, debouncedTypes, debouncedPeriodicity, openModal]);

  return (
    <>
      <ModalComponent
        open={openModal}
        onClose={() => setOpenModal(false)}
        title="Résumé des événements"
        withCloseButton={false}
        style={{
          width: "80%",
          maxWidth: "1200px",
          height: "100%",
        }}
      >
        <Stack gap={2} sx={{ width: "100%", height: "100%" }}>
          <Stack direction="row" gap={2}>
            <FormControl>
              <FormLabel>Types d'événements</FormLabel>
              <Stack direction="row" gap={0.5}>
                {Object.keys(namesOfEvents).map((type) => (
                  <Chip
                    key={type}
                    variant="soft"
                    color={types.includes(type as TypesOfEvents) ? "primary" : "neutral"}
                    sx={{
                      color: types.includes(type as TypesOfEvents)
                        ? "primary.main"
                        : "neutral.main",
                      bgcolor: types.includes(type as TypesOfEvents)
                        ? "primary.softBg"
                        : "neutral.softBg",
                      border: (theme) =>
                        types.includes(type as TypesOfEvents)
                          ? "1px solid " + (theme.vars.palette.primary[300] || "primary.main")
                          : "1px solid " + (theme.vars.palette.neutral[300] || "neutral.main"),
                      transition: "all 0.2s ease-in-out",
                      "&:hover": {
                        bgcolor: types.includes(type as TypesOfEvents)
                          ? "primary.softHoverBg"
                          : "neutral.softHoverBg",
                        transform: "scale(1.02)",
                      },
                    }}
                    onClick={() =>
                      // at least one type must be selected
                      setTypes(
                        types.length === 1 && types.includes(type as TypesOfEvents)
                          ? types
                          : types.includes(type as TypesOfEvents)
                            ? types.filter((t) => t !== type)
                            : [...types, type as TypesOfEvents]
                      )
                    }
                    size="lg"
                  >
                    {namesOfEvents[type as TypesOfEvents]}
                  </Chip>
                ))}
              </Stack>
            </FormControl>
            <FormControl>
              <FormLabel>Périodicité</FormLabel>
              <Select
                sx={{ width: "200px" }}
                value={periodicity}
                onChange={(e, value) => setPeriodicity(value as "am" | "pm" | "all")}
                defaultValue="all"
              >
                <Option value="am">Matin</Option>
                <Option value="pm">Après-midi</Option>
                <Option value="all">Matin & Après-midi</Option>
              </Select>
            </FormControl>
          </Stack>
          {!loading && pdfBlob ? (
            <Box sx={{ width: "100%", height: "calc(100% - 70px)" }}>
              <PDFPreviewAndPrint fileUrl={pdfBlob} onPrint={() => setOpenModal(false)} withPrint />
            </Box>
          ) : loading ? (
            <Box
              sx={{
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Stack gap={2} alignItems="center" justifyContent="center">
                <CircularProgress />
                <Typography level="body-sm">Chargement de la liste des événements...</Typography>
              </Stack>
            </Box>
          ) : (
            <Box
              sx={{
                width: "100%",
                height: "calc(100% - 40px)",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Typography level="body-sm">Aucun événement trouvé</Typography>
            </Box>
          )}
        </Stack>
      </ModalComponent>
      {!isMobile && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2, // spacing between the buttons
            mt: 2, // optional: top margin for spacing
          }}
        >
          <Button
            variant="outlined"
            color="neutral"
            onClick={handleOpenModal}
            startDecorator={<FormatListBulletedIcon />}
          >
            Imprimer la liste des événements
          </Button>

          <Button
            variant="outlined"
            color="neutral"
            onClick={() => (window.location.href = "/stats/daily")}
            // sx={{
            //   p: 2,
            //   textTransform: "none",
            //   // fontSize: "1rem",
            //   // fontWeight: "bold",
            // }}
          >
            📊 Statistiques du jour
          </Button>
        </Box>
      )}
    </>
  );
}
