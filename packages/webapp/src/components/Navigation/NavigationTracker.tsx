import React, { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";

import { useApi } from "../../contexts/ApiContext";
import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { useSessionPreferences } from "../../contexts/SessionPreferencesContext";
import { Patient } from "../../models/types";
import { useNavigationHistory } from "./HistoryIcon";

/**
 * Composant qui trace automatiquement toutes les pages visitées
 * et les ajoute à l'historique de navigation
 */
const NavigationTracker: React.FC = () => {
  const location = useLocation();
  const { currentPatient, setCurrentPatient } = useCurrentPatient();
  const { addToHistory } = useNavigationHistory();
  const { setData } = useSessionPreferences();
  const api = useApi();

  // Utilisation d'une référence pour stocker la page précédente
  const previousLocationRef = useRef<{
    pathname: string;
    search: string;
    title: string;
    type: "patient" | "consultation" | "hospitalisation" | "ordonnance" | "operation";
    patientName?: string;
    patientId?: number;
  } | null>(null);

  // Déterminer le type de page en fonction de l'URL
  const getPageType = (
    pathname: string
  ): "patient" | "consultation" | "hospitalisation" | "ordonnance" | "operation" => {
    if (pathname.includes("/patient/")) return "patient";
    if (pathname.includes("/consultation/")) return "consultation";
    if (pathname.includes("/hospitalisation/")) return "hospitalisation";
    if (pathname.includes("/ordonnances/")) return "ordonnance";
    if (pathname.includes("/operation/")) return "operation";

    // Si on ne trouve pas de correspondance, on utilise le type le plus proche
    if (pathname.includes("/documents")) return "consultation";
    if (pathname.includes("/service")) return "hospitalisation";
    if (pathname.includes("/calendar")) return "consultation";

    // Par défaut
    return "patient";
  };

  // Déterminer le titre de la page en fonction de l'URL
  const getPageTitle = (pathname: string): string => {
    if (pathname === "/") return "Accueil";
    if (pathname.includes("/patient/")) return "Fiche patient";
    if (pathname.includes("/consultation/")) return "Détail consultation";
    if (pathname.includes("/hospitalisation/")) return "Détail hospitalisation";
    if (pathname.includes("/ordonnances/")) return "Liste des ordonnances";
    if (pathname.includes("/operation/")) return "Détail opération";
    if (pathname.includes("/documents")) return "Documents";
    if (pathname.includes("/service")) return "Services";
    if (pathname.includes("/calendar")) return "Agenda";
    if (pathname.includes("/preferences")) return "Préférences";

    // Si on ne trouve pas de correspondance, on utilise le pathname
    return pathname.split("/").filter(Boolean).join(" > ") || "Page";
  };

  // Clé de localStorage pour stocker les patients par URL
  const PAGE_PATIENT_STORAGE_KEY = "babylone_page_patient_map";

  // Fonction pour sauvegarder le patient actif pour une page spécifique
  const savePatientForPage = (path: string, patientId: number | null) => {
    try {
      // Récupérer la map actuelle des patients par page
      const storedMap = localStorage.getItem(PAGE_PATIENT_STORAGE_KEY);
      const pagePatientMap: Record<string, number | null> = storedMap ? JSON.parse(storedMap) : {};

      // Déterminer la page de base (sans paramètres de requête)
      const basePath = path.split("?")[0];

      // Vérifier que l'ID du patient est un nombre valide
      const isValidPatientId = patientId !== null && !isNaN(patientId);

      // Mettre à jour la map avec le nouveau patient pour cette page
      if (isValidPatientId && patientId) {
        console.log(`Sauvegarde du patient ${patientId} pour la page ${basePath}`);
        pagePatientMap[basePath] = patientId;
      } else {
        // Si pas de patient actif ou ID invalide, on garde quand même l'entrée avec null comme valeur
        // pour indiquer explicitement qu'il n'y a pas de patient actif sur cette page
        console.log(`Sauvegarde d'une absence de patient pour la page ${basePath}`);
        pagePatientMap[basePath] = null;
      }

      // Sauvegarder la map mise à jour
      localStorage.setItem(PAGE_PATIENT_STORAGE_KEY, JSON.stringify(pagePatientMap));
    } catch (error) {
      console.error("Erreur lors de la sauvegarde du patient pour la page:", error);
    }
  };

  // Effect qui s'exécute au chargement pour initialiser previousLocationRef
  // Utiliser useRef pour éviter une boucle infinie
  const initialized = useRef(false);

  useEffect(() => {
    // N'exécuter qu'une seule fois au montage du composant
    if (initialized.current) return;
    initialized.current = true;

    // Au chargement initial, on enregistre la page courante mais on ne l'ajoute pas à l'historique
    const pageType = getPageType(location.pathname);
    const pageTitle = getPageTitle(location.pathname);
    const patientName = currentPatient
      ? `${currentPatient.nom.toUpperCase()} ${currentPatient.prenom}`
      : undefined;

    // Qu'il y ait un patient actif ou non, on enregistre cette information pour la page actuelle
    if (currentPatient?.id) {
      // Si un patient est actuellement sélectionné, on l'enregistre dans les préférences de session
      setData({ activePatientId: Number(currentPatient.id) });

      // Sauvegarder le patient actif pour la page actuelle
      savePatientForPage(location.pathname, Number(currentPatient.id));
    } else {
      // Si pas de patient actif, on sauvegarde explicitement null pour cette page
      savePatientForPage(location.pathname, null);
    }

    // Vérifier que l'ID du patient est un nombre valide
    const patientIdRaw = currentPatient?.id ? Number(currentPatient.id) : undefined;
    const patientId = patientIdRaw !== undefined && !isNaN(patientIdRaw) ? patientIdRaw : undefined;

    // Initialiser la référence avec la page courante
    previousLocationRef.current = {
      pathname: location.pathname,
      search: location.search,
      title: pageTitle,
      type: pageType,
      patientName,
      patientId: patientId,
    };

    // Cette fonction de nettoyage s'exécutera lorsque le composant sera démonté
    // ce qui ne devrait jamais arriver dans notre cas car le NavigationTracker
    // est monté une fois pour toute la durée de vie de l'application
    return () => {
      // Si le composant est démonté, on peut enregistrer la dernière page dans l'historique
      if (previousLocationRef.current) {
        const basePath = previousLocationRef.current.pathname + previousLocationRef.current.search;
        // Vérifier que l'ID du patient est un nombre valide
        const patientIdRaw = currentPatient?.id ? Number(currentPatient.id) : undefined;
        const patientId =
          patientIdRaw !== undefined && !isNaN(patientIdRaw) ? patientIdRaw : undefined;

        // Créer une clé unique pour identifier cette entrée
        // Ajouter un timestamp pour garantir l'unicité absolue de chaque entrée
        const timestamp = Date.now();
        // Vérifier que l'ID du patient est valide avant de l'inclure dans la clé unique
        const isValidPatientId = patientId !== undefined && !isNaN(Number(patientId));
        const uniqueKey = isValidPatientId
          ? `${basePath}|patient:${patientId}|time:${timestamp}`
          : `${basePath}|time:${timestamp}`;

        console.log("[NavigationTracker-Cleanup] Ajout d'une entrée avec uniqueKey:", uniqueKey);

        // Toujours utiliser le patient courant pour l'entrée d'historique, s'il est disponible
        // Cela permet de réactiver le patient quand on retourne sur cette page
        let typedPatientData: typeof currentPatient | undefined = undefined;
        if (currentPatient) {
          typedPatientData = currentPatient;
        }

        // Log désactivé pour éviter les boucles
        // console.log("Cleanup - Ajout à l'historique:", previousLocationRef.current.title,
        //   "| Patient ID:", patientId,
        //   "| Clé unique:", uniqueKey);

        addToHistory({
          path: basePath,
          title: previousLocationRef.current.title,
          icon: previousLocationRef.current.type,
          patientName: previousLocationRef.current.patientName,
          patientId: patientId,
          // Ajout de l'objet patient complet avec typage correct
          patientData: typedPatientData,
          // Ajouter la clé unique
          uniqueKey: uniqueKey,
        });
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Aucune dépendance pour éviter la boucle infinie

  // Effect qui s'exécute à chaque changement de route
  React.useEffect(() => {
    // Log désactivé pour éviter les boucles
    // console.log("Navigation: changement de route", {
    //   current: location.pathname,
    //   previous: previousLocationRef.current?.pathname,
    //   patient: currentPatient?.id
    // });

    // Récupérer le patient associé à la page actuelle à partir du localStorage
    const storedMap = localStorage.getItem(PAGE_PATIENT_STORAGE_KEY);
    if (storedMap) {
      try {
        const pagePatientMap: Record<string, number | null> = JSON.parse(storedMap);
        const basePath = location.pathname.split("?")[0];

        // Si cette page a un patient associé en mémoire
        if (basePath in pagePatientMap && pagePatientMap[basePath]) {
          const pagePatientId = pagePatientMap[basePath];

          // Vérifier si le patient actuel est différent de celui associé à la page
          const currentPatientId = currentPatient?.id ? Number(currentPatient.id) : undefined;

          // Si le patient est différent et qu'on a un patient valide pour cette page
          if (
            pagePatientId &&
            (!currentPatientId || Number(pagePatientId) !== Number(currentPatientId))
          ) {
            console.log(
              `[NavigationTracker] Changement de patient pour la page ${basePath}: ${pagePatientId}`
            );

            // Charger et définir le patient associé à cette page
            api
              .getPatient(String(pagePatientId))
              .then((patient: Patient) => {
                if (patient) {
                  setCurrentPatient(patient);
                }
              })
              .catch((error: unknown) => {
                console.error(
                  "[NavigationTracker] Erreur lors du chargement du patient associé à la page:",
                  error
                );
              });
          }
        }
      } catch (error) {
        console.error(
          "[NavigationTracker] Erreur lors de la récupération du patient pour la page:",
          error
        );
      }
    }

    // Si nous avons une page précédente, l'ajouter à l'historique avant de passer à la nouvelle
    // Nous ajoutons une entrée si la page a changé (pathname ou search)
    if (
      previousLocationRef.current &&
      (previousLocationRef.current.pathname !== location.pathname ||
        previousLocationRef.current.search !== location.search)
    ) {
      // Sauvegarde du patient actif pour la page précédente
      if (previousLocationRef.current.patientId) {
        savePatientForPage(
          previousLocationRef.current.pathname,
          previousLocationRef.current.patientId
        );
      } else {
        savePatientForPage(previousLocationRef.current.pathname, null);
      }

      // Ajouter la page précédente à l'historique
      // S'assurer que les bonnes données de patient sont associées à cette entrée
      const entryPatientId = previousLocationRef.current.patientId;
      const basePath = previousLocationRef.current.pathname;

      // Créer une clé unique pour cette combinaison page+patient
      // Ajouter un timestamp pour garantir l'unicité absolue de chaque entrée
      const timestamp = Date.now();
      // Vérifier que l'ID du patient est valide avant de l'inclure dans la clé unique
      const isValidPatientId = entryPatientId !== undefined && !isNaN(Number(entryPatientId));
      const uniqueKey = isValidPatientId
        ? `${basePath}|patient:${entryPatientId}|time:${timestamp}`
        : `${basePath}|time:${timestamp}`;

      console.log("[NavigationTracker] Ajout d'une entrée avec uniqueKey:", uniqueKey);

      // Utiliser une variable temporaire strictement typée pour éviter les problèmes de TypeScript
      // Si nous avons un patient et que son ID correspond à celui de l'entrée, nous l'utilisons
      let typedPatientData: typeof currentPatient | undefined = undefined;

      // Si nous avons un patient courant, l'utiliser pour l'entrée d'historique
      // Cela permet de réactiver le patient quand on retourne sur cette page
      if (currentPatient) {
        typedPatientData = currentPatient;
      }

      // Log désactivé pour éviter les boucles
      // console.log("Ajout à l'historique:", previousLocationRef.current.title,
      //   "| Patient ID:", entryPatientId,
      //   "| Patient courant:", currentPatient?.id,
      //   "| Clé unique:", uniqueKey);
      
      // Calculer le nom du patient pour l'entrée précédente
      const entryPatientName = typedPatientData 
        ? `${typedPatientData.nom.toUpperCase()} ${typedPatientData.prenom}`
        : previousLocationRef.current.patientName;

      addToHistory({
        path: basePath, // Utiliser le chemin de base sans modification
        title: previousLocationRef.current.title,
        icon: previousLocationRef.current.type,
        patientName: entryPatientName,
        patientId: entryPatientId,
        // Ajout de l'objet patient complet (seulement si c'est le bon patient)
        patientData: typedPatientData,
        // Ajouter la clé unique pour identifier cette entrée
        uniqueKey: uniqueKey,
      });

      // Mettre à jour la référence avec la nouvelle page courante
      const pageType = getPageType(location.pathname);
      const pageTitle = getPageTitle(location.pathname);
      const patientName = currentPatient
        ? `${currentPatient.nom.toUpperCase()} ${currentPatient.prenom}`
        : undefined;
      // Vérifier que l'ID du patient est un nombre valide
      const patientIdRaw = currentPatient?.id ? Number(currentPatient.id) : undefined;
      const patientId =
        patientIdRaw !== undefined && !isNaN(patientIdRaw) ? patientIdRaw : undefined;

      previousLocationRef.current = {
        pathname: location.pathname,
        search: location.search,
        title: pageTitle,
        type: pageType,
        patientName,
        patientId,
      };

      // Enregistrer le patient actif pour la nouvelle page
      savePatientForPage(location.pathname, patientId || null);
    } else if (!previousLocationRef.current) {
      // Si c'est la première navigation (previousLocationRef.current est null),
      // on initialise simplement la référence sans ajouter à l'historique
      const pageType = getPageType(location.pathname);
      const pageTitle = getPageTitle(location.pathname);
      const patientName = currentPatient
        ? `${currentPatient.nom.toUpperCase()} ${currentPatient.prenom}`
        : undefined;
      // Vérifier que l'ID du patient est un nombre valide
      const patientIdRaw = currentPatient?.id ? Number(currentPatient.id) : undefined;
      const patientId =
        patientIdRaw !== undefined && !isNaN(patientIdRaw) ? patientIdRaw : undefined;

      previousLocationRef.current = {
        pathname: location.pathname,
        search: location.search,
        title: pageTitle,
        type: pageType,
        patientName,
        patientId,
      };

      // Enregistrer le patient actif pour la première page
      savePatientForPage(location.pathname, patientId || null);
    }
  }, [location.pathname, location.search, currentPatient, addToHistory, api, setCurrentPatient]);

  // Ce composant ne rend rien, il est juste utilisé pour son effet
  return null;
};

export default NavigationTracker;
