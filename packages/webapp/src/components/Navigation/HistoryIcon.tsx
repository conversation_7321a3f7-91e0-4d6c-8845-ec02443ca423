import dayjs from "dayjs";
import "dayjs/locale/fr";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import { Assignment, History, MeetingRoom, Person } from "@mui/icons-material";
import {
  Divider,
  IconButton,
  List,
  ListItemContent,
  ListItemDecorator,
  Menu,
  MenuItem,
  Stack,
  Tooltip,
  Typography,
} from "@mui/joy";

import { useApi } from "../../contexts/ApiContext";
import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { Patient } from "../../models/types";

// Type pour une entrée d'historique
export interface HistoryEntry {
  id: string;
  path: string;
  title: string;
  timestamp: number;
  patientName?: string;
  patientId?: number;
  // Nouvelle propriété pour stocker l'objet patient complet
  patientData?: Patient;
  // Nouvel identifiant unique pour la combinaison page+patient
  uniqueKey?: string;
  icon: "patient" | "consultation" | "hospitalisation" | "ordonnance" | "operation";
}

// Type pour le contexte d'historique
export interface HistoryContextType {
  addToHistory: (entry: Omit<HistoryEntry, "id" | "timestamp">) => void;
  getHistory: () => HistoryEntry[];
  clearHistory: () => void;
}

// Clé pour le stockage local
const HISTORY_STORAGE_KEY = "babylone_navigation_history";
// Nombre maximum d'entrées à conserver
const MAX_HISTORY_ENTRIES = 10;

export const useNavigationHistory = (): HistoryContextType => {
  const loadHistoryFromStorage = (): HistoryEntry[] => {
    try {
      const storedHistory = localStorage.getItem(HISTORY_STORAGE_KEY);
      return storedHistory ? JSON.parse(storedHistory) : [];
    } catch (error) {
      console.error("Erreur lors du chargement de l'historique:", error);
      return [];
    }
  };

  const saveHistoryToStorage = (history: HistoryEntry[]) => {
    try {
      localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      console.error("Erreur lors de la sauvegarde de l'historique:", error);
    }
  };

  const addToHistory = (entry: Omit<HistoryEntry, "id" | "timestamp">) => {
    const history = loadHistoryFromStorage();

    // Normaliser le chemin et extraire les informations du patient
    let normalizedPath = "";
    let finalPath = entry.path;
    let finalPatientId = entry.patientId;

    // Traiter le chemin s'il contient déjà un identifiant de patient
    if (entry.path && typeof entry.path === "string") {
      // Extraire le chemin de base et le patient ID si présent dans le format path|patient:X
      if (entry.path.includes("|patient:")) {
        const [pathPart, patientPart] = entry.path.split("|patient:");
        finalPath = pathPart;
        if (!finalPatientId && patientPart) {
          const parsedId = parseInt(patientPart, 10);
          // Vérifier que l'ID du patient est un nombre valide
          finalPatientId = !isNaN(parsedId) ? parsedId : undefined;
        }
      }

      // Normaliser le chemin (sans paramètres de requête)
      normalizedPath = finalPath.split("?")[0];
    }

    // Créer un identifiant unique basé sur la page et le patient (POINT 1)
    // Ce format unique permet d'identifier précisément une combinaison page+patient
    // Vérifier que l'ID du patient est valide avant de l'inclure dans la clé unique
    const isValidPatientId = finalPatientId !== undefined && !isNaN(Number(finalPatientId));
    const uniqueKey = isValidPatientId
      ? `${normalizedPath}|patient:${finalPatientId}`
      : normalizedPath;

    // Créer la nouvelle entrée avec timestamp et identifiant unique
    const newEntry: HistoryEntry = {
      ...entry,
      id: Date.now().toString(),
      timestamp: Date.now(),
      path: finalPath,
      patientId: finalPatientId,
      uniqueKey: uniqueKey, // Utiliser uniqueKey sans timestamp pour permettre le remplacement
    };

    // Ajouter des logs pour déboguer le problème
    console.log(
      "[addToHistory] Nouvelle entrée:",
      entry.title,
      "Path:",
      finalPath,
      "Patient:",
      finalPatientId,
      "uniqueKey:",
      uniqueKey
    );

    // Débogage: afficher tout l'historique actuel
    console.log(
      "[addToHistory] Historique actuel:",
      history.map((item) => ({
        title: item.title,
        path: item.path,
        patientId: item.patientId,
        uniqueKey: item.uniqueKey,
      }))
    );

    // Important: NE PAS chercher d'entrée existante avec la même combinaison page+patient
    // On veut conserver toutes les entrées, même pour la même page avec le même patient
    // Toujours créer une nouvelle entrée pour éviter de perdre l'historique
    const existingEntryIndex = -1;

    let updatedHistory: HistoryEntry[];

    if (existingEntryIndex >= 0) {
      // Mettre à jour l'entrée existante pour cette combinaison path+patient
      // console.log("Mise à jour d'une entrée existante:",
      //   newEntry.title,
      //   newEntry.patientName || "sans patient",
      //   "Path:", newEntry.path);

      updatedHistory = [
        newEntry,
        ...history.slice(0, existingEntryIndex),
        ...history.slice(existingEntryIndex + 1),
      ];
    } else {
      // Ajouter une nouvelle entrée
      // console.log("Ajout d'une nouvelle entrée:",
      //   newEntry.title,
      //   newEntry.patientName || "sans patient",
      //   "Path:", newEntry.path);

      updatedHistory = [newEntry, ...history];
    }

    // Limiter le nombre d'entrées
    if (updatedHistory.length > MAX_HISTORY_ENTRIES) {
      updatedHistory = updatedHistory.slice(0, MAX_HISTORY_ENTRIES);
    }

    saveHistoryToStorage(updatedHistory);
  };

  const getHistory = (): HistoryEntry[] => {
    return loadHistoryFromStorage();
  };

  const clearHistory = () => {
    saveHistoryToStorage([]);
  };

  return {
    addToHistory,
    getHistory,
    clearHistory,
  };
};

// Clé de localStorage pour stocker les patients par URL
// const PAGE_PATIENT_STORAGE_KEY = "babylone_page_patient_map";

// Fonction pour récupérer le patient associé à une page spécifique
// const getPatientForPage = (path: string): number | null => {
//   try {
//     // Récupérer la map actuelle des patients par page
//     const storedMap = localStorage.getItem(PAGE_PATIENT_STORAGE_KEY);
//     if (!storedMap) return null;

//     const pagePatientMap: Record<string, number | null> = JSON.parse(storedMap);

//     // Déterminer la page de base (sans paramètres de requête)
//     const basePath = path.split("?")[0];

//     // Retourner le patient associé à cette page
//     return pagePatientMap[basePath] || null;
//   } catch (error) {
//     console.error("Erreur lors de la récupération du patient pour la page:", error);
//     return null;
//   }
// };

// Composant pour l'icône d'historique
const HistoryIconComponent: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const navigate = useNavigate();
  const location = useLocation();
  const { getHistory } = useNavigationHistory();
  const { setCurrentPatient } = useCurrentPatient();
  const api = useApi();

  // Obtenir le patient actuel pour le filtrage
  const { currentPatient } = useCurrentPatient();

  useEffect(() => {
    // Mettre à jour l'historique chaque fois que le menu est ouvert
    if (anchorEl) {
      const currentPath = location.pathname + location.search;
      const currentPatientId = currentPatient?.id;

      // Créer une clé unique pour la page actuellement affichée
      const currentBasePath = currentPath.split("?")[0];
      const currentUniqueKey = currentPatientId
        ? `${currentBasePath}|patient:${currentPatientId}`
        : currentBasePath;

      // Log désactivé pour éviter les boucles
      // console.log("Page actuelle:", currentBasePath,
      //   "Patient actuel:", currentPatientId || "aucun",
      //   "Clé unique:", currentUniqueKey);

      // Récupérer tout l'historique
      const fullHistory = getHistory();

      // Débogage: afficher l'historique complet
      console.log(
        "[useEffect] Historique complet:",
        fullHistory.map((item) => ({
          title: item.title,
          path: item.path,
          patientId: item.patientId,
          uniqueKey: item.uniqueKey,
        }))
      );

      console.log(
        "[useEffect] Page actuelle:",
        currentBasePath,
        "Patient actuel:",
        currentPatientId,
        "uniqueKey:",
        currentUniqueKey
      );

      // Filtrer l'historique pour ne pas afficher la page actuelle (POINT 2)
      // mais conserver toutes les autres entrées, y compris les mêmes pages avec patients différents
      const filteredHistory = fullHistory
        .filter((entry) => {
          // Entrées sans clé unique (anciennes entrées)
          if (!entry.uniqueKey) {
            console.log("[useEffect] Entrée sans uniqueKey conservée:", entry.title);
            return true;
          }

          // Extraire le chemin de base et l'ID du patient de l'entrée
          let entryBasePath = entry.path;
          let entryPatientId = entry.patientId;

          // Débogage: Afficher les détails de chaque entrée évaluée
          console.log(
            "[useEffect] Évaluation entrée:",
            entry.title,
            "Path:",
            entryBasePath,
            "Patient:",
            entryPatientId,
            "uniqueKey:",
            entry.uniqueKey
          );

          // Comparer les parties significatives de la clé unique (sans le timestamp)
          // Format attendu: "path|patient:id|time:timestamp" ou "path|time:timestamp"

          // Extraire la partie pertinente pour la comparaison (path + patient)
          let entryCompareKey = entry.uniqueKey;
          
          // Enlever la partie timestamp si elle existe
          if (entryCompareKey.includes("|time:")) {
            entryCompareKey = entryCompareKey.split("|time:")[0];
          }

          // Comparer seulement la partie path+patient
          const shouldKeep = entryCompareKey !== currentUniqueKey;

          console.log(
            "[useEffect] Comparaison:",
            "entryCompareKey:",
            entryCompareKey,
            "currentUniqueKey:",
            currentUniqueKey,
            "Décision:",
            shouldKeep ? "Conserver" : "Filtrer"
          );

          return shouldKeep;
        })
        .sort((a, b) => b.timestamp - a.timestamp); // Trier par date (plus récent en premier)

      console.log(
        "[useEffect] Historique filtré:",
        filteredHistory.map((item) => ({
          title: item.title,
          path: item.path,
          patientId: item.patientId,
          uniqueKey: item.uniqueKey,
        }))
      );

      setHistory(filteredHistory);
    }
    // Utiliser currentPatient?.id au lieu de currentPatient pour éviter les re-renders excessifs
  }, [anchorEl, getHistory, location.pathname, location.search, currentPatient?.id]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    // Si le menu est déjà ouvert, le fermer
    if (anchorEl) {
      setAnchorEl(null);
    } else {
      setAnchorEl(event.currentTarget);
    }
    // Empêcher la propagation pour éviter que le clic atteigne le document
    event.stopPropagation();
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // Effet pour ajouter un gestionnaire de clic global
  useEffect(() => {
    const handleClickOutside = () => {
      if (anchorEl) {
        setAnchorEl(null);
      }
    };

    // Ajouter l'écouteur seulement si le menu est ouvert
    if (anchorEl) {
      document.addEventListener("click", handleClickOutside);
    }

    // Nettoyer l'écouteur
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [anchorEl]);

  // Fonction pour extraire l'ID du patient d'un chemin d'URL (non utilisée actuellement, mais peut être utile à l'avenir)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _extractPatientId = (path: string): number | null => {
    // Regexp pour capturer l'ID du patient dans les différents formats d'URL
    const patientUrlPattern = /\/patient\/(\d+)/;

    // On essaie d'abord les URLs qui référencent directement un patient
    let match = path.match(patientUrlPattern);
    if (match && match[1]) {
      return parseInt(match[1], 10);
    }

    // Pour les autres types de pages (consultation, hospitalisation, etc.)
    // On devra faire une requête API pour obtenir l'objet complet et en extraire le patient
    return null;
  };

  // Fonction pour charger le patient associé à une entrée d'historique (non utilisée car remplacée par l'utilisation de patientData)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  // const _loadAssociatedPatient = async (entry: HistoryEntry): Promise<void> => {
  //   if (!entry.patientName) return;

  //   if (entry.path.includes("/consultation/")) {
  //     const consultationId = entry.path.match(/\/consultation\/(\d+)/)?.[1];
  //     if (consultationId) {
  //       try {
  //         const consultation = await api.getConsultation(consultationId);
  //         if (consultation.zkf_patient) {
  //           // zkf_patient peut être soit un ID, soit un objet Patient
  //           const patientId =
  //             typeof consultation.zkf_patient === "object"
  //               ? String((consultation.zkf_patient as any).id)
  //               : String(consultation.zkf_patient);

  //           const patient = await api.getPatient(patientId);
  //           setCurrentPatient(patient);
  //         }
  //       } catch (error) {
  //         console.error(
  //           "Erreur lors de la récupération du patient associé à la consultation:",
  //           error
  //         );
  //       }
  //     }
  //   } else if (entry.path.includes("/hospitalisation/")) {
  //     const hospitalisationId = entry.path.match(/\/hospitalisation\/(\d+)/)?.[1];
  //     if (hospitalisationId) {
  //       try {
  //         const hospitalisation = await api.getHospitalisation(hospitalisationId);
  //         if (hospitalisation.zkf_patient) {
  //           // zkf_patient peut être soit un ID, soit un objet Patient
  //           const patientId =
  //             typeof hospitalisation.zkf_patient === "object"
  //               ? String((hospitalisation.zkf_patient as any).id)
  //               : String(hospitalisation.zkf_patient);

  //           const patient = await api.getPatient(patientId);
  //           setCurrentPatient(patient);
  //         }
  //       } catch (error) {
  //         console.error(
  //           "Erreur lors de la récupération du patient associé à l'hospitalisation:",
  //           error
  //         );
  //       }
  //     }
  //   } else if (entry.path.includes("/operation/")) {
  //     const operationId = entry.path.match(/\/operation\/(\d+)/)?.[1];
  //     if (operationId) {
  //       try {
  //         const operation = await api.getOperation(operationId);
  //         if (operation.zkf_patient) {
  //           // zkf_patient peut être soit un ID, soit un objet Patient
  //           const patientId =
  //             typeof operation.zkf_patient === "object"
  //               ? String((operation.zkf_patient as any).id)
  //               : String(operation.zkf_patient);

  //           const patient = await api.getPatient(patientId);
  //           setCurrentPatient(patient);
  //         }
  //       } catch (error) {
  //         console.error("Erreur lors de la récupération du patient associé à l'opération:", error);
  //       }
  //     }
  //   }
  // };

  // Clé pour stocker le patient actif dans localStorage (non utilisée actuellement)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _ACTIVE_PATIENT_KEY = "babylone_temp_active_patient";

  // Clé de localStorage pour stocker les patients par URL
  // const PAGE_PATIENT_STORAGE_KEY = "babylone_page_patient_map";


  // Indique qu'on est en train de naviguer via l'historique (non utilisé actuellement)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _FROM_HISTORY_KEY = "babylone_from_history";

  const handleNavigate = (path: string, entry: HistoryEntry, event: React.MouseEvent) => {
    // Empêcher la propagation pour éviter que le clic atteigne le document
    event.stopPropagation();

    // Fermer le menu d'historique
    handleClose();

    // Extraire le chemin réel et l'ID du patient
    let realPath = path;
    let targetPatientId = entry.patientId;

    // Si nous avons une clé unique, l'extraire pour obtenir le chemin et l'ID du patient
    if (entry.uniqueKey && entry.uniqueKey.includes("|patient:")) {
      // Format possible: "path|patient:id" ou "path|patient:id|time:timestamp"
      let pathPart, patientPart;

      if (entry.uniqueKey.includes("|time:")) {
        // Format: "path|patient:id|time:timestamp"
        const parts = entry.uniqueKey.split("|");
        pathPart = parts[0];

        // La partie patient est entre |patient: et |time:
        const patientPartFull = parts[1]; // "patient:id"
        patientPart = patientPartFull.split("patient:")[1];
      } else {
        // Format: "path|patient:id"
        [pathPart, patientPart] = entry.uniqueKey.split("|patient:");
      }

      console.log(
        "[handleNavigate] Extraction depuis uniqueKey:",
        "pathPart:",
        pathPart,
        "patientPart:",
        patientPart
      );

      realPath = pathPart;
      if (patientPart) {
        const parsedId = parseInt(patientPart, 10);
        // Vérifier que l'ID du patient est un nombre valide
        targetPatientId = !isNaN(parsedId) ? parsedId : undefined;
      }
    }
    // Si le chemin contient directement l'info du patient (ancien format)
    else if (path && typeof path === "string" && path.includes("|patient:")) {
      const [pathPart, patientPart] = path.split("|patient:");
      realPath = pathPart;
      if (!targetPatientId && patientPart) {
        const parsedId = parseInt(patientPart, 10);
        // Vérifier que l'ID du patient est un nombre valide
        targetPatientId = !isNaN(parsedId) ? parsedId : undefined;
      }
    }

    // S'assurer que nous avons toujours l'ID du patient
    if (!targetPatientId && entry.patientId) {
      targetPatientId = entry.patientId;
    }

    // console.log("Navigation vers:", realPath, "avec patient ID:", targetPatientId || "aucun");

    // Si l'entrée d'historique contient déjà l'objet patient complet, on l'utilise directement
    if (entry.patientData) {
      // console.log("Utilisation des données de patient complètes depuis l'entrée d'historique");
      // Définir le patient actif avant la navigation
      setCurrentPatient(entry.patientData);

      // Naviguer vers la page sans rechargement
      navigate(realPath);
      return;
    }

    // Si l'entrée contient les données complètes du patient via patientData, les utiliser directement
    if (entry.patientData) {
      // console.log("Utilisation des données de patient complètes depuis l'entrée d'historique");
      // Définir le patient actif avant la navigation
      setCurrentPatient(entry.patientData);
      // Naviguer vers la page sans rechargement
      navigate(realPath);
      return;
    }
    // Sinon, si on a un ID de patient, on essaie de le récupérer
    else if (targetPatientId) {
      // console.log("Récupération du patient avec ID:", targetPatientId);
      // Récupérer le patient avant de naviguer
      api
        .getPatient(String(targetPatientId))
        .then((patient) => {
          if (patient) {
            // console.log("Patient récupéré avec succès:", patient.nom, patient.prenom);
            // Définir le patient actif avant la navigation
            setCurrentPatient(patient);
            // Naviguer vers la page sans rechargement
            navigate(realPath);
          }
        })
        .catch((error) => {
          console.error("Erreur lors de la récupération du patient:", error);
          navigate(realPath);
        });
      return;
    }
    // Si pas de patient associé, s'assurer qu'on efface le patient actif
    else {
      // console.log("Navigation sans patient");
      setCurrentPatient(null);

      // Naviguer vers la page sans rechargement
      navigate(realPath);
    }
  };

  const getIconForType = (type: string) => {
    switch (type) {
      case "patient":
        return <Person />;
      case "consultation":
      case "ordonnance":
        return <Assignment />;
      case "hospitalisation":
      case "operation":
        return <MeetingRoom />;
      default:
        return <History />;
    }
  };

  const formatDate = (timestamp: number) => {
    const date = dayjs(timestamp);
    const now = dayjs();
    const isToday = date.format("YYYY-MM-DD") === now.format("YYYY-MM-DD");

    // Définir la locale française
    dayjs.locale("fr");

    if (isToday) {
      return `Aujourd'hui ${date.format("HH:mm")}`;
    } else {
      return date.format("DD MMM HH:mm");
    }
  };

  return (
    <>
      <Tooltip title="Historique de navigation" placement="bottom">
        <IconButton
          onClick={handleClick}
          variant="plain"
          color="neutral"
          sx={{
            color: "white",
            mr: 1,
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
          // On ajoute un attribut aria-expanded qui reflète l'état du menu (ouvert/fermé)
          aria-expanded={Boolean(anchorEl)}
          aria-haspopup="true"
          aria-controls={Boolean(anchorEl) ? "history-menu" : undefined}
        >
          <History sx={{ color: "white" }} />
        </IconButton>
      </Tooltip>

      <Menu
        id="history-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        keepMounted={false}
        placement="bottom-end"
        sx={{ maxWidth: "350px", minWidth: "250px" }}
        onClick={(e) => e.stopPropagation()} // Empêcher la propagation des clics sur le menu
      >
        <Typography level="title-sm" sx={{ px: 2, py: 1 }}>
          Historique de navigation
        </Typography>
        <Divider />

        {history.length > 0 ? (
          <List sx={{ py: 0 }}>
            {history.map((entry) => {
              // Déterminer si l'entrée a un patient associé (soit directement, soit dans le chemin)
              let entryPatientId = entry.patientId;
              if (
                !entryPatientId &&
                entry.path &&
                typeof entry.path === "string" &&
                entry.path.includes("|patient:")
              ) {
                const patientPart = entry.path.split("|patient:")[1];
                if (patientPart) {
                  entryPatientId = parseInt(patientPart, 10);
                }
              }

              // Extraire le vrai chemin de navigation
              let displayPath = entry.path;
              if (
                entry.path &&
                typeof entry.path === "string" &&
                entry.path.includes("|patient:")
              ) {
                displayPath = entry.path.split("|patient:")[0];
              }

              return (
                <MenuItem
                  key={entry.id}
                  onClick={(e) => handleNavigate(entry.path, entry, e)}
                  sx={{
                    py: 0.5,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "flex-start",
                  }}
                >
                  <Stack direction="row" spacing={0.5} sx={{ width: "100%", alignItems: "center" }}>
                    <ListItemDecorator>{getIconForType(entry.icon)}</ListItemDecorator>
                    <ListItemContent>
                      <Typography level="body-sm" noWrap>
                        {entry.title}
                        {/* Afficher les paramètres de requête si présents */}
                        {displayPath.includes("?") && (
                          <Typography component="span" level="body-xs" sx={{ opacity: 0.6, ml: 1 }}>
                            {displayPath.split("?")[1]}
                          </Typography>
                        )}
                      </Typography>
                      {entry.patientName ? (
                        <Typography
                          level="body-xs"
                          sx={{
                            opacity: 0.8,
                            fontWeight: "bold",
                            color: "primary.main",
                            display: "flex",
                            alignItems: "center",
                          }}
                          noWrap
                        >
                          <Person sx={{ fontSize: 14, mr: 0.5 }} />
                          {entry.patientName}
                        </Typography>
                      ) : (
                        <Typography
                          level="body-xs"
                          sx={{
                            opacity: 0.6,
                            color: "text.secondary",
                          }}
                          noWrap
                        >
                          Sans patient
                        </Typography>
                      )}
                    </ListItemContent>
                  </Stack>
                  <Typography
                    level="body-xs"
                    sx={{
                      alignSelf: "flex-end",
                      opacity: 0.6,
                      fontSize: "0.7rem",
                    }}
                  >
                    {formatDate(entry.timestamp)}
                  </Typography>
                </MenuItem>
              );
            })}
          </List>
        ) : (
          <MenuItem disabled>
            <Typography level="body-sm" sx={{ py: 1, opacity: 0.7 }}>
              Aucun historique de navigation
            </Typography>
          </MenuItem>
        )}
      </Menu>
    </>
  );
};

export default HistoryIconComponent;
