import React, { useEffect, useRef, useState } from "react";

import { Fullscreen, FullscreenExit, ZoomIn, ZoomOut } from "@mui/icons-material";
import Button from "@mui/joy/Button";
import Stack from "@mui/joy/Stack";

// Composant pour l'image qui gère correctement l'URL d'objet
const ImageWithObjectUrl: React.FC<{ file: File; zoomLevel: number }> = ({ file, zoomLevel }) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  // Création de l'URL au montage du composant
  useEffect(() => {
    const url = URL.createObjectURL(file);
    setImageUrl(url);

    // Nettoyer l'URL quand le composant est démonté
    return () => {
      URL.revokeObjectURL(url);
    };
  }, [file]);

  if (!imageUrl) return null;

  return (
    <img
      src={imageUrl}
      alt="Selected Preview"
      style={{
        maxWidth: "auto",
        transform: `scale(${zoomLevel})`,
        transformOrigin: "0 0",
      }}
    />
  );
};

interface ImagePreviewProps {
  file: File;
  height?: string;
}

export default function ImagePreview({ file, height }: ImagePreviewProps) {
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const previewRef = useRef<HTMLDivElement>(null);
  const [buttonsRef, setButtonsRef] = useState<HTMLDivElement | null>();
  // Keep to force rerender after resizing
  const [, setImgWidth] = useState<number>();

  useEffect(() => {
    if (!buttonsRef) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        if (entry.target === buttonsRef) {
          const { width } = entry.contentRect;
          setImgWidth(width);
        }
      }
    });

    if (buttonsRef) {
      resizeObserver.observe(buttonsRef);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [buttonsRef]);

  const toggleFullscreen = () => {
    if (previewRef.current) {
      if (!document.fullscreenElement) {
        previewRef.current.requestFullscreen();
        setIsFullscreen(true);
      } else {
        document.exitFullscreen();
        setIsFullscreen(false);
      }
    }
  };

  return (
    <Stack
      ref={previewRef}
      direction={"column"}
      maxWidth={"-webkit-fill-available"}
      maxHeight={height}
    >
      <Stack
        ref={(ref) => setButtonsRef(ref)}
        direction={isFullscreen ? "column" : "row"}
        justifyContent={isFullscreen ? "" : "end"}
        sx={
          isFullscreen
            ? {
                backgroundColor: "transparent",
                position: "fixed",
                top: 0,
                right: 0,
                zIndex: 1,
              }
            : {}
        }
      >
        <Button variant={"plain"} color={"neutral"} onClick={toggleFullscreen}>
          {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
        </Button>
        <Button
          variant={"plain"}
          color={"neutral"}
          onClick={() => setZoomLevel((prev) => prev - 0.1)}
        >
          <ZoomOut />
        </Button>
        <Button
          variant={"plain"}
          color={"neutral"}
          onClick={() => setZoomLevel((prev) => prev + 0.1)}
        >
          <ZoomIn />
        </Button>
      </Stack>
      <Stack
        overflow={"auto"}
        width={isFullscreen ? "auto" : buttonsRef?.offsetWidth || 0}
        sx={{ backgroundColor: "white" }}
      >
        <ImageWithObjectUrl file={file} zoomLevel={zoomLevel} />
      </Stack>
    </Stack>
  );
}
