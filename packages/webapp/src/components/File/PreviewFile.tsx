import React, { useEffect, useRef } from "react";

import Box from "@mui/joy/Box";
import Typography from "@mui/joy/Typography";

import PDFPreviewAndPrint from "../PDF/PDFPreview";
import ImagePreview from "./Preview/ImagePreview";
import TextPreview from "./Preview/TextPreview";
import WordPreview from "./Preview/WordPreview";

interface PreviewFileProps {
  file: File | null;
  height?: string;
}

// Composant séparé pour les PDFs pour éviter les problèmes de hooks
const PDFPreview: React.FC<{ file: File; height?: string }> = ({ file, height }) => {
  const urlRef = useRef<string | null>(null);

  useEffect(() => {
    // Créer l'URL seulement au montage du composant
    urlRef.current = URL.createObjectURL(file);

    // Nettoyer l'URL quand le composant est démonté
    return () => {
      if (urlRef.current) {
        URL.revokeObjectURL(urlRef.current);
        urlRef.current = null;
      }
    };
  }, [file]);

  if (!urlRef.current) {
    return <Typography>Chargement du PDF...</Typography>;
  }

  return (
    <PDFPreviewAndPrint
      fileUrl={urlRef.current}
      withPrint={false}
      theme={"light"}
      height={height}
    />
  );
};

const PreviewFile: React.FC<PreviewFileProps> = ({ file, height }) => {
  // Tous les hooks en haut, sans conditions

  if (!file) {
    return <Typography>Aucun fichier sélectionné.</Typography>;
  }

  const renderPreview = () => {
    // Pas de hooks ici
    if (file.type === "application/pdf") {
      return <PDFPreview file={file} height={height} />;
    }

    if (
      file.type === "application/msword" ||
      file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      return <WordPreview file={file} height={height} />;
    }

    if (file.type.startsWith("image/")) {
      return <ImagePreview file={file} height={height} />;
    }

    if (file.type === "text/plain") {
      return <TextPreview file={file} height={height} />;
    }

    console.error(`Type ${file.type} is not supported`);
    return <Typography>Type de fichier non supporté pour l'aperçu.</Typography>;
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: height || "auto",
        display: "flex",
        flexDirection: "column",
        alignItems: "stretch",
        border: "1px solid",
        borderColor: "divider",
        borderRadius: "md",
        overflowY: "auto",
        p: 1,
      }}
    >
      {renderPreview()}
    </Box>
  );
};

export default PreviewFile;
