import { MetaData } from "../components/Uploader/DocumentUploader";
import {
  ActeSearchResponse,
  ComplicationPostOp,
  CROType,
  CustomContact,
  Vacation as CustomVacation,
  DocumentLibreTemplateSend,
  Field,
  FormField,
  ISODate,
  OperationTemplateFieldsResponse,
  OperationTemplatesResponse,
  PeriOpData,
  PrintData,
  PrintManagerFile,
  Service,
  SuggestedRdv,
  UnixTimestamp,
} from "../models/custom";
import {
  Acte,
  Adresse,
  CalendarItem,
  CategorieOrdonnance,
  CompteRendu,
  ConsignesSortie,
  Consultation,
  ConsultationType,
  Contact,
  Contraste,
  CorrespondantPatient,
  CouvertureMaladie,
  Devis,
  DocumentLibre,
  DocumentLibreTemplate,
  Email,
  EnteteDocument,
  FullTag,
  Group,
  Hospitalisation,
  Intervenant,
  Irradiation,
  MatrielIrradiation,
  NfTypeEnum,
  Notification,
  Operation,
  OperationDevisCcam,
  Ordonnance,
  OrdonnanceTemplate,
  PaginatedAuditLogList,
  PaginatedConsultationList,
  PaginatedDevisList,
  PaginatedHospitalisationList,
  PaginatedPatientList,
  PaginatedUserEventList,
  PaginatedUserList,
  PatchedAdresse,
  PatchedConsultation,
  PatchedEmail,
  PatchedPatient,
  PatchedTelephone,
  Patient,
  PreferenceUserSerialyzer,
  Profile,
  Protocole,
  R_activite,
  R_menu,
  Specialite,
  Suivi,
  Telephone,
  User,
  UserEvent,
  UserEventCategory,
  UserSites,
  UserSitesLieux,
  Vacation,
  Ville,
} from "../models/types";
import { getAudioExtension } from "../utils/utils";
import { BaseClient } from "./BaseClient";

export interface PaginatedResponse<T> {
  count: number;
  next: string;
  previous: string;
  results: T[];
}

interface LoginResponse {
  token: string;
  user_id: number;
}

export class ApiClient extends BaseClient {
  constructor(baseUrl: string, accessToken?: string) {
    super(baseUrl, async () => {
      return accessToken ? `Bearer ${accessToken}` : null;
    });
  }

  async listPatients(page?: number): Promise<PaginatedResponse<Patient>> {
    return this.httpGet(`/patient${page && page > 0 ? `?page=${page}` : ""}`);
  }
  async getPatient(id: string, depth?: number): Promise<Patient> {
    return this.httpGet(`/patient/${id}${depth ? `?depth=${depth}` : ""}`);
  }
  async createPatient(patient: PatchedPatient): Promise<Patient> {
    return this.httpPost("/patient/", patient);
  }
  async updatePatient(patient: Partial<Patient>): Promise<Patient> {
    return this.httpPatch(`/patient/${patient.id}/`, patient);
  }

  async deletePatient(patientId: string): Promise<void> {
    return this.httpDelete(`/patient/${patientId}/`);
  }

  // Method to fetch ALL patients, handling pagination
  async listAllPatients(): Promise<Patient[]> {
    let allPatients: Patient[] = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      try {
        const response: PaginatedResponse<Patient> = await this.listPatients(page);
        allPatients = allPatients.concat(response.results);
        if (response.next) {
          page++;
        } else {
          hasMore = false;
        }
      } catch (error) {
        console.error(`Error fetching page ${page} of patients:`, error);
        // Decide how to handle errors: stop, retry, or continue?
        // For now, let's stop and re-throw to indicate failure.
        throw new Error(`Failed to fetch all patients. Error on page ${page}.`);
      }
    }
    return allPatients;
  }

  async searchPatients(query: string): Promise<PaginatedPatientList> {
    return this.httpGet(`/patient/search?query=${query}`);
  }
  async loginUser(username: string, password: string): Promise<LoginResponse> {
    const formData = new FormData();
    formData.append("username", username);
    formData.append("password", password);
    try {
      return this.httpPostFormData(`/token-auth/`, formData, undefined, true);
    } catch (error) {
      if (error instanceof Error) {
        this.reportError(error);
      }
      throw new Error("An error occurred");
    }
  }
  async logoutUser(): Promise<void> {
    return localStorage.removeItem("token");
  }

  //get all users of an environnement
  async getUsers(depth: number = 1): Promise<PaginatedUserList> {
    return await this.httpGet(`/user/${depth ? `?depth=${depth}` : ""}`);
  }

  async getUser(id: number): Promise<User> {
    return await this.httpGet(`/user/${id}`);
  }

  async getUserProfile(id: string): Promise<Profile> {
    return await this.httpGet(`/user/profile/${id}`);
  }
  async getUserSites(): Promise<UserSites[]> {
    // need to be authed, returns list of sites for current user
    return await this.httpGet("/user/site");
  }
  async getSitePlaces(site_id: string): Promise<UserSitesLieux[]> {
    return await this.httpGet(`/user/site/${site_id}/lieu`);
  }
  async createConsultation(data: Partial<Consultation>): Promise<Consultation> {
    // TODO type post requests
    return await this.httpPost("/consultation/", data);
  }
  async getConsultation(id: string, depth = 0): Promise<Consultation> {
    return await this.httpGet(`/consultation/${id}?depth=${depth}`);
  }
  async updateConsultation(consultation: PatchedConsultation): Promise<Consultation> {
    return this.httpPatch(`/consultation/${consultation.id}`, consultation);
  }
  async deleteConsultation(id: string): Promise<void> {
    return this.httpDelete(`/consultation/${id}`);
  }

  async listConsultation(patientId: string): Promise<PaginatedConsultationList> {
    return await this.httpGet(`/consultation?patient=${patientId}&depth=1`);
  }

  async listRecentConsultations(
    // Cible le nouvel endpoint
    limit: number = 20,
    depth: number = 1
  ): Promise<PaginatedConsultationList> {
    // Utilise le nouvel endpoint '/recent-consultations/'
    // Les paramètres `ordering` et `limit` sont gérés par DRF grâce à OrderingFilter et la pagination.
    // Note: si votre URL de base pour l'API consultation est déjà préfixée (ex: /api/consultation/),
    // alors l'URL ici pourrait être simplement 'recent-consultations/' ou nécessiter un ajustement
    // en fonction de la configuration de votre httpGet et de la base URL de l'API.
    // Pour l'instant, je suppose que httpGet construit l'URL correctement à partir de ce chemin relatif.
    return await this.httpGet(
      `/consultation/recent-consultations/?ordering=-consultation_date&limit=${limit}&depth=${depth}`
    );
  }
  async listConsultationsForCRH(
    patientId: string,
    date: ISODate,
    count: number,
    depth?: number
  ): Promise<PaginatedResponse<Consultation>> {
    return this.httpGet(
      `/consultation?patient=${patientId}&depth=${depth || 0}&date=${date}&filter_consultation=${count}`
    );
  }
  async getAntecedents(patient_id: string, context?: string, id?: string): Promise<any> {
    return this.httpGet(
      `/antecedent/${patient_id}/chirurgie_vasculaire${
        context ? `/${context}` : ""
      }${id ? `/${id}` : ""}`
    );
  }
  async getAntecedentsFields(
    patient_id: string,
    context?: string,
    id?: string
  ): Promise<{ [key: string]: FormField }> {
    return this.httpOptions(
      `/antecedent/${patient_id}/chirurgie_vasculaire${
        context ? `/${context}` : ""
      }${id ? `/${id}` : ""}`
    ).then((res) => {
      return res.actions["PUT"];
    });
  }
  //get hospitalisations of a patient
  async getHospitalisation(hospitalisation_id: string): Promise<Hospitalisation> {
    return this.httpGet(`/hospitalisation/${hospitalisation_id}`);
  }
  async updateHospitalisation(hospitalisation: Partial<Hospitalisation>): Promise<Hospitalisation> {
    return this.httpPatch(`/hospitalisation/${hospitalisation.id}`, hospitalisation);
  }
  async patchHospitalisation(hospitalisation: Partial<Hospitalisation>): Promise<Hospitalisation> {
    return this.httpPatch(`/hospitalisation/${hospitalisation.id}`, hospitalisation);
  }
  async getOperation(operation_id: string, depth = 0): Promise<Operation> {
    return this.httpGet(`/operation/${operation_id}?depth=${depth}`);
  }
  async createOperation(payload: Partial<Operation>): Promise<Operation> {
    return this.httpPost("/operation/", payload);
  }
  async updateOperation(operation: Partial<Operation>): Promise<Operation> {
    return this.httpPatch("/operation/" + operation.id, operation);
  }
  // async listOperations(patientId: string): Promise<Operation[]> {
  //   return this.httpGet(`/operation?patient=${patientId}`);
  // }
  async getOperationsOfHospitalisation(hospitalisation_id: string): Promise<Operation[]> {
    return this.httpGet(`/operation?hospitalisation=${hospitalisation_id}`);
  }
  async getOperationsOfConsultation(consultation_id: string): Promise<Operation[]> {
    return this.httpGet(`/operation?consultation=${consultation_id}`);
  }
  async listHospitalisations(patientId: string): Promise<PaginatedHospitalisationList> {
    return this.httpGet(`/hospitalisation?patient=${patientId}&depth=1`);
  }
  async getOperationTemplates(
    specialite?: string,
    template_name?: string,
    operation_id?: string
  ): Promise<OperationTemplatesResponse> {
    return this.httpGet(
      `/operation-templates` +
        (specialite
          ? `/${specialite}` +
            (template_name ? `/${template_name}` + (operation_id ? `/${operation_id}` : "") : "")
          : "")
    );
  }
  async getOperationsTemplateFields(
    specialite: string,
    template_name: string
  ): Promise<{ [key: string]: FormField }> {
    return await this.httpOptions(`/operation-templates/${specialite}/${template_name}`).then(
      (res: OperationTemplateFieldsResponse) => {
        return Promise.resolve(res.actions["POST"]);
      }
    );
  }
  //Get current hospitalisations of a service
  async getServiceHospitalisations(depth?: number, pdf?: string): Promise<Hospitalisation[]> {
    const url = `/hospitalisation/service${depth ? `?depth=${depth}` : ""}${
      pdf ? `&pdf=${pdf}` : ""
    }`;
    return this.httpGet(url);
  }

  //Get current hospitalisations of a service
  async getServiceHospitalisationsPdf(
    pdf?: string,
    filter?: string,
    search?: string
  ): Promise<Blob> {
    const url = `/hospitalisation/service${pdf ? `?pdf=${pdf}` : ""}${filter ? `&filter=${filter}` : ""}${search ? search : ""}`;
    return this.httpGet(url);
  }
  async getLinkedUsers(): Promise<User[]> {
    return this.httpGet("/user/linked-users");
  }

  // need to rewrite the back and front end:
  //events have to be mixed together and sorted by time.
  //One column for each praticien !
  async getUserEvents(): Promise<{
    Operation: Operation[];
    Consultation: Consultation[];
    RendezVous: any[];
  }> {
    return this.httpGet("/user/user-events");
  }
  async getOperationPeriOp(
    operation_id: string
  ): Promise<{ [key: string]: Operation[] | PeriOpData[] }> {
    return this.httpGet(`/operation/${operation_id}/peri-op`);
  }

  // get a list of values for a specific Operation field => for autocomplete !
  async getOperationFieldValues(field: string): Promise<string[]> {
    return this.httpGet(`/operation/operation-model-fields${field ? `?field=${field}` : ""}`);
  }

  async getCROTypes(): Promise<CROType[]> {
    return this.httpGet("/operation/cro-type");
  }
  async getCROTypesForOperation(id: string): Promise<CROType[]> {
    return this.httpGet(`/operation/cro-type?operation=${id}`);
  }
  async createCROTypes(cro: Partial<CROType>, duplicateUsers: number[] = []): Promise<CROType> {
    return this.httpPost("/operation/cro-type", {
      ...cro,
      duplicate_user: duplicateUsers,
    });
  }
  async updateCROTypes(cro: Partial<CROType>): Promise<CROType> {
    return this.httpPatch(`/operation/cro-type/${cro.id}`, cro);
  }
  async deleteCROTypes(id: string): Promise<void> {
    return this.httpDelete(`/operation/cro-type/${id}`);
  }
  async createOperationTemplate(
    specialite: string,
    template_name: string,
    data: any
  ): Promise<any> {
    return this.httpPost(`/operation-templates/${specialite}/${template_name}`, data);
  }
  async updateOperationTemplate(
    specialite: string,
    template_name: string,
    id: string,
    data: any
  ): Promise<any> {
    return this.httpPatch(`/operation-templates/${specialite}/${template_name}/${id}`, data);
  }
  async listUserIntervenants(depth: number = 1): Promise<Intervenant[]> {
    return this.httpGet(`/user/intervenant${depth ? `?depth=${depth}` : ""}`);
  }

  async createIntervenant(intervenant: Partial<Intervenant>): Promise<Intervenant> {
    return this.httpPost(`/user/intervenant/`, intervenant);
  }

  async updateIntervenant(intervenant: Partial<Intervenant>): Promise<Intervenant> {
    return this.httpPatch(`/user/intervenant/${intervenant}`, intervenant);
  }

  async getUserIntervenantsEvents(
    consultationId?: string,
    hospitalisationId?: string,
    operationId?: string
  ): Promise<{
    Anesthesiste: Intervenant[];
    Operateur: Intervenant[];
    Redacteur: User[];
    "Aide Operatoire": Intervenant[];
  }> {
    let query = "";
    if (consultationId) {
      query = "?consultation=" + consultationId;
    } else if (hospitalisationId) {
      query = "?hospitalisation=" + hospitalisationId;
    } else if (operationId) {
      query = "?operation=" + operationId;
    }
    return this.httpGet(`/user/intervenants-event${query}`);
  }

  async createHospitalisation(h: Partial<Hospitalisation>): Promise<Hospitalisation> {
    return this.httpPost("/hospitalisation/", h);
  }
  async getCroContentWithTemplate(
    operation_id: string,
    cro_type_id: string
  ): Promise<{ cro_type: string; indication_operation_type: string }> {
    return this.httpGet(`/operation/${operation_id}/${cro_type_id}/generate-cro`);
  }
  async addToPrintManager(doc_type: string, doc_id: string): Promise<PrintManagerFile> {
    // ONLY USE THIS IN PRINTMANAGERCONTEXT
    // DO NOT USE THIS TO ADD CRH TO PRINT MANAGER AS IT WILL NOT RELOAD THE QUEUE
    return this.httpPost(`/print-manager/`, {
      uuid: doc_id,
      type_document: doc_type.toLowerCase(),
    });
  }
  // returns application/pdf file for selected docs ou ordonnances outside du gestionnaire d'impression
  async printManagerPrintAll(docs: PrintData[]): Promise<Blob> {
    return this.httpPostBlobResponse("/print-manager/print-selected", docs);
  }
  //returns application/pdf file for selected documents du gestionnaire d'impression
  async printManagerPrint(docs: string[], rectoVerso?: boolean): Promise<Blob> {
    return this.httpPostBlobResponse("/print-manager/print", {
      ids: docs,
      recto_verso: rectoVerso,
    });
  }

  async deletePrintManagerDoc(uuids?: string[]): Promise<void> {
    // if uuid is not provided, we delete all docs
    return this.httpDelete("/print-manager/", uuids ? uuids : undefined);
  }
  async getPrintManagerQueue(): Promise<PrintManagerFile[]> {
    return this.httpGet("/print-manager");
  }
  async getCalendarItems(
    start: UnixTimestamp,
    end: UnixTimestamp,
    users: number[]
  ): Promise<CalendarItem[]> {
    if (start && end) {
      return this.httpGet(`/agenda/items?start=${start}&end=${end}&users=${users.join(",")}`);
    } else {
      return this.httpGet(`/agenda/items`);
    }
  }
  async getNotifications(filter: NfTypeEnum[]): Promise<PaginatedResponse<Notification>> {
    return this.httpGet(`/notification?filter=${filter}`);
  }
  async markNotification(id: string, read: boolean): Promise<void> {
    return this.httpPatch(`/notification/${id}`, { read });
  }
  async flagNotification(id: string, flagged: boolean): Promise<void> {
    return this.httpPatch(`/notification/${id}`, { flagged });
  }
  async deleteNotification(id: string, all: boolean): Promise<void> {
    return this.httpDelete(`/notification/${id}?all=${all}`);
  }
  async deleteAllReadNotifications(ids: string[]): Promise<void> {
    return this.httpPost(`/notification/read`, { delete: true, ids });
  }
  async updateReadNotifications(ids: string[]): Promise<void> {
    return this.httpPost(`/notification/read`, { delete: false, ids });
  }
  async getProtocoles(): Promise<Protocole[]> {
    return this.httpGet(`/protocole`);
  }
  async getModificateur(code?: string, activite?: string): Promise<any> {
    // TODO: Update with the correct type
    let url = "/ccam/modificateur";
    if (code && activite) {
      url += `?aa_code=${code}&activite=${activite}`;
    }
    return this.httpGet(url);
  }
  async searchCCAM(query: string): Promise<Acte[]> {
    return this.httpGet(`/ccam/search?query=${query}`);
  }
  async getCCAM(code: string): Promise<ActeSearchResponse> {
    return this.httpGet(`/ccam/acte/${code}`);
  }
  async getMyself(): Promise<User> {
    return this.httpGet(`/user/me`);
  }
  async getListQuotes(operationId: string): Promise<PaginatedResponse<Devis>> {
    // TODO: Update with the correct type
    return this.httpGet(`/comptabilite/devis?operation=${operationId}`);
  }
  async printQuoteURl(quoteId: string): Promise<string> {
    return this.httpGet(`/devis/print-devis/${quoteId}/print`);
  }
  async removeClaimFromQuote(claimId: string): Promise<void> {
    return this.httpDelete(`/comptabilite/code/${claimId}/`);
  }
  async updateClaimOnQuote(claim: OperationDevisCcam): Promise<OperationDevisCcam> {
    return this.httpPut(`/comptabilite/code/${claim.id}/`, claim);
  }
  async postClaimOnQuote(claim: OperationDevisCcam, devisId: string): Promise<OperationDevisCcam> {
    return this.httpPost(`/comptabilite/code?devis=${devisId}`, claim);
  }
  async getQuotes(operationId: string): Promise<PaginatedDevisList> {
    return this.httpGet(`/comptabilite/devis?operation=${operationId}`);
  }
  async getClaimsFromQuote(quoteId: string): Promise<OperationDevisCcam[]> {
    return this.httpGet(`/comptabilite/code?devis=${quoteId}`);
  }
  async createQuote(operationId: string, patientId: string): Promise<Devis> {
    const date = new Date();
    const body = {
      // as ISO YYYY-MM-DD
      date_devis: date.toISOString().split("T")[0],
      depassement_honoraire: "",
      zkf_operation: operationId,
      zkf_patient: patientId,
    };
    return this.httpPost(`/comptabilite/devis/`, body);
  }
  async deleteQuote(quoteId: string): Promise<void> {
    return this.httpDelete(`/comptabilite/devis/${quoteId}/`);
  }
  async getCoverageOptions(): Promise<CouvertureMaladie[]> {
    return this.httpGet("/patient/coverage");
  }
  async getOrdonnances(
    consultationId?: string,
    hospitalisationId?: string,
    operationId?: string,
    patientId?: string
  ): Promise<PaginatedResponse<Ordonnance>> {
    let query = "";
    if (consultationId) {
      query = "?consultation=" + consultationId;
    } else if (hospitalisationId) {
      query = "?hospitalisation=" + hospitalisationId;
    } else if (operationId) {
      query = "?operation=" + operationId;
    } else if (patientId) {
      query = "?patient=" + patientId;
    }
    return this.httpGet(`/ordonnance/${query}`);
  }
  async getOrdonnanceTemplates(
    consultationId?: string,
    hospitalisationId?: string
  ): Promise<OrdonnanceTemplate[]> {
    const query = consultationId
      ? `?consultation=${consultationId}`
      : hospitalisationId
        ? `?hospitalisation=${hospitalisationId}`
        : "";
    return this.httpGet(`/ordonnance/template${query}`);
  }
  async postOrdonnance(data: Partial<Ordonnance>): Promise<Ordonnance> {
    return this.httpPost(`/ordonnance/`, data);
  }
  async putOrdonnance(id: string, data: Ordonnance): Promise<Ordonnance> {
    return this.httpPut(`/ordonnance/${id}`, data);
  }
  async deleteOrdonnance(id: string): Promise<void> {
    return this.httpDelete(`/ordonnance/${id}`);
  }
  reportError(error: Error): void {
    console.error(error);
  }

  async listUserSpecialites(): Promise<Specialite[]> {
    return this.httpGet(`/user/specialites`);
  }

  async getUserSpecialite(id: string): Promise<Specialite> {
    return this.httpGet(`/user/specialites/${id}`);
  }

  async listContacts(search: string): Promise<Contact[]> {
    return this.httpGet(`/contact?search=${search}`);
  }

  async getContact(id: string): Promise<Contact> {
    return this.httpGet(`/contact/${id}`);
  }

  async listContactsByPatient(patientId: string, depth?: number): Promise<CorrespondantPatient[]> {
    console.log(
      `Appel API: GET /contact/correspondant-patient?patient=${patientId}&depth=${depth}`
    );
    const response = await this.httpGet(
      `/contact/correspondant-patient?patient=${patientId}&depth=${depth}`
    );
    console.log("Réponse API listContactsByPatient:", JSON.stringify(response, null, 2));
    return response;
  }

  async createContact(contact: Partial<CustomContact>): Promise<Contact> {
    return this.httpPost(`/contact/`, contact);
  }

  async updateContact(contact: Partial<CustomContact>): Promise<Contact> {
    return this.httpPatch(`/contact/${contact.id}`, contact);
  }

  /**
   * Liste les comptes rendus (version simple)
   * @param patientId - ID du patient (optionnel)
   * @param status - Statut du compte rendu (optionnel)
   * @param page - Numéro de page (défaut: 1)
   * @param pageSize - Nombre d'éléments par page (défaut: 10)
   * @returns CompteRendu[] - Liste simple des comptes rendus
   */
  async listCompteRendu(
    patientId?: string,
    status?: string,
    page?: number,
    paginated?: boolean,
    sortBy?: string
  ): Promise<CompteRendu[]>;

  /**
   * Liste les comptes rendus avec pagination
   * @param patientId - ID du patient (optionnel)
   * @param status - Statut du compte rendu (optionnel)
   * @param page - Numéro de page (défaut: 1)
   * @param pageSize - Nombre d'éléments par page (défaut: 20)
   * @param paginated - Doit être true pour activer la pagination
   * @param sortBy - Critère de tri ("created_on" ou "date_examen")
   * @returns Objet paginé contenant les résultats et les métadonnées de pagination
   */
  async listCompteRendu(
    patientId: string | undefined,
    status: string | undefined,
    page: number,
    paginated: true,
    sortBy?: string
  ): Promise<{
    results: CompteRendu[];
    count: number;
    next: string | null;
    previous: string | null;
  }>;

  /**
   * Implémentation de la méthode listCompteRendu
   */
  async listCompteRendu(
    patientId?: string,
    status?: string,
    page: number = 1,
    paginated: boolean = false,
    sortBy?: string // Nouveau paramètre pour spécifier l'ordre de tri
  ): Promise<
    | CompteRendu[]
    | {
        results: CompteRendu[];
        count: number;
        next: string | null;
        previous: string | null;
      }
  > {
    let url = "/compte-rendu/";
    const params = [];

    // Only add patient parameter if it's a non-empty string
    if (patientId && patientId.trim() !== "") {
      params.push(`patient=${patientId}`);
    }

    // Only add status parameter if it's a non-empty string
    if (status && status.trim() !== "") {
      params.push(`status=${status}`);
    }

    // Add pagination parameters
    params.push(`page=${page}`);

    // Ajouter le paramètre de tri si spécifié
    if (sortBy) {
      params.push(`sort_by=${sortBy}`);
    }

    // Log pour débogage
    // console.log(`API request with page=${page}${sortBy ? `, sort_by=${sortBy}` : ''}`);

    if (params.length > 0) {
      url += `?${params.join("&")}`;
    }

    // console.log(`Making API request to: ${url}`);
    const response = await this.httpGet(url);

    // Si paginated est false, retourne juste les résultats pour maintenir la compatibilité avec le code existant
    if (!paginated) {
      return response.results;
    }

    // Sinon, retourne l'objet complet avec pagination
    return response;
  }

  async getCompteRendu(documentId: string): Promise<CompteRendu> {
    return this.httpGet(`/compte-rendu/${documentId}`);
  }

  async getContactAddresses(contactId: string): Promise<Adresse[]> {
    return this.httpGet(`/contact/adresse?contact=${contactId}`);
  }

  async createCompteRendu(compteRendus: MetaData, patientId: string | null): Promise<CompteRendu> {
    const formData = new FormData();
    if (patientId) {
      formData.append("zkf_patient", patientId);
    }
    formData.append("nom", compteRendus.nom || "");
    formData.append("zkf_auteur", compteRendus.auteur?.id || "");
    formData.append("zkf_contact", compteRendus.auteur?.id || "");
    formData.append("type_compte_rendu", compteRendus.type || "");
    compteRendus.file && formData.append("examen_file", compteRendus.file);
    compteRendus?.specialites?.forEach((spe) => formData.append("specialite", spe.id));
    compteRendus.dateExamen && formData.append("date_examen", compteRendus.dateExamen);
    compteRendus.dateEntree && formData.append("date_entree", compteRendus.dateEntree);
    compteRendus.dateSortie && formData.append("date_sortie", compteRendus.dateSortie);
    formData.append("notification", `${compteRendus.notification}`);
    return this.httpPostFormData(`/compte-rendu/`, formData);
  }

  async updateCompteRendu(compteRendu: CompteRendu): Promise<CompteRendu> {
    const formData = new FormData();
    compteRendu.zkf_patient && formData.append("zkf_patient", compteRendu.zkf_patient);
    formData.append("nom", compteRendu.nom || "");
    formData.append("zkf_contact", compteRendu.zkf_contact || "");
    formData.append("type_compte_rendu", compteRendu.type_compte_rendu || "");
    compteRendu?.specialite?.forEach((spe) => formData.append("specialite", spe));
    compteRendu.date_examen && formData.append("date_examen", compteRendu.date_examen);
    compteRendu.date_entree && formData.append("date_entree", compteRendu.date_entree);
    compteRendu.date_sortie && formData.append("date_sortie", compteRendu.date_sortie);

    // Ajouter le champ status s'il est défini
    if (compteRendu.status !== undefined) {
      formData.append("status", compteRendu.status.toString());
    }

    // Ajouter le champ should_be_contacted s'il est défini
    if (compteRendu.should_be_contacted !== undefined) {
      formData.append("should_be_contacted", compteRendu.should_be_contacted.toString());
    }

    return this.httpPatchFormData(`/compte-rendu/${compteRendu.id}`, formData);
  }

  async deleteCompteRendu(compteRenduId: string): Promise<void> {
    return this.httpDelete(`/compte-rendu/${compteRenduId}`);
  }

  async patchConsultation(
    consultatioId: string,
    body: any // format HH:MM:SS
  ): Promise<Consultation> {
    return this.httpPatch(`/consultation/${consultatioId}`, body);
  }

  async listPhonesByContact(contactId: string): Promise<Telephone[]> {
    return this.httpGet(`/contact/telephone?contact=${contactId}`);
  }

  async listEmailsByContact(contactId: string): Promise<Email[]> {
    return this.httpGet(`/contact/email?contact=${contactId}`);
  }

  async listAddressesByContact(contactId: string): Promise<Adresse[]> {
    return this.httpGet(`/contact/adresse?contact=${contactId}`);
  }

  async listPhonesByPatient(patientId: string): Promise<Telephone[]> {
    return this.httpGet(`/contact/telephone?patient=${patientId}`);
  }

  async listEmailsByPatient(patientId: string): Promise<Email[]> {
    return this.httpGet(`/contact/email?patient=${patientId}`);
  }

  async listAddressesByPatient(patientId: string): Promise<Adresse[]> {
    return this.httpGet(`/contact/adresse?patient=${patientId}`);
  }

  async createPhone(telephone: PatchedTelephone, patientId: string): Promise<Telephone> {
    const query = patientId ? `?patient=${patientId}` : "";
    return this.httpPost(`/contact/telephone${query}`, telephone);
  }

  async createEmail(email: PatchedEmail, patientId: string): Promise<Email> {
    const query = patientId ? `?patient=${patientId}` : "";
    return this.httpPost(`/contact/email${query}`, email);
  }

  async createAdresse(adresse: PatchedAdresse, patientId: string): Promise<Adresse> {
    const query = patientId ? `?patient=${patientId}` : "";
    return this.httpPost(`/contact/adresse${query}`, adresse);
  }

  async linkContactToPatient(
    patientId: string,
    correspondants: CorrespondantPatient[]
  ): Promise<void> {
    // S'assurer que les booléens sont correctement formatés pour le backend
    // Utiliser Boolean() pour forcer la conversion en booléen
    const processedCorrespondants = correspondants.map((item) => {
      const processedItem = {
        contact: item.contact,
        send_report: item.send_report,
        referral_source: item.referral_source,
      };

      return processedItem;
    });

    // Format attendu par Django DRF: un objet avec une propriété "correspondants"
    // contenant une liste de dictionnaires avec les informations du contact
    return this.httpPatch(`/patient/${patientId}/`, {
      correspondants: processedCorrespondants,
    });
  }

  async searchVilleByZipCode(zipCode: string): Promise<Ville[]> {
    return this.httpGet(`/contact/ville?zip_code=${zipCode}`);
  }

  async listDocumentLibreTemplates(userId: number): Promise<DocumentLibreTemplate[]> {
    return this.httpGet(`/document-libre/template?user=${userId}`);
  }

  uploadFile = async (file: File) => {
    const reader = new FileReader();

    return new Promise<string>((resolve, reject) => {
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  async createDocumentLibreTemplate(
    document: Partial<DocumentLibreTemplate>,
    file?: File,
    duplicateUser: number[] = []
  ): Promise<DocumentLibreTemplate> {
    const formData: DocumentLibreTemplateSend = {
      titre_document: document.titre_document,
      description_document: document.description_document,
      corps_document: document.corps_document,
      specialite: document.specialite,
      zkf_user: document.zkf_user,
      duplicate_user: duplicateUser,
    };
    if (file) {
      formData.file = {
        name: file.name,
        type: file.type,
        content: await this.uploadFile(file),
      };
    }
    return this.httpPost(`/document-libre/template`, formData);
  }

  async updateDocumentLibreTemplate(
    document: Partial<DocumentLibreTemplate>,
    file?: File
  ): Promise<DocumentLibreTemplate> {
    const formData = new FormData();
    Object.keys(document).forEach((key) => {
      const value = document[key as keyof DocumentLibreTemplate];
      if (Array.isArray(value)) {
        value.forEach((v) => formData.append(key, v));
      } else {
        value && formData.append(key, value.toString());
      }
    });
    file && formData.append("document_type_file", file);
    return this.httpPatchFormData(`/document-libre/template/${document.id}`, formData);
  }

  async deleteDocumentLibreTemplate(id: string): Promise<void> {
    return this.httpDelete(`/document-libre/template/${id}`);
  }

  async listDocumentsByOperation(operationId: string): Promise<DocumentLibre[]> {
    return this.httpGet(`/document-libre?operation=${operationId}`);
  }

  async createDocument(document: Partial<DocumentLibre>): Promise<DocumentLibre> {
    return this.httpPost(`/document-libre/`, document);
  }

  async deleteDocument(documentId: string): Promise<void> {
    return this.httpDelete(`/document-libre/${documentId}`);
  }

  async listUserServices(deep: boolean = false): Promise<Service[]> {
    return this.httpGet(`/user/service?depth=${deep ? 1 : 0}`);
  }

  async generateCRH(hospitalisationId: string): Promise<string> {
    return this.httpGet(`/hospitalisation/${hospitalisationId}/generate-crh`);
  }
  async getUserService(id: string, deep: boolean = false): Promise<Service> {
    return this.httpGet(`/user/service/${id}?depth=${deep ? 1 : 0}`);
  }

  async getOrdonnanceCategories(): Promise<CategorieOrdonnance[]> {
    return this.httpGet(`/ordonnance/categorie`);
  }

  async getOrdonnancesTemplate(userId: number): Promise<OrdonnanceTemplate[]> {
    return this.httpGet(`/ordonnance/template?user=${userId}`);
  }

  async createOrdonnanceTemplate(
    ordonnance: Partial<OrdonnanceTemplate>,
    duplicateUsers: number[] = []
  ): Promise<OrdonnanceTemplate> {
    return this.httpPost(`/ordonnance/template/`, {
      ...ordonnance,
      duplicate_user: duplicateUsers,
    });
  }

  async updateOrdonnanceTemplate(
    ordonnance: Partial<OrdonnanceTemplate>
  ): Promise<OrdonnanceTemplate> {
    return this.httpPatch(`/ordonnance/template/${ordonnance.id}`, ordonnance);
  }

  async deleteOrdonnanceTemplate(id: string): Promise<void> {
    return this.httpDelete(`/ordonnance/template/${id}`);
  }

  async getMaterielIrradiation(): Promise<MatrielIrradiation[]> {
    return this.httpGet(`/operation/appareil-scopie`);
  }

  async createMaterielIrradiation(
    materiel: Partial<MatrielIrradiation>
  ): Promise<MatrielIrradiation> {
    return this.httpPost(`/operation/appareil-scopie`, materiel);
  }

  async updateMaterielIrradiation(
    materiel: Partial<MatrielIrradiation>
  ): Promise<MatrielIrradiation> {
    return this.httpPatch(`/operation/appareil-scopie/${materiel.id}`, materiel);
  }

  async deleteMaterielIrradiation(id: string): Promise<void> {
    return this.httpDelete(`/operation/appareil-scopie/${id}`);
  }

  async getConsignesSortie(): Promise<ConsignesSortie[]> {
    return this.httpGet(`/hospitalisation/consigne-sortie`);
  }

  async createConsignesSortie(
    consigne: Partial<ConsignesSortie>,
    duplicateUsers: number[] = []
  ): Promise<ConsignesSortie> {
    return this.httpPost(`/hospitalisation/consigne-sortie/`, {
      ...consigne,
      duplicate_user: duplicateUsers,
    });
  }

  async updateConsignesSortie(consigne: Partial<ConsignesSortie>): Promise<ConsignesSortie> {
    return this.httpPatch(`/hospitalisation/consigne-sortie/${consigne.id}`, consigne);
  }

  async deleteConsignesSortie(id: string): Promise<void> {
    return this.httpDelete(`/hospitalisation/consigne-sortie/${id}`);
  }

  async getConsultationType(): Promise<ConsultationType[]> {
    return this.httpGet(`/consultation/consultation-type`);
  }

  async createConsultationType(
    consultation: Partial<ConsultationType>,
    duplicateUsers: number[] = []
  ): Promise<ConsultationType> {
    return this.httpPost(`/consultation/consultation-type/`, {
      ...consultation,
      duplicate_user: duplicateUsers,
    });
  }

  async updateConsultationType(consultation: Partial<ConsultationType>): Promise<ConsultationType> {
    return this.httpPatch(`/consultation/consultation-type/${consultation.id}`, consultation);
  }

  async deleteConsultationType(id: string): Promise<void> {
    return this.httpDelete(`/consultation/consultation-type/${id}`);
  }

  async getCourrierConsultationByTemplate(
    consultationId: string,
    templateId: string
  ): Promise<string | { corps?: string; bas_de_page?: string; should_replace_all?: boolean }> {
    return this.httpGet(
      `/consultation/generate-consultation-type?consultation=${consultationId}&consultation_type=${templateId}`
    );
  }

  async getVacations(): Promise<CustomVacation[]> {
    return this.httpGet(`/agenda/vacation?depth=1`);
  }

  async createVacation(vacation: Partial<Vacation>): Promise<Vacation> {
    return this.httpPost(`/agenda/vacation/`, vacation);
  }

  async updateVacation(vacation: Partial<Vacation>): Promise<Vacation> {
    return this.httpPatch(`/agenda/vacation/${vacation.id}/`, vacation);
  }

  async deleteVacation(id: Vacation["id"]): Promise<void> {
    return this.httpDelete(`/agenda/vacation/${id}/`);
  }

  // async getAbsences(): Promise<CustomAbsence[]> {
  //   return this.httpGet(`/agenda/user-event?depth=1`);
  // }

  // async createAbsence(vacation: Partial<UserEvent>): Promise<UserEvent> {
  //   return this.httpPost(`/agenda/user-event/`, vacation);
  // }

  // async updateAbsence(vacation: Partial<UserEvent>): Promise<UserEvent> {
  //   return this.httpPatch(`/agenda/user-event/${vacation.id}/`, vacation);
  // }

  // async deleteAbsence(id: UserEvent["id"]): Promise<void> {
  //   return this.httpDelete(`/agenda/user-event/${id}/`);
  // }

  async getPatientFields(): Promise<Field[]> {
    return this.httpGet(`/settings/model-fields`);
  }

  async getUserPreferences(id: string): Promise<PreferenceUserSerialyzer> {
    return this.httpGet(`/user/preferences-user/${id}`);
  }

  async getGroups(): Promise<Group[]> {
    return this.httpGet(`/user/groups`);
  }

  async getNotificationsChoices(): Promise<{
    channels: [string, string][];
    events: [string, string][];
  }> {
    return this.httpGet(`/user/notification-choices/`);
  }

  async updateUser(user: User): Promise<User> {
    return this.httpPatch(`/user/${user.id}/`, user);
  }

  async createUser(user: Partial<User>): Promise<User> {
    return this.httpPost(`/user/`, user);
  }

  async updateProfile(profile: Profile): Promise<Profile> {
    return this.httpPatch(`/user/profile/${profile.id}`, profile);
  }

  async updatePreferencesUser(
    preferences: Partial<PreferenceUserSerialyzer>
  ): Promise<PreferenceUserSerialyzer> {
    return this.httpPatch(`/user/preferences-user/${preferences.id}`, preferences);
  }

  async getCcamActivite(): Promise<R_activite[]> {
    return this.httpGet(`/api/ccam/r_activite`);
  }

  async getEnteteDocument(id: string): Promise<EnteteDocument> {
    return this.httpGet(`/api/user/entete-document/${id}`);
  }

  async createEnteteDocument(entete: Partial<EnteteDocument>): Promise<EnteteDocument> {
    return this.httpPost(`/api/user/entete-document/`, entete);
  }

  async updateEnteteDocument(entete: Partial<EnteteDocument>): Promise<EnteteDocument> {
    return this.httpPatch(`/api/user/entete-document/${entete.id}`, entete);
  }

  async getPatientEvents(id: string): Promise<CalendarItem[]> {
    return this.httpGet(`/patient/${id}/events`);
  }

  async getOperationsByPatient(id: string): Promise<Operation[]> {
    return this.httpGet(`/operation?patient=${id}`);
  }

  async getComplicationsPostOp(id: string): Promise<ComplicationPostOp[]> {
    return this.httpGet(`/operation/complications-post-op?operation=${id}`);
  }

  async updateComplicationsPostOp(
    complications: ComplicationPostOp,
    operationId: string
  ): Promise<ComplicationPostOp> {
    return this.httpPatch(
      `/operation/complications-post-op/${complications.id}?operation=${operationId}`,
      complications
    );
  }
  async getTags(): Promise<FullTag[]> {
    return this.httpGet(`/tag`);
  }

  async getOperationField(field: string): Promise<string[]> {
    return this.httpGet(`/api/operation/list-fields?field=${field}`);
  }

  async getLastConsultations(
    patientId: string,
    operationId?: string,
    hospitalisationId?: string,
    page?: number
  ): Promise<PaginatedConsultationList> {
    return this.httpGet(
      `/api/consultation?patient=${patientId}${
        operationId ? `&operation=${operationId}` : ""
      }${hospitalisationId ? `&hospitalisation=${hospitalisationId}` : ""}${page ? `&page=${page}` : ""}`
    );
  }

  async getIrradiationForOperation(id: string): Promise<Irradiation[]> {
    return this.httpGet(`/operation/irradiation?operation=${id}`);
  }

  async createIrradiation(irradiation: Partial<Irradiation>): Promise<Irradiation> {
    return this.httpPost(`/operation/irradiation`, irradiation);
  }

  async updateIrradiation(irradiation: Irradiation): Promise<Irradiation> {
    return this.httpPut(`/operation/irradiation/${irradiation.id}`, irradiation);
  }

  async getProduitsContraste(): Promise<Contraste[]> {
    return this.httpGet(`/api/operation/contrast`);
  }

  async updatePhone(telephone: Telephone): Promise<Telephone> {
    return this.httpPut(`/contact/telephone/${telephone.id}`, telephone);
  }

  async updateEmail(email: Email): Promise<Email> {
    return this.httpPut(`/contact/email/${email.id}`, email);
  }

  async updateAdresse(adresse: Adresse): Promise<Adresse> {
    return this.httpPatch(`/contact/adresse/${adresse.id}`, adresse);
  }

  async deleteEmail(id: string): Promise<void> {
    return this.httpDelete(`/contact/email/${id}`);
  }

  async deletePhone(id: string): Promise<void> {
    return this.httpDelete(`/contact/telephone/${id}`);
  }

  async deleteAdresse(id: string): Promise<void> {
    return this.httpDelete(`/contact/adresse/${id}`);
  }

  //Absences (UserEvent => for current user)
  async getUserEvent(depth: number = 1): Promise<PaginatedUserEventList> {
    return this.httpGet(`/agenda/user-event?${depth ? `depth=${depth}` : ""}`);
  }

  async getEvent(id: string): Promise<UserEvent> {
    return this.httpGet(`/agenda/user-event/${id}`);
  }

  async createEvent(event: Partial<UserEvent>): Promise<UserEvent> {
    return this.httpPost(`/agenda/user-event/`, event);
  }

  async updateEvent(event: Partial<UserEvent>): Promise<UserEvent> {
    return this.httpPatch(`/agenda/user-event/${event.id}/`, event);
  }

  async deleteEvent(id: string): Promise<void> {
    return this.httpDelete(`/agenda/user-event/${id}/`);
  }

  async getEventCategories(): Promise<UserEventCategory[]> {
    return this.httpGet(`/agenda/user-event-category`);
  }

  async getMedia(url: string): Promise<Blob> {
    return this.httpGetMedia(url);
  }

  // async getOrdonnancePdfUrl(ordonnanceId: string): Promise<string> {
  //   return this.httpGet(`/ordonnances/${ordonnanceId}/pdf/`);
  // }

  async deleteHospitalisation(id: string): Promise<void> {
    return this.httpDelete(`/hospitalisation/${id}`);
  }

  async extractDocumentMetadata(file: File): Promise<any> {
    try {
      // Create FormData to send the file
      const formData = new FormData();
      formData.append("file", file);

      // Use httpPostFormData to send the file
      const response = await this.httpPostFormData("/nlp/extract-metadata/", formData);

      return response;
    } catch (error) {
      console.error("Error extracting document metadata:", error);

      // Log detailed error information
      if (error instanceof Error) {
        console.error("Error Details:", {
          message: error.message,
          name: error.name,
        });
      }

      // Return an empty object or throw depending on your error handling strategy
      return {};
    }
  }
  async getSuggestedRdvForHospitalisation(
    patientId: string,
    hospitalisationId: string,
    date: string,
    duration: number // number in days to look for
  ): Promise<{ message: string; results: SuggestedRdv[] }> {
    return this.httpGet(
      `/consultation/suggested-consultations?patient=${patientId}&hospitalisation=${hospitalisationId}&date=${date}&duration=${duration}`
    );
  }

  async getSuiviHospitalisation(hospitalisationId: string, suiviId: string): Promise<Suivi> {
    return this.httpGet(`/hospitalisation/${hospitalisationId}/suivi/${suiviId}`);
  }

  async createSuiviHospitalisation(
    hospitalisationId: string,
    suivi: Partial<Suivi>
  ): Promise<Suivi> {
    return this.httpPost(`/hospitalisation/${hospitalisationId}/suivi`, suivi);
  }

  async updateSuiviHospitalisation(
    hospitalisationId: string,
    suivi: Partial<Suivi>
  ): Promise<Suivi> {
    return this.httpPatch(`/hospitalisation/${hospitalisationId}/suivi/${suivi.id}`, suivi);
  }

  async deleteSuiviHospitalisation(hospitalisationId: string, suiviId: string): Promise<void> {
    return this.httpDelete(`/hospitalisation/${hospitalisationId}/suivi/${suiviId}`);
  }

  async listSuiviHospitalisation(hospitalisationId: string): Promise<Suivi[]> {
    return this.httpGet(`/hospitalisation/${hospitalisationId}/suivi?depth=1`);
  }

  async getEventsListPDF(
    start: ISODate,
    end: ISODate,
    users: number[],
    types: ("consultation" | "operation" | "user_event")[],
    periodicity: "am" | "pm" | "all"
  ): Promise<Blob> {
    return this.httpGet(
      `/agenda/day-events-pdf/?start_date=${start}&end_date=${end}&users=${users}&events=${types.join(
        ","
      )}&periodicity=${periodicity}`
    );
  }

  async generateConsigneSortie(
    hospitalisationId: string,
    consigneSortieId: string
  ): Promise<string> {
    return this.httpGet(
      `/hospitalisation/generate-consigne-sortie?hospitalisation=${hospitalisationId}&consigne_sortie=${consigneSortieId}`
    );
  }

  async listComptesRendusConsultation(
    limit: number = 10,
    offset: number = 0,
    status?: string
  ): Promise<PaginatedResponse<Consultation>> {
    let url = `/print-manager/compte-rendus/consultation?limit=${limit}&offset=${offset}`;
    if (status) {
      url += `&status=${status}`;
    }
    return this.httpGet(url);
  }

  async listComptesRendusHospitalisation(
    limit: number = 10,
    offset: number = 0,
    status?: string
  ): Promise<PaginatedResponse<Hospitalisation>> {
    let url = `/print-manager/compte-rendus/hospitalisation?limit=${limit}&offset=${offset}`;
    if (status) {
      url += `&status=${status}`;
    }
    return this.httpGet(url);
  }

  async getActesForOperation(operationId: string): Promise<OperationDevisCcam[]> {
    return this.httpGet(`/comptabilite/code?operation=${operationId}`);
  }

  async getSuggestedActesForOperation(operationId: string): Promise<Acte[]> {
    return this.httpGet(`/ccam/suggested-actes?operation=${operationId}`);
  }

  async addActeToOperation(operationId: string, ccam: string): Promise<OperationDevisCcam> {
    return this.httpPost(`/comptabilite/code?operation=${operationId}`, {
      acte_ccam: ccam,
    });
  }

  async removeActeFromOperation(operationId: string, acteId: string): Promise<void> {
    // operationId is kept for frontend context but not used in the actual request
    return this.httpDelete(`/comptabilite/code/${acteId}/`);
  }

  async getActeMenus(): Promise<R_menu[]> {
    return this.httpGet(`/ccam/menu`);
  }

  // Add a new method to get submenus with the cod_menu parameter
  async getSubmenus(codMenu: string): Promise<R_menu[]> {
    return this.httpGet(`/ccam/menu?cod_menu=${codMenu}`);
  }

  async getActeListByMenu(menuId: string, page?: number): Promise<PaginatedResponse<Acte>> {
    const pageParam = page ? `&page=${page}` : "";
    return this.httpGet(`/ccam/acte?menu=${menuId}${pageParam}`);
  }

  async getAllActes(): Promise<Acte[]> {
    return this.httpGet(`/ccam/acte`);
  }

  async getCurrentPatientHospitalisation(
    patientId: string
  ): Promise<PaginatedResponse<Hospitalisation>> {
    return this.httpGet(`/hospitalisation?patient=${patientId}&only_active=true&depth=1`);
  }

  // Add resetPatientIns method
  async resetPatientIns(id: string): Promise<Patient> {
    // Assuming endpoint returns updated patient
    return this.httpPost(`/patient/${id}/reset_ins/`, {});
  }

  // Add verifyPatientIns method
  async verifyPatientIns(id: string): Promise<any> {
    // Return type is unknown (likely boolean), use 'any' for now
    return this.httpPost(`/patient/${id}/verify_ins/`, {});
  }

  // Auditlog: log of objects modifications
  async getAuditLog(
    objectId: string | null,
    objectName: string | null,
    user: number | null,
    depth: number = 1
  ): Promise<PaginatedAuditLogList> {
    return this.httpGet(
      `/api/auditlog/?id=${objectId ? objectId : ""}${objectName ? `&model_name=${objectName}` : ""}${user ? `&user=${user}` : ""}${depth ? `&depth=${depth}` : ""}`
    );
  }

  async getUserIntervenantList(id: string): Promise<Intervenant[]> {
    return this.httpGet(`/user/${id}/intervenant/`);
  }

  async getIntervenantDetail(id: string): Promise<Intervenant> {
    return this.httpGet(`/user/intervenant/${id}/`);
  }
  // --- AUDIO RECORDING ---
  async listAudioRecordings(
    patientId?: string,
    hospitalisationId?: string,
    consultationId?: string,
    page: number = 1,
    paginated: boolean = false
  ): Promise<any> {
    let url = "/audio-recording/";
    const params = [];
    if (patientId) params.push(`patient=${patientId}`);
    if (hospitalisationId) params.push(`hospitalisation=${hospitalisationId}`);
    if (consultationId) params.push(`consultation=${consultationId}`);
    if (paginated) params.push(`page=${page}`);
    if (params.length > 0) url += `?${params.join("&")}`;
    return this.httpGet(url);
  }

  /**
   * Create a new audio recording with optional patient, hospitalisation, consultation, and title.
   * @param audioBlob The audio file blob
   * @param patientId The patient ID
   * @param hospitalisationId The hospitalisation ID
   * @param consultationId The consultation ID
   * @param title The title of the audio recording
   * @param auteur The author of the audio recording
   */
  async createAudioRecording(
    audioBlob: Blob,
    patientId?: string,
    hospitalisationId?: string,
    consultationId?: string,
    title?: string,
    auteur?: string
  ): Promise<any> {
    const formData = new FormData();

    // ...
    const ext = getAudioExtension(audioBlob.type);
    formData.append("file", audioBlob, `audio.${ext}`);
    if (patientId) formData.append("patient_id", patientId); // Send as patient_id for DRF
    if (hospitalisationId) formData.append("hospitalisation_id", hospitalisationId);
    if (consultationId) formData.append("consultation_id", consultationId); // Send as consultation_id for DRF
    if (title) formData.append("title", title);
    if (auteur) formData.append("auteur_id", auteur);
    return this.httpPostFormData("/audio-recording/", formData);
  }

  async getAudioRecording(id: string): Promise<any> {
    return this.httpGet(`/audio-recording/${id}`);
  }

  /**
   * Update an audio recording (status, transcription, etc.).
   * Always uses multipart/form-data because the backend endpoint only supports this media type (even for status-only updates).
   */
  async updateAudioRecording(id: string, data: any): Promise<any> {
    const formData = new FormData();
    for (const key in data) {
      if (data[key] !== undefined && data[key] !== null) {
        formData.append(key, data[key]);
      }
    }
    return this.httpPatchFormData(`/audio-recording/${id}/`, formData);
  }

  async deleteAudioRecording(id: string): Promise<void> {
    return this.httpDelete(`/audio-recording/${id}/`);
  }

  // --- AUDIO TRANSCRIPTION ---
  async transcribeAudio(formData: FormData): Promise<any> {
    return this.httpPostFormData("/audio-recording/transcribe/", formData);
  }

  // -- STATISTICS --
  async getDailyStats(
    start_date: string,
    end_date: string,
    date_intervals?: any[],
    user_id?: number
  ): Promise<any> {
    const payload: any = {
      start_date,
      end_date,
    };

    if (date_intervals) {
      payload.date_intervals = date_intervals;
    }

    if (user_id) {
      payload.user_id = user_id;
    }

    return this.httpPost("/statistics/daily/", payload);
  }

  async getStatisticsOptions(): Promise<any> {
    return this.httpGet("/statistics/options/");
  }

  async queryStatistics(data: any): Promise<any> {
    return this.httpPost("/statistics/query/", data);
  }

  async exportStatistics(data: any): Promise<Blob> {
    return this.httpPostBlobResponse("/statistics/export/", data);
  }

  async getIntervalsComparison(data: any): Promise<any> {
    return this.httpPost("/statistics/intervals/", data);
  }
}
