import { Global } from "@emotion/react";
import React, { useState } from "react";

import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Drawer from "@mui/joy/Drawer";
import Grid from "@mui/joy/Grid";
import ModalClose from "@mui/joy/ModalClose";

import TranscriptionDetailsPanel from "../../components/SpeechToText/TranscriptionDetailsPanel";
import TranscriptionHistoryList from "../../components/SpeechToText/TranscriptionHistoryList";
import { useApi } from "../../contexts/ApiContext";
import { AudioRecording } from "../../models/types";
import SpeechToTextPage from "./SpeechToTextDrawer";

const fetchData = async (
  api: any,
  page: number,
  setRecordings: React.Dispatch<React.SetStateAction<AudioRecording[]>>,
  setHasMore: React.Dispatch<React.SetStateAction<boolean>>,
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  recordings: AudioRecording[]
) => {
  setIsLoading(true);
  try {
    const data = await api.listAudioRecordings(undefined, undefined, undefined, page, true);
    if (data && Array.isArray(data.results)) {
      setRecordings((prev: AudioRecording[]) =>
        page === 1 ? data.results : [...prev, ...data.results]
      );
      setHasMore(Boolean(data.next));
    } else if (Array.isArray(data)) {
      setRecordings(page === 1 ? data : [...recordings, ...data]);
      setHasMore(false);
    } else {
      setHasMore(false);
    }
  } catch (error) {
    setHasMore(false);
  } finally {
    setIsLoading(false);
  }
};

const TranscriptionHistoryPage: React.FC = () => {
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [openNewTranscriptionDrawer, setOpenNewTranscriptionDrawer] = React.useState(false);
  const [recordings, setRecordings] = useState<AudioRecording[]>([]);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const api = useApi();

  React.useEffect(() => {
    setRecordings([]);
    setPage(1);
    setHasMore(true);
  }, [api]);

  React.useEffect(() => {
    fetchData(api, page, setRecordings, setHasMore, setIsLoading, recordings);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, api, refreshFlag]);

  const handleAudioSaved = () => {
    setOpenNewTranscriptionDrawer(false);
    setRefreshFlag((f) => f + 1);
    setPage(1);
  };

  const listRef = React.useRef<HTMLDivElement>(null);
  React.useEffect(() => {
    const handleScroll = () => {
      const el = listRef.current;
      if (!el || isLoading || !hasMore) return;
      if (el.scrollHeight - el.scrollTop - el.clientHeight < 100) {
        setPage((p) => (hasMore && !isLoading ? p + 1 : p));
      }
    };
    const el = listRef.current;
    if (el) {
      el.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (el) el.removeEventListener("scroll", handleScroll);
    };
  }, [isLoading, hasMore]);

  const selectedRecording = recordings.find((r) => r.id === selectedId) || null;

  const handleSaveCorrection = async (id: string, corrected: string) => {
    setRecordings((recs) =>
      recs.map((r) => (r.id === id ? { ...r, transcription_corrected: corrected } : r))
    );
    try {
      await api.updateAudioRecording(id, { transcription_corrected: corrected });
    } catch (error) {
      setRecordings((recs) =>
        recs.map((r) =>
          r.id === id
            ? {
                ...r,
                transcription_corrected:
                  recs.find((rec) => rec.id === id)?.transcription_corrected || "",
              }
            : r
        )
      );
      console.error("Failed to save correction", error);
    }
  };

  const handleStatusChange = async (id: string, newStatus: string) => {
    setRecordings((recs) =>
      recs.map((r) =>
        r.id === id
          ? {
              ...r,
              status:
                newStatus as import("../../components/SpeechToText/StatusDropdown").AudioStatus,
            }
          : r
      )
    );
    try {
      await api.updateAudioRecording(id, { status: newStatus });
    } catch (error) {
      console.error("Failed to update status", error);
    }
  };

  return (
    <>
      <Global
        styles={{
          html: { height: "100vh", overflow: "hidden" },
          body: { height: "100vh", overflow: "hidden", margin: 0 },
          "#root": { height: "100vh", overflow: "hidden" },
        }}
      />
      <Box sx={{ width: "100%", maxWidth: "100vw", overflowX: "hidden", boxSizing: "border-box" }}>
        <Grid
          container
          sx={{
            height: "100vh",
            width: "100%",
            flexWrap: "nowrap",
            boxSizing: "border-box",
            maxWidth: "100vw",
            overflowX: "hidden",
          }}
        >
          <Grid
            ref={listRef}
            sx={{
              flex: "0 0 240px",
              width: 240,
              minWidth: 200,
              maxWidth: 400,
              borderRight: "1px solid #eee",
              height: "100vh",
              overflowY: "auto",
              overflowX: "hidden",
              boxSizing: "border-box",
            }}
          >
            <TranscriptionHistoryList
              recordings={recordings}
              selectedId={selectedId}
              onSelect={setSelectedId}
              isLoading={isLoading}
            />
          </Grid>
          <Grid
            sx={{
              flex: 1,
              minWidth: 0,
              width: "auto",
              height: "100vh",
              overflowY: "auto",
              overflowX: "hidden",
              boxSizing: "border-box",
            }}
          >
            <Box
              sx={{
                height: "100%",
                boxShadow: "sm",
                bgcolor: "background.body",
                borderRadius: 8,
                m: 2,
                p: 2,
                maxWidth: "100%",
                boxSizing: "border-box",
                overflowX: "hidden",
              }}
            >
              <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
                <Button
                  variant="solid"
                  color="primary"
                  onClick={() => setOpenNewTranscriptionDrawer(true)}
                >
                  Nouvelle transcription
                </Button>
              </Box>
              <Drawer
                anchor="right"
                open={openNewTranscriptionDrawer}
                onClose={() => setOpenNewTranscriptionDrawer(false)}
                sx={{
                  "& .MuiDrawer-paper": {
                    width: "clamp(360px, 25vw, 600px)",
                    boxSizing: "border-box",
                  },
                }}
              >
                <ModalClose />
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    height: "100%",
                    overflow: "auto",
                    p: 0,
                  }}
                >
                  <SpeechToTextPage
                    onAudioSaved={handleAudioSaved}
                    onClose={() => setOpenNewTranscriptionDrawer(false)}
                  />
                </Box>
              </Drawer>
              <TranscriptionDetailsPanel
                recording={selectedRecording}
                onSaveCorrection={handleSaveCorrection}
                onStatusChange={handleStatusChange}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default TranscriptionHistoryPage;
