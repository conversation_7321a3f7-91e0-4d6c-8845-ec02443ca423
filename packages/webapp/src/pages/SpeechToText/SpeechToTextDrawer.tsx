// Page principale pour la dictée vocale et la transcription
import React from "react";

import { Input } from "@mui/joy";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";
import Snackbar from "@mui/joy/Snackbar";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import PatientSelector from "../../components/Forms/PatientSelector";
import PatientCard from "../../components/PatientCard";
import {
  clearChunksFromDb,
  getChunksFromDb,
  listRecordingIdsInDb,
} from "../../components/SpeechToText/audioIndexedDb";
import AudioRecorder from "../../components/SpeechToText/AudioRecorder";
import ConsultationSelector from "../../components/SpeechToText/ConsultationSelector";
import SaveAudioButton from "../../components/SpeechToText/SaveAudioButton";
import { useApi } from "../../contexts/ApiContext";
import { useAuthenticatedUser } from "../../contexts/UserContext";
import { useAudioUpload } from "../../hooks/useAudioUpload";
import { Patient } from "../../models/types";

interface SpeechToTextDrawerProps {
  onAudioSaved?: () => void;
  initialConsultationId?: string | null;
  initialPatientId?: string | null;
  onClose?: () => void; // Garder onClose au cas où des boutons internes (Sauvegarder, Annuler) l'utiliseraient
  onRequestOpen?: () => void; // Callback to request the parent to open the drawer
}

export default function SpeechToTextDrawer({
  onAudioSaved,
  initialConsultationId = null,
  initialPatientId = null,
  onClose, // La prop onClose est toujours là, mais le bouton X spécifique à cette page est retiré
  onRequestOpen,
}: SpeechToTextDrawerProps) {
  const api = useApi();
  // Retrieve the authenticated user
  const { user } = useAuthenticatedUser();
  // States pour la gestion du workflow

  const [audioBlob, setAudioBlob] = React.useState<Blob | null>(null);
  const [title, setTitle] = React.useState<string>("");
  // Initialize state with prop, and it will be managed by the effect below
  const [consultationId, setConsultationId] = React.useState<string | null>(initialConsultationId);
  // State to store the full Patient object
  const [patient, setPatient] = React.useState<Patient | null>(null);
  const [isInitialLoadDone, setIsInitialLoadDone] = React.useState(false);

  const [showSnackbar, setShowSnackbar] = React.useState(false);
  const [audioResetKey, setAudioResetKey] = React.useState(0);
  const [resumeDialogOpen, setResumeDialogOpen] = React.useState(false);
  const [resumeInfo, setResumeInfo] = React.useState<{
    recordingId: string;
    lastChunkIndex: number;
    blob: Blob;
  } | null>(null);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { uploadAudio, loading: uploadLoading, error: uploadError } = useAudioUpload();

  // Ref to store the previous value of initialConsultationId prop
  const prevInitialConsultationIdRef = React.useRef<string | null | undefined>();

  // Update the ref after each render cycle so it holds the prop value for the *next* render's effect run
  React.useEffect(() => {
    prevInitialConsultationIdRef.current = initialConsultationId;
  }, [initialConsultationId]);

  // New useEffect to log consultationId when it changes (can be kept for debugging or removed if not needed)
  React.useEffect(() => {
    // console.log("Consultation ID changed to:", consultationId);
  }, [consultationId]);

  // Sync initialConsultationId prop to consultationId state
  React.useEffect(() => {
    const prevPropValue = prevInitialConsultationIdRef.current;

    if (initialConsultationId) {
      // Prop has a value, ensure state matches
      if (consultationId !== initialConsultationId) {
        setConsultationId(initialConsultationId);
      }
    } else {
      // Prop is null/undefined.
      // Only reset state if prop *changed* from a truthy value to null/undefined.
      // This prevents clearing user selection if prop was always null/undefined.
      if (prevPropValue && !initialConsultationId) {
        if (consultationId !== null) {
          // Avoid redundant set if already null
          setConsultationId(null);
        }
      }
    }
  }, [initialConsultationId, consultationId]);

  React.useEffect(() => {
    // Si le patient initial est fourni par ID, on ne cherche pas de brouillon pour éviter les conflits
    // Ou si une consultation initiale est fournie (car le patient sera dérivé de là)
    if (initialPatientId || initialConsultationId) return;

    async function checkForDraft() {
      const ids = await listRecordingIdsInDb();
      if (ids.length > 0) {
        const lastId = ids[ids.length - 1];
        const chunks = await getChunksFromDb(lastId);
        if (chunks.length > 0) {
          setResumeInfo({
            recordingId: lastId,
            lastChunkIndex: chunks.length - 1,
            blob: new Blob(chunks, { type: "audio/wav" }),
          });
          setResumeDialogOpen(true);
        }
      }
    }
    checkForDraft();
  }, [initialPatientId, initialConsultationId]);

  // useEffect for DATA LOADING based on `consultationId` (state) or `initialPatientId` (prop)
  React.useEffect(() => {
    // This function now loads data based on the current `consultationId` state or `initialPatientId` prop.
    // It no longer sets `consultationId` state directly; that's handled by the sync effect above.
    const loadDataInternal = async () => {
      // Use the `consultationId` from state, which should be synced with the `initialConsultationId` prop.
      const currentConsultationIdFromState = consultationId;
      setIsInitialLoadDone(false);
      let newPatient: Patient | null = null;
      // `consultationId` state is the source of truth now for which consultation to load.
      // `initialConsultationId` prop has already influenced `consultationId` state via the sync effect.

      if (currentConsultationIdFromState) {
        try {
          const consultationData = await api.getConsultation(currentConsultationIdFromState);
          // The consultation ID from state is confirmed by loading consultationData successfully.
          // No need to set loadedConsultationId here as state is already set.
          if (consultationData.zkf_patient) {
            let patientIdToLoad: string | null = null;
            if (typeof consultationData.zkf_patient === "string") {
              patientIdToLoad = consultationData.zkf_patient;
            } else if (
              consultationData.zkf_patient &&
              typeof consultationData.zkf_patient === "object" &&
              "id" in consultationData.zkf_patient
            ) {
              // Type assertion to access 'id', assuming it's a string. Adjust if 'id' has a different type.
              patientIdToLoad = (consultationData.zkf_patient as { id: string }).id;
            }

            if (patientIdToLoad) {
              newPatient = await api.getPatient(patientIdToLoad);
            } else {
              console.warn(
                "[SpeechToTextPage] zkf_patient is present but not a string or a valid object with an id."
              );
              // Fallback or further error handling if needed
              // If initialPatientId is available and different, consider using it as a fallback here too.
              if (initialPatientId) {
                newPatient = await api.getPatient(initialPatientId);
              }
            }
          } else {
            console.warn("[SpeechToTextPage] Consultation loaded, but zkf_patient is missing.");
            // Si initialConsultationId est là mais pas de patient, ET que initialPatientId n'est pas là non plus,
            // alors on ne charge pas de patient par défaut.
            if (initialPatientId) {
              newPatient = await api.getPatient(initialPatientId);
            }
          }
        } catch (error) {
          console.warn(
            "[SpeechToTextPage] Erreur lors du chargement de la consultation initiale ou du patient dérivé:",
            error
          );
          // Si erreur et qu'un patient initial était aussi demandé, tenter de le charger
        }
      }

      setPatient(newPatient);
      setIsInitialLoadDone(true);
    };

    // Only run loadDataInternal if we have a basis for loading (consultationId from state or initialPatientId from prop)
    if (consultationId || initialPatientId) {
      loadDataInternal();
    } else {
      // If both are null, ensure patient is null and load is "done" (as there's nothing to load)
      setPatient(null);
      setIsInitialLoadDone(true); // Consider if this should be false if nothing was ever attempted
    }
    // Dependencies: api, consultationId (state), initialPatientId (prop).
  }, [api, consultationId, initialPatientId]);

  return (
    // Box principal simplifié pour s'intégrer dans un Drawer/Modal
    <Stack
      sx={{
        width: "100%",
        height: "100%",
      }}
      gap={2}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography level="title-lg">Dictée et Transcription</Typography>
      </Box>

      <Stack gap={2}>
        <Typography id="document-info-title" level="title-md">
          Informations du document
        </Typography>
        <Stack spacing={2}>
          {/* PATIENT EN PREMIER */}
          {patient ? (
            <Box>
              <FormLabel sx={{ mb: 0.5 }}>Patient sélectionné</FormLabel>
              <PatientCard patient={patient} width="100%" hideButtons />
              {/* Bouton pour changer de patient, seulement si non fixé par props initiales */}
              {!(initialPatientId || initialConsultationId) && (
                <Button
                  variant="outlined"
                  color="neutral"
                  size="sm"
                  onClick={() => {
                    setPatient(null);
                    setConsultationId(null); // Réinitialiser la consultation aussi
                  }}
                  sx={{ mt: 1 }}
                  fullWidth
                >
                  Changer de patient
                </Button>
              )}
            </Box>
          ) : (
            <FormControl>
              <FormLabel>Patient</FormLabel>
              <PatientSelector
                value={""} // Patient is null here
                onSelect={(newlySelectedPatient: Patient | null) => {
                  // @ts-expect-error Persistent 'never' type issue with patient.id, possibly due to Patient type definition.
                  const currentPatientIdBeforeUpdate = patient?.id;
                  setPatient(newlySelectedPatient);

                  // Réinitialiser la consultation si le chargement initial est terminé
                  // ET que l'ID du patient a effectivement changé.
                  // Ou si le nouveau patient est null (désélection manuelle)
                  let newSelectedPatientId: string | undefined = undefined;
                  if (
                    newlySelectedPatient &&
                    typeof newlySelectedPatient === "object" &&
                    "id" in newlySelectedPatient
                  ) {
                    newSelectedPatientId = newlySelectedPatient.id as string; // Assumant que ID est string
                  }

                  if (
                    isInitialLoadDone &&
                    (currentPatientIdBeforeUpdate !== newSelectedPatientId || !newlySelectedPatient)
                  ) {
                    setConsultationId(null); // Réinitialiser la consultation si le patient change ou est désélectionné
                  }
                }}
                // Désactivé si le patient doit être chargé/dérivé automatiquement via props initiales
                // OU si une consultation initiale est fournie (car le patient sera dérivé de là)
                disabled={!!initialConsultationId || !!initialPatientId}
              />
            </FormControl>
          )}

          {/* CONSULTATION EN SECOND */}
          <Box>
            <FormLabel sx={{ mb: 1 }}>Consultation associée</FormLabel>
            <ConsultationSelector
              patientId={patient?.id || null} // Garder patientId pour filtrer si un patient EST déjà sélectionné
              value={consultationId}
              onChange={async (selectedConsultationId) => {
                setConsultationId(selectedConsultationId);
                if (selectedConsultationId) {
                  try {
                    const consultation = await api.getConsultation(selectedConsultationId);
                    // loadedConsultationId est déjà initialisé
                    if (consultation.zkf_patient) {
                      const selectedPatient = await api.getPatient(consultation.zkf_patient);
                      setPatient(selectedPatient); // Met à jour le patient, PatientCard se mettra à jour
                    } else if (!consultation.zkf_patient) {
                      // Si la consultation sélectionnée n'a pas de patient et qu'un patient était déjà là,
                      // on ne désélectionne pas le patient pour l'instant, cela pourrait être une action future.
                      // Ou, si initialPatientId n'est pas défini, on pourrait le déselectionner.
                      if (!initialPatientId && !initialConsultationId) {
                        // setPatient(null); // Optionnel: à discuter si on désélectionne le patient
                      }
                    }
                  } catch (error) {
                    console.error(
                      "[SpeechToTextPage] Erreur lors de la récupération de la consultation ou du patient associé:",
                      error
                    );
                    // Potentiellement désélectionner le patient si la consultation/patient ne peut être chargé
                    // setPatient(null);
                  }
                }
              }}
              // Le sélecteur est désactivé SEULEMENT si une consultation initiale est spécifiée via les props.
              // Sinon, il est toujours actif, permettant de choisir une consultation récente (si aucun patient n'est là)
              // ou une consultation du patient sélectionné.
              disabled={!!initialConsultationId}
            />
          </Box>

          <FormControl>
            <FormLabel>Titre de l'audio</FormLabel>
            <Input
              placeholder="Compte rendu de la consultation de M. Dupont..."
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              sx={{ width: "100%" }}
            />
          </FormControl>
        </Stack>
      </Stack>

      {/* Section pour l'enregistrement audio */}
      {/* Card simplifiée de la même manière */}
      <Stack gap={2}>
        <AudioRecorder
          onAudioRecorded={setAudioBlob}
          resetKey={audioResetKey}
          initialBlob={resumeInfo?.blob ?? null}
          resumeInfo={resumeInfo}
          patient={patient}
          consultationId={consultationId}
        />
        <SaveAudioButton
          audioBlob={audioBlob}
          patient={patient}
          title={title}
          consultationId={consultationId}
          auteur={user?.id ? String(user.id) : undefined}
          onSuccess={() => {
            setAudioBlob(null);
            setAudioResetKey((k) => k + 1);
            setTitle("");
            // setConsultationId(null); // Supprimé pour ne pas désélectionner la consultation
            if (onAudioSaved) {
              onAudioSaved();
            }
            if (!onAudioSaved) {
              setShowSnackbar(true);
            }
            if (onClose && !onAudioSaved) {
              onClose();
            }
          }}
          sx={{ mt: 2, width: "100%" }}
        />
      </Stack>

      {/* <MobileControls /> */}
      <Snackbar
        open={showSnackbar}
        autoHideDuration={2500}
        onClose={() => setShowSnackbar(false)}
        color="success"
        variant="soft"
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        Audio sauvegardé avec succès !
      </Snackbar>
      {/* Ensure the resume draft modal is always on top of any parent modal by setting a high zIndex */}
      <Modal
        open={resumeDialogOpen}
        onClose={() => setResumeDialogOpen(false)}
        sx={{ zIndex: 3000 }}
      >
        <ModalDialog sx={{ zIndex: 3100 }}>
          <Typography level="h4">Reprendre un enregistrement inachevé ?</Typography>
          <Typography sx={{ mb: 2 }}>
            Un enregistrement audio non finalisé a été retrouvé suite à un crash ou une fermeture de
            page.
            <br />
            Vous pouvez écouter ce qui a été sauvegardé, reprendre l'enregistrement, ou ignorer ce
            brouillon.
          </Typography>
          {resumeInfo?.blob && (
            <audio src={URL.createObjectURL(resumeInfo.blob)} controls style={{ width: "100%" }} />
          )}
          <div style={{ display: "flex", gap: 8, marginTop: 16 }}>
            <Button
              color="neutral"
              onClick={async () => {
                if (resumeInfo?.recordingId) {
                  await clearChunksFromDb(resumeInfo.recordingId);
                }
                setResumeDialogOpen(false);
                setResumeInfo(null);
                setAudioBlob(null);
                setAudioResetKey((k) => k + 1); // force le reset du composant AudioRecorder
              }}
            >
              Ignorer
            </Button>
            <Button
              color="primary"
              onClick={() => {
                setAudioBlob(resumeInfo?.blob ?? null);
                setResumeDialogOpen(false);
                onRequestOpen && onRequestOpen();
              }}
            >
              Reprendre l'enregistrement
            </Button>
          </div>
        </ModalDialog>
      </Modal>
    </Stack>
  );
}
