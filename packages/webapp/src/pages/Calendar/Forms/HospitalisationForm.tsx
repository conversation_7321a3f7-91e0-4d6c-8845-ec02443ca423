import dayjs from "dayjs";
import React from "react";

import Info from "@mui/icons-material/Info";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import Stack from "@mui/joy/Stack";
import Textarea from "@mui/joy/Textarea";
import ToggleButtonGroup from "@mui/joy/ToggleButtonGroup";
import Typography from "@mui/joy/Typography";

import OperationSelect from "../../../components/Forms/OperationSelect";
import HospitalisationServiceSelect from "../../../components/Forms/ServiceSelect";
import SpecialiteSelect from "../../../components/Forms/SpecialiteSelect";
import TagAutocomplete from "../../../components/Forms/TagAutocomplete";
import { useCurrentPatient } from "../../../contexts/CurrentPatientContext";
import { Hospitalisation } from "../../../models/types";

// Déclaration pour étendre le type Window
declare global {
  interface Window {
    operationFormValues?: {
      operation_start?: string;
      manualOperateur?: boolean;
    };
  }
}

interface HospitalisationFormProps {
  values: Partial<Hospitalisation>;
  setValues: (values: Partial<Hospitalisation>) => void;
}

export default function HospitalisationForm({ values, setValues }: HospitalisationFormProps) {
  const { currentPatient } = useCurrentPatient();

  // Cette variable est nécessaire pour le type radio button
  const [type, setType] = React.useState<"ambulatoire" | "veille" | "jour_meme" | "">("");

  // Le traitement de l'heure d'entrée a été déplacé vers CreateSlotDrawer.tsx
  // pour éviter les conflits et mieux centraliser la logique

  const handleTypeChange = (newType: "ambulatoire" | "veille" | "jour_meme" | "") => {
    if (newType === "ambulatoire") {
      const entree =
        type === "veille" && values.entree_date
          ? dayjs(values.entree_date).add(1, "days").format("YYYY-MM-DD")
          : values.entree_date;
      setValues({
        ...values,
        hospitalisation_ambulatoire: true,
        sortie_date: entree && dayjs(entree).format("YYYY-MM-DD"),
        entree_date: entree,
      });
    } else if (newType === "veille") {
      setValues({
        ...values,
        hospitalisation_ambulatoire: false,
        entree_date:
          values.entree_date && dayjs(values.entree_date).add(-1, "days").format("YYYY-MM-DD"),
        sortie_date: null,
      });
    } else if (newType === "jour_meme") {
      setValues({
        ...values,
        hospitalisation_ambulatoire: false,
        entree_date:
          type === "veille" && values.entree_date
            ? dayjs(values.entree_date).add(1, "days").format("YYYY-MM-DD")
            : values.entree_date,
        sortie_date: null,
      });
    } else {
      setValues({
        ...values,
        hospitalisation_ambulatoire: false,
        entree_date:
          type === "veille" && values.entree_date
            ? dayjs(values.entree_date).add(1, "days").format("YYYY-MM-DD")
            : values.entree_date,
        sortie_date: null,
      });
    }
    setType(newType);
  };

  return (
    <Stack gap={3} data-cy="hospitalisation-form">
      <Stack gap={2}>
        <Typography level="title-md">Dates</Typography>
        <Stack direction="row" gap={2}>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>
              Entrée<sup>*</sup>
            </FormLabel>
            <Input
              type="date"
              value={values.entree_date || ""}
              onChange={(e) =>
                setValues({
                  ...values,
                  entree_date: e.target.value,
                })
              }
            />
            {type === "ambulatoire" && values.entree_date && (
              <Typography level="body-sm" sx={{ mt: 1, fontStyle: "italic" }}>
                Sortie: {dayjs(values.entree_date).format("DD/MM/YYYY")}
              </Typography>
            )}
          </FormControl>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>Mode d'entrée</FormLabel>
            <ToggleButtonGroup
              value={type}
              onChange={(e, newMode) =>
                handleTypeChange(newMode as "ambulatoire" | "veille" | "jour_meme" | "")
              }
              defaultValue=""
              sx={{ width: "100%" }}
            >
              <Button variant="soft" key="jour_meme" value="jour_meme" sx={{ flex: 1 }}>
                Jour même
              </Button>
              <Button variant="soft" key="ambulatoire" value="ambulatoire" sx={{ flex: 1 }}>
                Ambulatoire
              </Button>
              <Button variant="soft" key="veille" value="veille" sx={{ flex: 1 }}>
                Veille
              </Button>
            </ToggleButtonGroup>
          </FormControl>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>Heure d'entrée</FormLabel>
            <Input
              type="time"
              value={values.entree_heure || ""}
              onChange={(e) =>
                setValues({
                  ...values,
                  entree_heure: e.target.value,
                })
              }
            />
          </FormControl>
        </Stack>
      </Stack>

      <Stack gap={2}>
        <Typography level="title-md">Service et spécialité</Typography>
        <Stack direction="row" gap={2}>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>
              Service<sup>*</sup>
            </FormLabel>
            <Box sx={{ maxWidth: "275px", overflow: "hidden" }}>
              <HospitalisationServiceSelect
                value={values.service_hospitalisation || null}
                onSelect={(service) =>
                  setValues({ ...values, service_hospitalisation: service.id })
                }
              />
            </Box>
          </FormControl>
          <FormControl
            sx={{ flex: 1 }}
            style={{ display: values.specialite_referente ? "none" : "block" }}
          >
            <FormLabel>
              Spécialité<sup>*</sup>
            </FormLabel>
            <SpecialiteSelect
              value={values.specialite_referente || null}
              onSelect={(spe) => setValues({ ...values, specialite_referente: spe?.id })}
            />
          </FormControl>
        </Stack>
      </Stack>

      {/* Champ médecin référent caché car défini par le "docteur" */}
      <input
        type="hidden"
        value={values.medecin_referent ? JSON.stringify(values.medecin_referent) : ""}
        data-cy="medecin-referent-hidden"
      />

      <Stack gap={2}>
        <Typography level="title-md">Détails</Typography>
        <Stack direction="row" gap={2}>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>Motif hospitalisation</FormLabel>
            <Textarea
              minRows={2}
              maxRows={4}
              value={values.motif_hospitalisation || ""}
              onChange={(e) => setValues({ ...values, motif_hospitalisation: e.target.value })}
            />
          </FormControl>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>Tags</FormLabel>
            <TagAutocomplete
              value={values.tags || []}
              onChange={(newValue) => setValues({ ...values, tags: newValue })}
            />
            <Typography sx={{ mt: 0.5 }} level="body-xs" startDecorator={<Info />}>
              Tapez le nom d'un tag, puis appuyez sur entrée pour l'ajouter
            </Typography>
          </FormControl>
        </Stack>
      </Stack>

      <FormControl>
        <FormLabel>Opérations liées</FormLabel>
        {currentPatient && (
          <OperationSelect
            patientId={currentPatient.id}
            value={(values.operation_liee as string[]) || []}
            onChange={(newValue) => {
              setValues({ ...values, operation_liee: newValue });
            }}
          />
        )}
      </FormControl>
    </Stack>
  );
}
