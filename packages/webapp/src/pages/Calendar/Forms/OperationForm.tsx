import { useState } from "react";

import Info from "@mui/icons-material/Info";
import Button from "@mui/joy/Button";
import Checkbox from "@mui/joy/Checkbox";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import Stack from "@mui/joy/Stack";
import Textarea from "@mui/joy/Textarea";
import Typography from "@mui/joy/Typography";

import IntervenantSelect from "../../../components/Forms/IntervenantSelect";
import ProtocoleSelect from "../../../components/Forms/ProtocoleSelect";
import TagAutocomplete from "../../../components/Forms/TagAutocomplete";
import { Intervenant, Operation } from "../../../models/types";

interface OperationFormProps {
  values: Partial<Operation>;
  setValues: (values: Partial<Operation>) => void;
}

export default function OperationForm({ values, setValues }: OperationFormProps) {
  const [showOperateurSelect, setShowOperateurSelect] = useState(false);

  // const removeOperateur = (operateurId: string) => {
  //   const newOperateurs = (values.operateur_new || []).filter(op =>
  //     typeof op === 'string' ? op !== operateurId : op.id !== operateurId
  //   );
  //   setValues({ ...values, operateur_new: newOperateurs });
  // };

  const addOperateurs = (newOperateurs: Intervenant["id"][]) => {
    const existingOperateurs = values.operateur_new || [];
    const combinedOperateurs = [
      ...existingOperateurs,
      ...newOperateurs.filter((id) => !existingOperateurs.includes(id)),
    ];
    setValues({ ...values, operateur_new: combinedOperateurs });
    setShowOperateurSelect(false);
  };

  // Marquer comme modifié manuellement pour éviter la mise à jour automatique
  const handleOperateurChange = (newValue: Intervenant["id"][]) => {
    // Utiliser un événement personnalisé pour communiquer avec CreateSlotDrawer
    const event = new CustomEvent("operateur_modified", { detail: { modified: true } });
    window.dispatchEvent(event);

    setValues({ ...values, operateur_new: newValue });
  };

  return (
    <Stack gap={3} data-cy="operation-form">
      <Stack gap={2}>
        <Typography level="title-md">Informations générales</Typography>

        {/* Titre */}
        <FormControl>
          <FormLabel>
            Titre<sup>*</sup>
          </FormLabel>
          <Input
            value={values.titre_operation || ""}
            onChange={(e) => {
              // Créer une copie pour éviter les boucles de mise à jour
              const newValues = { ...values };
              newValues.titre_operation = e.target.value;
              setValues(newValues);
            }}
          />
        </FormControl>

        {/* Indication opératoire sous le titre */}
        <FormControl>
          <FormLabel>Indication opératoire</FormLabel>
          <Textarea
            minRows={2}
            maxRows={4}
            value={values.indication_operatoire || ""}
            onChange={(e) => {
              // Créer une copie pour éviter les boucles de mise à jour
              const newValues = { ...values };
              newValues.indication_operatoire = e.target.value;
              setValues(newValues);
            }}
          />
        </FormControl>

        {/* Date, heure début et fin + commentaire à droite */}
        <Stack direction="row" gap={2}>
          <Stack gap={2} sx={{ flex: 1 }}>
            <Stack direction="row" gap={2} alignItems="end">
              <FormControl sx={{ flex: 2 }}>
                <FormLabel sx={{ fontSize: "0.875rem" }}>
                  Date<sup>*</sup>
                </FormLabel>
                <Input
                  type="date"
                  value={values.operation_date || ""}
                  onChange={(e) => setValues({ ...values, operation_date: e.target.value })}
                />
              </FormControl>
              {/* Checkbox urgence à côté de date */}
              <FormControl sx={{ flex: 1 }}>
                <Typography
                  level="body-sm"
                  startDecorator={
                    <Checkbox
                      checked={values.urgence === "urgence"}
                      onChange={(e) => {
                        e.target.checked
                          ? setValues({ ...values, urgence: "urgence" })
                          : setValues({ ...values, urgence: "programme" });
                      }}
                    />
                  }
                >
                  Urgence
                </Typography>
              </FormControl>
            </Stack>
            <Stack direction="row" gap={2}>
              <FormControl sx={{ flex: 1 }}>
                <FormLabel>
                  Début<sup>*</sup>
                </FormLabel>
                <Input
                  type="time"
                  value={values.operation_start || ""}
                  onChange={(e) => setValues({ ...values, operation_start: e.target.value })}
                />
              </FormControl>
              <FormControl sx={{ flex: 1 }}>
                <FormLabel>Fin</FormLabel>
                <Input
                  type="time"
                  value={values.operation_end || ""}
                  onChange={(e) => setValues({ ...values, operation_end: e.target.value })}
                />
              </FormControl>
            </Stack>
          </Stack>

          <FormControl sx={{ flex: 1 }}>
            <FormLabel>Commentaire pre-opératoire</FormLabel>
            <Textarea
              minRows={4}
              maxRows={6}
              value={values.commentaire_programmation_operation}
              onChange={(e) =>
                setValues({
                  ...values,
                  commentaire_programmation_operation: e.target.value,
                })
              }
            />
          </FormControl>
        </Stack>
      </Stack>

      {/* Opérateurs avec affichage sous forme de titre + chips */}
      <Stack gap={2}>
        {/* <Stack direction="row" alignItems="center" gap={1}>
          <Typography level="title-sm">Opérateur(s)</Typography>
          <Button
            size="sm"
            variant="outlined"
            startDecorator={<Add />}
            onClick={() => setShowOperateurSelect(true)}
          >
            Ajouter
          </Button>
        </Stack> */}

        {/* Affichage des opérateurs sélectionnés */}
        {/* {values.operateur_new && values.operateur_new.length > 0 && (
          <Stack direction="row" gap={1} flexWrap="wrap">
            {values.operateur_new.map((operateur) => (
              <Chip
                key={typeof operateur === 'string' ? operateur : operateur.id}
                variant="soft"
                endDecorator={
                  <Close
                    sx={{ cursor: 'pointer', fontSize: '16px' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      removeOperateur(typeof operateur === 'string' ? operateur : operateur.id);
                    }}
                  />
                }
              >
                {typeof operateur === 'string' 
                  ? `Opérateur ${operateur}` 
                  : `${operateur.nom} ${operateur.prenom}`
                }
              </Chip>
            ))}
          </Stack>
        )} */}

        <FormControl sx={{ flex: 1 }}>
          <FormLabel>Operateurs</FormLabel>
          <IntervenantSelect
            value={values.operateur_new || []}
            onChange={handleOperateurChange}
            initValueWithUser={values.operateur_new?.length === 0}
            // if values.operateur_new is not empty, don't init with user
          />
        </FormControl>

        {/* Sélecteur d'opérateurs (affiché conditionnellement) */}
        {showOperateurSelect && (
          <Stack gap={2}>
            <IntervenantSelect
              value={[]}
              onChange={(newValue: Intervenant["id"][]) => addOperateurs(newValue)}
              initValueWithUser={false}
            />
            <Button size="sm" variant="outlined" onClick={() => setShowOperateurSelect(false)}>
              Annuler
            </Button>
          </Stack>
        )}
      </Stack>

      {/* Tags et Protocole */}
      <Stack gap={2}>
        <Typography level="title-md">Tags et Protocole</Typography>
        <Stack direction="row" gap={2}>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>Tags</FormLabel>
            <TagAutocomplete
              value={values.tags || []}
              onChange={(newValue: string[]) => setValues({ ...values, tags: newValue })}
            />
            <Typography sx={{ mt: 0.5 }} level="body-xs" startDecorator={<Info />}>
              Tapez le nom d'un tag, puis appuyez sur entrée pour l'ajouter
            </Typography>
          </FormControl>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>Protocole</FormLabel>
            <ProtocoleSelect
              value={values.protocoles || []}
              onChange={(ids: string[]) =>
                setValues({
                  ...values,
                  protocoles: ids,
                })
              }
            />
          </FormControl>
        </Stack>
      </Stack>
    </Stack>
  );
}
