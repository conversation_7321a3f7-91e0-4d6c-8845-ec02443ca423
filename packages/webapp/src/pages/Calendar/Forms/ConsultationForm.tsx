import React from "react";

import Draw from "@mui/icons-material/Draw";
import Checkbox from "@mui/joy/Checkbox";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import Stack from "@mui/joy/Stack";
import Textarea from "@mui/joy/Textarea";
import Typography from "@mui/joy/Typography";

import OperationSelect from "../../../components/Forms/OperationSelect";
import SiteAndLieuSelect from "../../../components/Forms/SiteAndLieuSelect";
import TimeInput from "../../../components/Forms/TimeInput";
import { Consultation, Operation, UserSites, UserSitesLieux } from "../../../models/types";

interface ConsultationFormProps {
  values: Partial<Consultation>;
  setValues: React.Dispatch<React.SetStateAction<Partial<Consultation>>>;
}

export default function ConsultationForm({ values, setValues }: ConsultationFormProps) {
  const [date, setDate] = React.useState<string>(values.consultation_date || "");
  const [start, setStart] = React.useState<string>(values.consultation_debut_prevu || "");
  const [end, setEnd] = React.useState<string>(values.consultation_fin_prevu || "");
  const [mode, setMode] = React.useState<Consultation["mode_consultation"]>(
    values.mode_consultation || "urgent"
  );
  const [siteId, setSiteId] = React.useState<UserSites["id"] | null>(values.zkf_site || null);
  const [placeId, setPlaceId] = React.useState<UserSitesLieux["id"] | null>(
    values.zkf_lieu || null
  );
  const [description, setDescription] = React.useState<string>(values.motif_consultation || "");

  React.useEffect(() => {
    setValues((prev) => ({
      ...prev,
      consultation_date: date,
      consultation_debut_prevu: start,
      consultation_fin_prevu: end,
      mode_consultation: mode,
      motif_consultation: description,
      zkf_site: siteId || undefined,
      zkf_lieu: placeId || undefined,
    }));
  }, [mode, siteId, placeId, description, setValues, date, start, end]);

  return (
    <Stack gap={3} data-cy="consultation-form">
      <Stack direction="row" gap={2} alignItems="center">
        <FormControl sx={{ flex: 1 }}>
          <FormLabel>
            Date<sup>*</sup>
          </FormLabel>
          <Input type="date" value={date} onChange={(e) => setDate(e.target.value)} />
        </FormControl>
        <Stack direction="row" sx={{ flex: 1 }} gap={1}>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>
              Début<sup>*</sup>
            </FormLabel>
            <TimeInput value={start} onChange={(value) => setStart(value)} />
          </FormControl>
          <FormControl sx={{ flex: 1 }}>
            <FormLabel>&nbsp;</FormLabel>
            <TimeInput value={end} onChange={(value) => setEnd(value)} showIncrementButtons />
          </FormControl>
        </Stack>
      </Stack>
      <FormControl>
        <Typography
          level="title-sm"
          startDecorator={
            <Checkbox
              checked={mode === "urgent"}
              onChange={(e) => {
                e.target.checked ? setMode("urgent") : setMode("programme");
              }}
            />
          }
        >
          Urgence
        </Typography>
      </FormControl>

      <SiteAndLieuSelect
        title={
          <>
            Site et lieu de consultation<sup>*</sup>
          </>
        }
        site={siteId}
        lieu={placeId}
        onSiteChange={setSiteId}
        onLieuChange={setPlaceId}
      />

      <FormControl>
        <FormLabel>
          <Draw /> Motif<sup>*</sup>
        </FormLabel>
        <Textarea
          minRows={4}
          maxRows={4}
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </FormControl>

      <FormControl>
        <FormLabel>Opérations liées</FormLabel>
        <OperationSelect
          patientId={values.zkf_patient || ""}
          value={values.operation_liee || []}
          onChange={(newValue: Operation["id"][]) =>
            setValues({ ...values, operation_liee: newValue })
          }
        />
      </FormControl>
    </Stack>
  );
}
