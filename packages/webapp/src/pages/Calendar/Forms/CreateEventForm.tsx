import moment from "moment";
import React from "react";

import Delete from "@mui/icons-material/Delete";
import { Input, Stack, Textarea, Typography } from "@mui/joy";
import Button from "@mui/joy/Button";
import Skeleton from "@mui/joy/Skeleton";

import EventCategorySelect from "../../../components/Forms/EventCategorySelect";
import UserPicker from "../../../components/Forms/UserPicker";
import ModalComponent from "../../../components/Modals/Modal";
import { useApi } from "../../../contexts/ApiContext";
import { useSnackbar } from "../../../contexts/SnackbarContext";
import { useUser } from "../../../contexts/UserContext";

interface CreateEventFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (id?: string) => void;
  startDate: string;
  eventId?: string;
}

export default function CreateEventForm({
  open,
  onClose,
  startDate,
  onSave,
  eventId,
}: CreateEventFormProps) {
  const api = useApi();
  const { show } = useSnackbar();
  const { user } = useUser();
  const [title, setTitle] = React.useState<string>("");
  const [note, setNote] = React.useState<string>("");
  const [start, setStart] = React.useState<string>(moment(startDate).format("YYYY-MM-DDTHH:mm"));
  const [end, setEnd] = React.useState<string>(
    startDate && moment(startDate).add(15, "minutes").format("YYYY-MM-DDTHH:mm")
  );
  const [category, setCategory] = React.useState<string>("");
  const [users, setUsers] = React.useState<number[]>(user ? [user.id] : []);
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    if (!open) {
      setTitle("");
      setNote("");
      setStart("");
      setEnd("");
      setCategory("");
      setUsers(user ? [user.id] : []);
    } else {
      if (eventId) {
        setLoading(true);
        api
          .getEvent(eventId)
          .then((res) => {
            setTitle(res.event_title || "");
            setNote(res.event_note || "");
            setStart(moment(res.event_start).format("YYYY-MM-DDTHH:mm") || "");
            setEnd(moment(res.event_end).format("YYYY-MM-DDTHH:mm") || "");
            setCategory(res.event_category || "");
            setUsers(res.users || []);
          })
          .finally(() => setLoading(false));
      }
    }
  }, [api, eventId, open, user]);

  React.useEffect(() => {
    if (startDate) {
      setStart(moment(startDate).format("YYYY-MM-DDTHH:mm"));
      setEnd(moment(startDate).add(15, "minutes").format("YYYY-MM-DDTHH:mm"));
    }
  }, [startDate]);

  const handleSave = () => {
    if (eventId) {
      api
        .updateEvent({
          id: eventId,
          event_title: title,
          event_note: note,
          event_start: moment(start).utc().toISOString(),
          event_end: moment(end).utc().toISOString(),
          event_category: category,
          users,
        })
        .then((res) => {
          show("L'événement a été modifié avec succés", "success");
          onSave(res.id);
          onClose();
        })
        .catch(() => show("Erreur lors de la modification de l'événement", "danger"));
    } else {
      api
        .createEvent({
          event_title: title,
          event_note: note,
          event_start: moment(start).utc().toISOString(),
          event_end: moment(end).utc().toISOString(),
          event_category: category,
          users,
        })
        .then((res) => {
          show("L'événement a été ajouté avec succes", "success");
          onSave(res.id);
          onClose();
        })
        .catch(() => show("Erreur lors de l'enregistrement de l'événement", "danger"));
    }
  };

  const handleDelete = () => {
    eventId &&
      api
        .deleteEvent(eventId)
        .then(() => {
          show("L'événement a été supprimé avec succes", "success");
          onSave();
          onClose();
        })
        .catch(() => show("Erreur lors de la suppression de l'événement", "danger"));
  };

  return (
    <ModalComponent
      open={open}
      title={`${!eventId ? "Ajouter" : "Modifier"} un événement`}
      onClose={onClose}
      validateLabel={"Enregistrer"}
      onValidate={handleSave}
      canValidate={!!title && !!start && !!end}
      closeLabel={eventId && "Close"}
      style={{ minWidth: "445px", minHeight: "490px" }}
      leftButton={
        eventId && (
          <Button
            variant={"plain"}
            color={"danger"}
            startDecorator={<Delete />}
            onClick={handleDelete}
          >
            Supprimer
          </Button>
        )
      }
    >
      <Skeleton loading={loading} height={"350px"} width={"400px"}>
        <Stack direction="column" gap={2}>
          <Stack direction="column" gap={0.5}>
            <Typography level="title-md">Titre</Typography>
            <Input value={title} onChange={(e) => setTitle(e.target.value)} />
          </Stack>
          <Stack direction="column" gap={0.5}>
            <Typography level="title-md">Note</Typography>
            <Textarea
              value={note}
              onChange={(e) => setNote(e.target.value)}
              minRows={3}
              maxRows={3}
            />
          </Stack>
          <Stack direction="row" gap={2}>
            <Stack direction="column" gap={0.5}>
              <Typography level="title-md">Début</Typography>
              <Input
                type="datetime-local"
                value={start}
                onChange={(e) => {
                  setStart(e.target.value);
                  setEnd(e.target.value); // Synchronise la date de fin avec la date de début
                }}
              />
            </Stack>
            <Stack direction="column" gap={0.5}>
              <Typography level="title-md">Fin</Typography>
              <Input type="datetime-local" value={end} onChange={(e) => setEnd(e.target.value)} />
            </Stack>
          </Stack>
          <Stack direction="column" gap={0.5}>
            <Typography level="title-md">Categorie</Typography>
            <EventCategorySelect value={category} onChange={setCategory} />
          </Stack>
          <Stack direction="column" gap={0.5}>
            <Typography level="title-md">Personnes</Typography>
            <UserPicker value={users} onChange={setUsers} />
          </Stack>
        </Stack>
      </Skeleton>
    </ModalComponent>
  );
}
