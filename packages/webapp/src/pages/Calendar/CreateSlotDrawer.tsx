import dayjs from "dayjs";
import React from "react";

import { LocalHospital } from "@mui/icons-material";
import Add from "@mui/icons-material/Add";
import Delete from "@mui/icons-material/Delete";
import Masks from "@mui/icons-material/Masks";
import PeopleAlt from "@mui/icons-material/PeopleAlt";
import Button from "@mui/joy/Button";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import ToggleButtonGroup from "@mui/joy/ToggleButtonGroup";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import CurrentPatientManager from "../../components/CurrentPatientManager";
import CustomDrawer from "../../components/CustomDrawer";
import DoctorSelect from "../../components/Forms/DoctorSelect";
import { useApi } from "../../contexts/ApiContext";
import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { useUser } from "../../contexts/UserContext";
import { Consultation, Hospitalisation, Operation } from "../../models/types";
import { router } from "../../router";
import { Mode } from "../PatientPage/Consultation/CreateConsultation";
import ConsultationForm from "./Forms/ConsultationForm";
import HospitalisationForm from "./Forms/HospitalisationForm";
import OperationForm from "./Forms/OperationForm";

// Pas besoin de déclarer l'interface Window ici car elle est déjà déclarée dans HospitalisationForm.tsx

const radioValues = [
  { label: "Consultation", value: "consultation", icon: <PeopleAlt /> },
  {
    label: "Hospitalisation & Opération",
    value: "hospitalisation",
    icon: <Masks />,
  },
];

interface CreateSlotDrawerProps {
  initialDate?: string;
  initialStart?: string;
  initialEnd?: string;
  onSave: (id?: string) => void;
  open: boolean;
  onClose: () => void;
  initialType?: "consultation" | "operation" | "hospitalisation";
  initialSite?: string;
  initialUser?: number;
  redirectOnCreate?: boolean;
  forcedType?: "consultation" | "hospitalisation" | null;
  forcedCurrentPatient?: boolean;
}

const isMandatoryFieldValid = (field: any): boolean =>
  !(field === undefined || field === null || field === "");

export default function CreateSlotDrawer({
  initialDate,
  initialStart,
  initialEnd,
  onSave,
  open,
  onClose,
  initialType = "consultation",
  initialSite,
  initialUser,
  redirectOnCreate = true,
  forcedType = null,
  forcedCurrentPatient = false,
}: CreateSlotDrawerProps) {
  const api = useApi();
  const { user } = useUser();
  const snackbar = useSnackbar();
  const { currentPatient } = useCurrentPatient();

  const [loading, setLoading] = React.useState<boolean>(false);
  const [type, setType] = React.useState<"consultation" | "operation" | "hospitalisation">(
    forcedType ? forcedType : initialType ? initialType : "consultation"
  ); // if forcedType is set, use it, otherwise use initialType, otherwise use consultation
  const [selectedDr, setSelectedDr] = React.useState<number | null>(
    initialUser || user?.id || null
  );
  const [consultationFormValues, setConsultationFormValues] = React.useState<Partial<Consultation>>(
    {
      mode_consultation: Mode.PROGRAMME,
      consultation_date: initialDate,
      consultation_debut_prevu: initialStart,
      consultation_fin_prevu: initialEnd,
      zkf_site: initialSite || "",
      zkf_lieu: "",
      motif_consultation: "",
      operation_liee: [],
    }
  );
  const [operationFormValues, setOperationFormValues] = React.useState<Partial<Operation> | null>({
    operateur_new: [], // matcher zkf_user avec selectedDr sinon laisser vide
    zkf_patient: currentPatient?.id,
    zkf_redacteur: selectedDr,
    operation_start: initialStart,
    operation_end: initialEnd,
  });
  const [hospitalisationFormValues, setHospitalisationFormValues] = React.useState<
    Partial<Hospitalisation>
  >({
    zkf_patient: currentPatient?.id,
    entree_date: initialDate ? dayjs(initialDate).format("YYYY-MM-DD") : null,
    entree_heure: initialStart,
    operation_liee: [],
  });

  React.useEffect(() => {
    // update forms when currentPatient changes
    if (currentPatient) {
      setConsultationFormValues((prev) => ({
        ...prev,
        zkf_patient: currentPatient.id,
      }));
      setOperationFormValues((prev) => ({
        ...prev,
        zkf_patient: currentPatient.id,
      }));
      setHospitalisationFormValues((prev) => ({
        ...prev,
        zkf_patient: currentPatient.id,
      }));
    }
  }, [currentPatient]);

  // Synchroniser operation_date avec entree_date et définir l'heure d'entrée
  const operationFormRef = React.useRef(operationFormValues);
  const lastEntreeHeureRef = React.useRef(hospitalisationFormValues.entree_heure);
  // Garder une trace des opérateurs sélectionnés manuellement
  const manualOperateursRef = React.useRef<boolean>(false);

  // Écouter l'événement de modification manuelle d'opérateur
  React.useEffect(() => {
    const handleOperateurModified = (event: Event) => {
      // Marquer comme modifié manuellement
      manualOperateursRef.current = true;
    };

    window.addEventListener("operateur_modified", handleOperateurModified);

    return () => {
      window.removeEventListener("operateur_modified", handleOperateurModified);
    };
  }, []);

  React.useEffect(() => {
    operationFormRef.current = operationFormValues;

    if (operationFormValues) {
      // Synchroniser uniquement si operation_date est vide et entree_date existe
      if (
        operationFormValues &&
        !operationFormValues.operation_date &&
        hospitalisationFormValues.entree_date
      ) {
        setOperationFormValues((prev) => {
          if (prev?.operation_date === hospitalisationFormValues.entree_date) {
            return prev; // Pas de changement nécessaire
          }
          return {
            ...prev,
            operation_date: hospitalisationFormValues.entree_date,
          };
        });
      }

      // Définir l'heure d'entrée en fonction de l'heure de l'opération
      if (operationFormValues.operation_start && hospitalisationFormValues.entree_date) {
        const operationHour = parseInt(operationFormValues.operation_start.split(":")[0], 10);
        let newEntreeHeure = "";

        // Déterminer le type d'entrée
        const isVeille =
          hospitalisationFormValues.entree_date &&
          operationFormValues.operation_date &&
          dayjs(hospitalisationFormValues.entree_date).isBefore(
            dayjs(operationFormValues.operation_date),
            "day"
          );

        if (isVeille) {
          // Si date d'entrée = veille de l'opération, heure d'entrée = 17h
          newEntreeHeure = "17:00";
        } else if (operationHour < 14) {
          // Si bloc avant 14h, entrée à 7h
          newEntreeHeure = "07:00";
        } else {
          // Si bloc après 14h, entrée à 12h
          newEntreeHeure = "12:00";
        }

        // Ne mettre à jour que si l'heure a changé
        if (lastEntreeHeureRef.current !== newEntreeHeure) {
          lastEntreeHeureRef.current = newEntreeHeure;
          setHospitalisationFormValues((prev) => ({
            ...prev,
            entree_heure: newEntreeHeure,
          }));
        }
      }
    }
  }, [operationFormValues, hospitalisationFormValues]);

  // Stockage des intervenants pour éviter des appels répétés à l'API
  const [docteurIntervenants, setDocteurIntervenants] = React.useState<{ [key: string]: any }>({});
  const intervenantsLoadedRef = React.useRef(false);

  // Créons un wrapper simple pour la fonction setOperationFormValues
  const updateOperationForm = (
    values:
      | Partial<Operation>
      | null
      | ((prev: Partial<Operation> | null) => Partial<Operation> | null)
  ) => {
    // Si c'est une fonction, on la passe directement
    if (typeof values === "function") {
      setOperationFormValues(values);
    }
    // Si c'est null, on efface les valeurs
    else if (values === null) {
      setOperationFormValues(null);
    }
    // Sinon on passe les valeurs normalement
    else {
      setOperationFormValues(values);
    }
  };

  // Charger les intervenants une seule fois
  React.useEffect(() => {
    if (!intervenantsLoadedRef.current) {
      intervenantsLoadedRef.current = true;
      api
        .getUserIntervenantsEvents()
        .then((intervenants) => {
          if (intervenants && intervenants.Operateur) {
            // Créer une map pour accéder rapidement aux intervenants par user ID
            const intervenantsMap: { [key: string]: any } = {};
            intervenants.Operateur.forEach((intervenant) => {
              if (intervenant.zkf_user) {
                intervenantsMap[intervenant.zkf_user] = intervenant;
              }
            });
            setDocteurIntervenants(intervenantsMap);
          }
        })
        .catch((error) => console.error("Erreur lors de la récupération des intervenants:", error));
    }
  }, [api]);

  React.useEffect(() => {
    // change medecin_referent when selectedDr changes
    if (selectedDr) {
      setHospitalisationFormValues((prev) => ({
        ...prev,
        medecin_referent: [selectedDr],
      }));

      // Mettre à jour l'intervenant (opérateur) lorsque le docteur change
      if (operationFormValues && Object.keys(docteurIntervenants).length > 0) {
        const docteurIntervenant = docteurIntervenants[selectedDr];

        // Vérifier si une modification manuelle a été faite via la référence
        if (docteurIntervenant && !manualOperateursRef.current) {
          // Mettre à jour l'opérateur uniquement si aucune modification manuelle n'a été faite
          // Cela permet à l'utilisateur de le modifier manuellement par la suite
          setOperationFormValues({
            ...operationFormValues,
            operateur_new: [docteurIntervenant.id],
          });
        }
      }
    }
  }, [selectedDr, docteurIntervenants, operationFormValues]);

  const checkAndSave = async () => {
    if (type === "consultation") {
      setLoading(true);
      try {
        const consultation = await api?.createConsultation({
          ...consultationFormValues,
          zkf_patient: currentPatient?.id,
          tags: [],
          zkf_redacteur: selectedDr,
          operation_liee: consultationFormValues.operation_liee || [],
        });

        snackbar.show("Consultation créée avec succès", "success");

        onSave(consultation.id);
        onClose();
        if (redirectOnCreate) {
          router.navigate(`/consultation/${consultation.id}`);
        }
      } catch (error) {
        console.error("Error saving consultation:", error);
        snackbar.show("Erreur lors de la création de la consultation", "danger");
      } finally {
        setLoading(false);
      }
    } else if (type === "hospitalisation") {
      // first create hospitalisation
      try {
        setLoading(true);
        const hospitalisation = await api.createHospitalisation({
          ...hospitalisationFormValues,
          zkf_redacteur: selectedDr,
          sortie_date:
            hospitalisationFormValues.hospitalisation_ambulatoire ||
            hospitalisationFormValues.sortie_date === ""
              ? null
              : hospitalisationFormValues.sortie_date, // if sortie_date is empty string, set it to null
        });
        // check if we're adding an operation as well, if operationFormValues is not null
        let ope: Operation | undefined = undefined;
        if (operationFormValues !== null) {
          const start = dayjs(
            `${operationFormValues.operation_date} ${operationFormValues.operation_start}`
          );
          const endDate =
            !operationFormValues.operation_end ||
            (operationFormValues.operation_start || "") < operationFormValues.operation_end
              ? operationFormValues.operation_date
              : dayjs(operationFormValues.operation_date).add(1, "days").format("YYYY-MM-DD");
          const end = operationFormValues.operation_end
            ? dayjs(`${endDate} ${operationFormValues.operation_end}`)
            : start.add(1, "hour");
          const payload: Partial<Operation> = {
            ...operationFormValues,
            zkf_hospitalisation: hospitalisation.id,
            zkf_redacteur: selectedDr,
            operation_start: start.toISOString(),
            operation_end: end.toISOString(),
          };
          ope = await api?.createOperation(payload);
        }
        onSave(ope?.id);
        onClose();
        snackbar.show("Événement créée avec succès", "success");
        if (redirectOnCreate) {
          if (ope) {
            router.navigate(`/operation/${ope.id}`);
          } else {
            router.navigate(`/hospitalisation/${hospitalisation.id}`);
          }
        }
      } catch (error) {
        console.error("Error saving operation:", error);
        snackbar.show("Erreur lors de la création de l'opération", "danger");
      } finally {
        setLoading(false);
      }
    }
  };

  const initOperationFormValues = () => {
    updateOperationForm({
      zkf_patient: currentPatient?.id,
      tags: [],
      operation_date: initialDate || "",
      operation_start: initialStart || "",
      operation_end: initialEnd || "",
    });
  };

  const canSave = () => {
    if (!selectedDr) {
      return false;
    }
    if (type === "consultation") {
      return (
        currentPatient !== null &&
        isMandatoryFieldValid(consultationFormValues.consultation_date) &&
        isMandatoryFieldValid(consultationFormValues.consultation_debut_prevu) &&
        isMandatoryFieldValid(consultationFormValues.zkf_site) &&
        isMandatoryFieldValid(consultationFormValues.motif_consultation)
      );
    } else if (type === "hospitalisation") {
      if (operationFormValues === null) {
        // Cas d'une hospitalisation sans opération
        return (
          isMandatoryFieldValid(hospitalisationFormValues.entree_date) &&
          isMandatoryFieldValid(hospitalisationFormValues.zkf_patient) &&
          // Le mode_entree est déterminé par le type dans le radio button (jour_meme, ambulatoire, veille)
          // On peut le considérer comme toujours valide
          // isMandatoryFieldValid(hospitalisationFormValues.mode_entree) &&
          isMandatoryFieldValid(hospitalisationFormValues.service_hospitalisation) &&
          isMandatoryFieldValid(hospitalisationFormValues.specialite_referente) &&
          isMandatoryFieldValid(hospitalisationFormValues.medecin_referent)
        );
      } else {
        // Cas d'une hospitalisation avec opération
        // console.log("Verifying operation form values:", operationFormValues);
        return (
          // Required fields for hospitalisation
          isMandatoryFieldValid(hospitalisationFormValues.entree_date) &&
          isMandatoryFieldValid(hospitalisationFormValues.zkf_patient) &&
          // Le mode_entree est déterminé par le type dans le radio button
          // isMandatoryFieldValid(hospitalisationFormValues.mode_entree) &&
          isMandatoryFieldValid(hospitalisationFormValues.service_hospitalisation) &&
          isMandatoryFieldValid(hospitalisationFormValues.specialite_referente) &&
          isMandatoryFieldValid(hospitalisationFormValues.medecin_referent) &&
          // Required fields for operation
          isMandatoryFieldValid(operationFormValues.operation_date) &&
          isMandatoryFieldValid(operationFormValues.operation_start) &&
          isMandatoryFieldValid(operationFormValues.zkf_patient) &&
          isMandatoryFieldValid(operationFormValues.titre_operation) &&
          // L'opérateur est maintenant géré automatiquement en fonction du docteur
          // On peut le considérer comme toujours valide
          true
          //isMandatoryFieldValid(operationFormValues.indication_operatoire)
        );
      }
    }
  };

  const getTitle = () => {
    if (forcedType === "consultation") {
      return "Ajouter une consultation";
    }
    if (forcedType === "hospitalisation") {
      return "Ajouter une hospitalisation";
    }
    return "Ajouter un événement";
  };

  const getMandatoryFieldsHelperText = () => {
    const missingFields: string[] = [];

    if (!selectedDr) {
      missingFields.push("Docteur");
    }

    if (type === "consultation") {
      if (!currentPatient) missingFields.push("Patient");
      if (!isMandatoryFieldValid(consultationFormValues.consultation_date))
        missingFields.push("Date");
      if (!isMandatoryFieldValid(consultationFormValues.consultation_debut_prevu))
        missingFields.push("Heure de début");
      if (!isMandatoryFieldValid(consultationFormValues.zkf_site)) missingFields.push("Site");
      if (!isMandatoryFieldValid(consultationFormValues.motif_consultation))
        missingFields.push("Motif de consultation");
    } else if (type === "hospitalisation") {
      if (!isMandatoryFieldValid(hospitalisationFormValues.zkf_patient))
        missingFields.push("Patient");
      if (!isMandatoryFieldValid(hospitalisationFormValues.entree_date))
        missingFields.push("Date d'entrée");
      if (!isMandatoryFieldValid(hospitalisationFormValues.service_hospitalisation))
        missingFields.push("Service d'hospitalisation");
      if (!isMandatoryFieldValid(hospitalisationFormValues.specialite_referente))
        missingFields.push("Spécialité référente");
      if (!isMandatoryFieldValid(hospitalisationFormValues.medecin_referent))
        missingFields.push("Médecin référent");

      if (operationFormValues !== null) {
        if (!isMandatoryFieldValid(operationFormValues.operation_date))
          missingFields.push("Date de l'opération");
        if (!isMandatoryFieldValid(operationFormValues.operation_start))
          missingFields.push("Heure de début de l'opération");
        if (!isMandatoryFieldValid(operationFormValues.titre_operation))
          missingFields.push("Titre de l'opération");
        if (!isMandatoryFieldValid(operationFormValues.operateur_new))
          missingFields.push("Opérateurs");
      }
    }

    return missingFields.length > 0
      ? `Champs obligatoires manquants : ${missingFields.join(", ")}`
      : "";
  };

  return (
    <CustomDrawer
      open={open}
      onClose={onClose}
      title={getTitle()}
      actions={
        <Stack direction="row" justifyContent="flex-end">
          <Button variant="plain" color="neutral" onClick={onClose}>
            Annuler
          </Button>
          <Tooltip
            title={!canSave() ? getMandatoryFieldsHelperText() : ""}
            placement="top"
            arrow
            // Le tooltip ne doit apparaitre que quand la souris est dessus,
            // au lieu d'être forcé à rester ouvert quand canSave() est false
          >
            <span>
              <Button onClick={() => checkAndSave()} disabled={!canSave()} loading={loading}>
                Enregistrer
              </Button>
            </span>
          </Tooltip>
        </Stack>
      }
    >
      <Stack gap={3} sx={{ flex: 1 }}>
        {/* Patient Section */}
        <Sheet
          color="neutral"
          variant="soft"
          sx={{
            borderRadius: "lg",
            border: "1px solid transparent",
            pointerEvents: loading ? "none" : "auto",
            opacity: loading ? 0.7 : 1,
            p: 2,
          }}
        >
          <CurrentPatientManager forcedCurrentPatient={forcedCurrentPatient} />
        </Sheet>

        {/* Event Type and Doctor Selection */}
        <Stack gap={2}>
          {!forcedType && (
            <FormControl>
              <FormLabel>Type d'événement</FormLabel>
              <ToggleButtonGroup
                value={type}
                onChange={(event, newValue) => {
                  setType(newValue as "consultation" | "hospitalisation");
                }}
                defaultValue="consultation"
                disabled={loading}
                sx={{ width: "100%" }}
              >
                {radioValues.map((radio) => (
                  <Button
                    variant="soft"
                    key={radio.value}
                    value={radio.value}
                    startDecorator={radio.icon}
                    data-cy={`create-slot-modal-type-${radio.value}`}
                    sx={{ flex: 1 }}
                  >
                    {radio.label}
                  </Button>
                ))}
              </ToggleButtonGroup>
            </FormControl>
          )}

          <FormControl>
            <FormLabel>Docteur</FormLabel>
            <DoctorSelect
              value={selectedDr!}
              onSelect={(doctor) => {
                setSelectedDr(doctor as number);
              }}
              disabled={loading}
            />
          </FormControl>
        </Stack>

        {/* Form Section */}
        {type === "consultation" ? (
          <ConsultationForm
            values={consultationFormValues as Consultation}
            setValues={setConsultationFormValues}
          />
        ) : (
          <Stack gap={2}>
            <Typography level="title-md" startDecorator={<LocalHospital />} sx={{ mb: 2 }}>
              Hospitalisation
            </Typography>
            <HospitalisationForm
              values={hospitalisationFormValues}
              setValues={setHospitalisationFormValues}
            />

            {!operationFormValues && (
              <Button
                onClick={() => initOperationFormValues()}
                startDecorator={<Add />}
                variant="plain"
                color="success"
                sx={{ width: "100%" }}
              >
                <Masks />
                &nbsp; Ajouter une opération à cette hospitalisation
              </Button>
            )}

            {operationFormValues !== null && (
              <Sheet sx={{ p: 2, borderRadius: "lg" }} variant="soft" color="success">
                <Stack
                  direction="row"
                  gap={2}
                  mb={2}
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Typography level="title-md" startDecorator={<Masks />}>
                    Opération
                  </Typography>
                  <Button
                    variant="outlined"
                    size="sm"
                    color="neutral"
                    onClick={() => updateOperationForm(null)}
                  >
                    <Delete /> {/*Annuler */}
                  </Button>
                </Stack>
                <OperationForm values={operationFormValues} setValues={updateOperationForm} />
              </Sheet>
            )}
          </Stack>
        )}
      </Stack>
    </CustomDrawer>
  );
}
