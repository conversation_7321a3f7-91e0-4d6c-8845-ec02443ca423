import dayjs from "dayjs";
import moment from "moment";
import React from "react";
import { SlotInfo } from "react-big-calendar";
import { EventInteractionArgs } from "react-big-calendar/lib/addons/dragAndDrop";

import { FormControl, FormLabel } from "@mui/joy";
import Box from "@mui/joy/Box";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import CalendarComponent from "../../components/Calendar/CalendarComponent";
import DoctorSelectChips from "../../components/Forms/DoctorSelectChips";
import { useApi } from "../../contexts/ApiContext";
import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { useSessionPreferences } from "../../contexts/SessionPreferencesContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { useAuthenticatedUser } from "../../contexts/UserContext";
import { ExtendedCalendarEvent } from "../../models/custom";
import { CalendarItem } from "../../models/types";
import CalendarItemViewer from "./CalendarItemViewer";
import CreateSlotDrawer from "./CreateSlotDrawer";
import CreateEventForm from "./Forms/CreateEventForm";

export default function CalendarPage() {
  const { user } = useAuthenticatedUser();
  const api = useApi();
  const snackbar = useSnackbar();
  const { data, setData } = useSessionPreferences();
  const { currentPatient, setCurrentPatient } = useCurrentPatient();

  const [selectedDoctors, setSelectedDoctors] = React.useState<number[]>(
    data.selectedUsers || [Number(user.id)]
  );
  const [selectedSlot, setSelectedSlot] = React.useState<SlotInfo | null>(null);
  const [clickedEvent, setClickedEvent] = React.useState<CalendarItem | null>(null);
  const [startDate, setStartDate] = React.useState<string | null>(null);
  const [vacation, setVacation] = React.useState<CalendarItem | null>(null);
  const [openNewEventModal, setOpenNewEventModal] = React.useState<boolean>(false);
  const [refresh, setRefresh] = React.useState<number>(new Date().getTime());

  // Clés pour la gestion du patient actif
  const ACTIVE_PATIENT_KEY = "babylone_temp_active_patient";
  const FROM_HISTORY_KEY = "babylone_from_history";

  // Effet pour restaurer le patient actif lors des navigations depuis l'historique
  React.useEffect(() => {
    // Vérifier si on vient de naviguer depuis l'historique
    const fromHistory = localStorage.getItem(FROM_HISTORY_KEY) === "true";

    if (fromHistory) {
      console.log("Navigation depuis l'historique détectée");
      // Récupérer le patient associé à cette page
      const tempPatientId = localStorage.getItem(ACTIVE_PATIENT_KEY);

      if (tempPatientId && tempPatientId !== "null") {
        // Si on a un patient associé à cette page, on le définit comme patient actif
        console.log(`Restauration du patient ${tempPatientId}`);
        api
          .getPatient(tempPatientId)
          .then((patient) => {
            if (patient) {
              console.log(`Patient restauré: ${patient.nom} ${patient.prenom}`);
              setCurrentPatient(patient);
            }
          })
          .catch((error: unknown) => {
            console.error("Erreur lors de la récupération du patient:", error);
          });
      } else if (tempPatientId === "null") {
        // Si la page avait explicitement pas de patient actif
        console.log("Cette page n'avait pas de patient actif");
        setCurrentPatient(null);
      }

      // Nettoyer après utilisation
      localStorage.removeItem(FROM_HISTORY_KEY);
      localStorage.removeItem(ACTIVE_PATIENT_KEY);
    }
    // Si on n'est pas en navigation depuis l'historique, on utilise le comportement par défaut
    else if (!currentPatient && data.activePatientId) {
      // Récupérer le patient à partir de son ID (conversion en string car l'API attend une string)
      api
        .getPatient(String(data.activePatientId))
        .then((patient) => {
          if (patient) {
            setCurrentPatient(patient);
          }
        })
        .catch((error: unknown) => {
          console.error("Erreur lors de la récupération du patient actif :", error);
        });
    }
  }, [currentPatient, data.activePatientId, api, setCurrentPatient]);

  React.useEffect(() => {
    if (JSON.stringify(data.selectedUsers) !== JSON.stringify(selectedDoctors)) {
      setData({
        ...data,
        selectedUsers: selectedDoctors,
      });
    }
  }, [data, selectedDoctors, setData]);

  React.useEffect(() => {
    setClickedEvent(null);
    // setNewEventId("");
  }, [refresh]);

  const handleCloseMenu = React.useCallback(() => {
    // setMenuPosition(null);
    setStartDate(null);
    setClickedEvent(null);
  }, []);

  const handleClickEvent = React.useCallback(
    (event: ExtendedCalendarEvent) => {
      handleCloseMenu();
      if (event.start instanceof Date) {
        const eventStart = Math.floor(event.start.getTime() / 1000);
        const eventEnd = eventStart + 86400; // One day in seconds
        api?.getCalendarItems(eventStart, eventEnd, selectedDoctors).then((items) => {
          const item = items?.find((i) => i.id === event.id);
          if (item) {
            setClickedEvent(item);
            item.type === "user_event" && setOpenNewEventModal(true);
          }
        });
      }
    },
    [api, handleCloseMenu, selectedDoctors]
  );

  const handleSelectSlot = React.useCallback(
    (slotInfo: SlotInfo, overlappingVacation?: CalendarItem) => {
      handleCloseMenu();
      if (slotInfo.start.getTime() === slotInfo.end.getTime()) {
        slotInfo.end = new Date(slotInfo.start.getTime() + 15 * 60 * 1000);
      }
      if (overlappingVacation) {
        console.log("overlappingVacation", overlappingVacation);
        setVacation(overlappingVacation);
      }
      setSelectedSlot(slotInfo);
    },
    [handleCloseMenu]
  );

  const handleSelectDoctors = (ids: number | number[]) => {
    setSelectedDoctors(ids as number[]);
  };

  const handleEventDrop = (event: EventInteractionArgs<ExtendedCalendarEvent>) => {
    if (event.start instanceof Date) {
      const eventStart = Math.floor(event.start.getTime() / 1000);
      const eventEnd = eventStart + 86400; // One day in seconds
      api?.getCalendarItems(eventStart, eventEnd, selectedDoctors).then((items) => {
        const item = items?.find((i) => i.id === event.event.id);
        if (item) {
          const start = moment(event.start);
          const end = moment(event.end);

          switch (item.type.toLowerCase()) {
            case "consultation":
              api
                .updateConsultation({
                  id: item.id,
                  consultation_date: start.format("YYYY-MM-DD"),
                  consultation_debut_prevu: start.utcOffset("+01:00").format("HH:mm:SS"),
                  consultation_fin_prevu: end.utcOffset("+01:00").format("HH:mm:SS"),
                })
                .then(() => setRefresh(new Date().getTime()))
                .catch(() =>
                  snackbar.show("Erreur lors de la modification de la consultation", "danger")
                );
              break;
            case "operation":
              api
                .updateOperation({
                  id: item.id,
                  operation_date: start.format("YYYY-MM-DD"),
                  operation_start: start.toISOString(),
                  operation_end: end.toISOString(),
                })
                .then(() => setRefresh(new Date().getTime()))
                .catch(() =>
                  snackbar.show("Erreur lors de la modification de l'operation", "danger")
                );
              break;
            case "user_event":
              api
                .updateEvent({
                  id: item.id,
                  event_start: start.toISOString(),
                  event_end: end.toISOString(),
                })
                .then(() => setRefresh(new Date().getTime()))
                .catch(() =>
                  snackbar.show("Erreur lors de la modification de l'evenement", "danger")
                );
              break;
          }
        }
      });
    }
  };

  const handleCreateEvent = React.useCallback(() => {
    setOpenNewEventModal(true);
  }, []);

  // Écoute l'événement de clic droit sur le calendrier
  React.useEffect(() => {
    const handleCalendarContextMenu = (e: CustomEvent) => {
      const { date } = e.detail;
      if (date) {
        // Formate la date pour le formulaire d'événement
        setStartDate(moment(date).format("YYYY-MM-DDTHH:mm"));
      }
    };

    // Ajoute l'écouteur d'événement
    document.addEventListener("calendarContextMenu", handleCalendarContextMenu as EventListener);

    // Nettoie l'écouteur d'événement
    return () => {
      document.removeEventListener(
        "calendarContextMenu",
        handleCalendarContextMenu as EventListener
      );
    };
  }, []);

  return (
    <Box
      sx={{
        height: "calc(100vh - 80px)",
        overflow: "auto",
        position: "relative",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Stack direction="row" gap={2} sx={{ mb: 2 }} justifyContent="space-between">
        <FormControl>
          <FormLabel>Docteurs</FormLabel>
          <DoctorSelectChips value={selectedDoctors} multiple onSelect={handleSelectDoctors} />
        </FormControl>
      </Stack>
      <Box sx={{ position: "relative", flex: 1, minHeight: 0 }}>
        <CalendarComponent
          selectedDoctors={selectedDoctors}
          onEventClick={handleClickEvent}
          onSlotSelect={handleSelectSlot}
          onEventDrop={handleEventDrop}
          onEventResize={handleEventDrop}
          onCreateEvent={handleCreateEvent}
          refresh={refresh}
          height="100%"
        />
        {selectedDoctors.length === 0 && (
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.3)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1,
              borderRadius: (theme) => theme.vars.radius.sm,
            }}
          >
            <Typography level="h4" sx={{ color: "white", textAlign: "center" }}>
              Veuillez sélectionner au moins un docteur pour afficher le calendrier.
            </Typography>
          </Box>
        )}
      </Box>
      {selectedSlot && (
        <CreateSlotDrawer
          redirectOnCreate={false}
          initialDate={dayjs(selectedSlot.start).format("YYYY-MM-DD")}
          initialStart={dayjs(selectedSlot.start).format("HH:mm")}
          initialEnd={dayjs(selectedSlot.end).format("HH:mm")}
          onSave={(id?: string) => {
            setRefresh(new Date().getTime());
          }}
          open={!!selectedSlot}
          onClose={() => {
            setSelectedSlot(null);
            setVacation(null);
          }}
          initialType={
            vacation?.event_data?.type_vacation === "OPERATION" ? "hospitalisation" : "consultation"
          }
          initialSite={vacation?.event_data?.site}
          initialUser={Number(vacation?.zkf_user)}
          forcedType={null}
        />
      )}
      {clickedEvent && clickedEvent.type !== "user_event" && (
        <CalendarItemViewer
          open={!!clickedEvent}
          onClose={() => {
            setClickedEvent(null);
          }}
          event={clickedEvent}
        />
      )}
      <CreateEventForm
        open={openNewEventModal}
        onClose={() => {
          setOpenNewEventModal(false);
          handleCloseMenu();
        }}
        onSave={(id?: string) => {
          setRefresh(new Date().getTime());
        }}
        startDate={startDate || ""}
        eventId={clickedEvent?.id}
      />
    </Box>
  );
}
