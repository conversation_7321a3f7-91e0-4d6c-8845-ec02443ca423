import dayjs from "dayjs";
import "dayjs/locale/fr";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import { Add, ArrowBack, ContentCopy, Delete, ExpandMore, Refresh } from "@mui/icons-material";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Card,
  Grid,
  IconButton,
  Stack,
  Typography,
} from "@mui/joy";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";

import DeleteConfirmationModal from "../../components/Modals/DeleteConfirmationModal";
import OrdonnanceForm from "../../components/Ordonnances/OrdonnanceForm";
import PDFPreviewAndPrint from "../../components/PDF/PDFPreview";
import { useApi } from "../../contexts/ApiContext";
import { usePrintManager } from "../../contexts/PrintManagerContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { PrintData } from "../../models/custom";
import { Ordonnance } from "../../models/types";

// Interface pour le groupe d'ordonnances par mois
interface OrdonnanceGroup {
  monthKey: string;
  monthLabel: string;
  ordonnances: Ordonnance[];
}

export default function OrdonnancesList() {
  const { patientId } = useParams<{ patientId: string }>();
  const api = useApi();
  const snackbar = useSnackbar();
  const navigate = useNavigate();
  const { removeFromPrintManagerViaDocumentId } = usePrintManager();

  // Pour le regroupement par mois
  dayjs.locale("fr");

  // State
  const [ordonnances, setOrdonnances] = useState<Ordonnance[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrdonnance, setSelectedOrdonnance] = useState<Ordonnance | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [duplicateModalOpen, setDuplicateModalOpen] = useState(false);
  const [ordonnanceToDuplicate, setOrdonnanceToDuplicate] = useState<Ordonnance | null>(null);

  // Grouper les ordonnances par mois
  const ordonnancesByMonth = useMemo(() => {
    if (!ordonnances.length) return [];

    // Créer un Map pour regrouper les ordonnances par mois
    const groupedMap = new Map<string, Ordonnance[]>();

    // Trier les ordonnances par date (plus récente en premier)
    const sortedOrdonnances = [...ordonnances].sort(
      (a, b) => dayjs(b.date_ordonnance).valueOf() - dayjs(a.date_ordonnance).valueOf()
    );

    // Regrouper par mois/année
    sortedOrdonnances.forEach((ordonnance) => {
      const date = dayjs(ordonnance.date_ordonnance);
      const monthKey = date.format("YYYY-MM");

      if (!groupedMap.has(monthKey)) {
        groupedMap.set(monthKey, []);
      }

      groupedMap.get(monthKey)?.push(ordonnance);
    });

    // Convertir la Map en tableau de groupes
    const groups: OrdonnanceGroup[] = [];
    groupedMap.forEach((ordonnances, monthKey) => {
      groups.push({
        monthKey,
        monthLabel: dayjs(monthKey).format("MMMM YYYY"),
        ordonnances,
      });
    });

    return groups;
  }, [ordonnances]);

  // Fetch ordonnances
  const fetchOrdonnances = useCallback(async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      const response = await api.getOrdonnances(undefined, undefined, undefined, patientId);
      setOrdonnances(response.results);
    } catch (error) {
      console.error("Erreur lors de la récupération des ordonnances:", error);
      snackbar.show("Erreur lors de la récupération des ordonnances", "danger");
    } finally {
      setLoading(false);
    }
  }, [api, patientId, snackbar]);

  useEffect(() => {
    fetchOrdonnances();
  }, [fetchOrdonnances]);

  // Fetch PDF for selected ordonnance
  const fetchPdf = useCallback(
    async (ordonnanceId: string, ordonnance: Ordonnance) => {
      try {
        // Créer les données pour l'impression
        const filesToPrint: PrintData[] = [
          {
            id: ordonnanceId,
            type_document: "ordonnance",
            model_app_datas: ordonnance.model_app_datas,
          },
        ];

        // Générer le PDF avec l'API
        const pdfBlob = await api.printManagerPrintAll(filesToPrint);

        // Créer une URL pour le blob
        const pdfUrl = window.URL.createObjectURL(pdfBlob);
        setPdfUrl(pdfUrl);
      } catch (error) {
        console.error("Erreur lors de la récupération du PDF:", error);
        snackbar.show("Erreur lors de la récupération du PDF", "danger");
        setPdfUrl(null);
      }
    },
    [api, snackbar]
  );

  // Select ordonnance and load PDF
  const handleSelectOrdonnance = (ordonnance: Ordonnance) => {
    setSelectedOrdonnance(ordonnance);
    fetchPdf(ordonnance.id, ordonnance);
  };

  // Delete ordonnance
  const deleteOrdonnance = async (id: string) => {
    try {
      await api.deleteOrdonnance(id);
      fetchOrdonnances();
      // Make sure the ordonnance is removed from the print manager
      removeFromPrintManagerViaDocumentId([id]);
      snackbar.show("Ordonnance supprimée", "success");

      // If the deleted ordonnance was selected, clear the selection
      if (selectedOrdonnance && selectedOrdonnance.id === id) {
        setSelectedOrdonnance(null);
        setPdfUrl(null);
      }
    } catch (error) {
      console.error("Erreur lors de la suppression de l'ordonnance:", error);
      snackbar.show("Erreur lors de la suppression de l'ordonnance", "danger");
    }
  };

  // Duplicate ordonnance
  const handleDuplicateOrdonnance = (ordonnance: Ordonnance) => {
    setOrdonnanceToDuplicate({
      ...ordonnance,
      date_ordonnance: dayjs().format("YYYY-MM-DD"), // Update date to today
    });
    setDuplicateModalOpen(true);
  };

  return (
    <Box sx={{ height: "calc(100vh - 64px)", p: 2 }}>
      <Grid container spacing={2} sx={{ height: "100%" }}>
        {/* Left side - List of ordonnances */}
        <Grid xs={4} sx={{ height: "100%", overflowY: "auto" }}>
          <Stack spacing={2} sx={{ height: "100%" }}>
            {/* Header avec actions */}
            <Stack spacing={2}>
              <Stack direction="row" spacing={1} alignItems="center">
                <IconButton
                  variant="plain"
                  color="neutral"
                  onClick={() => {
                    // En retournant, le composant destinataire va déjà avoir la bonne valeur d'onglet
                    // chargée depuis le localStorage lors de son initialisation
                    navigate(-1);
                  }}
                  title="Retour à la page précédente"
                >
                  <ArrowBack />
                </IconButton>
                {/* <Typography level="h4">Ordonnances du patient</Typography> */}
              </Stack>
              <Stack direction="row" spacing={1} justifyContent="space-between">
                <Button startDecorator={<Refresh />} variant="plain" onClick={fetchOrdonnances}>
                  Actualiser
                </Button>
                <Button
                  startDecorator={<Add />}
                  onClick={() => setCreateModalOpen(true)}
                  data-cy="create-ordonnance-button"
                  sx={{ whiteSpace: "nowrap" }}
                >
                  Nouvelle ordonnance
                </Button>
              </Stack>
            </Stack>

            {loading ? (
              <Typography>Chargement des ordonnances...</Typography>
            ) : ordonnances.length === 0 ? (
              <Card variant="outlined" sx={{ p: 4, textAlign: "center" }}>
                <Typography level="body-lg" sx={{ mb: 2 }}>
                  Aucune ordonnance disponible pour ce patient
                </Typography>
                <Button startDecorator={<Add />} onClick={() => setCreateModalOpen(true)}>
                  Créer une ordonnance
                </Button>
              </Card>
            ) : (
              <Box sx={{ flex: 1, overflowY: "auto" }}>
                {ordonnancesByMonth.map((group) => (
                  <Accordion key={group.monthKey} defaultExpanded>
                    <AccordionSummary indicator={<ExpandMore />}>
                      <Typography level="title-md" textTransform="capitalize">
                        {group.monthLabel}{" "}
                        <Typography component="span" color="neutral" fontWeight="normal">
                          ({group.ordonnances.length})
                        </Typography>
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Stack spacing={1}>
                        {group.ordonnances.map((ordonnance) => (
                          <Card
                            key={ordonnance.id}
                            variant={
                              selectedOrdonnance?.id === ordonnance.id ? "solid" : "outlined"
                            }
                            onClick={() => handleSelectOrdonnance(ordonnance)}
                            sx={{
                              cursor: "pointer",
                              "&:hover": { bgcolor: "background.level1" },
                            }}
                          >
                            <Stack spacing={1}>
                              <Stack
                                direction="row"
                                justifyContent="space-between"
                                alignItems="center"
                              >
                                <Typography level="title-md">
                                  {ordonnance.titre_ordonnance}
                                </Typography>
                                <Typography level="body-sm">
                                  {dayjs(ordonnance.date_ordonnance).format("DD/MM/YYYY")}
                                </Typography>
                              </Stack>
                              <Stack direction="row" spacing={1} justifyContent="flex-end">
                                <IconButton
                                  size="sm"
                                  variant="plain"
                                  color="neutral"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDuplicateOrdonnance(ordonnance);
                                  }}
                                  title="Dupliquer l'ordonnance"
                                >
                                  <ContentCopy />
                                </IconButton>
                                <IconButton
                                  size="sm"
                                  variant="plain"
                                  color="danger"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setDeletingId(ordonnance.id);
                                  }}
                                  title="Supprimer l'ordonnance"
                                >
                                  <Delete />
                                </IconButton>
                              </Stack>
                            </Stack>
                          </Card>
                        ))}
                      </Stack>
                    </AccordionDetails>
                  </Accordion>
                ))}
              </Box>
            )}
          </Stack>
        </Grid>

        {/* Right side - PDF Viewer */}
        <Grid xs={8} sx={{ height: "100%" }}>
          {selectedOrdonnance && pdfUrl ? (
            <Box
              sx={{
                height: "100%",
                bgcolor: "background.surface",
                borderRadius: "md",
                overflow: "hidden",
              }}
            >
              <PDFPreviewAndPrint fileUrl={pdfUrl} height="100%" />
            </Box>
          ) : (
            <Box
              sx={{
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                bgcolor: "background.surface",
                borderRadius: "md",
              }}
            >
              <Typography level="body-lg">
                {loading ? "Chargement..." : "Sélectionnez une ordonnance pour afficher le PDF"}
              </Typography>
            </Box>
          )}
        </Grid>
      </Grid>

      {/* Create New Ordonnance Modal */}
      {createModalOpen && (
        <Modal open onClose={() => setCreateModalOpen(false)}>
          <ModalDialog size="lg">
            <OrdonnanceForm
              zkf_patient={patientId || ""}
              onCancel={() => setCreateModalOpen(false)}
              onCreated={() => {
                setCreateModalOpen(false);
                fetchOrdonnances();
              }}
            />
          </ModalDialog>
        </Modal>
      )}

      {/* Duplicate Ordonnance Modal */}
      {duplicateModalOpen && ordonnanceToDuplicate && (
        <Modal open onClose={() => setDuplicateModalOpen(false)}>
          <ModalDialog size="lg">
            <OrdonnanceForm
              zkf_patient={patientId || ""}
              onCancel={() => setDuplicateModalOpen(false)}
              onCreated={() => {
                setDuplicateModalOpen(false);
                fetchOrdonnances();
              }}
              ordonnance={ordonnanceToDuplicate}
            />
          </ModalDialog>
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        open={!!deletingId}
        onClose={() => setDeletingId(null)}
        onConfirm={() => {
          if (deletingId) {
            deleteOrdonnance(deletingId);
            setDeletingId(null);
          }
        }}
        title="Supprimer l'ordonnance"
        message="Êtes-vous sûr de vouloir supprimer cette ordonnance ?"
      />
    </Box>
  );
}
