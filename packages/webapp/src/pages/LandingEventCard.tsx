import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import React from "react";

import AlarmOnIcon from "@mui/icons-material/AlarmOn";
import ArrowRight from "@mui/icons-material/ArrowRight";
import DoneIcon from "@mui/icons-material/Done";
import Event from "@mui/icons-material/Event";
import HailIcon from "@mui/icons-material/Hail";
import Masks from "@mui/icons-material/Masks";
import MicIcon from "@mui/icons-material/Mic";
import People from "@mui/icons-material/People";
import PunchClock from "@mui/icons-material/PunchClock";
import IconButton from "@mui/joy/IconButton";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import { useTheme } from "@mui/joy/styles";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";
import useMediaQuery from "@mui/material/useMediaQuery";

import { useApi } from "../contexts/ApiContext";
import { useSnackbar } from "../contexts/SnackbarContext";
import { CalendarItem, Consultation } from "../models/types";
import { router } from "../router";

dayjs.extend(customParseFormat);

function getEventIcon(type: CalendarItem["type"]) {
  switch (type.toLowerCase()) {
    case "consultation":
      return <People color="primary" />;
    case "operation":
      return <Masks color="success" />;
    default:
      return <Event color="warning" />;
  }
}

function getEventBorder(event: CalendarItem) {
  if (event.type.toLowerCase() === "consultation") {
    switch ((event.event_data as unknown as Consultation).etat_consultation) {
      case "a_venir":
        return "4px solid #D97706";
      case "arrive":
        return "4px solid #FFD700";
      case "debutee":
        return "4px solid #008000";
      case "finie":
        return "4px solid #999999";
      case "non_venu":
        return "4px solid #FF0000";
      case "annule":
        return "4px solid #FF0000";
      default:
        return "4px solid #D97706";
    }
  }
}

interface LandingEventCardProps {
  event: CalendarItem;
  refresh: () => void;
  id?: string;
  onOpenSpeechDrawer: (event: CalendarItem) => void;
}

export default function LandingEventCard({
  event,
  refresh,
  id,
  onOpenSpeechDrawer,
}: LandingEventCardProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handlePatientArrival = async () => {
    try {
      if (event.type.toLowerCase() === "consultation") {
        const currentTime = dayjs().format("HH:mm:ss");
        await api.patchConsultation(event.id, {
          consultation_arrive: currentTime,
          etat_consultation: "arrive",
        });
        refresh();
        snackbar.show("Patient arrival time recorded", "success");
      }
    } catch (error: any) {
      snackbar.show("An issue occurred while recording the patient's arrival time", "danger");
      console.error(error);
    }
  };

  // This function is now simplified to call the prop from Landing.tsx
  const handleMicIconClick = React.useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      // The actual logic to check if event is suitable for dictation
      // is now in Landing.tsx's handleOpenSpeechDrawer
      onOpenSpeechDrawer(event);
    },
    [event, onOpenSpeechDrawer]
  );

  const goToEvent = (event: CalendarItem) => {
    switch (event.type.toLowerCase()) {
      case "consultation":
        router.navigate(`/consultation/${event.id}`);
        break;
      case "operation":
        router.navigate(`/operation/${event.id}`);
        break;
    }
  };

  const handleConsultationStartOrEnd = async (event: CalendarItem) => {
    try {
      if (event.type.toLowerCase() === "consultation") {
        if (!(event.event_data as unknown as Consultation).consultation_debut_reel) {
          await api.patchConsultation(event.id, {
            consultation_debut_reel: dayjs().format("HH:mm:ss"),
            etat_consultation: "debutee",
          });
          snackbar.show("Consultation démarrée", "success");
          router.navigate(`/consultation/${event.id}`);
        } else {
          await api.patchConsultation(event.id, {
            consultation_fin_reel: dayjs().format("HH:mm:ss"),
            etat_consultation: "finie",
          });
          snackbar.show("Consultation terminée", "success");
          refresh();
        }
      }
    } catch (error: any) {
      snackbar.show("Un problème est survenu lors de la mise à jour de la consultation", "danger");
      console.error(error);
    }
  };

  return (
    <Sheet
      variant="outlined"
      sx={{
        px: 2,
        py: 1,
        borderRadius: "sm",
        borderLeft: getEventBorder(event),
        cursor: isMobile ? "default" : "pointer", // Conditional cursor
        width: "100%",
      }}
      id={id}
      onClick={(e: React.MouseEvent<HTMLDivElement>) => {
        let targetElement = e.target as HTMLElement;
        // Traverse up to the current target (the Sheet)
        // to check if the click originated from an interactive element or within the drawer.
        while (targetElement && targetElement !== e.currentTarget) {
          if (
            targetElement.tagName === "BUTTON" ||
            targetElement.getAttribute("role") === "button" ||
            targetElement.closest(".MuiIconButton-root") || // Targets Joy UI IconButton (and MUI ones if present)
            targetElement.closest('[class*="MuiDrawer-root"]') // Click inside the Drawer
          ) {
            // Click was on a button, an IconButton, or inside the drawer; do not navigate.
            return;
          }
          targetElement = targetElement.parentElement as HTMLElement;
        }

        // If on a mobile device, prevent navigation for general card clicks.
        // if (isMobile) {
        //   return;
        // }

        // If the click was not on a specific interactive element and not on mobile, proceed with navigation.
        goToEvent(event);
      }}
    >
      <Stack gap={1}>
        <Stack direction="row" gap={1}>
          <div>{getEventIcon(event.type)}</div>
          <Stack direction="row" gap={1} style={{ flex: 1 }}>
            <div style={{ flex: 1 }}>
              <Stack gap={1}>
                <Typography level="title-sm">{dayjs.unix(event.start).format("HH:mm")}</Typography>
              </Stack>
              <Stack gap={1}>
                <Typography
                  level="body-sm"
                  sx={{
                    "&:hover": {
                      cursor: "pointer",
                    },
                  }}
                >
                  {event.title}
                </Typography>
                {event.type.toLowerCase() === "consultation" &&
                  dayjs.unix(event.start).isSame(dayjs(), "day") && (
                    <Stack direction="row" gap={0.5} alignItems="center">
                      <Tooltip title="Enregistrer l'arrivée du patient">
                        <IconButton
                          size="sm"
                          variant="outlined"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePatientArrival();
                          }}
                          color={
                            !!(event.event_data as unknown as Consultation).consultation_arrive
                              ? "success"
                              : "neutral"
                          }
                          disabled={
                            !!(event.event_data as unknown as Consultation).consultation_arrive
                          }
                        >
                          <HailIcon />
                        </IconButton>
                      </Tooltip>

                      <Tooltip title="Commencer/Terminer la consultation">
                        <IconButton
                          size="sm"
                          variant="outlined"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleConsultationStartOrEnd(event);
                          }}
                          color={
                            !!(event.event_data as unknown as Consultation).consultation_debut_reel
                              ? "warning"
                              : "success"
                          }
                          disabled={
                            !!(event.event_data as unknown as Consultation).consultation_fin_reel &&
                            !!(event.event_data as unknown as Consultation).consultation_debut_reel
                          }
                        >
                          {!(event.event_data as unknown as Consultation)
                            .consultation_debut_reel ? (
                            <DoneIcon />
                          ) : (
                            <PunchClock />
                          )}
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  )}
              </Stack>
            </div>

            <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={0.5}>
              {event.type.toLowerCase() === "consultation" && (
                <Tooltip title="Lancer la dictée vocale" arrow>
                  <IconButton
                    size="sm"
                    variant="outlined"
                    color="neutral"
                    onClick={handleMicIconClick} // Updated handler
                    sx={{ ml: "auto" }}
                  >
                    <MicIcon />
                  </IconButton>
                </Tooltip>
              )}

              {!isMobile && (
                <Tooltip title="Voir l'événement" arrow>
                  <IconButton
                    size="sm"
                    variant="outlined"
                    onClick={(e) => {
                      e.stopPropagation();
                      goToEvent(event);
                    }}
                  >
                    <ArrowRight />
                  </IconButton>
                </Tooltip>
              )}
            </Stack>
          </Stack>
        </Stack>
        <Stack sx={{ ml: 3 }} direction="row" gap={2} justifyContent="flex-start">
          {(event.event_data as unknown as Consultation).consultation_arrive && (
            <Typography level="body-xs" startDecorator={<HailIcon />}>
              Patient arrivé
              <b>
                &nbsp;
                {
                  //formatting the time to HH:mm. Need to use dayjs and customParseFormat to parse the time
                  dayjs(
                    (event.event_data as unknown as Consultation).consultation_arrive,
                    "HH:mm:ss"
                  ).format("HH:mm")
                }
              </b>
            </Typography>
          )}
          {(event.event_data as unknown as Consultation).consultation_debut_reel && (
            <Typography level="body-xs" startDecorator={<AlarmOnIcon />}>
              Débuté à{" "}
              {
                //formatting the time to HH:mm. Need to use dayjs and customParseFormat to parse the time
                dayjs(
                  (event.event_data as unknown as Consultation).consultation_debut_reel,
                  "HH:mm:ss"
                ).format("HH:mm")
              }
            </Typography>
          )}
          {(event.event_data as unknown as Consultation).consultation_fin_reel && (
            <Typography level="body-xs" startDecorator={<DoneIcon />}>
              Terminé à{" "}
              {
                //formatting the time to HH:mm. Need to use dayjs and customParseFormat to parse the time
                dayjs(
                  (event.event_data as unknown as Consultation).consultation_fin_reel,
                  "HH:mm:ss"
                ).format("HH:mm")
              }
            </Typography>
          )}
        </Stack>
      </Stack>
    </Sheet>
  );
}
