import React from "react";
import { useNavigate } from "react-router-dom";

import AssessmentIcon from "@mui/icons-material/Assessment";
import BarChartIcon from "@mui/icons-material/BarChart";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import CompareArrowsIcon from "@mui/icons-material/CompareArrows";
import SearchIcon from "@mui/icons-material/Search";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import Grid from "@mui/joy/Grid";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

const StatsIndex: React.FC = () => {
  const navigate = useNavigate();

  const statsOptions = [
    {
      title: "Statistiques Quotidiennes",
      description:
        "Analysez les performances quotidiennes, retards, ponctualité et taux d'occupation des vacations",
      icon: <CalendarTodayIcon sx={{ fontSize: 40 }} />,
      path: "/stats/daily",
      color: "primary",
    },
    {
      title: "Comparaison de Périodes",
      description:
        "Comparez plusieurs intervalles de dates avec tableaux comparatifs et graphiques temporels",
      icon: <CompareArrowsIcon sx={{ fontSize: 40 }} />,
      path: "/stats/intervals",
      color: "warning",
    },
    {
      title: "Recherche et Export",
      description:
        "Recherchez et exportez des données par tags, codes CCAM ou protocoles sur une période donnée",
      icon: <SearchIcon sx={{ fontSize: 40 }} />,
      path: "/stats/query",
      color: "success",
    },
    {
      title: "Analyses Avancées",
      description: "Tendances et analyses prédictives (à venir)",
      icon: <BarChartIcon sx={{ fontSize: 40 }} />,
      path: "/stats/advanced",
      color: "info",
      disabled: true,
    },
    {
      title: "Rapports Personnalisés",
      description: "Créez et programmez des rapports automatiques (à venir)",
      icon: <AssessmentIcon sx={{ fontSize: 40 }} />,
      path: "/stats/reports",
      color: "neutral",
      disabled: true,
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography level="h1" sx={{ mb: 1 }}>
        Vue d'ensemble
      </Typography>
      <Typography level="body-lg" color="neutral" sx={{ mb: 4 }}>
        Analysez vos données médicales, générez des rapports et exportez vos informations
      </Typography>

      <Grid container spacing={3}>
        {statsOptions.map((option, index) => (
          <Grid xs={12} md={6} key={index}>
            <Card
              sx={{
                p: 3,
                height: "100%",
                cursor: option.disabled ? "not-allowed" : "pointer",
                opacity: option.disabled ? 0.6 : 1,
                "&:hover": {
                  transform: option.disabled ? "none" : "translateY(-2px)",
                  boxShadow: option.disabled ? "none" : "lg",
                },
                transition: "all 0.2s ease-in-out",
              }}
              onClick={() => !option.disabled && navigate(option.path)}
            >
              <Stack spacing={2}>
                <Box
                  sx={{
                    color: `var(--joy-palette-${option.color}-500)`,
                    alignSelf: "flex-start",
                  }}
                >
                  {option.icon}
                </Box>

                <Box>
                  <Typography level="h3" sx={{ mb: 1 }}>
                    {option.title}
                    {option.disabled && (
                      <Typography component="span" level="body-xs" sx={{ ml: 1, opacity: 0.7 }}>
                        (Bientôt disponible)
                      </Typography>
                    )}
                  </Typography>
                  <Typography level="body-md" color="neutral">
                    {option.description}
                  </Typography>
                </Box>

                <Button
                  variant={option.disabled ? "outlined" : "soft"}
                  color={option.color as any}
                  disabled={option.disabled}
                  sx={{ alignSelf: "flex-start" }}
                >
                  {option.disabled ? "Bientôt disponible" : "Accéder"}
                </Button>
              </Stack>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Section d'aide */}
      <Card sx={{ mt: 4, p: 3, bgcolor: "background.surface" }}>
        <Typography level="h4" sx={{ mb: 2 }}>
          Aide et conseils
        </Typography>
        <Stack spacing={1}>
          <Typography level="body-sm" color="neutral">
            • <strong>Statistiques Quotidiennes</strong> : Idéal pour analyser la performance d'une
            journée spécifique
          </Typography>
          <Typography level="body-sm" color="neutral">
            • <strong>Comparaison de Périodes</strong> : Comparez plusieurs intervalles avec
            graphiques temporels et métriques détaillées
          </Typography>
          <Typography level="body-sm" color="neutral">
            • <strong>Recherche et Export</strong> : Parfait pour extraire des données spécifiques
            et créer des rapports personnalisés
          </Typography>
          <Typography level="body-sm" color="neutral">
            • Toutes les données respectent les permissions utilisateur et les règles de
            confidentialité
          </Typography>
          <Typography level="body-sm" color="neutral">
            • Les exports Excel incluent toutes les métadonnées disponibles pour vos analyses
          </Typography>
        </Stack>
      </Card>
    </Box>
  );
};

export default StatsIndex;
