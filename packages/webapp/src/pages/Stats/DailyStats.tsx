import React, { useC<PERSON>back, useEffect, useState } from "react";

import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import Alert from "@mui/joy/Alert";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import Chip from "@mui/joy/Chip";
import CircularProgress from "@mui/joy/CircularProgress";
import Divider from "@mui/joy/Divider";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useAuthenticatedUser } from "../../contexts/UserContext";

// interface ConsultationStats {
//   total_count: number;
//   morning_afternoon_analysis: {
//     morning_count: number;
//     afternoon_count: number;
//     first_morning_consultation?: {
//       mean_time: string;
//       earliest: string;
//       latest: string;
//     };
//     first_afternoon_consultation?: {
//       mean_time: string;
//       earliest: string;
//       latest: string;
//     };
//   };
//   timing_analysis: {
//     delay_statistics?: {
//       mean_delay_minutes: number;
//       min_delay_minutes: number;
//       max_delay_minutes: number;
//       on_time_count: number;
//       late_count: number;
//       early_count: number;
//     };
//   };
//   status_distribution: {
//     counts: Record<string, number>;
//     percentages: Record<string, number>;
//   };
// }

// interface OperationStats {
//   total_count: number;
//   first_incision_analysis: {
//     total_operations_with_start: number;
//     morning_operations_count: number;
//     afternoon_operations_count: number;
//     overall_first_incision?: {
//       earliest_time: string;
//       latest_time: string;
//       mean_time: string;
//     };
//   };
//   duration_analysis: {
//     total_operative_time_minutes: number;
//     mean_duration_minutes: number;
//     operations_with_duration: number;
//   };
//   status_analysis: {
//     completed_operations: number;
//     completion_rate_percentage: number;
//     cancelled_operations?: number;
//   };
// }

// Types simplifiés pour affichage JSON flexible
interface StatsData {
  [key: string]: any;
}

// Fonction utilitaire pour formater les nombres
const formatNumber = (value: any): string => {
  if (typeof value !== "number") return String(value);
  if (Number.isInteger(value)) return value.toString();
  return value.toFixed(2);
};

// Fonction utilitaire pour formater les minutes en heures
const formatMinutes = (minutes: any): string => {
  if (typeof minutes !== "number") return String(minutes);
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  if (hours > 0) {
    return `${hours}h${mins.toString().padStart(2, "0")}min`;
  }
  return `${mins}min`;
};

// Composant pour afficher une statistique individuelle
const StatItem: React.FC<{ label: string; value: any; unit?: string; color?: string }> = ({
  label,
  value,
  unit = "",
  color = "neutral",
}) => {
  const formattedValue = unit === "minutes" ? formatMinutes(value) : formatNumber(value);

  return (
    <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", py: 0.5 }}>
      <Typography level="body-sm" sx={{ color: "text.secondary" }}>
        {label}:
      </Typography>
      <Chip color={color as any} variant="soft" size="sm">
        {formattedValue}
        {unit && unit !== "minutes" ? ` ${unit}` : ""}
      </Chip>
    </Box>
  );
};

// Composant pour afficher les données de manière friendly
const FriendlyDisplay: React.FC<{ data: any; title: string }> = ({ data, title }) => {
  const [showAllOperations, setShowAllOperations] = useState(false);
  if (!data || Object.keys(data).length === 0) {
    return (
      <Card sx={{ p: 3 }}>
        <Typography level="h4" sx={{ mb: 2 }}>
          {title}
        </Typography>
        <Typography level="body-sm" color="neutral">
          Aucune donnée disponible
        </Typography>
      </Card>
    );
  }

  const renderFriendlyContent = () => {
    // Si c'est les nouvelles statistiques de consultations avec système vacation
    if (
      data.total_consultations !== undefined ||
      (data.vacation_analysis && data.consultations_detail)
    ) {
      return (
        <Stack spacing={2}>
          {data.total_consultations !== undefined && (
            <StatItem
              label="Total consultations"
              value={data.total_consultations}
              color="primary"
            />
          )}

          {/* Analyse vacation pour consultations */}
          {(data.vacation_analysis || data.vacation_metrics_summary) && (
            <>
              <Divider />
              <Typography level="title-sm">Analyse vacations consultation</Typography>
              {(() => {
                const vacMetrics = data.vacation_analysis || data.vacation_metrics_summary;
                return (
                  <>
                    <StatItem
                      label="Consultations avec vacation"
                      value={vacMetrics.consultations_with_vacation || 0}
                      color="success"
                    />
                    <StatItem
                      label="Consultations sans vacation"
                      value={vacMetrics.consultations_without_vacation || 0}
                      color="warning"
                    />

                    {/* Ponctualité consultations */}
                    {vacMetrics.on_time_consultations !== undefined && (
                      <>
                        <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                          Ponctualité consultations:
                        </Typography>
                        <StatItem
                          label="À l'heure (±5min)"
                          value={vacMetrics.on_time_consultations || 0}
                          color="success"
                        />
                        <StatItem
                          label="En retard (>5min)"
                          value={vacMetrics.late_consultations || 0}
                          color="danger"
                        />
                        <StatItem
                          label="En avance (>5min)"
                          value={vacMetrics.early_consultations || 0}
                          color="info"
                        />
                        {vacMetrics.average_consultation_delay !== undefined && (
                          <StatItem
                            label="Retard moyen consultation"
                            value={vacMetrics.average_consultation_delay}
                            unit="minutes"
                            color={
                              vacMetrics.average_consultation_delay > 0 ? "warning" : "success"
                            }
                          />
                        )}
                      </>
                    )}

                    {/* Ponctualité patients */}
                    {vacMetrics.on_time_patients !== undefined && (
                      <>
                        <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                          Ponctualité patients:
                        </Typography>
                        <StatItem
                          label="Patients à l'heure (±5min)"
                          value={vacMetrics.on_time_patients || 0}
                          color="success"
                        />
                        <StatItem
                          label="Patients en retard (>5min)"
                          value={vacMetrics.late_patients || 0}
                          color="danger"
                        />
                        <StatItem
                          label="Patients en avance (>5min)"
                          value={vacMetrics.early_patients || 0}
                          color="info"
                        />
                        {vacMetrics.average_patient_delay !== undefined && (
                          <StatItem
                            label="Retard moyen patient"
                            value={vacMetrics.average_patient_delay}
                            unit="minutes"
                            color={vacMetrics.average_patient_delay > 0 ? "warning" : "success"}
                          />
                        )}
                      </>
                    )}

                    {/* Première/dernière consultation par vacation */}
                    {vacMetrics.vacation_first_last_analysis &&
                      Array.isArray(vacMetrics.vacation_first_last_analysis) &&
                      vacMetrics.vacation_first_last_analysis.length > 0 && (
                        <>
                          <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                            Première/dernière consultation par vacation:
                          </Typography>
                          {vacMetrics.vacation_first_last_analysis.map(
                            (vacInfo: any, index: number) => (
                              <Box
                                key={index}
                                sx={{
                                  ml: 1,
                                  p: 1,
                                  border: "1px solid",
                                  borderColor: "divider",
                                  borderRadius: 1,
                                  mb: 0.5,
                                }}
                              >
                                <Typography level="body-xs" sx={{ fontWeight: "bold" }}>
                                  Vacation {vacInfo.vacation_start} - {vacInfo.vacation_end}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  Première consultation: {vacInfo.first_consultation_time}
                                </Typography>
                                <Typography
                                  level="body-xs"
                                  color={
                                    vacInfo.delay_first_vs_vacation_minutes > 0
                                      ? "warning"
                                      : vacInfo.delay_first_vs_vacation_minutes < 0
                                        ? "success"
                                        : "neutral"
                                  }
                                >
                                  {vacInfo.first_delay_status}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  Dernière fin: {vacInfo.last_consultation_end_time}
                                </Typography>
                                <Typography
                                  level="body-xs"
                                  color={
                                    vacInfo.delay_last_vs_vacation_minutes > 0
                                      ? "danger"
                                      : vacInfo.delay_last_vs_vacation_minutes < 0
                                        ? "success"
                                        : "neutral"
                                  }
                                >
                                  {vacInfo.last_delay_status}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  {vacInfo.consultations_count} consultation
                                  {vacInfo.consultations_count > 1 ? "s" : ""}
                                </Typography>
                              </Box>
                            )
                          )}
                        </>
                      )}

                    {/* Temps inter-consultations */}
                    {vacMetrics.inter_consultation_metrics &&
                      Object.keys(vacMetrics.inter_consultation_metrics).length > 0 && (
                        <>
                          <Divider />
                          <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                            Temps entre consultations:
                          </Typography>
                          <StatItem
                            label="Temps total inter-consultations"
                            value={
                              vacMetrics.inter_consultation_metrics
                                .total_inter_consultation_time_minutes
                            }
                            unit="minutes"
                            color="info"
                          />
                          <StatItem
                            label="Temps moyen inter-consultations"
                            value={
                              vacMetrics.inter_consultation_metrics
                                .mean_inter_consultation_time_minutes
                            }
                            unit="minutes"
                            color="info"
                          />
                          <StatItem
                            label="Min/Max inter-consultations"
                            value={`${formatNumber(vacMetrics.inter_consultation_metrics.min_inter_consultation_time_minutes)} / ${formatNumber(vacMetrics.inter_consultation_metrics.max_inter_consultation_time_minutes)}`}
                            unit="minutes"
                            color="neutral"
                          />
                        </>
                      )}

                    {/* Taux d'occupation par vacation */}
                    {vacMetrics.vacation_occupation_details &&
                      Array.isArray(vacMetrics.vacation_occupation_details) &&
                      vacMetrics.vacation_occupation_details.length > 0 && (
                        <>
                          <Divider />
                          <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                            Taux d'occupation par vacation:
                          </Typography>
                          {vacMetrics.vacation_occupation_details.map(
                            (vacOccup: any, index: number) => (
                              <Box
                                key={index}
                                sx={{
                                  ml: 1,
                                  p: 1,
                                  border: "1px solid",
                                  borderColor: "divider",
                                  borderRadius: 1,
                                  mb: 0.5,
                                }}
                              >
                                <Typography level="body-xs" sx={{ fontWeight: "bold" }}>
                                  Vacation {vacOccup.vacation_start} - {vacOccup.vacation_end}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  Durée vacation:{" "}
                                  {formatMinutes(vacOccup.vacation_duration_minutes)}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  Temps consultations:{" "}
                                  {formatMinutes(vacOccup.total_consultation_time_minutes)}
                                </Typography>
                                <Typography
                                  level="body-xs"
                                  color={
                                    vacOccup.occupation_rate_percent > 80
                                      ? "success"
                                      : vacOccup.occupation_rate_percent > 50
                                        ? "warning"
                                        : "danger"
                                  }
                                >
                                  Taux d'occupation: {vacOccup.occupation_rate_percent.toFixed(1)}%
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  {vacOccup.consultations_count} consultation
                                  {vacOccup.consultations_count > 1 ? "s" : ""}
                                </Typography>
                              </Box>
                            )
                          )}
                        </>
                      )}

                    {/* Extrêmes retards */}
                    {vacMetrics.delay_extremes &&
                      Object.keys(vacMetrics.delay_extremes).length > 0 && (
                        <>
                          <Divider />
                          <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                            Extrêmes retards/avances:
                          </Typography>
                          {vacMetrics.delay_extremes.consultation_delays && (
                            <>
                              <Typography
                                level="body-xs"
                                color="neutral"
                                sx={{ fontWeight: "bold" }}
                              >
                                Consultations:
                              </Typography>
                              <Typography level="body-xs" color="success" sx={{ ml: 1 }}>
                                {vacMetrics.delay_extremes.consultation_delays.min_delay_status}
                              </Typography>
                              <Typography level="body-xs" color="danger" sx={{ ml: 1 }}>
                                {vacMetrics.delay_extremes.consultation_delays.max_delay_status}
                              </Typography>
                            </>
                          )}
                          {vacMetrics.delay_extremes.patient_delays && (
                            <>
                              <Typography
                                level="body-xs"
                                color="neutral"
                                sx={{ fontWeight: "bold" }}
                              >
                                Patients:
                              </Typography>
                              <Typography level="body-xs" color="success" sx={{ ml: 1 }}>
                                {vacMetrics.delay_extremes.patient_delays.min_delay_status}
                              </Typography>
                              <Typography level="body-xs" color="danger" sx={{ ml: 1 }}>
                                {vacMetrics.delay_extremes.patient_delays.max_delay_status}
                              </Typography>
                            </>
                          )}
                        </>
                      )}

                    {/* Détail des consultations - MOVED TO END */}
                    {data.consultations_detail && (
                      <>
                        <Divider />
                        <Stack direction="row" justifyContent="space-between" alignItems="center">
                          <Typography level="title-sm">Détail des consultations</Typography>
                          <Button
                            size="sm"
                            variant="soft"
                            color="neutral"
                            onClick={() => setShowAllOperations(!showAllOperations)}
                            sx={{ minHeight: "auto", py: 0.5, px: 1 }}
                          >
                            {showAllOperations ? "Masquer" : "Voir tout"}
                          </Button>
                        </Stack>
                        {(() => {
                          const consultations = data.consultations_detail;
                          if (Array.isArray(consultations) && consultations.length > 0) {
                            const consultationsToShow = showAllOperations
                              ? consultations
                              : consultations.slice(0, 3);
                            return (
                              <>
                                {consultationsToShow.map((consult: any, index: number) => (
                                  <Box
                                    key={index}
                                    sx={{
                                      ml: 1,
                                      p: 1,
                                      border: "1px solid",
                                      borderColor: "divider",
                                      borderRadius: 1,
                                    }}
                                  >
                                    <Typography level="body-xs" sx={{ fontWeight: "bold" }}>
                                      {consult.patient_nom || "Patient"}
                                    </Typography>
                                    <Typography level="body-xs" color="neutral">
                                      Prévu: {consult.consultation_debut_prevu} | Réel:{" "}
                                      {consult.consultation_debut_reel || "N/A"} | Arrivée:{" "}
                                      {consult.consultation_arrive || "N/A"}
                                    </Typography>
                                    <Typography level="body-xs" color="neutral">
                                      État: {consult.etat_consultation || "N/A"}
                                    </Typography>
                                    {consult.vacation_metrics && (
                                      <Stack spacing={0.5} sx={{ mt: 0.5 }}>
                                        {consult.vacation_metrics.vacation_start && (
                                          <Typography level="body-xs" color="neutral">
                                            Vacation: {consult.vacation_metrics.vacation_start} -{" "}
                                            {consult.vacation_metrics.vacation_end}
                                          </Typography>
                                        )}
                                        {consult.vacation_metrics.delay_from_planned_minutes !==
                                          undefined && (
                                          <Typography
                                            level="body-xs"
                                            color={
                                              consult.vacation_metrics.delay_from_planned_minutes >
                                              0
                                                ? "warning"
                                                : "success"
                                            }
                                          >
                                            Retard consultation:{" "}
                                            {consult.vacation_metrics.delay_from_planned_minutes > 0
                                              ? `${Math.abs(consult.vacation_metrics.delay_from_planned_minutes).toFixed(0)}min`
                                              : consult.vacation_metrics
                                                    .delay_from_planned_minutes < 0
                                                ? `Avance ${Math.abs(consult.vacation_metrics.delay_from_planned_minutes).toFixed(0)}min`
                                                : "À l'heure"}
                                          </Typography>
                                        )}
                                        {consult.vacation_metrics.patient_arrival_delay_minutes !==
                                          undefined && (
                                          <Typography
                                            level="body-xs"
                                            color={
                                              consult.vacation_metrics
                                                .patient_arrival_delay_minutes > 0
                                                ? "warning"
                                                : "success"
                                            }
                                          >
                                            Retard patient:{" "}
                                            {consult.vacation_metrics
                                              .patient_arrival_delay_minutes > 0
                                              ? `${Math.abs(consult.vacation_metrics.patient_arrival_delay_minutes).toFixed(0)}min`
                                              : consult.vacation_metrics
                                                    .patient_arrival_delay_minutes < 0
                                                ? `Avance ${Math.abs(consult.vacation_metrics.patient_arrival_delay_minutes).toFixed(0)}min`
                                                : "À l'heure"}
                                          </Typography>
                                        )}
                                      </Stack>
                                    )}
                                  </Box>
                                ))}
                                {!showAllOperations && consultations.length > 3 && (
                                  <Typography
                                    level="body-xs"
                                    color="neutral"
                                    sx={{ textAlign: "center" }}
                                  >
                                    ... et {consultations.length - 3} autres consultations
                                  </Typography>
                                )}
                              </>
                            );
                          }
                          return (
                            <Typography level="body-xs" color="neutral">
                              Aucun détail disponible
                            </Typography>
                          );
                        })()}
                      </>
                    )}

                    {/* Vacations disponibles - MOVED TO END */}
                    {data.available_vacations &&
                      Array.isArray(data.available_vacations) &&
                      data.available_vacations.length > 0 && (
                        <>
                          <Divider />
                          <Typography level="title-sm">
                            Vacation{data.available_vacations.length > 1 ? "s" : ""} consultation
                            {data.available_vacations.length > 1 ? "s" : ""} (
                            {data.available_vacations.length})
                          </Typography>
                          {data.available_vacations.map((vacation: any, index: number) => (
                            <Box
                              key={index}
                              sx={{
                                ml: 1,
                                p: 1,
                                border: "1px solid",
                                borderColor: "divider",
                                borderRadius: 1,
                              }}
                            >
                              <Typography level="body-xs">
                                {vacation.start_time} - {vacation.end_time}
                                {vacation.service && ` (${vacation.service})`}
                              </Typography>
                            </Box>
                          ))}
                        </>
                      )}
                  </>
                );
              })()}
            </>
          )}
        </Stack>
      );
    }

    // Si c'est les anciennes statistiques de consultations (fallback)
    if (data.total_count !== undefined || data.morning_afternoon_analysis) {
      return (
        <Stack spacing={2}>
          {data.total_count !== undefined && (
            <StatItem label="Total consultations" value={data.total_count} color="primary" />
          )}

          {data.morning_afternoon_analysis && (
            <>
              <Divider />
              <Typography level="title-sm">Répartition matin/après-midi</Typography>
              <StatItem
                label="Matin"
                value={data.morning_afternoon_analysis.morning_count || 0}
                color="success"
              />
              <StatItem
                label="Après-midi"
                value={data.morning_afternoon_analysis.afternoon_count || 0}
                color="warning"
              />
            </>
          )}

          {data.timing_analysis?.delay_statistics && (
            <>
              <Divider />
              <Typography level="title-sm">Ponctualité</Typography>
              <StatItem
                label="Retard moyen"
                value={data.timing_analysis.delay_statistics.mean_delay_minutes}
                unit="minutes"
                color="neutral"
              />
              <StatItem
                label="À l'heure"
                value={data.timing_analysis.delay_statistics.on_time_count}
                color="success"
              />
              <StatItem
                label="En retard"
                value={data.timing_analysis.delay_statistics.late_count}
                color="danger"
              />
            </>
          )}

          {data.status_distribution?.counts && (
            <>
              <Divider />
              <Typography level="title-sm">Statuts</Typography>
              {Object.entries(data.status_distribution.counts).map(([status, count]) => (
                <StatItem
                  key={status}
                  label={status}
                  value={count}
                  color={
                    status === "finie" ? "success" : status === "non_venu" ? "danger" : "warning"
                  }
                />
              ))}
            </>
          )}
        </Stack>
      );
    }

    // Si c'est les statistiques d'opérations avec nouveau système vacation
    if (
      data.vacation_analysis ||
      data.vacation_metrics_summary ||
      data.first_incision_analysis ||
      data.duration_analysis
    ) {
      return (
        <Stack spacing={2}>
          {data.total_operations !== undefined && (
            <StatItem label="Total opérations" value={data.total_operations} color="primary" />
          )}

          {/* Nouvelle section vacation metrics */}
          {(data.vacation_analysis || data.vacation_metrics_summary) && (
            <>
              <Divider />
              <Typography level="title-sm">Analyse vacations</Typography>
              {(() => {
                const vacMetrics = data.vacation_analysis || data.vacation_metrics_summary;
                return (
                  <>
                    <StatItem
                      label="Opérations avec vacation"
                      value={vacMetrics.operations_with_vacation || 0}
                      color="success"
                    />
                    <StatItem
                      label="Opérations sans vacation"
                      value={vacMetrics.operations_without_vacation || 0}
                      color="warning"
                    />

                    {/* Première incision par vacation */}
                    {vacMetrics.vacation_first_incisions &&
                      Array.isArray(vacMetrics.vacation_first_incisions) &&
                      vacMetrics.vacation_first_incisions.length > 0 && (
                        <>
                          <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                            Première incision par vacation:
                          </Typography>
                          {vacMetrics.vacation_first_incisions.map(
                            (vacInfo: any, index: number) => (
                              <Box
                                key={index}
                                sx={{
                                  ml: 1,
                                  p: 1,
                                  border: "1px solid",
                                  borderColor: "divider",
                                  borderRadius: 1,
                                  mb: 0.5,
                                }}
                              >
                                <Typography level="body-xs" sx={{ fontWeight: "bold" }}>
                                  Vacation {vacInfo.vacation_start} - {vacInfo.vacation_end}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  Première incision: {vacInfo.first_incision_time}
                                </Typography>
                                <Typography
                                  level="body-xs"
                                  color={
                                    vacInfo.delay_minutes > 0
                                      ? "warning"
                                      : vacInfo.delay_minutes < 0
                                        ? "success"
                                        : "neutral"
                                  }
                                >
                                  {vacInfo.delay_status}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  {vacInfo.operations_count} opération
                                  {vacInfo.operations_count > 1 ? "s" : ""}
                                </Typography>
                              </Box>
                            )
                          )}
                        </>
                      )}

                    {/* Nouvelles métriques opératoires globales */}
                    {vacMetrics.total_operative_time_minutes !== undefined && (
                      <>
                        <Divider />
                        <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                          Métriques opératoires:
                        </Typography>
                        <StatItem
                          label="Temps opératoire total"
                          value={vacMetrics.total_operative_time_minutes}
                          unit="minutes"
                          color="primary"
                        />
                        <StatItem
                          label="Durée moyenne d'opération"
                          value={vacMetrics.mean_operative_duration_minutes}
                          unit="minutes"
                          color="neutral"
                        />
                        <StatItem
                          label="Durée min/max d'opération"
                          value={`${formatNumber(vacMetrics.min_operative_duration_minutes)} / ${formatNumber(vacMetrics.max_operative_duration_minutes)}`}
                          unit="minutes"
                          color="neutral"
                        />
                        {vacMetrics.night_operations_count !== undefined && (
                          <>
                            <StatItem
                              label="Blocs de nuit"
                              value={vacMetrics.night_operations_count}
                              color="warning"
                            />
                            <StatItem
                              label="Blocs de jour"
                              value={vacMetrics.day_operations_count}
                              color="success"
                            />
                            {vacMetrics.night_operations_count > 0 && (
                              <Typography
                                level="body-xs"
                                color="neutral"
                                sx={{ ml: 1, fontStyle: "italic" }}
                              >
                                Bloc de nuit : incision/fin après 20h ou fin avant 8h
                              </Typography>
                            )}
                          </>
                        )}
                      </>
                    )}

                    {/* Métriques temps entre opérations */}
                    {vacMetrics.total_inter_operation_time_minutes !== undefined &&
                      vacMetrics.inter_operation_intervals_count > 0 && (
                        <>
                          <Divider />
                          <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                            Temps entre opérations:
                          </Typography>
                          <StatItem
                            label="Temps total entre opérations"
                            value={vacMetrics.total_inter_operation_time_minutes}
                            unit="minutes"
                            color="info"
                          />
                          <StatItem
                            label="Temps moyen entre opérations"
                            value={vacMetrics.mean_inter_operation_time_minutes}
                            unit="minutes"
                            color="info"
                          />
                        </>
                      )}

                    {/* Taux d'occupation par vacation (corrigé) */}
                    {vacMetrics.vacation_occupation_details &&
                      Array.isArray(vacMetrics.vacation_occupation_details) &&
                      vacMetrics.vacation_occupation_details.length > 0 && (
                        <>
                          <Divider />
                          <Typography level="body-sm" sx={{ mt: 1, mb: 0.5, fontWeight: "bold" }}>
                            Taux d'occupation par vacation:
                          </Typography>
                          {vacMetrics.vacation_occupation_details.map(
                            (vacOccup: any, index: number) => (
                              <Box
                                key={index}
                                sx={{
                                  ml: 1,
                                  p: 1,
                                  border: "1px solid",
                                  borderColor: "divider",
                                  borderRadius: 1,
                                  mb: 0.5,
                                }}
                              >
                                <Typography level="body-xs" sx={{ fontWeight: "bold" }}>
                                  Vacation {vacOccup.vacation_start} - {vacOccup.vacation_end}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  Durée vacation:{" "}
                                  {formatMinutes(vacOccup.vacation_duration_minutes)}
                                </Typography>
                                <Typography level="body-xs" color="neutral">
                                  Temps opératoire:{" "}
                                  {formatMinutes(vacOccup.total_operative_time_minutes)}
                                </Typography>
                                <Typography
                                  level="body-xs"
                                  color={
                                    vacOccup.occupation_rate_percent > 80
                                      ? "success"
                                      : vacOccup.occupation_rate_percent > 50
                                        ? "warning"
                                        : "danger"
                                  }
                                >
                                  Taux d'occupation: {vacOccup.occupation_rate_percent.toFixed(1)}%
                                </Typography>
                              </Box>
                            )
                          )}
                        </>
                      )}
                  </>
                );
              })()}
            </>
          )}

          {/* Métriques traditionnelles */}
          {data.first_incision_analysis && (
            <>
              <Divider />
              <Typography level="title-sm">Répartition</Typography>
              <StatItem
                label="Matin"
                value={data.first_incision_analysis.morning_operations_count || 0}
                color="success"
              />
              <StatItem
                label="Après-midi"
                value={data.first_incision_analysis.afternoon_operations_count || 0}
                color="warning"
              />
            </>
          )}

          {data.duration_analysis && (
            <>
              <Divider />
              <Typography level="title-sm">Durées</Typography>
              <StatItem
                label="Temps opératoire total"
                value={data.duration_analysis.total_operative_time_minutes}
                unit="minutes"
                color="primary"
              />
              <StatItem
                label="Durée moyenne"
                value={data.duration_analysis.mean_duration_minutes}
                unit="minutes"
                color="neutral"
              />
            </>
          )}

          {data.status_analysis && (
            <>
              <Divider />
              <Typography level="title-sm">Réalisation</Typography>
              <StatItem
                label="Terminées"
                value={data.status_analysis.completed_operations || 0}
                color="success"
              />
              <StatItem
                label="Taux de réalisation"
                value={data.status_analysis.completion_rate_percentage}
                unit="%"
                color="success"
              />
            </>
          )}

          {/* Autres métriques nouvelles */}
          {data.first_incision_time && (
            <>
              <Divider />
              <Typography level="title-sm">Horaires</Typography>
              <StatItem label="Première incision" value={data.first_incision_time} color="info" />
              <StatItem label="Dernière fermeture" value={data.last_closure_time} color="info" />
            </>
          )}

          {data.mean_inter_operation_time_minutes !== undefined && (
            <>
              <StatItem
                label="Temps inter-opérations"
                value={data.mean_inter_operation_time_minutes}
                unit="minutes"
                color="neutral"
              />
              <StatItem
                label="Durée session"
                value={data.session_duration_minutes}
                unit="minutes"
                color="primary"
              />
            </>
          )}

          {/* Détail des opérations par vacation - MOVED TO END */}
          {(data.operations_detail || data.operations_with_vacations) && (
            <>
              <Divider />
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography level="title-sm">Détail des opérations</Typography>
                <Button
                  size="sm"
                  variant="soft"
                  color="neutral"
                  onClick={() => setShowAllOperations(!showAllOperations)}
                  sx={{ minHeight: "auto", py: 0.5, px: 1 }}
                >
                  {showAllOperations ? "Masquer" : "Voir tout"}
                </Button>
              </Stack>
              {(() => {
                const operations = data.operations_detail || data.operations_with_vacations;
                if (Array.isArray(operations) && operations.length > 0) {
                  const operationsToShow = showAllOperations ? operations : operations.slice(0, 3);
                  return (
                    <>
                      {operationsToShow.map((op: any, index: number) => (
                        <Box
                          key={index}
                          sx={{
                            ml: 1,
                            p: 1,
                            border: "1px solid",
                            borderColor: "divider",
                            borderRadius: 1,
                          }}
                        >
                          <Typography level="body-xs" sx={{ fontWeight: "bold" }}>
                            {op.operation_title || "Opération sans titre"}
                          </Typography>
                          <Typography level="body-xs" color="neutral">
                            {op.operation_start} - {op.operation_end} (
                            {formatMinutes(op.duration_minutes)})
                          </Typography>
                          {op.vacation_metrics && (
                            <Stack spacing={0.5} sx={{ mt: 0.5 }}>
                              {op.vacation_metrics.vacation_start && (
                                <Typography level="body-xs" color="neutral">
                                  Vacation: {op.vacation_metrics.vacation_start} -{" "}
                                  {op.vacation_metrics.vacation_end}
                                </Typography>
                              )}
                              {op.vacation_metrics.match_type && (
                                <Typography level="body-xs" color="neutral">
                                  Match: {op.vacation_metrics.match_type}
                                </Typography>
                              )}
                              {op.vacation_metrics.delay_from_start_minutes !== undefined && (
                                <Typography
                                  level="body-xs"
                                  color={
                                    op.vacation_metrics.delay_from_start_minutes > 0
                                      ? "warning"
                                      : "success"
                                  }
                                >
                                  {op.vacation_metrics.delay_from_start_minutes > 0
                                    ? `Retard: ${Math.abs(op.vacation_metrics.delay_from_start_minutes).toFixed(0)}min`
                                    : op.vacation_metrics.delay_from_start_minutes < 0
                                      ? `Avance: ${Math.abs(op.vacation_metrics.delay_from_start_minutes).toFixed(0)}min`
                                      : "À l'heure"}
                                </Typography>
                              )}
                            </Stack>
                          )}
                        </Box>
                      ))}
                      {!showAllOperations && operations.length > 3 && (
                        <Typography level="body-xs" color="neutral" sx={{ textAlign: "center" }}>
                          ... et {operations.length - 3} autres opérations
                        </Typography>
                      )}
                    </>
                  );
                }
                return (
                  <Typography level="body-xs" color="neutral">
                    Aucun détail disponible
                  </Typography>
                );
              })()}
            </>
          )}

          {/* Vacations disponibles - MOVED TO END */}
          {data.available_vacations &&
            Array.isArray(data.available_vacations) &&
            data.available_vacations.length > 0 && (
              <>
                <Divider />
                <Typography level="title-sm">
                  Vacation{data.available_vacations.length > 1 ? "s" : ""} opératoire
                  {data.available_vacations.length > 1 ? "s" : ""} (
                  {data.available_vacations.length})
                </Typography>
                {data.available_vacations.map((vacation: any, index: number) => (
                  <Box
                    key={index}
                    sx={{
                      ml: 1,
                      p: 1,
                      border: "1px solid",
                      borderColor: "divider",
                      borderRadius: 1,
                    }}
                  >
                    <Typography level="body-xs">
                      {vacation.start_time} - {vacation.end_time}
                      {vacation.service && ` (${vacation.service})`}
                    </Typography>
                  </Box>
                ))}
              </>
            )}
        </Stack>
      );
    }

    // Affichage générique pour autres données
    return (
      <Stack spacing={1}>
        {Object.entries(data).map(([key, value]) => (
          <StatItem key={key} label={key} value={value} />
        ))}
      </Stack>
    );
  };

  return (
    <Card sx={{ p: 3 }}>
      <Typography level="h4" sx={{ mb: 2 }}>
        {title}
      </Typography>
      {renderFriendlyContent()}
    </Card>
  );
};

export default function DailyStats() {
  const api = useApi();
  const auth = useAuthenticatedUser();
  const [consultationStats, setConsultationStats] = useState<StatsData | null>(null);
  const [operationStats, setOperationStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split("T")[0]);

  const loadStats = useCallback(async () => {
    if (!auth.user) {
      setError("Utilisateur non authentifié");
      return;
    }

    try {
      setStatsLoading(true);
      setError(null);

      // Appel à l'API réelle avec l'ID utilisateur
      const response = await api.getDailyStats(
        selectedDate,
        selectedDate,
        undefined, // pas d'intervalles multiples pour l'instant
        auth.user.id
      );

      // La structure de réponse peut être différente selon le backend
      if (response.data) {
        // Si la réponse a une structure { success: true, data: {...} }
        setConsultationStats(response.data.consultations || response.data);
        setOperationStats(response.data.operations || response.data);
      } else {
        // Si c'est la structure originale
        setConsultationStats(response.consultations);
        setOperationStats(response.operations);
      }

      setLoading(false);
      setStatsLoading(false);
    } catch (err) {
      console.error("Erreur lors du chargement des statistiques:", err);
      setError("Erreur lors du chargement des statistiques");
      setLoading(false);
      setStatsLoading(false);
    }
  }, [api, auth.user, selectedDate]);

  useEffect(() => {
    if (api && auth.user) {
      loadStats();
    }
  }, [selectedDate, api, auth.user, loadStats]);

  // Fonctions de navigation de dates
  const goToPreviousDay = () => {
    const currentDate = new Date(selectedDate);
    currentDate.setDate(currentDate.getDate() - 1);
    setSelectedDate(currentDate.toISOString().split("T")[0]);
  };

  const goToNextDay = () => {
    const currentDate = new Date(selectedDate);
    currentDate.setDate(currentDate.getDate() + 1);
    setSelectedDate(currentDate.toISOString().split("T")[0]);
  };

  const goToToday = () => {
    setSelectedDate(new Date().toISOString().split("T")[0]);
  };

  // Formater la date pour l'affichage
  const formatDisplayDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isToday = selectedDate === new Date().toISOString().split("T")[0];

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "50vh" }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert color="danger">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        {/* En-tête avec navigation de dates */}
        <Card sx={{ p: 3 }}>
          <Stack spacing={2}>
            <Typography level="h2" sx={{ textAlign: "center" }}>
              Statistiques du jour
            </Typography>

            {/* Navigation de dates */}
            <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
              <IconButton onClick={goToPreviousDay} variant="soft" color="primary">
                <ArrowBackIcon />
              </IconButton>

              <Box sx={{ minWidth: 300, textAlign: "center" }}>
                <Typography level="title-lg" sx={{ mb: 1 }}>
                  {formatDisplayDate(selectedDate)}
                </Typography>
                <Input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  sx={{
                    maxWidth: 200,
                    mx: "auto",
                  }}
                />
              </Box>

              <IconButton onClick={goToNextDay} variant="soft" color="primary">
                <ArrowForwardIcon />
              </IconButton>
            </Stack>

            {/* Bouton aujourd'hui */}
            {!isToday && (
              <Box sx={{ textAlign: "center" }}>
                <Button
                  onClick={goToToday}
                  variant="outlined"
                  color="primary"
                  startDecorator={<CalendarTodayIcon />}
                  size="sm"
                >
                  Aujourd'hui
                </Button>
              </Box>
            )}
          </Stack>
        </Card>

        {/* Affichage des statistiques */}
        <Stack spacing={3} direction={{ xs: "column", md: "row" }}>
          {/* Statistiques des consultations */}
          <Box sx={{ flex: 1, position: "relative" }}>
            {statsLoading && (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 1,
                  borderRadius: "8px",
                }}
              >
                <CircularProgress size="sm" />
              </Box>
            )}
            <FriendlyDisplay data={consultationStats} title="Consultations" />
          </Box>

          {/* Statistiques des opérations */}
          <Box sx={{ flex: 1, position: "relative" }}>
            {statsLoading && (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  zIndex: 1,
                  borderRadius: "8px",
                }}
              >
                <CircularProgress size="sm" />
              </Box>
            )}
            <FriendlyDisplay data={operationStats} title="Opérations" />
          </Box>
        </Stack>
      </Stack>
    </Box>
  );
}
