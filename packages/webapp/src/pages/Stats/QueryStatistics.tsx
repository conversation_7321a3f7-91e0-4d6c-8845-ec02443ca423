import React, { useCallback, useEffect, useState } from "react";

import AddIcon from "@mui/icons-material/Add";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import ClearIcon from "@mui/icons-material/Clear";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import SearchIcon from "@mui/icons-material/Search";
import Alert from "@mui/joy/Alert";
import Autocomplete from "@mui/joy/Autocomplete";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import Chip from "@mui/joy/Chip";
import CircularProgress from "@mui/joy/CircularProgress";
import Divider from "@mui/joy/Divider";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Table from "@mui/joy/Table";
import Typography from "@mui/joy/Typography";

import TagAutocomplete from "../../components/Forms/TagAutocomplete";
import { useApi } from "../../contexts/ApiContext";
import { useAuthenticatedUser } from "../../contexts/UserContext";
import { Protocole } from "../../models/types";

// Types pour les filtres de recherche
interface SearchFilters {
  startDate: string;
  endDate: string;
  dataType: "patients" | "operations" | "consultations" | "hospitalisations";
  tags: string[];
  ccamCodes: string[];
  protocols: string[];
}

// Types pour les résultats
interface SearchResult {
  id: string;
  type: string;
  patient_name?: string;
  patient_id?: string;
  date: string;
  title?: string;
  tags?: string[];
  ccam_codes?: string[];
  protocols?: string[];
  status?: string;
}

const QueryStatistics: React.FC = () => {
  const api = useApi();
  const user = useAuthenticatedUser();

  // États pour les filtres
  const [filters, setFilters] = useState<SearchFilters>({
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    dataType: "patients",
    tags: [],
    ccamCodes: [],
    protocols: [],
  });

  // États pour les données
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // États pour les options disponibles
  const [availableProtocols, setAvailableProtocols] = useState<Protocole[]>([]);

  // État pour l'input CCAM libre
  const [ccamInput, setCcamInput] = useState<string>("");

  const loadAvailableOptions = useCallback(async () => {
    try {
      // Charger les protocoles depuis la même route que OperationForm
      const protocols = await api.getProtocoles();
      setAvailableProtocols(protocols);
    } catch (error) {
      console.error("Erreur lors du chargement des options:", error);
    }
  }, [api]);

  // Charger les options disponibles au montage
  useEffect(() => {
    loadAvailableOptions();
  }, [loadAvailableOptions]);

  const handleSearch = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.queryStatistics({
        start_date: filters.startDate,
        end_date: filters.endDate,
        data_type: filters.dataType,
        tags: filters.tags,
        ccam_codes: filters.ccamCodes,
        protocols: filters.protocols,
        user_id: user.user.id,
      });

      if (response && response.success) {
        setResults(response.results || []);
      } else {
        setError(response?.error || "Erreur lors de la recherche");
      }
    } catch (error: any) {
      setError(error.message || "Erreur lors de la recherche");
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = async () => {
    try {
      const response = await api.exportStatistics({
        start_date: filters.startDate,
        end_date: filters.endDate,
        data_type: filters.dataType,
        tags: filters.tags,
        ccam_codes: filters.ccamCodes,
        protocols: filters.protocols,
        user_id: user.user.id,
        format: "excel",
      });

      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `statistiques_${filters.dataType}_${filters.startDate}_${filters.endDate}.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      setError("Erreur lors de l'export Excel: " + (error.message || "Erreur inconnue"));
    }
  };

  const handleAddCcamCode = () => {
    if (ccamInput.trim() && !filters.ccamCodes.includes(ccamInput.trim())) {
      setFilters((prev) => ({
        ...prev,
        ccamCodes: [...prev.ccamCodes, ccamInput.trim()],
      }));
      setCcamInput("");
    }
  };

  const handleRemoveCcamCode = useCallback((codeToRemove: string) => {
    setFilters((prev) => ({
      ...prev,
      ccamCodes: prev.ccamCodes.filter((code) => code !== codeToRemove),
    }));
  }, []);

  const handleCcamInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddCcamCode();
    }
  };

  const clearFilters = () => {
    setFilters({
      startDate: new Date().toISOString().split("T")[0],
      endDate: new Date().toISOString().split("T")[0],
      dataType: "patients",
      tags: [],
      ccamCodes: [],
      protocols: [],
    });
    setCcamInput("");
    setResults([]);
    setError(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("fr-FR");
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography level="h2" sx={{ mb: 3 }}>
        Recherche et Export de Données
      </Typography>

      {/* Filtres de recherche */}
      <Card sx={{ p: 3, mb: 3 }}>
        <Typography level="h4" sx={{ mb: 2 }}>
          Critères de recherche
        </Typography>

        <Stack spacing={3}>
          {/* Dates */}
          <Stack direction="row" spacing={2}>
            <FormControl sx={{ flex: 1 }}>
              <FormLabel>Date de début</FormLabel>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters((prev) => ({ ...prev, startDate: e.target.value }))}
                startDecorator={<CalendarTodayIcon />}
              />
            </FormControl>
            <FormControl sx={{ flex: 1 }}>
              <FormLabel>Date de fin</FormLabel>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters((prev) => ({ ...prev, endDate: e.target.value }))}
                startDecorator={<CalendarTodayIcon />}
              />
            </FormControl>
          </Stack>

          {/* Type de données */}
          <FormControl>
            <FormLabel>Type de données</FormLabel>
            <Select
              value={filters.dataType}
              onChange={(_, value) => setFilters((prev) => ({ ...prev, dataType: value as any }))}
            >
              <Option value="patients">Patients</Option>
              <Option value="operations">Opérations</Option>
              <Option value="consultations">Consultations</Option>
              <Option value="hospitalisations">Hospitalisations</Option>
            </Select>
          </FormControl>

          {/* Tags */}
          <FormControl>
            <FormLabel>Tags</FormLabel>
            <TagAutocomplete
              value={filters.tags}
              onChange={(newTags) => setFilters((prev) => ({ ...prev, tags: newTags }))}
              helperText={true}
            />
          </FormControl>

          {/* Codes CCAM */}
          <FormControl>
            <FormLabel>Codes CCAM</FormLabel>
            <Stack spacing={1}>
              <Stack direction="row" spacing={1}>
                <Input
                  placeholder="Tapez un code CCAM (ex: YYYY123)..."
                  value={ccamInput}
                  onChange={(e) => setCcamInput(e.target.value)}
                  onKeyDown={handleCcamInputKeyPress}
                  sx={{ flex: 1 }}
                />
                <Button
                  variant="outlined"
                  size="sm"
                  onClick={handleAddCcamCode}
                  disabled={!ccamInput.trim()}
                  startDecorator={<AddIcon />}
                >
                  Ajouter
                </Button>
              </Stack>
              {filters.ccamCodes.length > 0 && (
                <Stack direction="row" spacing={0.5} flexWrap="wrap">
                  {filters.ccamCodes.map((code) => (
                    <Box
                      key={code}
                      sx={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: 0.5,
                        px: 1,
                        py: 0.5,
                        backgroundColor: 'success.softBg',
                        color: 'success.softColor',
                        borderRadius: 'sm',
                        fontSize: 'sm',
                        fontWeight: 'md'
                      }}
                    >
                      <span>{code}</span>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('Suppression du code CCAM:', code);
                          handleRemoveCcamCode(code);
                        }}
                        style={{
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          padding: '2px',
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: '#d32f2f',
                          fontSize: '16px',
                          fontWeight: 'bold',
                          width: '18px',
                          height: '18px',
                          lineHeight: '1'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#ffebee';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                        title={`Supprimer ${code}`}
                      >
                        ×
                      </button>
                    </Box>
                  ))}
                </Stack>
              )}
            </Stack>
          </FormControl>

          {/* Protocoles */}
          <FormControl>
            <FormLabel>Protocoles</FormLabel>
            <Autocomplete
              multiple
              options={availableProtocols}
              value={availableProtocols.filter((p) => filters.protocols.includes(p.id))}
              onChange={(_, newValue) => {
                const protocolIds = newValue.map((protocol) => protocol.id);
                setFilters((prev) => ({ ...prev, protocols: protocolIds }));
              }}
              getOptionLabel={(option) => option.libelle}
              placeholder="Sélectionnez des protocoles..."
              renderTags={(tags, getTagProps) =>
                tags.map((item, index) => (
                  <Chip variant="soft" color="warning" {...getTagProps({ index })}>
                    {item.libelle}
                  </Chip>
                ))
              }
            />
          </FormControl>

          {/* Boutons d'action */}
          <Stack direction="row" spacing={2}>
            <Button
              startDecorator={<SearchIcon />}
              onClick={handleSearch}
              loading={loading}
              disabled={loading}
            >
              Rechercher
            </Button>
            <Button variant="outlined" startDecorator={<ClearIcon />} onClick={clearFilters}>
              Effacer
            </Button>
            {results.length > 0 && (
              <Button
                variant="soft"
                color="success"
                startDecorator={<FileDownloadIcon />}
                onClick={handleExportExcel}
              >
                Export Excel
              </Button>
            )}
          </Stack>
        </Stack>
      </Card>

      {/* Messages d'erreur */}
      {error && (
        <Alert color="danger" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Résultats */}
      {results.length > 0 && (
        <Card sx={{ p: 0 }}>
          <Box
            sx={{ p: 2, display: "flex", justifyContent: "space-between", alignItems: "center" }}
          >
            <Typography level="h4">
              Résultats ({results.length} {filters.dataType})
            </Typography>
            <Chip color="primary" variant="soft">
              {formatDate(filters.startDate)} - {formatDate(filters.endDate)}
            </Chip>
          </Box>
          <Divider />

          <Sheet sx={{ overflow: "auto", maxHeight: "600px" }}>
            <Table stickyHeader>
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Patient</th>
                  <th>Titre/Description</th>
                  <th>Tags</th>
                  <th>CCAM</th>
                  <th>Protocoles</th>
                  <th>Statut</th>
                </tr>
              </thead>
              <tbody>
                {results.map((result) => (
                  <tr key={result.id}>
                    <td>{formatDate(result.date)}</td>
                    <td>{result.patient_name || "-"}</td>
                    <td>{result.title || "-"}</td>
                    <td>
                      {result.tags && result.tags.length > 0 ? (
                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                          {result.tags.map((tag, idx) => (
                            <Chip key={idx} size="sm" variant="outlined" color="primary">
                              {tag}
                            </Chip>
                          ))}
                        </Stack>
                      ) : (
                        "-"
                      )}
                    </td>
                    <td>
                      {result.ccam_codes && result.ccam_codes.length > 0 ? (
                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                          {result.ccam_codes.map((code, idx) => (
                            <Chip key={idx} size="sm" variant="outlined" color="success">
                              {code}
                            </Chip>
                          ))}
                        </Stack>
                      ) : (
                        "-"
                      )}
                    </td>
                    <td>
                      {result.protocols && result.protocols.length > 0 ? (
                        <Stack direction="row" spacing={0.5} flexWrap="wrap">
                          {result.protocols.map((protocol, idx) => (
                            <Chip key={idx} size="sm" variant="outlined" color="warning">
                              {protocol}
                            </Chip>
                          ))}
                        </Stack>
                      ) : (
                        "-"
                      )}
                    </td>
                    <td>
                      {result.status && (
                        <Chip
                          size="sm"
                          color={
                            result.status === "termine"
                              ? "success"
                              : result.status === "annule"
                                ? "danger"
                                : "neutral"
                          }
                        >
                          {result.status}
                        </Chip>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Sheet>
        </Card>
      )}

      {/* État de chargement */}
      {loading && (
        <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Message si aucun résultat */}
      {!loading && results.length === 0 && !error && (
        <Card sx={{ p: 3, textAlign: "center" }}>
          <Typography level="body-lg" color="neutral">
            Aucun résultat trouvé. Utilisez les filtres ci-dessus pour rechercher des données.
          </Typography>
        </Card>
      )}
    </Box>
  );
};

export default QueryStatistics;
