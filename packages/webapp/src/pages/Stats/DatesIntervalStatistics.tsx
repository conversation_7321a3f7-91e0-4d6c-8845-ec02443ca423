import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import AddIcon from "@mui/icons-material/Add";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import CompareArrowsIcon from "@mui/icons-material/CompareArrows";
import DeleteIcon from "@mui/icons-material/Delete";
import HistoryIcon from "@mui/icons-material/History";
import Alert from "@mui/joy/Alert";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import Checkbox from "@mui/joy/Checkbox";
import Chip from "@mui/joy/Chip";
import CircularProgress from "@mui/joy/CircularProgress";
import Divider from "@mui/joy/Divider";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Grid from "@mui/joy/Grid";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Table from "@mui/joy/Table";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useAuthenticatedUser } from "../../contexts/UserContext";

// Types pour les intervalles de dates
interface DateInterval {
  id: string;
  label: string;
  startDate: string;
  endDate: string;
  color?: string;
}

// Types pour les données statistiques
interface IntervalStats {
  interval_info: {
    label: string;
    start_date: string;
    end_date: string;
    duration_days: number;
  };
  operations?: any;
  consultations?: any;
}

// Types pour les graphiques
interface ChartData {
  period: string;
  [key: string]: any;
}

const DatesIntervalStatistics: React.FC = () => {
  const api = useApi();
  const user = useAuthenticatedUser();

  // Palette de couleurs pour les périodes
  const colorPalette = [
    "#0B6BCB", // Bleu primary
    "#1C7A00", // Vert success
    "#EE5A24", // Orange warning
    "#5F27CD", // Violet
    "#00A8CC", // Cyan
    "#FF6B6B", // Rouge
    "#FFA726", // Orange clair
    "#66BB6A", // Vert clair
    "#42A5F5", // Bleu clair
    "#AB47BC", // Violet clair
  ];

  // États pour les intervalles
  const [intervals, setIntervals] = useState<DateInterval[]>([
    {
      id: "1",
      label: "Période 1",
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
      endDate: new Date().toISOString().split("T")[0],
      color: colorPalette[0],
    },
  ]);

  // États pour les options
  const [includeOperations, setIncludeOperations] = useState(true);
  const [includeConsultations, setIncludeConsultations] = useState(true);
  const [groupBy, setGroupBy] = useState<"day" | "week" | "month" | "year">("day");

  // États pour les données
  const [statsResults, setStatsResults] = useState<IntervalStats[]>([]);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // États pour l'affichage
  // Removed unused state: selectedMetrics

  // États pour la modal graphique
  const [chartModalOpen, setChartModalOpen] = useState(false);
  const [selectedChart, setSelectedChart] = useState<{
    title: string;
    component: React.ReactNode;
  } | null>(null);

  // Fonctions helper pour les dates
  const getDateRange = (
    type:
      | "current-year"
      | "rolling-year"
      | "current-month"
      | "current-week"
      | "previous-week"
      | "previous-month"
      | "previous-year"
  ) => {
    const today = new Date();
    let startDate: Date;
    let endDate: Date;
    let label: string;

    switch (type) {
      case "current-year":
        startDate = new Date(today.getFullYear(), 0, 1); // 1er janvier
        endDate = new Date(today); // Copie de today pour éviter la mutation
        label = `Année ${today.getFullYear()}`;
        break;

      case "rolling-year":
        startDate = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000); // 365 jours avant
        endDate = today;
        label = `Année glissante`;
        break;

      case "current-month":
        startDate = new Date(today.getFullYear(), today.getMonth(), 1); // 1er du mois
        endDate = new Date(today); // Copie de today pour éviter la mutation
        label = `${today.toLocaleDateString("fr-FR", { month: "long", year: "numeric" })}`;
        break;

      case "current-week":
        const currentWeekStart = new Date(today);
        currentWeekStart.setDate(today.getDate() - today.getDay() + 1); // Lundi
        startDate = currentWeekStart;
        endDate = today;
        label = `Semaine en cours`;
        break;

      case "previous-week":
        const prevWeekEnd = new Date(today);
        prevWeekEnd.setDate(today.getDate() - today.getDay()); // Dimanche précédent
        const prevWeekStart = new Date(prevWeekEnd);
        prevWeekStart.setDate(prevWeekEnd.getDate() - 6); // Lundi précédent
        startDate = prevWeekStart;
        endDate = prevWeekEnd;
        label = `Semaine précédente`;
        break;

      case "previous-month":
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1); // 1er du mois précédent
        endDate = new Date(today.getFullYear(), today.getMonth(), 0); // Dernier jour du mois précédent
        label = `${startDate.toLocaleDateString("fr-FR", { month: "long", year: "numeric" })}`;
        break;

      case "previous-year":
        startDate = new Date(today.getFullYear() - 1, 0, 1); // 1er janvier année précédente
        endDate = new Date(today.getFullYear() - 1, 11, 31); // 31 décembre année précédente
        label = `Année ${today.getFullYear() - 1}`;
        break;

      default:
        startDate = today;
        endDate = today;
        label = "Période personnalisée";
    }

    // Assurer que les dates sont en timezone locale pour éviter les décalages
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    };

    return {
      startDate: formatLocalDate(startDate),
      endDate: formatLocalDate(endDate),
      label,
    };
  };

  const setFirstIntervalQuick = (
    type: "current-year" | "rolling-year" | "current-month" | "current-week"
  ) => {
    const { startDate, endDate, label } = getDateRange(type);

    // Remplacer la première période ou créer une nouvelle si aucune n'existe
    const updatedIntervals = [...intervals];
    if (updatedIntervals.length > 0) {
      // Remplacer la première période
      updatedIntervals[0] = {
        ...updatedIntervals[0],
        label,
        startDate,
        endDate,
      };
    } else {
      // Créer la première période si aucune n'existe
      updatedIntervals.push({
        id: Math.random().toString(36).substring(2, 11),
        label,
        startDate,
        endDate,
        color: colorPalette[0],
      });
    }
    setIntervals(updatedIntervals);
  };

  const addOrUpdateRelativePeriod = (
    baseInterval: DateInterval,
    type: "previous-week" | "previous-month" | "previous-year"
  ) => {
    const baseStart = new Date(baseInterval.startDate);
    const baseEnd = new Date(baseInterval.endDate);
    let newStart: Date;
    let newEnd: Date;
    let label: string;

    // Chercher s'il existe déjà une période relative de ce type pour cet intervalle de base
    const relativeTypeLabels = {
      "previous-week": "Semaine -1",
      "previous-month": "Mois -1",
      "previous-year": "Année -1",
    };

    const existingRelativeIndex = intervals.findIndex(
      (interval) =>
        interval.label.includes(baseInterval.label) &&
        interval.label.includes(relativeTypeLabels[type])
    );

    switch (type) {
      case "previous-week":
        const weekDuration = 7 * 24 * 60 * 60 * 1000;
        newStart = new Date(baseStart.getTime() - weekDuration);
        newEnd = new Date(baseEnd.getTime() - weekDuration);
        label = `${baseInterval.label} (Semaine -1)`;
        break;

      case "previous-month":
        newStart = new Date(baseStart);
        newStart.setMonth(newStart.getMonth() - 1);
        newStart.setDate(1); // Commencer au 1er du mois précédent

        // Fin au dernier jour du mois précédent
        newEnd = new Date(newStart.getFullYear(), newStart.getMonth() + 1, 0);

        label = `${baseInterval.label} (Mois -1)`;
        break;

      case "previous-year":
        newStart = new Date(baseStart);
        newEnd = new Date(baseEnd);
        newStart.setFullYear(newStart.getFullYear() - 1);
        newEnd.setFullYear(newEnd.getFullYear() - 1);

        // Commencer au 1er janvier
        newStart.setMonth(0, 1);
        // Finir au 31 décembre
        newEnd.setMonth(11, 31);

        label = `${baseInterval.label} (Année -1)`;
        break;

      default:
        return;
    }

    // Utiliser le même formatage local pour éviter les décalages timezone
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    };

    const relativeInterval: DateInterval = {
      id:
        existingRelativeIndex >= 0
          ? intervals[existingRelativeIndex].id
          : Math.random().toString(36).substring(2, 11),
      label,
      startDate: formatLocalDate(newStart),
      endDate: formatLocalDate(newEnd),
      color:
        existingRelativeIndex >= 0
          ? intervals[existingRelativeIndex].color
          : colorPalette[intervals.length % colorPalette.length],
    };

    // Mettre à jour ou ajouter la période relative
    const updatedIntervals = [...intervals];
    if (existingRelativeIndex >= 0) {
      // Modifier l'existante
      updatedIntervals[existingRelativeIndex] = relativeInterval;
    } else {
      // Ajouter une nouvelle
      updatedIntervals.push(relativeInterval);
    }

    setIntervals(updatedIntervals);
  };

  const addNewInterval = () => {
    const newInterval: DateInterval = {
      id: Math.random().toString(36).substring(2, 11),
      label: `Période ${intervals.length + 1}`,
      startDate: new Date().toISOString().split("T")[0],
      endDate: new Date().toISOString().split("T")[0],
      color: colorPalette[intervals.length % colorPalette.length],
    };
    setIntervals([...intervals, newInterval]);
  };

  const updateInterval = (id: string, updates: Partial<DateInterval>) => {
    setIntervals(
      intervals.map((interval) => (interval.id === id ? { ...interval, ...updates } : interval))
    );
  };

  const removeInterval = (id: string) => {
    if (intervals.length > 1) {
      setIntervals(intervals.filter((interval) => interval.id !== id));
    }
  };

  const generateStatistics = async () => {
    setLoading(true);
    setError(null);

    try {
      const requestData = {
        date_intervals: intervals.map((interval) => ({
          start: interval.startDate,
          end: interval.endDate,
          label: interval.label,
        })),
        user_id: user.user.id,
        include_operations: includeOperations,
        include_consultations: includeConsultations,
        group_by: groupBy,
      };

      const response = await api.getIntervalsComparison(requestData);

      if (response && response.success) {
        setStatsResults(response.intervals_analysis || []);
        setChartData(response.chart_data || []);
      } else {
        setError(response?.error || "Erreur lors de la génération des statistiques");
      }
    } catch (error: any) {
      setError(error.message || "Erreur lors de la génération des statistiques");
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (value: any) => {
    if (value === null || value === undefined || isNaN(value)) return "-";
    return typeof value === "number" ? value.toFixed(2) : value.toString();
  };

  const formatMinutes = (minutes: any) => {
    if (minutes === null || minutes === undefined || isNaN(minutes)) return "-";
    const hours = Math.floor(Math.abs(minutes) / 60);
    const mins = Math.abs(minutes) % 60;
    const sign = minutes < 0 ? "-" : "";
    return hours > 0 ? `${sign}${hours}h${mins.toFixed(0)}min` : `${sign}${mins.toFixed(0)}min`;
  };

  const openChartModal = (title: string, chartComponent: React.ReactNode) => {
    setSelectedChart({ title, component: chartComponent });
    setChartModalOpen(true);
  };

  const closeChartModal = () => {
    setChartModalOpen(false);
    setSelectedChart(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography level="h2" sx={{ mb: 3 }}>
        Comparaison de Périodes Multiples
      </Typography>

      {/* Configuration des intervalles */}
      <Card sx={{ p: 3, mb: 3 }}>
        <Typography level="h4" sx={{ mb: 2 }}>
          Configuration des périodes
        </Typography>

        <Stack spacing={2}>
          {/* Options globales */}
          <Stack direction="row" spacing={3} alignItems="center">
            <Checkbox
              checked={includeOperations}
              onChange={(e) => setIncludeOperations(e.target.checked)}
              label="Inclure les opérations"
            />
            <Checkbox
              checked={includeConsultations}
              onChange={(e) => setIncludeConsultations(e.target.checked)}
              label="Inclure les consultations"
            />
            <FormControl sx={{ minWidth: 120 }}>
              <FormLabel>Grouper par</FormLabel>
              <Select value={groupBy} onChange={(_, value) => setGroupBy(value as any)}>
                <Option value="day">Jour</Option>
                <Option value="week">Semaine</Option>
                <Option value="month">Mois</Option>
                <Option value="year">Année</Option>
              </Select>
            </FormControl>
          </Stack>

          <Divider />

          {/* Raccourcis rapides pour la première période */}
          <Box sx={{ mb: 3 }}>
            <Typography level="title-sm" sx={{ mb: 2 }}>
              📅 Raccourcis pour la première période
            </Typography>
            <Grid container spacing={1}>
              <Grid xs={6} sm={3}>
                <Button
                  variant="outlined"
                  size="sm"
                  fullWidth
                  onClick={() => setFirstIntervalQuick("current-year")}
                  startDecorator={<CalendarTodayIcon />}
                >
                  Année en cours
                </Button>
              </Grid>
              <Grid xs={6} sm={3}>
                <Button
                  variant="outlined"
                  size="sm"
                  fullWidth
                  onClick={() => setFirstIntervalQuick("rolling-year")}
                  startDecorator={<CalendarTodayIcon />}
                >
                  Année glissante
                </Button>
              </Grid>
              <Grid xs={6} sm={3}>
                <Button
                  variant="outlined"
                  size="sm"
                  fullWidth
                  onClick={() => setFirstIntervalQuick("current-month")}
                  startDecorator={<CalendarTodayIcon />}
                >
                  Mois en cours
                </Button>
              </Grid>
              <Grid xs={6} sm={3}>
                <Button
                  variant="outlined"
                  size="sm"
                  fullWidth
                  onClick={() => setFirstIntervalQuick("current-week")}
                  startDecorator={<CalendarTodayIcon />}
                >
                  Semaine en cours
                </Button>
              </Grid>
            </Grid>
          </Box>

          <Divider />

          {/* Liste des intervalles */}
          {intervals.map((interval, index) => (
            <Card
              key={interval.id}
              variant="outlined"
              sx={{
                p: 2,
                border: `2px solid ${interval.color}`,
                "&:hover": {
                  boxShadow: `0 0 12px ${interval.color}40`,
                },
              }}
            >
              <Stack spacing={2}>
                {/* Indicateur de couleur et numéro de période */}
                <Stack direction="row" spacing={1} alignItems="center">
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      borderRadius: "50%",
                      backgroundColor: interval.color,
                      border: "2px solid white",
                      boxShadow: "0 0 4px rgba(0,0,0,0.2)",
                    }}
                  />
                  <Typography level="title-sm" sx={{ color: interval.color, fontWeight: "bold" }}>
                    Période {index + 1}
                  </Typography>
                  <Divider orientation="vertical" sx={{ height: 20 }} />
                  <Typography level="body-xs" sx={{ color: "neutral.500" }}>
                    {interval.color}
                  </Typography>
                </Stack>
                {/* Ligne principale avec dates */}
                <Stack direction="row" spacing={2} alignItems="center">
                  <Input
                    value={interval.label}
                    onChange={(e) => updateInterval(interval.id, { label: e.target.value })}
                    sx={{ minWidth: 150 }}
                    placeholder="Nom de la période"
                  />
                  <Input
                    type="date"
                    value={interval.startDate}
                    onChange={(e) => updateInterval(interval.id, { startDate: e.target.value })}
                    startDecorator={<CalendarTodayIcon />}
                  />
                  <Typography level="body-sm">à</Typography>
                  <Input
                    type="date"
                    value={interval.endDate}
                    onChange={(e) => updateInterval(interval.id, { endDate: e.target.value })}
                    startDecorator={<CalendarTodayIcon />}
                  />
                  <IconButton
                    color="danger"
                    disabled={intervals.length <= 1}
                    onClick={() => removeInterval(interval.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Stack>

                {/* Ligne des raccourcis pour périodes précédentes - basées sur l'intervalle actuel */}
                <Stack
                  direction="row"
                  spacing={1}
                  sx={{ justifyContent: "flex-start", flexWrap: "wrap" }}
                >
                  <Typography
                    level="body-xs"
                    sx={{ alignSelf: "center", color: "neutral.500", minWidth: "max-content" }}
                  >
                    Période relative (ajouter/modifier):
                  </Typography>
                  <Button
                    variant="soft"
                    size="sm"
                    color="neutral"
                    onClick={() => addOrUpdateRelativePeriod(interval, "previous-week")}
                    startDecorator={<HistoryIcon />}
                  >
                    Semaine -1
                  </Button>
                  <Button
                    variant="soft"
                    size="sm"
                    color="neutral"
                    onClick={() => addOrUpdateRelativePeriod(interval, "previous-month")}
                    startDecorator={<HistoryIcon />}
                  >
                    Mois -1
                  </Button>
                  <Button
                    variant="soft"
                    size="sm"
                    color="neutral"
                    onClick={() => addOrUpdateRelativePeriod(interval, "previous-year")}
                    startDecorator={<HistoryIcon />}
                  >
                    Année -1
                  </Button>
                </Stack>
              </Stack>
            </Card>
          ))}

          {/* Boutons d'action */}
          <Stack direction="row" spacing={2}>
            <Button startDecorator={<AddIcon />} variant="outlined" onClick={addNewInterval}>
              Ajouter une période
            </Button>
            <Button
              startDecorator={<CompareArrowsIcon />}
              onClick={generateStatistics}
              loading={loading}
              disabled={loading || (!includeOperations && !includeConsultations)}
            >
              Générer la comparaison
            </Button>
          </Stack>
        </Stack>
      </Card>

      {/* Messages d'erreur */}
      {error && (
        <Alert color="danger" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tableau comparatif Opérations */}
      {statsResults.length > 0 && includeOperations && (
        <Card sx={{ mb: 3 }}>
          <Box sx={{ p: 2 }}>
            <Typography level="h4" sx={{ color: "primary.500" }}>
              📊 Tableau Comparatif - Opérations
            </Typography>
          </Box>
          <Divider />

          <Sheet sx={{ overflow: "auto" }}>
            <Table>
              <thead>
                <tr>
                  <th>Métrique Opération</th>
                  {statsResults.map((result, index) => {
                    const interval = intervals[index];
                    return (
                      <th key={index}>
                        <Stack spacing={0.5} alignItems="center">
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: "50%",
                                backgroundColor:
                                  interval?.color || colorPalette[index % colorPalette.length],
                                border: "2px solid white",
                                boxShadow: "0 0 3px rgba(0,0,0,0.2)",
                              }}
                            />
                            <Typography
                              level="body-sm"
                              fontWeight="bold"
                              sx={{
                                color: interval?.color || colorPalette[index % colorPalette.length],
                              }}
                            >
                              Période {index + 1}
                            </Typography>
                          </Stack>
                          <Typography level="body-xs" color="neutral" textAlign="center">
                            {new Date(result.interval_info.start_date).toLocaleDateString("fr-FR")}{" "}
                            -{new Date(result.interval_info.end_date).toLocaleDateString("fr-FR")}
                          </Typography>
                          <Typography level="body-xs" color="neutral">
                            ({result.interval_info.duration_days} jours)
                          </Typography>
                        </Stack>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <strong>Total opérations</strong>
                  </td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      <Chip color="primary" variant="soft">
                        {result.operations?.total_operations || 0}
                      </Chip>
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Taux d'occupation moyen (%)</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      {formatNumber(result.operations?.vacation_analysis?.average_occupation_rate)}%
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Retard moyen première incision (min)</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      {formatMinutes(result.operations?.vacation_analysis?.average_delay)}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Opérations avec vacation</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      {result.operations?.vacation_analysis?.operations_with_vacation || 0}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>
                    Moyenne par{" "}
                    {groupBy === "day"
                      ? "jour"
                      : groupBy === "week"
                        ? "semaine"
                        : groupBy === "month"
                          ? "mois"
                          : "année"}
                  </td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      <Chip color="primary" size="sm">
                        {groupBy === "day" && (result.operations?.average_per_day || 0)}
                        {groupBy === "week" && (result.operations?.average_per_week || 0)}
                        {groupBy === "month" && (result.operations?.average_per_month || 0)}
                        {groupBy === "year" && (result.operations?.average_per_year || 0)}
                      </Chip>
                    </td>
                  ))}
                </tr>
              </tbody>
            </Table>
          </Sheet>
        </Card>
      )}

      {/* Tableau comparatif Consultations */}
      {statsResults.length > 0 && includeConsultations && (
        <Card sx={{ mb: 3 }}>
          <Box sx={{ p: 2 }}>
            <Typography level="h4" sx={{ color: "success.500" }}>
              🩺 Tableau Comparatif - Consultations
            </Typography>
          </Box>
          <Divider />

          <Sheet sx={{ overflow: "auto" }}>
            <Table>
              <thead>
                <tr>
                  <th>Métrique Consultation</th>
                  {statsResults.map((result, index) => {
                    const interval = intervals[index];
                    return (
                      <th key={index}>
                        <Stack spacing={0.5} alignItems="center">
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Box
                              sx={{
                                width: 12,
                                height: 12,
                                borderRadius: "50%",
                                backgroundColor:
                                  interval?.color || colorPalette[index % colorPalette.length],
                                border: "2px solid white",
                                boxShadow: "0 0 3px rgba(0,0,0,0.2)",
                              }}
                            />
                            <Typography
                              level="body-sm"
                              fontWeight="bold"
                              sx={{
                                color: interval?.color || colorPalette[index % colorPalette.length],
                              }}
                            >
                              Période {index + 1}
                            </Typography>
                          </Stack>
                          <Typography level="body-xs" color="neutral" textAlign="center">
                            {new Date(result.interval_info.start_date).toLocaleDateString("fr-FR")}{" "}
                            -{new Date(result.interval_info.end_date).toLocaleDateString("fr-FR")}
                          </Typography>
                          <Typography level="body-xs" color="neutral">
                            ({result.interval_info.duration_days} jours)
                          </Typography>
                        </Stack>
                      </th>
                    );
                  })}
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <strong>Total consultations</strong>
                  </td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      <Chip color="success" variant="soft">
                        {result.consultations?.total_consultations || 0}
                      </Chip>
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Consultations avec vacation</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      {result.consultations?.vacation_analysis?.consultations_with_vacation || 0}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>
                    Moyenne par{" "}
                    {groupBy === "day"
                      ? "jour"
                      : groupBy === "week"
                        ? "semaine"
                        : groupBy === "month"
                          ? "mois"
                          : "année"}
                  </td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      <Chip color="primary" size="sm">
                        {groupBy === "day" && (result.consultations?.average_per_day || 0)}
                        {groupBy === "week" && (result.consultations?.average_per_week || 0)}
                        {groupBy === "month" && (result.consultations?.average_per_month || 0)}
                        {groupBy === "year" && (result.consultations?.average_per_year || 0)}
                      </Chip>
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Retard moyen consultation (min)</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      {formatMinutes(
                        result.consultations?.vacation_analysis?.average_consultation_delay
                      )}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Retard moyen patient (min)</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      {formatMinutes(
                        result.consultations?.vacation_analysis?.average_patient_delay
                      )}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Patients à l'heure</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      <Chip color="success" size="sm">
                        {result.consultations?.vacation_analysis?.on_time_patients || 0}
                      </Chip>
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Patients en retard</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      <Chip color="danger" size="sm">
                        {result.consultations?.vacation_analysis?.late_patients || 0}
                      </Chip>
                    </td>
                  ))}
                </tr>
                <tr>
                  <td>Patients en avance</td>
                  {statsResults.map((result, index) => (
                    <td key={index}>
                      <Chip color="primary" size="sm">
                        {result.consultations?.vacation_analysis?.early_patients || 0}
                      </Chip>
                    </td>
                  ))}
                </tr>
              </tbody>
            </Table>
          </Sheet>
        </Card>
      )}

      {/* Graphiques Opérations */}
      {chartData.length > 0 && includeOperations && (
        <Card sx={{ p: 3, mb: 3 }}>
          <Typography level="h4" sx={{ mb: 2, color: "primary.500" }}>
            📊 Évolution des Opérations
          </Typography>

          <Grid container spacing={3}>
            {/* Volume opérations */}
            <Grid xs={12} md={6}>
              <Card
                variant="outlined"
                sx={{
                  p: 2,
                  cursor: "pointer",
                  transition: "all 0.2s ease",
                  "&:hover": {
                    boxShadow: "md",
                    transform: "translateY(-2px)",
                  },
                }}
                onClick={() => {
                  const chartTitle = `Volume d'opérations par ${groupBy === "day" ? "jour" : groupBy === "week" ? "semaine" : groupBy === "month" ? "mois" : "année"}`;
                  const chartComponent = (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="period" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        {intervals.map((interval, index) => (
                          <Bar
                            key={`operations-${index}`}
                            dataKey={`total_operations_periode_${index + 1}`}
                            fill={interval.color || colorPalette[index % colorPalette.length]}
                            name={`Opérations ${interval.label}`}
                          />
                        ))}
                      </BarChart>
                    </ResponsiveContainer>
                  );
                  openChartModal(chartTitle, chartComponent);
                }}
              >
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Volume d'opérations par{" "}
                  {groupBy === "day"
                    ? "jour"
                    : groupBy === "week"
                      ? "semaine"
                      : groupBy === "month"
                        ? "mois"
                        : "année"}
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <Bar
                        key={`operations-${index}`}
                        dataKey={`total_operations_periode_${index + 1}`}
                        fill={interval.color || colorPalette[index % colorPalette.length]}
                        name={`Opérations ${interval.label}`}
                      />
                    ))}
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Grid>

            {/* Retards opérations */}
            <Grid xs={12} md={6}>
              <Card
                variant="outlined"
                sx={{
                  p: 2,
                  cursor: "pointer",
                  transition: "all 0.2s ease",
                  "&:hover": {
                    boxShadow: "md",
                    transform: "translateY(-2px)",
                  },
                }}
                onClick={() => {
                  const chartTitle = "Retards moyens première incision (minutes)";
                  const chartComponent = (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="period" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        {intervals.map((interval, index) => (
                          <Line
                            key={`operation-delay-${index}`}
                            type="monotone"
                            dataKey={`operation_delay_periode_${index + 1}`}
                            stroke={interval.color || colorPalette[index % colorPalette.length]}
                            name={`Retard opérations ${interval.label}`}
                            strokeWidth={2}
                          />
                        ))}
                      </LineChart>
                    </ResponsiveContainer>
                  );
                  openChartModal(chartTitle, chartComponent);
                }}
              >
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Retards moyens première incision (minutes)
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <Line
                        key={`operation-delay-${index}`}
                        type="monotone"
                        dataKey={`operation_delay_periode_${index + 1}`}
                        stroke={interval.color || colorPalette[index % colorPalette.length]}
                        name={`Retard opérations ${interval.label}`}
                        strokeWidth={2}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Grid>

            {/* Taux d'occupation opérations */}
            <Grid xs={12} md={6}>
              <Card variant="outlined" sx={{ p: 2 }}>
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Taux d'occupation des vacations (%)
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <Line
                        key={`occupation-rate-${index}`}
                        type="monotone"
                        dataKey={`occupation_rate_periode_${index + 1}`}
                        stroke={interval.color || colorPalette[index % colorPalette.length]}
                        name={`Taux d'occupation ${interval.label}`}
                        strokeWidth={2}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Grid>
          </Grid>
        </Card>
      )}

      {/* Graphiques Consultations */}
      {chartData.length > 0 && includeConsultations && (
        <Card sx={{ p: 3, mb: 3 }}>
          <Typography level="h4" sx={{ mb: 2, color: "success.500" }}>
            🩺 Évolution des Consultations
          </Typography>

          <Grid container spacing={3}>
            {/* Volume consultations */}
            <Grid xs={12} md={6}>
              <Card
                variant="outlined"
                sx={{
                  p: 2,
                  cursor: "pointer",
                  transition: "all 0.2s ease",
                  "&:hover": {
                    boxShadow: "md",
                    transform: "translateY(-2px)",
                  },
                }}
                onClick={() => {
                  const chartTitle = `Volume de consultations par ${groupBy === "day" ? "jour" : groupBy === "week" ? "semaine" : groupBy === "month" ? "mois" : "année"}`;
                  const chartComponent = (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="period" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        {intervals.map((interval, index) => (
                          <Bar
                            key={`consultations-${index}`}
                            dataKey={`total_consultations_periode_${index + 1}`}
                            fill={interval.color || colorPalette[index % colorPalette.length]}
                            name={`Consultations ${interval.label}`}
                          />
                        ))}
                      </BarChart>
                    </ResponsiveContainer>
                  );
                  openChartModal(chartTitle, chartComponent);
                }}
              >
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Volume de consultations par{" "}
                  {groupBy === "day"
                    ? "jour"
                    : groupBy === "week"
                      ? "semaine"
                      : groupBy === "month"
                        ? "mois"
                        : "année"}
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <Bar
                        key={`consultations-${index}`}
                        dataKey={`total_consultations_periode_${index + 1}`}
                        fill={interval.color || colorPalette[index % colorPalette.length]}
                        name={`Consultations ${interval.label}`}
                      />
                    ))}
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Grid>

            {/* Retards consultations */}
            <Grid xs={12} md={6}>
              <Card variant="outlined" sx={{ p: 2 }}>
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Retards moyens consultations (minutes)
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <Line
                        key={`consultation-delay-${index}`}
                        type="monotone"
                        dataKey={`consultation_delay_periode_${index + 1}`}
                        stroke={interval.color || colorPalette[index % colorPalette.length]}
                        name={`Retard consultations ${interval.label}`}
                        strokeWidth={2}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Grid>

            {/* Ponctualité patients */}
            <Grid xs={12}>
              <Card variant="outlined" sx={{ p: 2 }}>
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Ponctualité des patients
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <React.Fragment key={`ponctualite-${index}`}>
                        <Bar
                          dataKey={`on_time_patients_periode_${index + 1}`}
                          fill={interval.color || colorPalette[index % colorPalette.length]}
                          name={`À l'heure ${interval.label}`}
                          opacity={0.8}
                        />
                        <Bar
                          dataKey={`late_patients_periode_${index + 1}`}
                          fill={interval.color || colorPalette[index % colorPalette.length]}
                          name={`En retard ${interval.label}`}
                          opacity={0.5}
                        />
                        <Bar
                          dataKey={`early_patients_periode_${index + 1}`}
                          fill={interval.color || colorPalette[index % colorPalette.length]}
                          name={`En avance ${interval.label}`}
                          opacity={0.3}
                        />
                      </React.Fragment>
                    ))}
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Grid>
          </Grid>
        </Card>
      )}

      {/* Graphique de comparaison globale (si les deux sont activés) */}
      {chartData.length > 0 && includeOperations && includeConsultations && (
        <Card sx={{ p: 3, mb: 3 }}>
          <Typography level="h4" sx={{ mb: 2, color: "neutral.600" }}>
            📈 Comparaison Opérations vs Consultations
          </Typography>

          <Grid container spacing={3}>
            {/* Volume comparé */}
            <Grid xs={12} md={6}>
              <Card
                variant="outlined"
                sx={{
                  p: 2,
                  cursor: "pointer",
                  transition: "all 0.2s ease",
                  "&:hover": {
                    boxShadow: "md",
                    transform: "translateY(-2px)",
                  },
                }}
                onClick={() => {
                  const chartTitle = "Volume d'activité comparé";
                  const chartComponent = (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="period" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        {intervals.map((interval, index) => (
                          <React.Fragment key={`comparison-${index}`}>
                            <Bar
                              dataKey={`total_operations_periode_${index + 1}`}
                              fill={interval.color || colorPalette[index % colorPalette.length]}
                              name={`Opérations ${interval.label}`}
                              opacity={0.8}
                            />
                            <Bar
                              dataKey={`total_consultations_periode_${index + 1}`}
                              fill={interval.color || colorPalette[index % colorPalette.length]}
                              name={`Consultations ${interval.label}`}
                              opacity={0.5}
                            />
                          </React.Fragment>
                        ))}
                      </BarChart>
                    </ResponsiveContainer>
                  );
                  openChartModal(chartTitle, chartComponent);
                }}
              >
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Volume d'activité comparé
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <React.Fragment key={`comparison-${index}`}>
                        <Bar
                          dataKey={`total_operations_periode_${index + 1}`}
                          fill={interval.color || colorPalette[index % colorPalette.length]}
                          name={`Opérations ${interval.label}`}
                          opacity={0.8}
                        />
                        <Bar
                          dataKey={`total_consultations_periode_${index + 1}`}
                          fill={interval.color || colorPalette[index % colorPalette.length]}
                          name={`Consultations ${interval.label}`}
                          opacity={0.5}
                        />
                      </React.Fragment>
                    ))}
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Grid>

            {/* Retards comparés */}
            <Grid xs={12} md={6}>
              <Card variant="outlined" sx={{ p: 2 }}>
                <Typography level="title-sm" sx={{ mb: 2 }}>
                  Retards moyens comparés (minutes)
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {intervals.map((interval, index) => (
                      <React.Fragment key={`delay-comparison-${index}`}>
                        <Line
                          type="monotone"
                          dataKey={`operation_delay_periode_${index + 1}`}
                          stroke={interval.color || colorPalette[index % colorPalette.length]}
                          name={`Retard opérations ${interval.label}`}
                          strokeWidth={3}
                          strokeDasharray="5 5"
                        />
                        <Line
                          type="monotone"
                          dataKey={`consultation_delay_periode_${index + 1}`}
                          stroke={interval.color || colorPalette[index % colorPalette.length]}
                          name={`Retard consultations ${interval.label}`}
                          strokeWidth={2}
                        />
                      </React.Fragment>
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Grid>
          </Grid>
        </Card>
      )}

      {/* État de chargement */}
      {loading && (
        <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Message si aucun résultat */}
      {!loading && statsResults.length === 0 && !error && (
        <Card sx={{ p: 3, textAlign: "center" }}>
          <Typography level="body-lg" color="neutral">
            Configurez les périodes ci-dessus et cliquez sur "Générer la comparaison" pour voir les
            résultats.
          </Typography>
        </Card>
      )}

      {/* Modal pour affichage graphique en plein écran */}
      <Modal
        open={chartModalOpen}
        onClose={closeChartModal}
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <ModalDialog
          variant="outlined"
          size="lg"
          sx={{
            width: "95vw",
            height: "90vh",
            maxWidth: "1400px",
            p: 3,
            overflow: "auto",
          }}
        >
          <ModalClose />
          {selectedChart && (
            <>
              <Typography level="h3" sx={{ mb: 3 }}>
                {selectedChart.title}
              </Typography>
              <Box
                sx={{
                  height: "calc(90vh - 120px)",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                {selectedChart.component}
              </Box>
            </>
          )}
        </ModalDialog>
      </Modal>
    </Box>
  );
};

export default DatesIntervalStatistics;
