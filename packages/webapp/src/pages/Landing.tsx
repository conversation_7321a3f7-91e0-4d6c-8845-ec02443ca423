import dayjs from "dayjs";
import React, { useEffect } from "react";

import { CalendarMonth } from "@mui/icons-material";
import ArrowBack from "@mui/icons-material/ArrowBack";
import ArrowForward from "@mui/icons-material/ArrowForward";
import MicIcon from "@mui/icons-material/Mic";
import SearchIcon from "@mui/icons-material/Search";
import { Skeleton, Tooltip } from "@mui/joy";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Divider from "@mui/joy/Divider";
import Drawer from "@mui/joy/Drawer";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Stack from "@mui/joy/Stack";
import { useTheme } from "@mui/joy/styles";
import Typography from "@mui/joy/Typography";
import { Grid } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";

import DoctorSelectChips from "../components/Forms/DoctorSelectChips";
import PatientSelector from "../components/Forms/PatientSelector";
import TodayEventsModalAndButton from "../components/Landing/TodayEventsModalAndButton";
import { useApi } from "../contexts/ApiContext";
import { useCurrentPatient } from "../contexts/CurrentPatientContext";
import { useSessionPreferences } from "../contexts/SessionPreferencesContext";
import { useSnackbar } from "../contexts/SnackbarContext";
import { useAuthenticatedUser } from "../contexts/UserContext";
import { useDebounce } from "../hooks/useDebounce";
import { useInterval } from "../hooks/useInterval";
import { CalendarItem, User } from "../models/types";
import LandingEventCard from "./LandingEventCard";
import SpeechToTextPage from "./SpeechToText/SpeechToTextDrawer";

export default function Landing() {
  const api = useApi();
  const auth = useAuthenticatedUser();
  const { show: originalShowSnackbar } = useSnackbar();
  const sessionPreferences = useSessionPreferences();
  const { currentPatient, setCurrentPatient } = useCurrentPatient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const showSnackbar = React.useCallback(
    (message: string, color: "success" | "danger" | "warning" | "neutral" | "primary") => {
      if (!isMobile) {
        originalShowSnackbar(message, color);
      }
    },
    [isMobile, originalShowSnackbar]
  );

  const [loading, setLoading] = React.useState<boolean>(true);
  const [selectedDr, setSelectedDr] = React.useState<User["id"][]>(() => {
    const initialPrefs = sessionPreferences.data.selectedUsers || [];
    if (isMobile) {
      if (initialPrefs.length > 0) return [initialPrefs[0]];
      if (auth.user?.id) return [auth.user.id];
      return [];
    }
    return initialPrefs;
  });
  const [linkedUsers, setLinkedUsers] = React.useState<User[]>([]);
  // events per user
  const [events, setEvents] = React.useState<{
    [key: number]: CalendarItem[];
  }>({
    [auth.user.id]: [],
  });

  // Sync selectedDr with sessionPreferences.data.selectedUsers and isMobile state
  React.useEffect(() => {
    const prefsUsers = sessionPreferences.data.selectedUsers || [];
    let newTargetSelection;

    if (isMobile) {
      // Determine the correct single selection for mobile based on current prefs or auth user
      const mobileDefaultSelection =
        prefsUsers.length > 0 ? [prefsUsers[0]] : auth.user?.id ? [auth.user.id] : [];
      // If selectedDr currently has multiple items, or if its single item doesn't match the mobile default logic
      if (
        selectedDr.length > 1 ||
        (selectedDr.length === 1 &&
          JSON.stringify(selectedDr) !== JSON.stringify(mobileDefaultSelection)) ||
        (selectedDr.length === 0 && mobileDefaultSelection.length > 0)
      ) {
        newTargetSelection = mobileDefaultSelection;
      }
    } else {
      // Desktop: selectedDr should match session preferences
      if (JSON.stringify(selectedDr) !== JSON.stringify(prefsUsers)) {
        newTargetSelection = prefsUsers;
      }
    }

    if (newTargetSelection && JSON.stringify(selectedDr) !== JSON.stringify(newTargetSelection)) {
      setSelectedDr(newTargetSelection);
    }
  }, [isMobile, sessionPreferences.data.selectedUsers, auth.user?.id, selectedDr]); // selectedDr is included to re-evaluate if it's externally changed in a way that conflicts with mobile/desktop logic

  const [date, setDate] = React.useState<string>(
    sessionPreferences.data.landingDate || dayjs().format("YYYY-MM-DD")
  );
  const debouncedDate = useDebounce<string>(date, 500);
  const [lastRefresh, setLastRefresh] = React.useState<number>(0);
  const [loadingUsers, setLoadingUsers] = React.useState<boolean>(true);
  const [autoScrolled, setAutoScrolled] = React.useState(false);
  // Store the scroll position to maintain it during refresh
  const [scrollPosition, setScrollPosition] = React.useState<number | null>(null);
  // Reference to the container element
  const eventsContainerRef = React.useRef<HTMLDivElement>(null);

  // State for the single speech dictation drawer
  const [isSpeechDrawerOpen, setIsSpeechDrawerOpen] = React.useState<boolean>(false);
  const [selectedEventForSpeech, setSelectedEventForSpeech] = React.useState<CalendarItem | null>(
    null
  );

  // State for patient search modal
  const [isPatientSearchOpen, setIsPatientSearchOpen] = React.useState<boolean>(false);

  useInterval(() => {
    handleRefresh();
  }, 60000);

  const scrollToCurrentTime = React.useCallback(() => {
    const now = dayjs();
    const currentTime = now.hour() * 60 + now.minute();

    if (autoScrolled) return;
    // if the date is not today, do not scroll
    if (!dayjs(date).startOf("day").isSame(now.startOf("day"))) return;

    let closestTime = Infinity;
    let closestElementId: string | null = null;

    // Flatten all events across all doctors to find the closest event
    const allEvents: CalendarItem[] = [];
    Object.values(events).forEach((doctorEvents) => {
      allEvents.push(...doctorEvents);
    });

    // Sort all events by start time
    allEvents.sort((a, b) => a.start - b.start);

    // Find the closest event to current time
    for (const event of allEvents) {
      const eventTime = dayjs.unix(event.start);
      const eventMinutes = eventTime.hour() * 60 + eventTime.minute();

      if (eventMinutes >= currentTime && eventMinutes < closestTime) {
        closestTime = eventMinutes;
        closestElementId = `event-${event.id}`;
      }
    }

    // If no event found after current time, use the last event of the day
    if (!closestElementId && allEvents.length > 0) {
      const lastEvent = allEvents[allEvents.length - 1];
      closestElementId = `event-${lastEvent.id}`;
    }

    if (closestElementId) {
      const element = document.getElementById(closestElementId);
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "center" });
        setAutoScrolled(true);
      }
    }
  }, [events, autoScrolled, date]);

  // Save scroll position before refresh
  const saveScrollPosition = React.useCallback(() => {
    if (eventsContainerRef.current) {
      setScrollPosition(eventsContainerRef.current.scrollTop);
    }
  }, []);

  // Restore scroll position after refresh
  const restoreScrollPosition = React.useCallback(() => {
    if (eventsContainerRef.current && scrollPosition !== null) {
      eventsContainerRef.current.scrollTop = scrollPosition;
      // Reset scroll position after it's restored
      setScrollPosition(null);
    }
  }, [scrollPosition]);

  React.useEffect(() => {
    if (!loading && Object.keys(events).length > 0) {
      if (scrollPosition !== null) {
        // Restore previous scroll position
        restoreScrollPosition();
      } else if (!autoScrolled) {
        // Perform auto-scroll to current time
        scrollToCurrentTime();
      }
    }
  }, [loading, events, autoScrolled, scrollToCurrentTime, scrollPosition, restoreScrollPosition]);

  React.useEffect(() => {
    const fetchLinkedUsers = async () => {
      try {
        setLoadingUsers(true);
        const fetchedLinkedUsers = await api.getLinkedUsers();
        setLinkedUsers(fetchedLinkedUsers);
        setLoadingUsers(false);
      } catch (error: any) {
        showSnackbar("Un problème est survenu lors du chargement des utilisateurs liés.", "danger");
        console.error(error);
      }
    };

    fetchLinkedUsers();
  }, [api, showSnackbar]);

  React.useEffect(() => {
    const fetchConsultationsOfTheDay = async () => {
      setLoading(true);
      if (!api) return;
      try {
        const start = dayjs(debouncedDate).startOf("day").unix();
        const end = dayjs(debouncedDate).endOf("day").unix();

        const newEvents: { [key: number]: CalendarItem[] } = {};
        // selectedDr will be single on mobile, potentially multiple on desktop due to state management
        const doctorsToFetch = selectedDr;

        await Promise.all(
          doctorsToFetch.map(async (dr) => {
            const eventsList = (await api.getCalendarItems(start, end, [dr])).filter(
              (event) =>
                event.type.toLowerCase() === "consultation" ||
                event.type.toLowerCase() === "operation" ||
                event.type.toLowerCase() === "user_event"
            );
            // order events by start time
            eventsList.sort((a, b) => {
              const aStart = dayjs(a.start).unix();
              const bStart = dayjs(b.start).unix();
              return aStart - bStart;
            });
            newEvents[dr] = eventsList;
          })
        );

        // Set all events at once
        setEvents(newEvents);

        // Clean up events for unselected users
        const usersToRemove = linkedUsers
          .filter((u) => !selectedDr.includes(u.id))
          .map((u) => u.id);

        if (usersToRemove.length > 0) {
          setEvents((prev) => {
            const updated = { ...prev };
            usersToRemove.forEach((user) => {
              delete updated[user];
            });
            return updated;
          });
        }
      } catch (error: any) {
        showSnackbar("Un problème est survenu lors du chargement des données", "danger");
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchConsultationsOfTheDay();
  }, [lastRefresh, linkedUsers, debouncedDate, api, showSnackbar, selectedDr]);

  // Handlers for the single speech dictation drawer
  const handleOpenSpeechDrawer = React.useCallback(
    (event: CalendarItem) => {
      if (event.type.toLowerCase() === "consultation" && event.id && event.zkf_patient?.id) {
        setSelectedEventForSpeech(event);
        setIsSpeechDrawerOpen(true);
      } else {
        showSnackbar(
          "Informations de consultation ou patient manquantes pour la dictée.",
          "warning"
        );
      }
    },
    [showSnackbar]
  );

  const ensureSpeechDrawerIsOpen = React.useCallback(() => {
    setIsSpeechDrawerOpen(true);
  }, [setIsSpeechDrawerOpen]);

  const handleCloseSpeechDrawer = React.useCallback(() => {
    setIsSpeechDrawerOpen(false);
    // Optionally reset selected event: setSelectedEventForSpeech(null);
  }, []);

  // Define handleRefresh first
  const handleRefresh = React.useCallback(() => {
    // Save current scroll position before refreshing
    saveScrollPosition();
    setLoading(true);
    setLastRefresh(dayjs().unix());
  }, [saveScrollPosition]);

  const handleSpeechAudioSaved = React.useCallback(() => {
    showSnackbar("Dictée enregistrée avec succès.", "success");
    handleRefresh(); // Refresh the events list
    handleCloseSpeechDrawer();
  }, [showSnackbar, handleCloseSpeechDrawer, handleRefresh]); // Added handleRefresh to dependencies

  const handleOpenPatientSearch = React.useCallback(() => {
    setIsPatientSearchOpen(true);
  }, []);

  const handleClosePatientSearch = React.useCallback(() => {
    setIsPatientSearchOpen(false);
  }, []);

  const handlePatientSelected = React.useCallback(
    (patient: any) => {
      if (patient) {
        setCurrentPatient(patient);
        // Navigate to patient page
        window.location.href = `/patient/${patient.id}`;
      }
      setIsPatientSearchOpen(false);
    },
    [setCurrentPatient]
  );

  const handleOpenSpeechDrawerForDictation = React.useCallback(() => {
    setSelectedEventForSpeech(null);
    setIsSpeechDrawerOpen(true);
  }, [setSelectedEventForSpeech, setIsSpeechDrawerOpen]);

  const usersToDisplay = React.useMemo(() => {
    if (auth.user?.id && !linkedUsers.find((u) => u.id === auth.user.id)) {
      return [auth.user, ...linkedUsers];
    }
    return linkedUsers;
  }, [linkedUsers, auth.user]);

  const handleSelectUsersResponsive = (ids: User["id"] | User["id"][]) => {
    let newSelectedDr: User["id"][] = [];
    if (isMobile) {
      // On mobile, ensure only one ID is selected. If 'ids' is an array, take the first.
      // If 'ids' is a single id (string/number), wrap it in an array.
      const singleId = Array.isArray(ids) ? ids[0] : ids;
      if (singleId) {
        // ensure singleId is not undefined
        newSelectedDr = [singleId];
      }
    } else {
      newSelectedDr = Array.isArray(ids) ? ids : [ids];
    }
    setSelectedDr(newSelectedDr);
    sessionPreferences.setData({ ...sessionPreferences.data, selectedUsers: newSelectedDr });
  };

  const renderEvents = (userEvents: CalendarItem[]) => {
    if (userEvents.length === 0) {
      return (
        <Typography level="body-sm" sx={{ textAlign: "center", mt: 8, mb: 8 }}>
          Aucun événement pour l'utilisateur
        </Typography>
      );
    }

    const splitTime = 13 * 60 + 30; // 13:30 in minutes
    const morning: CalendarItem[] = [];
    const afternoon: CalendarItem[] = [];

    userEvents.forEach((event) => {
      const eventTime = dayjs.unix(event.start);
      const eventMinutes = eventTime.hour() * 60 + eventTime.minute();
      if (eventMinutes < splitTime) {
        morning.push(event);
      } else {
        afternoon.push(event);
      }
    });

    return (
      <>
        {morning.map((event) => (
          <LandingEventCard
            key={event.id}
            event={event}
            refresh={handleRefresh}
            id={`event-${event.id}`}
            onOpenSpeechDrawer={handleOpenSpeechDrawer}
          />
        ))}
        {morning.length > 0 && afternoon.length > 0 && (
          <Divider sx={{ my: 2 }}>
            <Typography level="body-sm" color="neutral">
              Après-midi
            </Typography>
          </Divider>
        )}
        {afternoon.map((event) => (
          <LandingEventCard
            key={event.id}
            event={event}
            refresh={handleRefresh}
            id={`event-${event.id}`}
            onOpenSpeechDrawer={handleOpenSpeechDrawer}
          />
        ))}
      </>
    );
  };

  // Clés pour la gestion du patient actif
  const ACTIVE_PATIENT_KEY = "babylone_temp_active_patient";
  const FROM_HISTORY_KEY = "babylone_from_history";

  // Effet pour restaurer le patient actif lors des navigations depuis l'historique
  useEffect(() => {
    // Vérifier si on vient de naviguer depuis l'historique
    const fromHistory = localStorage.getItem(FROM_HISTORY_KEY) === "true";

    if (fromHistory) {
      console.log("Navigation depuis l'historique détectée");
      // Récupérer le patient associé à cette page
      const tempPatientId = localStorage.getItem(ACTIVE_PATIENT_KEY);

      if (tempPatientId && tempPatientId !== "null") {
        // Si on a un patient associé à cette page, on le définit comme patient actif
        console.log(`Restauration du patient ${tempPatientId}`);
        api
          .getPatient(tempPatientId)
          .then((patient) => {
            if (patient) {
              console.log(`Patient restauré: ${patient.nom} ${patient.prenom}`);
              setCurrentPatient(patient);
            }
          })
          .catch((error: unknown) => {
            console.error("Erreur lors de la récupération du patient:", error);
          });
      } else if (tempPatientId === "null") {
        // Si la page avait explicitement pas de patient actif
        console.log("Cette page n'avait pas de patient actif");
        setCurrentPatient(null);
      }

      // Nettoyer après utilisation
      localStorage.removeItem(FROM_HISTORY_KEY);
      localStorage.removeItem(ACTIVE_PATIENT_KEY);
    }
    // Si on n'est pas en navigation depuis l'historique, on utilise le comportement par défaut
    else if (!currentPatient && sessionPreferences.data.activePatientId) {
      // Récupérer le patient à partir de son ID (conversion en string car l'API attend une string)
      api
        .getPatient(String(sessionPreferences.data.activePatientId))
        .then((patient) => {
          if (patient) {
            setCurrentPatient(patient);
          }
        })
        .catch((error: unknown) => {
          console.error("Erreur lors de la récupération du patient actif :", error);
        });
    }
  }, [currentPatient, sessionPreferences.data.activePatientId, api, setCurrentPatient]);

  React.useEffect(() => {
    if (date !== sessionPreferences.data.landingDate) {
      sessionPreferences.setData({
        landingDate: date,
      });
    }
  }, [date, sessionPreferences]);

  return (
    <>
      {/* Single Drawer for Speech Dictation */}
      <Drawer
        open={isSpeechDrawerOpen}
        onClose={handleCloseSpeechDrawer}
        anchor="right" // Changed anchor to right
        // size="lg" has been removed, sx will control the size
        sx={{
          // Apply previous sizing logic
          "--Drawer-horizontalSize": isMobile ? "100vw" : "clamp(460px, 25vw, 600px)",
          boxSizing: "border-box",
          // Ensure the content area within the drawer is styled correctly
          "& .MuiDrawer-content": {
            p: 2, // Keep padding
            height: "100%", // Make content area take full height of the drawer
            boxSizing: "border-box",
          },
        }}
      >
        <ModalClose onClick={handleCloseSpeechDrawer} />
        {/* 
          The Drawer's 'open' prop (isSpeechDrawerOpen) already controls visibility.
          SpeechToTextPage is rendered if the drawer is open.
          Props for initial patient/consultation are passed conditionally based on selectedEventForSpeech.
        */}
        <SpeechToTextPage
          initialConsultationId={selectedEventForSpeech?.id || null}
          initialPatientId={selectedEventForSpeech?.zkf_patient?.id || null}
          onAudioSaved={handleSpeechAudioSaved}
          onRequestOpen={ensureSpeechDrawerIsOpen}
          onClose={handleCloseSpeechDrawer}
        />
      </Drawer>

      <Box ref={eventsContainerRef} sx={{ height: "calc(100vh - 100px)", overflow: "auto" }}>
        {isMobile && (
          <Stack spacing={1} sx={{ p: 1, backgroundColor: "background.level1", mb: 2 }}>
            {/* Recherche de patient très visible en haut */}
            <Button
              variant="solid"
              color="primary"
              onClick={handleOpenPatientSearch}
              startDecorator={<SearchIcon />}
              sx={{
                justifyContent: "flex-start",
                p: 2,
                textTransform: "none",
                fontSize: "1.1rem",
                fontWeight: "bold",
                boxShadow: "md",
              }}
            >
              🔍 Rechercher un patient
            </Button>

            {/* Bouton pour dicter un courrier */}
            <Button
              variant="outlined"
              color="success"
              onClick={handleOpenSpeechDrawerForDictation}
              startDecorator={<MicIcon />}
              sx={{
                justifyContent: "flex-start",
                p: 2,
                textTransform: "none",
                fontSize: "1rem",
                fontWeight: "bold",
              }}
            >
              🎤 Dicter un courrier
            </Button>

            {/* Bouton pour accéder aux statistiques du jour */}
            <Button
              variant="outlined"
              color="neutral"
              onClick={() => (window.location.href = "/stats/daily")}
              sx={{
                justifyContent: "flex-start",
                p: 2,
                textTransform: "none",
                fontSize: "1rem",
                fontWeight: "bold",
              }}
            >
              📊 Statistiques du jour
            </Button>
          </Stack>
        )}
        {/* <Box ref={eventsContainerRef} sx={{ height: "calc(100vh - 80px)", overflow: "auto" }}> */}
        {loadingUsers && loading ? (
          <Stack gap={2} sx={{ overflow: "hidden" }}>
            <Box sx={{ height: "32px", width: "200px", overflow: "hidden" }}>
              <Skeleton animation="wave" sx={{ height: "32px", width: "200px" }} />
            </Box>
            <Stack direction="row" gap={2}>
              {Array.from({ length: 4 }).map((_, index) => (
                <Box sx={{ height: "24px", width: "150px" }} key={index}>
                  <Skeleton sx={{ height: "24px", width: "150px" }} animation="wave" />
                </Box>
              ))}
            </Stack>
            <Stack gap={0.5}>
              {Array.from({ length: 20 }).map((_, index) => (
                <Box sx={{ height: "64px", width: "100%" }} key={index}>
                  <Skeleton sx={{ height: "60px", width: "calc(100% - 32px)" }} animation="wave" />
                </Box>
              ))}
            </Stack>
          </Stack>
        ) : (
          <Grid container spacing={isMobile ? 0 : 2}>
            <Grid item xs={12} md={12} lg={12} xl={12}>
              <Stack direction="row" gap={4} justifyContent="space-between">
                <Stack direction="row" gap={1}>
                  <Tooltip title="Revenir à la date du jour">
                    <IconButton
                      disabled={dayjs(date).isSame(dayjs(), "day")}
                      onClick={() => {
                        setDate(dayjs().format("YYYY-MM-DD"));
                      }}
                      variant="solid"
                      color="primary"
                    >
                      <CalendarMonth />
                    </IconButton>
                  </Tooltip>
                  <IconButton
                    onClick={() => {
                      setDate(dayjs(date).subtract(1, "day").format("YYYY-MM-DD"));
                    }}
                  >
                    <ArrowBack />
                  </IconButton>
                  <Input
                    type="date"
                    value={date}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setDate(e.target.value)}
                  />
                  <IconButton
                    onClick={() => {
                      setDate(dayjs(date).add(1, "day").format("YYYY-MM-DD"));
                    }}
                  >
                    <ArrowForward />
                  </IconButton>
                </Stack>
                <TodayEventsModalAndButton date={date} selectedDr={selectedDr} />
              </Stack>
              <Stack direction="row" gap={1} sx={{ mt: 1 }}>
                {isMobile ? (
                  <Select
                    // Convert number to string for value, use null if no selection
                    value={selectedDr.length > 0 ? selectedDr[0].toString() : null}
                    onChange={(_event: React.SyntheticEvent | null, newValue: string | null) => {
                      // Parse string back to number, ensure newValue is not null
                      if (newValue !== null) {
                        handleSelectUsersResponsive(parseInt(newValue, 10));
                      }
                    }}
                    placeholder="Sélectionner un docteur..."
                    sx={{ mt: 1, mb: 1, flexGrow: 1, backgroundColor: "background.body" }}
                  >
                    {usersToDisplay.map((user) => (
                      // Convert number to string for option value
                      <Option key={user.id} value={user.id.toString()}>
                        {user.first_name} {user.last_name}
                      </Option>
                    ))}
                  </Select>
                ) : (
                  <DoctorSelectChips
                    value={selectedDr}
                    onSelect={handleSelectUsersResponsive}
                    multiple={!isMobile} // multiple is true on desktop
                  />
                )}
              </Stack>
              {/* Inner Grid container: add mt here for desktop */}
              <Grid container spacing={isMobile ? 0 : 2} sx={{ mt: isMobile ? 0 : 2 }}>
                {selectedDr.length === 0 ? (
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: "200px",
                        width: "100%",
                      }}
                    >
                      <Typography level="h4" color="neutral">
                        Veuillez sélectionner au moins un docteur pour afficher les événements
                      </Typography>
                    </Box>
                  </Grid>
                ) : (
                  Object.keys(events).map((id, key) => {
                    const userEvents = events[parseInt(id, 10)];
                    const user = linkedUsers.find((u) => u.id === Number(id));
                    return (
                      <Grid
                        item
                        key={`doctor-column-${user?.id || key}`}
                        xs={12} // Mobile: Full width
                        sm // Desktop (sm and up): Share available space with other 'sm' items
                        sx={{
                          overflow: "auto",
                          maxHeight: "calc(100vh - 200px)",
                          paddingRight: isMobile
                            ? 0
                            : selectedDr.length > 1 && key < Object.keys(events).length - 1
                              ? 2
                              : 0,
                          borderRight:
                            isMobile ||
                            selectedDr.length <= 1 ||
                            key === Object.keys(events).length - 1
                              ? "none"
                              : (theme) => `1px solid ${theme.palette.divider}`,
                        }}
                      >
                        <Stack
                          direction="row"
                          justifyContent="space-between"
                          alignItems="center"
                          sx={{ mb: 1 }}
                        >
                          <Typography level="h4" sx={{ mb: 1 }}>
                            {user?.last_name} {user?.first_name}
                          </Typography>
                          {userEvents.length > 0 && (
                            <Stack direction="row" spacing={2} alignItems="center">
                              <Tooltip title="Consultations">
                                <Typography
                                  level="body-sm"
                                  color="primary"
                                  sx={{
                                    fontWeight: "bold",
                                    border: "1px solid",
                                    borderColor: "primary.500",
                                    borderRadius: "4px",
                                    px: 1,
                                  }}
                                >
                                  C:{" "}
                                  {
                                    userEvents.filter(
                                      (event) => event.type.toLowerCase() === "consultation"
                                    ).length
                                  }
                                </Typography>
                              </Tooltip>
                              <Tooltip title="Opérations">
                                <Typography
                                  level="body-sm"
                                  color="warning"
                                  sx={{
                                    fontWeight: "bold",
                                    border: "1px solid",
                                    borderColor: "warning.500",
                                    borderRadius: "4px",
                                    px: 1,
                                  }}
                                >
                                  O:{" "}
                                  {
                                    userEvents.filter(
                                      (event) => event.type.toLowerCase() === "operation"
                                    ).length
                                  }
                                </Typography>
                              </Tooltip>
                              <Tooltip title="Évènements">
                                <Typography
                                  level="body-sm"
                                  color="success"
                                  sx={{
                                    fontWeight: "bold",
                                    border: "1px solid",
                                    borderColor: "success.500",
                                    borderRadius: "4px",
                                    px: 1,
                                  }}
                                >
                                  E:{" "}
                                  {
                                    userEvents.filter(
                                      (event) => event.type.toLowerCase() === "user_event"
                                    ).length
                                  }
                                </Typography>
                              </Tooltip>
                            </Stack>
                          )}
                        </Stack>
                        <Stack
                          gap={0.5}
                          sx={{
                            overflow: "auto", // Allows this inner stack to scroll if content exceeds maxHeight of parent Grid
                            // flexGrow: 1, // If the parent Grid item has a fixed height and this should fill it
                          }}
                        >
                          {renderEvents(userEvents)}
                        </Stack>
                      </Grid>
                    );
                  })
                )}
              </Grid>
            </Grid>
          </Grid>
        )}
      </Box>

      {/* Modal de recherche de patient */}
      <Modal open={isPatientSearchOpen} onClose={handleClosePatientSearch}>
        <ModalDialog sx={{ minWidth: 400 }}>
          <Typography level="h4" sx={{ mb: 2 }}>
            Rechercher un patient
          </Typography>
          <PatientSelector value="" onSelect={handlePatientSelected} />
          <Button variant="outlined" onClick={handleClosePatientSearch} sx={{ mt: 2 }}>
            Annuler
          </Button>
        </ModalDialog>
      </Modal>
      {/* </Box> */}
    </>
  );
}
