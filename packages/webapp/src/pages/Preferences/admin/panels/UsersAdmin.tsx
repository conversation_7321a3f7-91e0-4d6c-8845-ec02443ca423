import { useCallback, useEffect, useMemo, useState } from "react";
import React from "react";

import { Add, Edit } from "@mui/icons-material";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Divider from "@mui/joy/Divider";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import List from "@mui/joy/List";
import ListItem from "@mui/joy/ListItem";
import ListItemButton from "@mui/joy/ListItemButton";
import ListItemContent from "@mui/joy/ListItemContent";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import ModalComponent from "../../../../components/Modals/Modal";
import { useApi } from "../../../../contexts/ApiContext";
import { useSnackbar } from "../../../../contexts/SnackbarContext";
import { Group, User } from "../../../../models/types";

export default function UsersAdmin() {
  const api = useApi();
  const snackbar = useSnackbar();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [specialites, setSpecialites] = useState<Record<string, Record<string, User[]>>>({});
  const [openEditModal, setOpenEditModal] = useState(false);
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const [editFormData, setEditFormData] = useState<Partial<User>>({});
  const [groupOptions, setGroupOptions] = useState<Group[]>([]);

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const res = await api.getGroups();
        setGroupOptions(res);
      } catch (err) {
        snackbar.show("Erreur lors du chargement des groupes", "danger");
      }
    };
    fetchGroups();
  }, [api, snackbar]);

  const getGroupNames = (groups: (number | { id: number; name: string })[]): string => {
    if (!groups?.length) return "Non spécifié";

    // Si les éléments sont des objets (groupes enrichis)
    if (typeof groups[0] === "object" && groups[0] !== null && "name" in groups[0]) {
      return (groups as { id: number; name: string }[]).map((g) => g.name).join(", ");
    }

    // Sinon, on assume que ce sont des IDs
    return (
      groupOptions
        .filter((g) => (groups as number[]).includes(g.id))
        .map((g) => g.name)
        .join(", ") || "Non spécifié"
    );
  };

  const fetchUsers = useCallback(async () => {
    try {
      const res = await api.getUsers(1);
      console.log(res.results);
      const grouped = res.results.reduce(
        (acc: Record<string, Record<string, User[]>>, user: User) => {
          const statusKey = user.is_active ? "Actifs" : "Inactifs";
          const specialiteKey = user.specialite_principale || "Non spécifié";
          if (!acc[statusKey]) acc[statusKey] = {};
          if (!acc[statusKey][specialiteKey]) acc[statusKey][specialiteKey] = [];
          acc[statusKey][specialiteKey].push(user);
          return acc;
        },
        {}
      );
      setSpecialites(grouped);
    } catch (error) {
      snackbar.show("Erreur lors du chargement des utilisateurs", "danger");
    }
  }, [api, snackbar]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleUserClick = (user: User) => setSelectedUser(user);

  const handleEdit = (user: User) => {
    setEditFormData({
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      is_active: user.is_active,
      groups: user.groups,
      username: user.username,
    });
    setOpenEditModal(true);
  };

  const handleCreate = () => {
    setEditFormData({
      last_name: "",
      first_name: "",
      email: "",
      is_active: true,
      groups: [],
      username: "",
    });
    setOpenCreateModal(true);
  };

  const handleSaveCreate = async () => {
    try {
      snackbar.show("User créé avec succès", "success");
      setOpenCreateModal(false);
      fetchUsers();
    } catch (error) {
      snackbar.show("Erreur lors de la création de l'utilisateur", "danger");
    }
  };

  const handleSaveEdit = async () => {
    try {
      snackbar.show("User modifié avec succès", "success");
      setOpenEditModal(false);
      fetchUsers();
    } catch (error) {
      snackbar.show("Erreur lors de la modification de l'utilisateur", "danger");
    }
  };

  const sortedSpecialites: [string, Record<string, User[]>][] = useMemo(() => {
    return ["Actifs", "Inactifs"].filter((s) => specialites[s]).map((s) => [s, specialites[s]]);
  }, [specialites]);

  const createEditModal = (isCreate: boolean) => (
    <ModalComponent
      open={isCreate ? openCreateModal : openEditModal}
      title={isCreate ? "Créer un nouvel utilisateur" : "Modifier l'utilisateur"}
      onClose={() => (isCreate ? setOpenCreateModal(false) : setOpenEditModal(false))}
      validateLabel="Enregistrer"
      onValidate={isCreate ? handleSaveCreate : handleSaveEdit}
      canValidate={!!editFormData.last_name && !!editFormData.first_name}
    >
      <Stack spacing={2} sx={{ pt: 1 }}>
        {isCreate ? (
          <FormControl>
            <FormLabel>Nom d'utilisateur</FormLabel>
            <Input
              value={editFormData.username || ""}
              onChange={(e) => setEditFormData({ ...editFormData, username: e.target.value })}
            />
          </FormControl>
        ) : (
          <Typography>Nom d'utilisateur : {editFormData.username || "Non spécifié"}</Typography>
        )}

        <FormControl>
          <FormLabel>Nom</FormLabel>
          <Input
            value={editFormData.last_name || ""}
            onChange={(e) => setEditFormData({ ...editFormData, last_name: e.target.value })}
          />
        </FormControl>

        <FormControl>
          <FormLabel>Prénom</FormLabel>
          <Input
            value={editFormData.first_name || ""}
            onChange={(e) => setEditFormData({ ...editFormData, first_name: e.target.value })}
          />
        </FormControl>

        <FormControl>
          <FormLabel>Email</FormLabel>
          <Input
            type="email"
            value={editFormData.email || ""}
            onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
          />
        </FormControl>

        <FormControl>
          <FormLabel>Actif</FormLabel>
          <Select
            value={String(editFormData.is_active)}
            onChange={(_, value) =>
              setEditFormData({ ...editFormData, is_active: value === "true" })
            }
          >
            <Option value="true">Oui</Option>
            <Option value="false">Non</Option>
          </Select>
        </FormControl>

        <FormControl>
          <FormLabel>Groupes</FormLabel>
          <Select
            multiple
            value={editFormData.groups || []}
            onChange={(_, value) => setEditFormData({ ...editFormData, groups: value })}
          >
            {groupOptions.map((group) => (
              <Option key={group.id} value={group.name}>
                {group.name}
              </Option>
            ))}
          </Select>
        </FormControl>
      </Stack>
    </ModalComponent>
  );

  return (
    <Stack spacing={2}>
      <Box display="flex" justifyContent="flex-end">
        <Button startDecorator={<Add />} onClick={handleCreate}>
          Créer un user
        </Button>
      </Box>
      <Divider />
      <Stack direction="row" spacing={2} sx={{ height: "calc(100vh - 250px)" }}>
        <Sheet
          sx={{
            width: 300,
            overflow: "auto",
            borderRadius: "sm",
            boxShadow: "sm",
          }}
        >
          <List size="sm" sx={{ "--List-gap": "0px" }}>
            {sortedSpecialites.map(([status, specGroup]) => (
              <ListItem nested key={status}>
                <Typography level="title-md" sx={{ p: 1, backgroundColor: "background.level1" }}>
                  {String(status)}
                </Typography>
                <List size="sm">
                  {Object.entries(specGroup)
                    .sort(([a], [b]) => a.localeCompare(b))
                    .map(([specialite, users]) => (
                      <ListItem nested key={specialite}>
                        <Typography
                          level="title-sm"
                          sx={{
                            pl: 2,
                            py: 0.5,
                            backgroundColor: "background.level2",
                          }}
                        >
                          {specialite}
                        </Typography>
                        <List size="sm">
                          {users.map((user) => (
                            <ListItem key={user.id}>
                              <ListItemButton
                                selected={selectedUser?.id === user.id}
                                onClick={() => handleUserClick(user)}
                              >
                                <ListItemContent>
                                  <Typography level="body-sm">
                                    {user.last_name} {user.first_name}
                                  </Typography>
                                </ListItemContent>
                              </ListItemButton>
                            </ListItem>
                          ))}
                        </List>
                      </ListItem>
                    ))}
                </List>
              </ListItem>
            ))}
          </List>
        </Sheet>

        <Sheet
          sx={{
            flexGrow: 1,
            borderRadius: "sm",
            boxShadow: "sm",
            p: 2,
            display: "flex",
            flexDirection: "column",
          }}
        >
          {selectedUser ? (
            <>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 2,
                }}
              >
                <Typography level="h4">
                  {selectedUser.last_name} {selectedUser.first_name}
                </Typography>
                <Button
                  startDecorator={<Edit />}
                  onClick={() => handleEdit(selectedUser)}
                  size="sm"
                  variant="outlined"
                >
                  Modifier
                </Button>
              </Box>
              <Divider />
              <Stack spacing={2} sx={{ mt: 2 }}>
                <Box>
                  <Typography level="title-sm">Spécialité principale</Typography>
                  <Typography level="body-md">
                    {selectedUser.specialite_principale || "Non spécifié"}
                  </Typography>
                </Box>
                <Box>
                  <Typography level="title-sm">Email</Typography>
                  <Typography level="body-md">{selectedUser.email || "Non spécifié"}</Typography>
                </Box>
                <Box>
                  <Typography level="title-sm">Nom d'utilisateur</Typography>
                  <Typography level="body-md">{selectedUser.username || "Non spécifié"}</Typography>
                </Box>
                <Box>
                  <Typography level="title-sm">Actif</Typography>
                  <Typography level="body-md">{selectedUser.is_active ? "Oui" : "Non"}</Typography>
                </Box>
                <Box>
                  <Typography level="title-sm">Groupes</Typography>
                  <Typography level="body-md">
                    {selectedUser.groups?.length ? getGroupNames(selectedUser.groups) : "Aucun"}
                  </Typography>
                </Box>
              </Stack>
            </>
          ) : (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
              }}
            >
              <Typography level="body-lg" color="neutral">
                Sélectionnez un User pour voir ses détails
              </Typography>
            </Box>
          )}
        </Sheet>
      </Stack>
      {createEditModal(false)}
      {createEditModal(true)}
    </Stack>
  );
}
