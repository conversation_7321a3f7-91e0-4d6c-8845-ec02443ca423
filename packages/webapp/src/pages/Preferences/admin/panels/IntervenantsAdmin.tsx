import React, { useCallback, useEffect, useMemo } from "react";

import { Add, Edit } from "@mui/icons-material";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Divider from "@mui/joy/Divider";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import List from "@mui/joy/List";
import ListItem from "@mui/joy/ListItem";
import ListItemButton from "@mui/joy/ListItemButton";
import ListItemContent from "@mui/joy/ListItemContent";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import ModalComponent from "../../../../components/Modals/Modal";
import { useApi } from "../../../../contexts/ApiContext";
import { useSnackbar } from "../../../../contexts/SnackbarContext";
import { isSpecialite } from "../../../../models/type_guards";
import { Intervenant } from "../../../../models/types";

export default function IntervenantsAdmin() {
  const api = useApi();
  const snackbar = useSnackbar();

  const [selectedIntervenant, setSelectedIntervenant] = React.useState<Intervenant | null>(null);
  const [specialites, setSpecialites] = React.useState<Record<string, Intervenant[]>>({});
  const [openEditModal, setOpenEditModal] = React.useState<boolean>(false);
  const [openCreateModal, setOpenCreateModal] = React.useState<boolean>(false);
  const [editFormData, setEditFormData] = React.useState<Partial<Intervenant>>({});

  const fetchIntervenants = useCallback(async () => {
    try {
      const result = await api.listUserIntervenants(1);

      // Group intervenants by specialite_principale
      const grouped = result.reduce(
        (acc: Record<string, Intervenant[]>, intervenant: Intervenant) => {
          const specialiteId = isSpecialite(intervenant.specialite_principale)
            ? intervenant.specialite_principale.spe
            : "undefined";

          if (!acc[specialiteId]) {
            acc[specialiteId] = [];
          }

          acc[specialiteId].push(intervenant);
          return acc;
        },
        {}
      );

      setSpecialites(grouped);
    } catch (error) {
      console.error(error);
      snackbar.show("Erreur lors du chargement des intervenants", "danger");
    }
  }, [api, snackbar]);

  useEffect(() => {
    fetchIntervenants();
  }, [fetchIntervenants]);

  const handleIntervenantClick = (intervenant: Intervenant) => {
    setSelectedIntervenant(intervenant);
  };

  const handleEdit = (intervenant: Intervenant) => {
    setEditFormData({
      id: intervenant.id,
      nom: intervenant.nom,
      prenom: intervenant.prenom,
      specialite_principale: intervenant.specialite_principale,
    });
    setOpenEditModal(true);
  };

  const handleCreate = () => {
    setEditFormData({
      nom: "",
      prenom: "",
    });
    setOpenCreateModal(true);
  };

  const handleSaveCreate = async () => {
    try {
      // await api.createIntervenant(editFormData);
      snackbar.show("Intervenant créé avec succès", "success");
      setOpenCreateModal(false);
      fetchIntervenants();
    } catch (error) {
      snackbar.show("Erreur lors de la création de l'intervenant", "danger");
    }
  };

  const handleSaveEdit = async () => {
    try {
      // await api.updateIntervenant(editFormData);
      snackbar.show("Intervenant modifié avec succès", "success");
      setOpenEditModal(false);
      fetchIntervenants();
    } catch (error) {
      snackbar.show("Erreur lors de la modification de l'intervenant", "danger");
    }
  };

  // Sort specialites by name for display
  const sortedSpecialites = useMemo(() => {
    return Object.entries(specialites).sort((a, b) => {
      const aName = a[0] ?? "Non spécifié";
      const bName = b[0] ?? "Non spécifié";
      return aName.localeCompare(bName);
    });
  }, [specialites]);

  const createEditModal = (isCreate: boolean) => (
    <ModalComponent
      open={isCreate ? openCreateModal : openEditModal}
      title={isCreate ? "Créer un nouvel intervenant" : "Modifier l'intervenant"}
      onClose={() => (isCreate ? setOpenCreateModal(false) : setOpenEditModal(false))}
      validateLabel="Enregistrer"
      onValidate={isCreate ? handleSaveCreate : handleSaveEdit}
      canValidate={!!editFormData.nom && !!editFormData.prenom}
    >
      <Stack spacing={2} sx={{ pt: 1 }}>
        <FormControl>
          <FormLabel>Nom</FormLabel>
          <Input
            value={editFormData.nom || ""}
            onChange={(e) => setEditFormData({ ...editFormData, nom: e.target.value })}
          />
        </FormControl>
        <FormControl>
          <FormLabel>Prénom</FormLabel>
          <Input
            value={editFormData.prenom || ""}
            onChange={(e) => setEditFormData({ ...editFormData, prenom: e.target.value })}
          />
        </FormControl>
        <FormControl>
          <FormLabel>Spécialité principale</FormLabel>
          <Select
            value={
              isSpecialite(editFormData.specialite_principale)
                ? editFormData.specialite_principale.spe
                : ""
            }
            onChange={(e, newValue) =>
              setEditFormData({
                ...editFormData,
                specialite_principale: newValue || null,
              })
            }
          >
            <Option value="">Non spécifié</Option>
            {Object.entries(specialites).map(([spe, intervenants]) => {
              if (
                spe !== "undefined" &&
                intervenants.length > 0 &&
                isSpecialite(intervenants[0].specialite_principale)
              ) {
                return (
                  <Option key={spe} value={spe}>
                    {spe}
                  </Option>
                );
              }
              return null;
            })}
          </Select>
        </FormControl>
      </Stack>
    </ModalComponent>
  );

  return (
    <Stack spacing={2}>
      <Box display="flex" justifyContent="flex-end">
        <Button startDecorator={<Add />} onClick={handleCreate}>
          Créer un intervenant
        </Button>
      </Box>
      <Divider />
      <Stack direction="row" spacing={2} sx={{ height: "calc(100vh - 250px)" }}>
        {/* Left side: List of intervenants by specialite */}
        <Sheet
          sx={{
            width: 300,
            overflow: "auto",
            borderRadius: "sm",
            boxShadow: "sm",
          }}
        >
          <Typography level="title-md" sx={{ p: 2, backgroundColor: "background.level1" }}>
            Intervenants par spécialité
          </Typography>
          <List size="sm" sx={{ "--List-gap": "0px" }}>
            {sortedSpecialites.map(([specialiteId, intervenants]) => (
              <div key={specialiteId}>
                <ListItem nested>
                  <Typography level="title-sm" sx={{ p: 1, backgroundColor: "background.level2" }}>
                    {specialiteId}
                  </Typography>
                  <List size="sm">
                    {intervenants.map((intervenant) => (
                      <ListItem key={intervenant.id}>
                        <ListItemButton
                          selected={selectedIntervenant?.id === intervenant.id}
                          onClick={() => handleIntervenantClick(intervenant)}
                        >
                          <ListItemContent>
                            <Typography level="body-sm">
                              {intervenant.nom} {intervenant.prenom}
                            </Typography>
                          </ListItemContent>
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </ListItem>
              </div>
            ))}
          </List>
        </Sheet>

        {/* Right side: Selected intervenant details */}
        <Sheet
          sx={{
            flexGrow: 1,
            borderRadius: "sm",
            boxShadow: "sm",
            p: 2,
            display: "flex",
            flexDirection: "column",
          }}
        >
          {selectedIntervenant ? (
            <>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 2,
                }}
              >
                <Typography level="h4">
                  {selectedIntervenant.nom} {selectedIntervenant.prenom}
                </Typography>
                <Button
                  startDecorator={<Edit />}
                  onClick={() => handleEdit(selectedIntervenant)}
                  size="sm"
                  variant="outlined"
                >
                  Modifier
                </Button>
              </Box>
              <Divider />
              <Stack spacing={2} sx={{ mt: 2 }}>
                <Box>
                  <Typography level="title-sm">Spécialité principale</Typography>
                  <Typography level="body-md">
                    {isSpecialite(selectedIntervenant.specialite_principale)
                      ? selectedIntervenant.specialite_principale.spe
                      : "Non spécifié"}
                  </Typography>
                </Box>
              </Stack>
            </>
          ) : (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
              }}
            >
              <Typography level="body-lg" color="neutral">
                Sélectionnez un intervenant pour voir ses détails
              </Typography>
            </Box>
          )}
        </Sheet>
      </Stack>
      {/* Modals */}
      {createEditModal(false)} {/* Edit modal */}
      {createEditModal(true)} {/* Create modal */}
    </Stack>
  );
}
