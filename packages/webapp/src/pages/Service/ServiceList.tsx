import React from "react";

import { People } from "@mui/icons-material";
import Accordion from "@mui/joy/Accordion";
import AccordionDetails from "@mui/joy/AccordionDetails";
import AccordionGroup from "@mui/joy/AccordionGroup";
import AccordionSummary from "@mui/joy/AccordionSummary";
import Chip from "@mui/joy/Chip";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import ServiceHospitalisationTable from "./ServiceHospitalisationTable";
import { HospitalisationByService } from "./ServicePage";

interface ServiceListProps {
  hospitalisations: HospitalisationByService;
  onLeave: (leaveDate: string | undefined, key: string, serviceId: string) => void;
  onSortChange?: (field: string, direction: string) => void;
}

export default function ServiceList({ hospitalisations, onLeave, onSortChange }: ServiceListProps) {
  const [expanded, setExpanded] = React.useState<boolean[]>([]);

  React.useEffect(() => {
    const isExpanded = [];
    for (let i = 0; i < Object.keys(hospitalisations).length; i++) {
      isExpanded.push(true);
    }
    setExpanded(isExpanded);
  }, [hospitalisations]);

  const handleChange = (index: number, isExpanded: boolean) => {
    const newExpanded = [...expanded];
    newExpanded[index] = isExpanded;
    setExpanded(newExpanded);
  };

  if (!expanded.length) {
    return null;
  }

  return (
    <Stack spacing={1}>
      {Object.keys(hospitalisations).map((key: string, index: number) => (
        <AccordionGroup key={key} variant="outlined" sx={{ borderRadius: "sm" }}>
          <Accordion
            expanded={expanded[index]}
            onChange={(_, isExpanded) => handleChange(index, isExpanded)}
            sx={{
              "& .MuiAccordionSummary-root": {
                backgroundColor: "background.level1",
                borderRadius: "sm",
                mb: 1,
              },
            }}
          >
            <AccordionSummary
              sx={{
                "&.MuiAccordionSummary-root": {
                  mb: 0,
                },
              }}
            >
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography level="title-lg">{key}</Typography>
                <Chip size="sm" variant="soft" color="primary" startDecorator={<People />}>
                  {hospitalisations[key].length} patient
                  {hospitalisations[key].length > 1 ? "s" : ""}
                </Chip>
              </Stack>
            </AccordionSummary>
            <AccordionDetails
              sx={{
                "& .MuiAccordionDetails-content": {
                  paddingInline: "0",
                },
              }}
            >
              <ServiceHospitalisationTable
                hospitalisations={hospitalisations[key]}
                onLeave={(leaveDate, serviceId) => onLeave(leaveDate, key, serviceId)}
                onSortChange={onSortChange}
              />
            </AccordionDetails>
          </Accordion>
        </AccordionGroup>
      ))}
    </Stack>
  );
}
