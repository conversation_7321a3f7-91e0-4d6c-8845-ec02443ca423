import moment from "moment";
import "moment/locale/fr";
import React from "react";

import ExitToAppRoundedIcon from "@mui/icons-material/ExitToAppRounded";
import FemaleIcon from "@mui/icons-material/Female";
import LocalHospitalIcon from "@mui/icons-material/LocalHospital";
import LoginIcon from "@mui/icons-material/Login";
import MaleIcon from "@mui/icons-material/Male";
import SwapHorizIcon from "@mui/icons-material/SwapHoriz";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";
import Table from "@mui/joy/Table";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { isPatient, isUser } from "../../models/type_guards";
import { Hospitalisation } from "../../models/types";
import { router } from "../../router";
import { formatDate, formatNameAndSurname, ISODateToAge } from "../../utils/utils";

moment.locale("fr");

interface ServiceHospitalisationTableProps {
  hospitalisations: Hospitalisation[];
  onLeave: (leaveDate: string | undefined, serviceId: string) => void;
  onSortChange?: (field: string, direction: string) => void;
}

type SortField = "name" | "entryDate" | "birthDate" | "doctor" | "exitDate" | "gender";
type SortDirection = "asc" | "desc";

// Fonction pour déterminer la couleur et l'icône selon les dates
const getStatusInfo = (
  entreeDate: string | null | undefined,
  sortieDate: string | null | undefined
) => {
  const today = moment().format("YYYY-MM-DD");
  const entree = moment(entreeDate).format("YYYY-MM-DD");
  const sortie = sortieDate ? moment(sortieDate).format("YYYY-MM-DD") : null;

  // Si date entrée = date de sortie: bleu
  if (sortie && entree === sortie) {
    return {
      color: "primary" as const,
      icon: <SwapHorizIcon fontSize="small" />,
      label: "Ambulatoire",
      bgColor: "primary.50",
      borderColor: "primary.300",
    };
  }

  // Si date de sortie = today: orange
  if (sortie === today) {
    return {
      color: "warning" as const,
      icon: <ExitToAppRoundedIcon fontSize="small" />,
      label: "Sortie",
      bgColor: "warning.50",
      borderColor: "warning.300",
    };
  }

  // Si date entrée = today: vert
  if (entree === today) {
    return {
      color: "success" as const,
      icon: <LoginIcon fontSize="small" />,
      label: "Entrée",
      bgColor: "success.50",
      borderColor: "success.300",
    };
  }

  // Pas de couleur spéciale (date entrée < today)
  return null;
};

export default function ServiceHospitalisationTable({
  hospitalisations,
  onLeave,
  onSortChange,
}: ServiceHospitalisationTableProps) {
  const api = useApi();
  const snackbar = useSnackbar();
  const [open, setOpen] = React.useState(false);
  const [selectedHospitalization, setSelectedHospitalization] =
    React.useState<Hospitalisation | null>(null);
  const [leave, setLeave] = React.useState<string>();
  const [sortField, setSortField] = React.useState<SortField>("entryDate");
  const [sortDirection, setSortDirection] = React.useState<SortDirection>("desc");

  const updateSortieDate = async () => {
    if (!selectedHospitalization) return;

    try {
      await api.patchHospitalisation({
        id: selectedHospitalization.id,
        sortie_date: leave,
      });
      onLeave(leave, selectedHospitalization.id);
      snackbar.show("Date de sortie mise à jour", "success");
    } catch (e) {
      snackbar.show("Erreur lors de la mise à jour de la date de sortie", "danger");
    }
  };

  const handleSort = (field: SortField) => {
    let newDirection: SortDirection;

    if (sortField === field) {
      newDirection = sortDirection === "asc" ? "desc" : "asc";
      setSortDirection(newDirection);
    } else {
      newDirection = "asc";
      setSortField(field);
      setSortDirection(newDirection);
    }

    // Propagate sort change to parent component
    if (onSortChange) {
      onSortChange(field, newDirection);
    }
  };

  const sortedHospitalisations = React.useMemo(() => {
    return [...hospitalisations].sort((a, b) => {
      const aPatient = isPatient(a.zkf_patient) ? a.zkf_patient : null;
      const bPatient = isPatient(b.zkf_patient) ? b.zkf_patient : null;
      // Get first doctor for sorting (or empty string if none)
      const aDoctorRef = a.medecin_referent.find((m) => isUser(m));
      const bDoctorRef = b.medecin_referent.find((m) => isUser(m));

      const aDoctor = aDoctorRef && isUser(aDoctorRef) ? aDoctorRef.last_name : "";
      const bDoctor = bDoctorRef && isUser(bDoctorRef) ? bDoctorRef.last_name : "";

      let comparison = 0;
      switch (sortField) {
        case "name":
          const aName = aPatient ? formatNameAndSurname(aPatient.nom, aPatient.prenom) : "";
          const bName = bPatient ? formatNameAndSurname(bPatient.nom, bPatient.prenom) : "";
          comparison = aName.localeCompare(bName);
          break;
        case "entryDate":
          comparison = moment(a.entree_date).diff(moment(b.entree_date));
          break;
        case "exitDate":
          const aSortie = a.sortie_date || "";
          const bSortie = b.sortie_date || "";
          comparison = moment(aSortie || "9999-12-31").diff(moment(bSortie || "9999-12-31"));
          break;
        case "birthDate":
          comparison = moment(aPatient?.dob).diff(moment(bPatient?.dob));
          break;
        case "doctor":
          comparison = (aDoctor || "").localeCompare(bDoctor || "");
          break;
        case "gender":
          comparison = (aPatient?.sexe || "").localeCompare(bPatient?.sexe || "");
          break;
      }
      return sortDirection === "asc" ? comparison : -comparison;
    });
  }, [hospitalisations, sortField, sortDirection]);

  const SortButton = ({ field, children }: { field: SortField; children: React.ReactNode }) => (
    <Button
      variant="plain"
      color="neutral"
      onClick={() => handleSort(field)}
      sx={{
        fontWeight: sortField === field ? "bold" : "normal",
        "&:hover": { backgroundColor: "transparent" },
        justifyContent: "flex-start",
        width: "100%",
        textAlign: "left",
        padding: 0,
      }}
    >
      {children}
      {sortField === field && (
        <Typography component="span" sx={{ ml: 0.5 }}>
          {sortDirection === "asc" ? "↑" : "↓"}
        </Typography>
      )}
    </Button>
  );

  return (
    <>
      <Table>
        <thead>
          <tr>
            <th style={{ width: "22%" }}>
              <SortButton field="name">Patient</SortButton>
            </th>
            <th style={{ width: "17%" }}>
              <SortButton field="entryDate">Date d'entrée</SortButton>
            </th>
            <th style={{ width: "17%" }}>
              <SortButton field="exitDate">Date de sortie</SortButton>
            </th>
            <th style={{ width: "17%" }}>
              <SortButton field="birthDate">Âge</SortButton>
            </th>
            <th style={{ width: "17%" }}>
              <SortButton field="doctor">Médecin</SortButton>
            </th>
            <th style={{ width: "5%", textAlign: "center" }}></th>
          </tr>
        </thead>
        <tbody>
          {sortedHospitalisations.map((hospi) => {
            const patient = isPatient(hospi.zkf_patient) ? hospi.zkf_patient : null;
            if (!patient) return null;
            const statusInfo = getStatusInfo(hospi.entree_date, hospi.sortie_date);

            return (
              <tr
                key={hospi.id}
                style={{
                  backgroundColor: statusInfo ? statusInfo.bgColor : "transparent",
                  borderLeft: statusInfo
                    ? `3px solid var(--joy-palette-${statusInfo.color}-300)`
                    : "none",
                }}
              >
                <td>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 0.5 }}>
                    <Typography
                      sx={{
                        cursor: "pointer",
                        "&:hover": { textDecoration: "underline" },
                      }}
                      onClick={() => router.navigate(`/patient/${patient.id}`)}
                    >
                      {formatNameAndSurname(patient.nom, patient.prenom)}
                    </Typography>
                    <Tooltip title={patient.sexe === "MALE" ? "Homme" : "Femme"}>
                      {patient.sexe === "MALE" ? (
                        <MaleIcon sx={{ fontSize: 16, color: "primary.500" }} />
                      ) : (
                        <FemaleIcon sx={{ fontSize: 16, color: "danger.500" }} />
                      )}
                    </Tooltip>
                    {statusInfo && (
                      <Tooltip title={statusInfo.label}>
                        <Chip
                          color={statusInfo.color}
                          size="sm"
                          variant="soft"
                          startDecorator={statusInfo.icon}
                        >
                          {statusInfo.label}
                        </Chip>
                      </Tooltip>
                    )}
                  </Box>
                  <Typography
                    level="body-xs"
                    color="warning"
                    startDecorator={<LocalHospitalIcon sx={{ opacity: 0.6 }} />}
                    sx={{
                      cursor: "pointer",
                      "&:hover": { textDecoration: "underline" },
                    }}
                    onClick={() => router.navigate(`/hospitalisation/${hospi.id}`)}
                  >
                    {hospi.motif_hospitalisation}
                  </Typography>
                </td>
                <td>
                  {formatDate(hospi.entree_date)}
                  <Typography level="body-xs" color="neutral">
                    {moment(hospi.entree_date).fromNow()}
                  </Typography>
                </td>
                <td>
                  {hospi.sortie_date ? (
                    <>
                      {formatDate(hospi.sortie_date)}
                      <Typography level="body-xs" color="neutral">
                        {moment(hospi.sortie_date).fromNow()}
                      </Typography>
                    </>
                  ) : (
                    <Typography level="body-sm" color="neutral">
                      —
                    </Typography>
                  )}
                </td>
                <td>
                  {new Date(patient.dob).toLocaleDateString("fr")}
                  <Typography component="span" color="neutral" level="body-xs">
                    {ISODateToAge(patient.dob)}
                  </Typography>
                </td>
                <td>
                  <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5 }}>
                    {hospi.medecin_referent.map(
                      (doctor, index) =>
                        isUser(doctor) && (
                          <Chip
                            key={`${doctor.id}-${index}`}
                            size="sm"
                            variant="soft"
                            color="neutral"
                          >
                            Dr {doctor.last_name}
                          </Chip>
                        )
                    )}
                  </Box>
                </td>
                <td style={{ textAlign: "center", padding: "8px" }}>
                  <Tooltip title="Patient sorti">
                    <IconButton
                      variant="plain"
                      color="neutral"
                      size="sm"
                      onClick={() => {
                        setSelectedHospitalization(hospi);
                        setOpen(true);
                      }}
                    >
                      <ExitToAppRoundedIcon />
                    </IconButton>
                  </Tooltip>
                </td>
              </tr>
            );
          })}
        </tbody>
      </Table>

      <Modal open={open} onClose={() => setOpen(false)}>
        <ModalDialog>
          <Typography level="title-md">Date de sortie</Typography>
          <Input type="date" value={leave} onChange={(e) => setLeave(e.target.value)} />
          <Box
            sx={{
              mt: 1,
              display: "flex",
              gap: 1,
              flexDirection: { xs: "column", sm: "row-reverse" },
            }}
          >
            <Button
              variant="solid"
              color="primary"
              onClick={() => {
                setOpen(false);
                updateSortieDate();
              }}
            >
              Confirmer
            </Button>
            <Button variant="plain" color="neutral" onClick={() => setOpen(false)}>
              Annuler
            </Button>
          </Box>
        </ModalDialog>
      </Modal>
    </>
  );
}
