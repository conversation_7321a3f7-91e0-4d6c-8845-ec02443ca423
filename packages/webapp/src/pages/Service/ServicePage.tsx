import moment from "moment";
import React from "react";

import { Masks } from "@mui/icons-material";
import CloseIcon from "@mui/icons-material/Close";
import SearchIcon from "@mui/icons-material/Search";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Sheet from "@mui/joy/Sheet";
import Skeleton from "@mui/joy/Skeleton";
import Stack from "@mui/joy/Stack";
import ToggleButtonGroup from "@mui/joy/ToggleButtonGroup";
import Typography from "@mui/joy/Typography";

import ModalComponent from "../../components/Modals/Modal";
import ModalLoading from "../../components/Modals/ModalLoading";
import PDFPreviewAndPrint from "../../components/PDF/PDFPreview";
import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { useAuthenticatedUser } from "../../contexts/UserContext";
import { OperationWithinHospitalisation } from "../../models/custom";
import {
  isOperationWithinHospitalisation,
  isPatient,
  isService,
  isUser,
} from "../../models/type_guards";
import { Hospitalisation, Patient } from "../../models/types";
import { router } from "../../router";
import { formatNameAndSurname, isToday, isUserDoctor, truncateText } from "../../utils/utils";
import ServiceList from "./ServiceList";

export interface HospitalisationByService {
  [serviceId: string]: Hospitalisation[];
}

interface OperationWithPatient {
  operation: OperationWithinHospitalisation;
  patient: Patient;
}

interface OperationsByDoctorLastName {
  [doctorLastName: string]: OperationWithPatient[];
}

enum HospitalisationFilter {
  SPE = "SPE",
  PATIENT = "PATIENT",
  SYNTHESE = "SYNTHESE",
}

const ServiceSkeletonLoader = () => {
  return (
    <Stack sx={{ height: "calc(100vh - 100px)", width: "100%" }} gap={2} direction="row">
      <Stack flex={1} gap={2}>
        {Array.from({ length: 3 }).map((_, index) => (
          <Stack gap={2} key={index}>
            <Box
              sx={{
                width: "400px",
                height: "32px",
                position: "relative",
              }}
            >
              <Skeleton sx={{ width: "400px", height: "32px" }} animation="wave" />
            </Box>
            <Stack gap={1}>
              {Array.from({ length: 5 }).map((_, index) => (
                <Box
                  sx={{
                    width: "100%",
                    height: "70px",
                    position: "relative",
                  }}
                  key={index}
                >
                  <Skeleton sx={{ width: "100%", height: "70px" }} animation="wave" />
                </Box>
              ))}
            </Stack>
          </Stack>
        ))}
      </Stack>
      <Stack width={"30%"} gap={2}>
        <Box sx={{ width: "60%", height: "32px", position: "relative" }}>
          <Skeleton sx={{ width: "100%", height: "32px" }} animation="wave" />
        </Box>
        <Stack gap={2}>
          {Array.from({ length: 5 }).map((_, index) => (
            <Box
              sx={{
                width: "100%",
                height: "70px",
                position: "relative",
              }}
              key={index}
            >
              <Skeleton sx={{ width: "100%", height: "70px" }} animation="wave" />
            </Box>
          ))}
        </Stack>
      </Stack>
    </Stack>
  );
};

export default function Hospitalisations() {
  const snackbar = useSnackbar();
  const api = useApi();
  const { user, profile } = useAuthenticatedUser();

  const [openModal, setOpenModal] = React.useState(false);
  const [isPdfReady, setIsPdfReady] = React.useState(false);
  const [isPdfLoading, setIsPdfLoading] = React.useState(false);
  const [showLoadingModal, setShowLoadingModal] = React.useState(false);
  const [hospitalisation, setHospitalisation] = React.useState<HospitalisationByService>({});
  const [hospitalisationFiltered, setHospitalisationFiltered] =
    React.useState<HospitalisationByService>({});
  const [operations, setOperations] = React.useState<OperationsByDoctorLastName>({});
  const [loading, setLoading] = React.useState<boolean>(true);
  const [filter, setFilter] = React.useState<HospitalisationFilter>(() => {
    // if user is not doctor, set filter to service
    if (!isUserDoctor(user)) return HospitalisationFilter.SPE;
    const savedFilter = localStorage.getItem("serviceFilter");
    return savedFilter ? (savedFilter as HospitalisationFilter) : HospitalisationFilter.SPE;
  });
  const [fileBlobURL, setFileBlobURL] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Add sort state for PDF generation
  const [sortField, setSortField] = React.useState<string | null>(null);
  const [sortDirection, setSortDirection] = React.useState<string | null>(null);

  const label =
    filter === HospitalisationFilter.SPE
      ? "Synthèse du service"
      : filter === HospitalisationFilter.PATIENT
        ? "Synthèse des patients propres"
        : "";
  // filter for pdf generation
  const pdfFilter =
    filter === HospitalisationFilter.SPE
      ? "service"
      : filter === HospitalisationFilter.PATIENT
        ? "user"
        : "";

  const handlePrintService = async (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    try {
      setShowLoadingModal(true);
      setIsPdfLoading(true);

      // Include search query and sorting parameters
      const searchParam = searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : "";
      const sortFieldParam = sortField ? `&sort_field=${sortField}` : "";
      const sortDirectionParam = sortDirection ? `&sort_direction=${sortDirection}` : "";

      const blob = await api.getServiceHospitalisationsPdf(
        "true",
        pdfFilter,
        `${searchParam}${sortFieldParam}${sortDirectionParam}`
      );
      const blobURL = window.URL.createObjectURL(blob);
      setFileBlobURL(blobURL);
      setIsPdfReady(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Error fetching PDF:", error);
      snackbar.show("Erreur lors de la génération du PDF", "danger");
    } finally {
      setIsPdfLoading(false);
      setShowLoadingModal(false);
    }
  };

  React.useEffect(() => {
    async function fetchHospitalisations() {
      if (!api) return;
      try {
        const hospitalisations = await api.getServiceHospitalisations(1);
        const hospitalisationsByServices: HospitalisationByService = {};
        const opeByLastName: OperationsByDoctorLastName = {};

        hospitalisations.forEach((h) => {
          if (!isService(h.service_hospitalisation)) return;
          if (!isPatient(h.zkf_patient)) return;
          if (!Array.isArray(h.operations)) return;

          const serviceName = h.service_hospitalisation.nom_service;
          if (!hospitalisationsByServices[serviceName])
            hospitalisationsByServices[serviceName] = [];
          hospitalisationsByServices[serviceName].push(h);

          h.operations.forEach((o) => {
            if (!isOperationWithinHospitalisation(o)) return;
            if (!!o.operation_date && isToday(o.operation_date)) {
              const redactorLastName = o.zkf_redacteur_last_name || "Non spécifié";
              if (!opeByLastName[redactorLastName]) opeByLastName[redactorLastName] = [];
              if (!isPatient(h.zkf_patient)) return;
              opeByLastName[redactorLastName].push({
                operation: o,
                patient: h.zkf_patient,
              });
            }
          });
        });
        setHospitalisation(hospitalisationsByServices);
        Object.keys(opeByLastName).forEach((k) => {
          opeByLastName[k].sort((a, b) =>
            moment(a.operation.operation_start) < moment(b.operation.operation_start) ? -1 : 1
          );
        });
        setOperations(opeByLastName);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    }

    fetchHospitalisations();
  }, [api]);

  React.useEffect(() => {
    if (filter === HospitalisationFilter.SPE) {
      setHospitalisationFiltered(hospitalisation);
    }
    if (filter === HospitalisationFilter.PATIENT) {
      const filtered: HospitalisationByService = {};
      Object.keys(hospitalisation).forEach((key: string) => {
        const hospi = hospitalisation[key].filter((h: Hospitalisation) =>
          h.medecin_referent.some((m) => isUser(m) && m.id.toString() === user.id.toString())
        );
        if (hospi.length) filtered[key] = hospi;
      });
      setHospitalisationFiltered(filtered);
    }
    if (filter === HospitalisationFilter.SYNTHESE) setHospitalisationFiltered(hospitalisation);
  }, [filter, hospitalisation, user.id, profile.specialite_profile]);

  const filteredHospitalisations = React.useMemo(() => {
    if (!searchQuery) return hospitalisationFiltered;

    const filtered: HospitalisationByService = {};
    Object.keys(hospitalisationFiltered).forEach((serviceName) => {
      const filteredPatients = hospitalisationFiltered[serviceName].filter((hospi) => {
        const patient = isPatient(hospi.zkf_patient) ? hospi.zkf_patient : null;
        if (!patient) return false;

        const fullName = formatNameAndSurname(patient.nom, patient.prenom).toLowerCase();
        return fullName.includes(searchQuery.toLowerCase());
      });

      if (filteredPatients.length > 0) {
        filtered[serviceName] = filteredPatients;
      }
    });

    return filtered;
  }, [hospitalisationFiltered, searchQuery]);

  const listOfOperations = React.useMemo(
    () => (
      <Box>
        <Typography mb={2} level="title-lg" color="success" startDecorator={<Masks />}>
          Opérations du jour
        </Typography>
        <Stack direction="column" gap={1}>
          {operations && Object.keys(operations).length > 0 ? (
            Object.keys(operations).map((lastName) => (
              <Stack key={lastName}>
                <Typography level="title-lg">Dr {lastName}</Typography>
                {operations[lastName].map(({ operation, patient }) => (
                  <Sheet
                    variant="soft"
                    color="success"
                    sx={{
                      p: 2,
                      mt: 1,
                      borderRadius: "sm",
                      "&:hover": { cursor: "pointer" },
                    }}
                    key={operation.id}
                    onClick={() => router.navigate("/operation/" + operation.id)}
                  >
                    <Typography level="body-sm">
                      {moment(operation.operation_start).format("HH:mm")} -{" "}
                      {formatNameAndSurname(patient.nom, patient.prenom)}
                    </Typography>
                    <Typography level="body-xs">
                      {truncateText(operation.titre_operation, 130)}
                    </Typography>
                  </Sheet>
                ))}
              </Stack>
            ))
          ) : (
            <Typography
              display={"flex"}
              height={"100px"}
              justifyContent={"center"}
              alignItems={"center"}
              level="body-md"
            >
              Pas d'opération
            </Typography>
          )}
        </Stack>
      </Box>
    ),
    [operations]
  );

  const onLeave = (leaveDate: string | undefined, key: string, serviceId: string) => {
    setHospitalisation((prevState) => ({
      ...prevState,
      [key]: prevState[key].map((s) => {
        if (s.id === serviceId) {
          return { ...s, sortie_date: leaveDate };
        }
        return s;
      }),
    }));
  };

  const handleFilterChange = (newValue: HospitalisationFilter) => {
    setFilter(newValue);
    localStorage.setItem("serviceFilter", newValue);
  };

  return (
    <>
      {/* Ne rendre la modal que quand on est prêt à l'afficher */}
      {openModal && isPdfReady && (
        <ModalComponent
          open={openModal}
          onClose={() => {
            setOpenModal(false);
            // Clean up the blob URL when closing
            if (fileBlobURL) {
              window.URL.revokeObjectURL(fileBlobURL);
              setFileBlobURL(null);
            }
            setIsPdfReady(false);
          }}
          title="Synthèse service"
          withCloseButton={false}
          style={{
            width: "90%",
            maxWidth: "1600px",
            height: "100%",
          }}
        >
          {fileBlobURL ? (
            <>
              <Box sx={{ width: "100%", height: "calc(100% - 70px)" }}>
                <PDFPreviewAndPrint
                  fileUrl={fileBlobURL}
                  onPrint={() => {
                    setOpenModal(false);
                    if (fileBlobURL) {
                      window.URL.revokeObjectURL(fileBlobURL);
                      setFileBlobURL(null);
                    }
                    setIsPdfReady(false);
                  }}
                  withPrint
                />
              </Box>
            </>
          ) : (
            <div>Loading PDF...</div>
          )}
        </ModalComponent>
      )}

      {showLoadingModal && (
        <ModalLoading
          open={showLoadingModal}
          onClose={() => setShowLoadingModal(false)}
          filter={filter}
          title={`Génération de la synthèse du service en cours...${label}`}
          description=""
          withCloseButton={false}
          withCloseIcon={false}
        />
      )}

      <Box>
        {loading ? (
          <ServiceSkeletonLoader />
        ) : (
          <Stack
            direction="row"
            sx={{
              marginTop: "10px",
              gap: 2,
            }}
          >
            <Box width={"70%"}>
              <Box sx={{ mb: 2 }}>
                <Stack direction="row" gap={2}>
                  <FormControl sx={{ flex: 1 }}>
                    <FormLabel>Rechercher un patient dans tous les services</FormLabel>
                    <Input
                      placeholder="Marie Dupont..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      startDecorator={<SearchIcon />}
                      endDecorator={
                        searchQuery && (
                          <IconButton
                            variant="plain"
                            color="neutral"
                            size="sm"
                            onClick={() => setSearchQuery("")}
                          >
                            <CloseIcon />
                          </IconButton>
                        )
                      }
                    />
                  </FormControl>
                  {isUserDoctor(user) && (
                    <FormControl>
                      <FormLabel>Filtrer par</FormLabel>
                      <Stack direction="row" spacing={2}>
                        <ToggleButtonGroup
                          spacing={1}
                          value={filter}
                          onChange={(event, newValue) => {
                            handleFilterChange(newValue || HospitalisationFilter.SPE);
                          }}
                        >
                          <Button value={HospitalisationFilter.SPE}>Par service</Button>
                          <Button value={HospitalisationFilter.PATIENT}>Mes patients</Button>
                        </ToggleButtonGroup>
                      </Stack>
                    </FormControl>
                  )}
                  <FormControl>
                    <FormLabel>Synthèse</FormLabel>
                    <Button
                      variant="solid"
                      color="neutral"
                      onClick={(e) => handlePrintService(e)}
                      startDecorator={<Masks />}
                      disabled={isPdfLoading}
                      sx={{
                        "&:focus": { outline: "none" },
                        "&:active": { transform: "none" },
                      }}
                    >
                      Générer la synthèse
                    </Button>
                  </FormControl>
                </Stack>
              </Box>
              {Object.keys(filteredHospitalisations).length === 0 && searchQuery ? (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    p: 4,
                    border: "1px solid",
                    borderColor: "divider",
                    borderRadius: "sm",
                  }}
                >
                  <Typography level="body-lg" color="neutral">
                    Aucun patient ne correspond à la recherche "{searchQuery}"
                  </Typography>
                </Box>
              ) : (
                <ServiceList
                  hospitalisations={filteredHospitalisations}
                  onLeave={onLeave}
                  onSortChange={(field, direction) => {
                    setSortField(field);
                    setSortDirection(direction);
                  }}
                />
              )}
            </Box>
            <Sheet
              variant="outlined"
              sx={{
                height: "auto",
                width: "30%",
                position: "sticky",
                top: "20px",
                borderRadius: "sm",
                p: 2,
              }}
            >
              {listOfOperations}
            </Sheet>
          </Stack>
        )}
      </Box>
    </>
  );
}
