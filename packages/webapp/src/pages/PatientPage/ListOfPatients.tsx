import React from "react";

import { CreditCard, PersonAdd } from "@mui/icons-material";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import DialogTitle from "@mui/joy/DialogTitle";
import Input from "@mui/joy/Input";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import ModalOverflow from "@mui/joy/ModalOverflow";
import Stack from "@mui/joy/Stack";
import Table from "@mui/joy/Table";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { PaginatedResponse } from "../../clients/ApiClient";
import { CarteVitaleModal } from "../../components/CarteVitale/CarteVitaleModal";
import { CreatePatient } from "../../components/Forms/CreatePatient";
import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { Patient } from "../../models/types";
import { router } from "../../router";
import { CarteVitaleService } from "../../services/CarteVitaleService";
import { CarteVitaleInfo } from "../../types/CarteVitaleTypes";
import { processCarteVitaleData } from "../../utils/CarteVitaleParser";
import { useQuery } from "../../utils/utils";

export default function ListOfPatients() {
  // Context
  const api = useApi();
  const snackbar = useSnackbar();

  // check if create=true in the query params
  const create = useQuery().get("create") === "true";

  // States
  const [page, setPage] = React.useState(1);
  const [search, setSearch] = React.useState("");
  const [patients, setPatients] = React.useState<PaginatedResponse<Patient> | null>(null);
  const [openCreatePatientModal, setOpenCreatePatientModal] = React.useState(create);
  const [openCarteVitaleModal, setOpenCarteVitaleModal] = React.useState(false);
  const [carteVitaleInfo, setCarteVitaleInfo] = React.useState<CarteVitaleInfo | null>(null);
  const [carteVitaleLoading, setCarteVitaleLoading] = React.useState(false);
  const [carteVitaleAvailable, setCarteVitaleAvailable] = React.useState<boolean | null>(null);

  const fetchPatients = React.useCallback(() => {
    if (api) {
      api.listPatients(page).then((res) => {
        setPatients(res);
      });
    }
  }, [api, page, setPatients]);

  React.useEffect(() => {
    fetchPatients();
  }, [fetchPatients]);

  // Check carte vitale service availability on component mount
  React.useEffect(() => {
    const checkCarteVitaleService = async () => {
      try {
        const status = await CarteVitaleService.checkServiceAvailability();
        setCarteVitaleAvailable(status.available);
      } catch (error) {
        setCarteVitaleAvailable(false);
      }
    };

    checkCarteVitaleService();
  }, []);

  const handleReadCarteVitale = async () => {
    setCarteVitaleLoading(true);
    try {
      // For development, use mock data. In production, use the real service
      const isDevelopment = process.env.NODE_ENV === "development";
      const response = isDevelopment
        ? await CarteVitaleService.mockReadCarteVitale()
        : await CarteVitaleService.readCarteVitale();

      if (response.success && response.data) {
        const processingResult = processCarteVitaleData(response.data);

        if (processingResult.success && processingResult.carteVitaleInfo) {
          setCarteVitaleInfo(processingResult.carteVitaleInfo);
          setOpenCarteVitaleModal(true);
        } else {
          snackbar.show(
            processingResult.error || "Erreur lors du traitement des données de la carte vitale",
            "danger"
          );
        }
      } else {
        snackbar.show(response.error || "Erreur lors de la lecture de la carte vitale", "danger");
      }
    } catch (error) {
      console.error("Error reading carte vitale:", error);
      snackbar.show("Erreur lors de la lecture de la carte vitale", "danger");
    } finally {
      setCarteVitaleLoading(false);
    }
  };

  const handleCarteVitalePatientCreated = (patient: Patient) => {
    setOpenCarteVitaleModal(false);
    setCarteVitaleInfo(null);
    fetchPatients(); // Refresh the patient list
    snackbar.show(`Patient créé depuis la carte vitale`, "success");
  };

  if (!patients) {
    return null;
  }

  return (
    <>
      <Box>
        <Stack
          direction="row"
          spacing={2}
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography level="h1" sx={{ mt: 2 }}>
            Patients
          </Typography>
          <Stack direction="row" spacing={2}>
            <Tooltip
              title={
                carteVitaleAvailable === null
                  ? "Vérification du service en cours..."
                  : carteVitaleAvailable
                    ? "Service carte vitale disponible"
                    : "Service carte vitale non disponible - Vérifiez que le lecteur est connecté et le service démarré"
              }
              placement="bottom"
            >
              <Button
                onClick={handleReadCarteVitale}
                startDecorator={<CreditCard />}
                variant="outlined"
                color="primary"
                disabled={carteVitaleAvailable === false || carteVitaleLoading}
                loading={carteVitaleLoading}
              >
                Lire carte vitale
              </Button>
            </Tooltip>
            <Button onClick={() => setOpenCreatePatientModal(true)} startDecorator={<PersonAdd />}>
              Ajouter un patient
            </Button>
          </Stack>
        </Stack>
        {patients.results.length === 0 ? (
          <Typography level="title-md" sx={{ mt: 2 }}>
            Aucun patient trouvé
          </Typography>
        ) : (
          <>
            <Stack direction="row" gap={2} sx={{ mt: 4 }}>
              <Stack direction="column">
                <Typography level="title-md">Rechercher un patient</Typography>
                <Input
                  placeholder="Antoine DUPONT"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </Stack>
            </Stack>

            <Table sx={{ borderRadius: "sm", mt: 2 }} variant="outlined">
              <thead>
                <tr>
                  <th>Nom</th>
                  <th>Prénom</th>
                  <th>Age</th>
                </tr>
              </thead>
              <tbody>
                {patients.results.map((patient) => (
                  <tr
                    key={patient.id}
                    style={{ cursor: "pointer" }}
                    onClick={() => router.navigate("/patient/" + patient.id)}
                  >
                    <td>{patient.nom_naissance}</td>
                    <td>{patient.prenom}</td>
                    <td>{patient.dob}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr>
                  <td colSpan={3}>
                    <Box sx={{ display: "flex", justifyContent: "center" }}>
                      <Button
                        variant="soft"
                        onClick={() => setPage(page - 1)}
                        disabled={page === 0}
                      >
                        Page précédente
                      </Button>
                      <Button variant="soft" sx={{ mx: 2 }} disabled>
                        Page {page} {patients?.next && " — " + Math.floor(patients?.count / 10)}
                      </Button>
                      <Button
                        variant="soft"
                        onClick={() => setPage(page + 1)}
                        disabled={!patients?.next}
                      >
                        Page suivante
                      </Button>
                    </Box>
                  </td>
                </tr>
              </tfoot>
            </Table>
          </>
        )}
      </Box>
      <Modal open={openCreatePatientModal} onClose={() => setOpenCreatePatientModal(false)}>
        <ModalOverflow>
          <ModalDialog sx={{ width: "400px" }}>
            <ModalClose />
            <DialogTitle>Ajouter un nouveau patient</DialogTitle>
            <CreatePatient />
          </ModalDialog>
        </ModalOverflow>
      </Modal>

      <CarteVitaleModal
        open={openCarteVitaleModal}
        onClose={() => {
          setOpenCarteVitaleModal(false);
          setCarteVitaleInfo(null);
        }}
        carteVitaleInfo={carteVitaleInfo}
        onPatientCreated={handleCarteVitalePatientCreated}
      />
    </>
  );
}
