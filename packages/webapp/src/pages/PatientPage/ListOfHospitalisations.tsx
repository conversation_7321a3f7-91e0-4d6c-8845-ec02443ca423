import dayjs from "dayjs";
import React from "react";

import Add from "@mui/icons-material/Add";
import ExitToApp from "@mui/icons-material/ExitToApp";
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import LocalHospital from "@mui/icons-material/LocalHospital";
import Mask from "@mui/icons-material/Masks";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button/Button";
import Chip from "@mui/joy/Chip";
import Divider from "@mui/joy/Divider";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import { isService } from "../../models/type_guards";
import { Hospitalisation, Operation } from "../../models/types";
import { router } from "../../router";
import { formatDate } from "../../utils/utils";
import CreateSlotModal from "../Calendar/CreateSlotDrawer";

interface ListOfHospitalisationsProps {
  currentHospitalisations: Hospitalisation[];
  hospitalisations: Hospitalisation[];
  refresh: () => void;
  hideHeader?: boolean;
}

export default function ListOfHospitalisations({
  currentHospitalisations,
  hospitalisations,
  hideHeader,
  refresh,
}: ListOfHospitalisationsProps) {
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  // Filter out current hospitalizations from the past hospitalizations list
  const pastHospitalisations = hospitalisations.filter(
    (hospi) => !currentHospitalisations.some((current) => current.id === hospi.id)
  );

  // Sort past hospitalizations by entry date (most recent first)
  pastHospitalisations.sort(
    (a, b) => new Date(b.entree_date!).getTime() - new Date(a.entree_date!).getTime()
  );

  const handleOpenCreateHospitalisationModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseCreateHospitalisationModal = () => {
    setIsModalOpen(false);
  };

  const handleOnSave = () => {
    handleCloseCreateHospitalisationModal();
    refresh();
  };

  // Function to navigate to operation details page
  const handleOperationClick = (event: React.MouseEvent<HTMLDivElement>, operationId: string) => {
    event.stopPropagation(); // Prevent the click from bubbling up to the hospitalization sheet
    router.navigate(`/operation/${operationId}`);
  };

  // Helper function to render operations list for tooltip
  const renderOperationsList = (operations: Operation[]) => {
    return (
      <Stack gap={0.5} sx={{ p: 1 }}>
        {operations.map((operation) => (
          <Typography key={operation.id} level="body-sm" sx={{ whiteSpace: "nowrap" }}>
            {formatDate(operation.operation_date)} — {operation.titre_operation}
          </Typography>
        ))}
      </Stack>
    );
  };

  // Helper function to render a single hospitalization
  const renderHospitalisation = (hospi: Hospitalisation) => {
    const isFutureHospitalisation = dayjs(hospi.entree_date) > dayjs();
    return (
      <Sheet
        key={hospi.id}
        color={isFutureHospitalisation ? "success" : "warning"}
        variant="soft"
        sx={{
          position: "relative",
          border: "1px solid transparent",
          borderRadius: "sm",
          p: 2,
          pt: 3,
          "&:hover": {
            borderColor: (theme) => theme.palette.warning[500],
            cursor: "pointer",
          },
        }}
        onClick={() => router.navigate(`/hospitalisation/${hospi.id}`)}
      >
        <Stack direction="column" gap={1}>
          <Stack
            direction="row"
            justifyContent="space-between"
            sx={{
              position: "absolute",
              top: -10,
              right: 0,
              width: "100%",
              px: 1,
            }}
          >
            <Stack direction="row" gap={1}>
              <Chip size="sm" variant="outlined" startDecorator={<LocalHospital />}>
                {formatDate(hospi.entree_date)}
                {hospi.entree_heure && " " + hospi.entree_heure.slice(0, 5)}
              </Chip>
              {hospi.sortie_date && (
                <Chip
                  size="sm"
                  variant="outlined"
                  startDecorator={<ExitToApp />}
                  color={hospi.sortie_date ? "neutral" : "warning"}
                >
                  {formatDate(hospi.sortie_date)}
                </Chip>
              )}
            </Stack>

            {hospi.operations.length > 0 && (
              <Tooltip
                title={renderOperationsList(hospi.operations as unknown as Operation[])}
                placement="bottom-end"
                variant="outlined"
                arrow
              >
                <Chip variant="outlined" startDecorator={<Mask />} color="warning" size="sm">
                  {hospi.operations.length} opération
                  {hospi.operations.length > 1 && "s"}
                  &nbsp;liée
                  {hospi.operations.length > 1 && "s"}
                </Chip>
              </Tooltip>
            )}
          </Stack>
          <Typography level="body-md">
            {hospi.motif_hospitalisation || (
              <span style={{ opacity: 0.7 }}>Motif non renseigné</span>
            )}
          </Typography>
          {isService(hospi.service_hospitalisation) && (
            <Typography level="body-xs" textAlign="right" fontStyle="italic">
              {hospi.service_hospitalisation.nom_service}
            </Typography>
          )}

          {/* List of operations for this hospitalization, only shown for current hospitalizations */}
          {Array.isArray(hospi.operations) &&
            hospi.operations.length > 0 &&
            currentHospitalisations.some((current) => current.id === hospi.id) && (
              <Box>
                <Divider>
                  <Typography level="body-sm" color="warning">
                    Opérations liées
                  </Typography>
                </Divider>
                <Stack spacing={1} sx={{ mt: 1 }}>
                  {hospi.operations.map((operation: Operation) => (
                    <Sheet
                      key={operation.id}
                      variant="outlined"
                      color="success"
                      sx={{
                        p: 1,
                        borderRadius: "sm",
                        "&:hover": {
                          backgroundColor: (theme) => theme.palette.success[100],
                          cursor: "pointer",
                        },
                      }}
                      onClick={(e) => handleOperationClick(e, operation.id)}
                    >
                      <Stack direction="row" alignItems="center" justifyContent="space-between">
                        <Stack>
                          <Typography level="body-sm">{operation.titre_operation}</Typography>
                          <Typography level="body-xs">
                            {formatDate(operation.operation_date)}
                            {operation.operation_start &&
                              " - " + dayjs(operation.operation_start).format("HH:mm")}
                          </Typography>
                        </Stack>
                        <KeyboardArrowRight fontSize="small" />
                      </Stack>
                    </Sheet>
                  ))}
                </Stack>
              </Box>
            )}
        </Stack>
      </Sheet>
    );
  };

  return (
    <>
      <Box>
        {!hideHeader && (
          <Stack sx={{ mb: 2 }} direction="row" justifyContent="space-between" alignItems="center">
            <Typography level="title-lg" color="warning" startDecorator={<LocalHospital />}>
              Hospitalisations
            </Typography>
            <Button
              data-cy={"hospitalisation-create-button"}
              variant="plain"
              startDecorator={<Add />}
              color="warning"
              size="sm"
              onClick={handleOpenCreateHospitalisationModal}
            >
              Créer
            </Button>
          </Stack>
        )}
        <Stack direction="column" gap={3}>
          {pastHospitalisations.length === 0 && currentHospitalisations.length === 0 ? (
            <Stack sx={{ height: "120px" }} alignItems="center" justifyContent="center">
              <Typography level="body-md" color="neutral">
                Aucune hospitalisation
              </Typography>
            </Stack>
          ) : (
            <>
              {/* Current hospitalizations section */}
              {currentHospitalisations.length > 0 && (
                <>
                  <Box>
                    <Typography level="title-sm" color="warning" sx={{ mb: 2 }}>
                      Hospitalisation
                      {currentHospitalisations.length > 1 ? "s" : ""} en cours
                    </Typography>
                    <Stack direction="column" gap={2}>
                      {currentHospitalisations.map((hospi) => renderHospitalisation(hospi))}
                    </Stack>
                  </Box>

                  {pastHospitalisations.length > 0 && (
                    <Divider>
                      <Typography level="body-sm" color="neutral" sx={{ px: 1 }}>
                        Hospitalisations passées
                      </Typography>
                    </Divider>
                  )}
                </>
              )}

              {/* Past hospitalizations section */}
              {pastHospitalisations.length > 0 && (
                <Box>
                  {/* {!currentHospitalisations.length > 0 && (
                    <Typography level="title-sm" color="neutral" sx={{ mb: 2 }}>
                      Hospitalisations passées
                    </Typography>
                  )} */}
                  <Stack direction="column" gap={2}>
                    {pastHospitalisations.map((hospi) => renderHospitalisation(hospi))}
                  </Stack>
                </Box>
              )}
            </>
          )}
        </Stack>
      </Box>
      {isModalOpen && (
        <CreateSlotModal
          onSave={handleOnSave}
          open={isModalOpen}
          onClose={handleCloseCreateHospitalisationModal}
          forcedType="hospitalisation"
          forcedCurrentPatient
        />
      )}
    </>
  );
}
