import debounce from "lodash/debounce";
import React from "react";

import { AccountTree, Add, Search } from "@mui/icons-material";
import MoreIcon from "@mui/icons-material/MoreHoriz";
import Autocomplete from "@mui/joy/Autocomplete";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Card from "@mui/joy/Card";
import CardContent from "@mui/joy/CardContent";
import Chip from "@mui/joy/Chip";
import CircularProgress from "@mui/joy/CircularProgress";
import DialogContent from "@mui/joy/DialogContent";
import Drawer from "@mui/joy/Drawer";
import Dropdown from "@mui/joy/Dropdown";
import Menu from "@mui/joy/Menu";
import MenuButton from "@mui/joy/MenuButton";
import MenuItem from "@mui/joy/MenuItem";
import ModalClose from "@mui/joy/ModalClose";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Table from "@mui/joy/Table";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";
import { keyframes } from "@mui/system";

import AiSuggested from "../../../../../components/Icons/AiSuggested";
import { useApi } from "../../../../../contexts/ApiContext";
import { useSnackbar } from "../../../../../contexts/SnackbarContext";
import { Acte, Operation, OperationDevisCcam } from "../../../../../models/types";
import CCAMListAndMenus from "./CCamListAndMenus";

interface OperationCCAMPanelProps {
  operation: Operation;
}

// Define a pulse animation for successful actions
const pulseAnimation = keyframes`
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
`;

export default function OperationCCAMPanel({ operation }: OperationCCAMPanelProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [actes, setActes] = React.useState<OperationDevisCcam[]>([]);
  const [searchOptions, setSearchOptions] = React.useState<Acte[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [searchLoading, setSearchLoading] = React.useState(false);
  const [suggestedActes, setSuggestedActes] = React.useState<Acte[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = React.useState(false);
  const [modalOpen, setModalOpen] = React.useState(false);

  // New state for tracking loading state when adding an acte
  const [addingActe, setAddingActe] = React.useState(false);
  // State to track the most recently added acte for animation
  const [recentlyAddedActe, setRecentlyAddedActe] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchActes = async () => {
      const res = await api.getActesForOperation(operation.id);
      setActes(res);
    };
    fetchActes();

    const fetchSuggestedActes = async () => {
      setLoadingSuggestions(true);
      try {
        const res = await api.getSuggestedActesForOperation(operation.id);
        setSuggestedActes(res);
      } catch (error) {
        console.error("Error fetching suggested actes:", error);
        snackbar.show("Erreur lors du chargement des actes suggérés", "danger");
      } finally {
        setLoadingSuggestions(false);
      }
    };
    fetchSuggestedActes();
  }, [api, snackbar, operation.id]);

  // Effect to clear the recently added acte animation after 2 seconds
  React.useEffect(() => {
    if (recentlyAddedActe) {
      const timer = setTimeout(() => {
        setRecentlyAddedActe(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [recentlyAddedActe]);

  const fetchCCAMSearch = React.useCallback(
    async (query: string) => {
      setSearchLoading(true);
      if (query.length === 0) {
        setSearchOptions([]);
        setSearchLoading(false);
        return;
      }
      try {
        const res = await api.searchCCAM(query);
        setSearchOptions(res);
        // Ne pas mettre à jour searchQuery ici pour éviter les conflits
      } catch (error: any) {
        console.error(error);
        snackbar.show("Erreur lors de la recherche", "danger");
      } finally {
        setSearchLoading(false);
      }
    },
    [api, snackbar]
  );

  const debouncedFetchCCAMSearch = React.useMemo(
    () => debounce(fetchCCAMSearch, 300),
    [fetchCCAMSearch]
  );

  React.useEffect(() => {
    return () => {
      debouncedFetchCCAMSearch.cancel();
    };
  }, [debouncedFetchCCAMSearch]);

  const handleAddActe = React.useCallback(
    async (acte: Acte) => {
      try {
        if (!acte.cod_acte) {
          throw new Error("Le code acte est manquant");
        }

        setAddingActe(true);
        const res = await api.addActeToOperation(operation.id, acte.cod_acte);

        // Check if the acte is already added to avoid duplicates
        if (!actes.some((a) => a.acte_ccam === acte.cod_acte)) {
          setActes((prevActes) => [...prevActes, res]);
          // Set the recently added acte for animation
          setRecentlyAddedActe(res.acte_ccam);
          snackbar.show("Acte ajouté à l'opération", "success");
        } else {
          snackbar.show("Cet acte est déjà ajouté à l'opération", "warning");
        }
      } catch (error: any) {
        console.error(error);
        snackbar.show("Erreur lors de l'ajout de l'acte", "danger");
      } finally {
        setAddingActe(false);
      }
    },
    [api, operation.id, actes, snackbar]
  );

  const handleDeleteActe = async (acte: OperationDevisCcam) => {
    try {
      await api.removeActeFromOperation(operation.id, acte.id);
      setActes(actes.filter((a) => a.id !== acte.id));
      snackbar.show("Acte supprimé de l'opération", "success");
    } catch (error: any) {
      console.error(error);
      snackbar.show("Erreur lors de la suppression de l'acte", "danger");
    }
  };

  return (
    <Stack gap={2}>
      <Typography level="h4">Codage CCAM</Typography>
      <Stack direction="row" gap={2}>
        <Stack gap={2} sx={{ width: "100%" }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Autocomplete
              sx={{ minWidth: 500 }}
              placeholder="Entrez du texte pour lancer la recherche"
              noOptionsText={
                !!searchQuery
                  ? searchLoading
                    ? "Chargement..."
                    : "Aucun résultat"
                  : "Recherchez un acte dans la barre ci-dessus"
              }
              options={searchOptions}
              getOptionLabel={(option) => `${option.cod_acte} - ${option.nom_long}`}
              inputValue={searchQuery}
              onInputChange={(event, newValue) => {
                setSearchQuery(newValue);
                if (newValue.trim().length >= 2) {
                  debouncedFetchCCAMSearch(newValue);
                } else if (newValue.trim().length === 0) {
                  setSearchOptions([]);
                  setSearchLoading(false);
                }
              }}
              onChange={(event, newValue) => {
                if (newValue) {
                  handleAddActe(newValue);
                  // Effacer la recherche après ajout d'un acte
                  setSearchQuery("");
                  setSearchOptions([]);
                }
              }}
              disabled={addingActe}
              loading={searchLoading}
              clearOnBlur={false}
              clearOnEscape={true}
              blurOnSelect={false}
              startDecorator={searchLoading ? <CircularProgress size="sm" /> : <Search />}
            />
            <Button
              variant="outlined"
              color="primary"
              startDecorator={<AccountTree />}
              onClick={() => setModalOpen(true)}
              disabled={addingActe}
            >
              Arborescence CCAM
            </Button>
          </Box>

          {/* Suggested actes section */}
          {!loadingSuggestions &&
            suggestedActes.length > 0 &&
            suggestedActes.some((acte) => !actes.some((a) => a.acte_ccam === acte.cod_acte)) && (
              <Card variant="outlined">
                <CardContent>
                  <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                    <Typography level="title-md" startDecorator={<AiSuggested />}>
                      Actes suggérés
                    </Typography>
                    <Tooltip title="Actes recommandés basés sur l'opération" placement="top">
                      <Typography level="body-sm" color="neutral">
                        (selon type d'opération)
                      </Typography>
                    </Tooltip>
                  </Stack>

                  {suggestedActes.length === 0 ? (
                    <Typography level="body-sm">Aucun acte suggéré pour cette opération</Typography>
                  ) : (
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                      {suggestedActes
                        .filter((acte) => !actes.some((a) => a.acte_ccam === acte.cod_acte))
                        .map((acte) => (
                          <Tooltip
                            key={acte.cod_acte}
                            title={
                              acte.nom_long && acte.nom_long.length > 60
                                ? acte.cod_acte + " — " + acte.nom_long
                                : undefined
                            }
                            placement="top"
                            arrow
                          >
                            <Chip
                              variant="outlined"
                              color="neutral"
                              startDecorator={
                                addingActe ? (
                                  <CircularProgress size="sm" />
                                ) : (
                                  <Add fontSize="small" />
                                )
                              }
                              onClick={() => handleAddActe(acte)}
                              disabled={addingActe}
                            >
                              {acte.cod_acte} - {acte.nom_long?.substring(0, 60)}
                              {acte.nom_long && acte.nom_long.length > 60 ? "..." : ""}
                            </Chip>
                          </Tooltip>
                        ))}
                    </Box>
                  )}
                </CardContent>
              </Card>
            )}
          {actes.length === 0 ? (
            <Stack
              sx={{ minHeight: 300 }}
              direction="column"
              gap={2}
              textAlign="center"
              justifyContent="center"
              alignItems="center"
            >
              <Stack direction="column" gap={0.5}>
                <Typography level="body-lg">
                  Il n'y a pas d'acte CCAM pour cette opération.
                </Typography>
                <Typography level="body-sm">
                  Utilisez la barre de recherche ci-dessus pour ajouter un acte CCAM à l'opération,
                  <br />
                  ou explorez l'arborescence CCAM.
                </Typography>
              </Stack>
            </Stack>
          ) : (
            <Sheet variant="outlined" sx={{ p: 0 }}>
              <Table
                sx={{
                  "--Table-headerUnderlineThickness": "1px",
                  "--Table-headerUnderlineColor": "var(--joy-palette-neutral-200)",
                  "--TableCell-paddingY": "12px",
                  "--TableCell-paddingX": "16px",
                  "--TableRow-hoverBackground": "var(--joy-palette-neutral-50)",
                  "--TableRow-borderColor": "var(--joy-palette-neutral-200)",
                  "--TableRow-hoverBorderColor": "var(--joy-palette-neutral-300)",
                  boxShadow: "none",
                  overflow: "auto",
                  width: "100%",
                  border: "1px solid var(--joy-palette-neutral-200)",
                  borderRadius: "sm",
                  "& tr": {
                    borderBottom: "1px solid var(--joy-palette-neutral-200)",
                    "&:last-child": {
                      borderBottom: "none",
                    },
                    "&:nth-of-type(even)": {
                      backgroundColor: "var(--joy-palette-neutral-50)",
                    },
                  },
                  "& th": {
                    fontWeight: "bold",
                    textTransform: "uppercase",
                    fontSize: "0.75rem",
                    letterSpacing: "0.05em",
                    color: "var(--joy-palette-neutral-600)",
                    backgroundColor: "var(--joy-palette-neutral-50)",
                    padding: "12px 16px",
                  },
                  "& td": {
                    padding: "12px 16px",
                  },
                  "& td:first-of-type": {
                    paddingLeft: "16px",
                  },
                  "& td:last-of-type": {
                    paddingRight: "16px",
                    textAlign: "right",
                  },
                }}
                variant="outlined"
              >
                <thead>
                  <tr>
                    <th style={{ width: "150px" }}>Code CCAM</th>
                    <th>Description</th>
                    <th style={{ width: "75px" }}></th>
                  </tr>
                </thead>
                <tbody>
                  {actes.map((acte) => (
                    <tr
                      key={acte.id}
                      style={
                        recentlyAddedActe === acte.acte_ccam
                          ? {
                              animation: `${pulseAnimation} 1s ease`,
                              backgroundColor: "var(--joy-palette-success-softBg)",
                            }
                          : {}
                      }
                    >
                      <td>
                        <Typography
                          level="title-md"
                          sx={{
                            fontWeight: "bold",
                            color: "var(--joy-palette-primary-600)",
                          }}
                        >
                          {acte.acte_ccam}
                        </Typography>
                      </td>
                      <td>
                        <Typography
                          level="body-sm"
                          sx={{
                            display: "-webkit-box",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            color: "text.primary",
                          }}
                        >
                          {acte.acte_nom_long}
                        </Typography>
                      </td>
                      <td>
                        <Dropdown>
                          <MenuButton
                            variant="plain"
                            sx={{
                              borderRadius: "sm",
                              "&:hover": {
                                backgroundColor: "var(--joy-palette-neutral-100)",
                              },
                            }}
                          >
                            <MoreIcon />
                          </MenuButton>
                          <Menu placement="bottom-end" size="sm">
                            <MenuItem onClick={() => handleDeleteActe(acte)}>Supprimer</MenuItem>
                          </Menu>
                        </Dropdown>
                      </td>
                    </tr>
                  ))}
                  {actes.length === 0 && (
                    <tr>
                      <td colSpan={3}>
                        <Box
                          sx={{
                            py: 4,
                            display: "flex",
                            justifyContent: "center",
                            color: "text.tertiary",
                          }}
                        >
                          <Typography level="body-md">Aucun acte CCAM ajouté</Typography>
                        </Box>
                      </td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </Sheet>
          )}

          {/* Loading overlay */}
          {addingActe && (
            <Box
              sx={{
                position: "fixed",
                bottom: 16,
                right: 16,
                zIndex: 9999,
                display: "flex",
                alignItems: "center",
                gap: 1,
                bgcolor: "background.surface",
                p: 1,
                borderRadius: "sm",
                boxShadow: "sm",
              }}
            >
              <CircularProgress size="sm" />
              <Typography level="body-sm">Ajout en cours...</Typography>
            </Box>
          )}
        </Stack>
      </Stack>

      {/* Modal for ActeListAndMenus */}
      <Drawer
        anchor="right"
        open={modalOpen}
        onClose={() => !addingActe && setModalOpen(false)}
        size="lg"
      >
        <DialogContent sx={{ p: 2 }}>
          <ModalClose disabled={addingActe} />
          <Typography level="h4" sx={{ mb: 2 }}>
            Arborescence CCAM
          </Typography>
          <CCAMListAndMenus onAddActe={handleAddActe} />
        </DialogContent>
      </Drawer>
    </Stack>
  );
}
