import React, { useMemo } from "react";

import { Radar, Save } from "@mui/icons-material";
import { Button, styled } from "@mui/joy";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import ContrasteSelect from "../../../../../components/Forms/ContrasteSelect";
import MaterielIrradiationSelect from "../../../../../components/Forms/MaterielIrradiationSelect";
import { useApi } from "../../../../../contexts/ApiContext";
import { useSnackbar } from "../../../../../contexts/SnackbarContext";
import { Irradiation, Operation } from "../../../../../models/types";

const DurationInput = styled("input")(({ theme }) => ({
  lineHeight: "32px",
  paddingInline: "12px",
  border: `1px solid`,
  borderRadius: "6px",
  fontFamily: `${theme.fontFamily.body}`,
  fontSize: `${theme.fontSize.md || "1rem"}`,
  boxSizing: "border-box",
  boxShadow: `${theme.shadowRing || "0 0 #000"}, 0px 1px 2px 0px rgba(${theme.shadowChannel || "21 21 21"} / 0.08)`,
  color: `${theme.variants.outlined.neutral.color}`,
  borderColor: `${theme.variants.outlined.neutral.borderColor}`,
  backgroundColor: `${theme.palette.background.surface}`,
}));

export interface ScopieState {
  materielIrradiation: string;
  pdsTotal: number | null;
  pdsUnite: string;
  airKermaTotal: number | null;
  airKermaUnite: string;
  dureeScopieTotal: string;
  volumeIode: number | null;
  pdtContraste: string;
  pdsScopie: number | null;
  airKermaScopie: number | null;
  dureeScopie: string;
  pdsAcquisition: number | null;
  airKermaAcquisition: number | null;
  dureeAcquisition: string;
  expanded: boolean;
}

interface OperationCROPanelScopieProps {
  operation: Operation;
}

export default function OperationCROPanelScopie({ operation }: OperationCROPanelScopieProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [irradiation, setIrradiation] = React.useState<Irradiation>();
  const [state, setState] = React.useState<ScopieState>({
    materielIrradiation: operation.materiel_irradiation || "",
    pdsTotal: null,
    pdsUnite: "",
    airKermaTotal: null,
    airKermaUnite: "",
    dureeScopieTotal: "",
    volumeIode: null,
    pdtContraste: "",
    pdsScopie: null,
    airKermaScopie: null,
    dureeScopie: "",
    pdsAcquisition: null,
    airKermaAcquisition: null,
    dureeAcquisition: "",
    expanded: !!operation?.materiel_irradiation,
  });

  React.useEffect(() => {
    api.getIrradiationForOperation(operation.id).then((res) => setIrradiation(res[0]));
  }, [api, operation.id]);

  React.useEffect(() => {
    setState((prev) => ({
      ...prev,
      pdsTotal: irradiation?.pds_total || null,
      pdsUnite: irradiation?.pds_unite || "",
      airKermaTotal: irradiation?.air_kerma_total || null,
      airKermaUnite: irradiation?.air_kerma_unite || "",
      dureeScopieTotal: irradiation?.duree_scopie_total || "",
      volumeIode: irradiation?.volume_iode || null,
      pdtContraste: irradiation?.produit_contraste || "",
      pdsScopie: irradiation?.pds_scopie || null,
      airKermaScopie: irradiation?.air_kerma_scopie || null,
      dureeScopie: irradiation?.duree_scopie_scopie || "",
      pdsAcquisition: irradiation?.pds_acquisition || null,
      airKermaAcquisition: irradiation?.air_kerma_acquisition || null,
      dureeAcquisition: irradiation?.duree_scopie_acquisition || "",
    }));
  }, [irradiation]);

  React.useEffect(() => {
    setState((prev) => ({
      ...prev,
      expanded: !!prev.materielIrradiation,
    }));
  }, [state.materielIrradiation]);

  const hasModification = useMemo(
    () =>
      (operation.materiel_irradiation ?? "") !== state.materielIrradiation ||
      (irradiation?.pds_total ?? null) !== state.pdsTotal ||
      (irradiation?.pds_unite ?? "") !== state.pdsUnite ||
      (irradiation?.air_kerma_total ?? null) !== state.airKermaTotal ||
      (irradiation?.air_kerma_unite ?? "") !== state.airKermaUnite ||
      (irradiation?.duree_scopie_total ?? "") !== state.dureeScopieTotal ||
      (irradiation?.volume_iode ?? null) !== state.volumeIode ||
      (irradiation?.produit_contraste ?? "") !== state.pdtContraste ||
      (irradiation?.pds_scopie ?? null) !== state.pdsScopie ||
      (irradiation?.air_kerma_scopie ?? null) !== state.airKermaScopie ||
      (irradiation?.duree_scopie_scopie ?? "") !== state.dureeScopie ||
      (irradiation?.pds_acquisition ?? null) !== state.pdsAcquisition ||
      (irradiation?.air_kerma_acquisition ?? null) !== state.airKermaAcquisition ||
      (irradiation?.duree_scopie_acquisition ?? "") !== state.dureeAcquisition,
    [irradiation, operation, state]
  );

  const updateState = (updates: Partial<ScopieState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  const handleSave = async () => {
    try {
      const newIrradiation: Partial<Irradiation> = {
        pds_total: state.pdsTotal,
        pds_unite: state.pdsUnite,
        air_kerma_total: state.airKermaTotal,
        air_kerma_unite: state.airKermaUnite,
        duree_scopie_total: state.dureeScopieTotal || null,
        volume_iode: state.volumeIode,
        produit_contraste: state.pdtContraste,
        pds_scopie: state.pdsScopie,
        air_kerma_scopie: state.airKermaScopie,
        duree_scopie_scopie: state.dureeScopie || null,
        pds_acquisition: state.pdsAcquisition,
        air_kerma_acquisition: state.airKermaAcquisition,
        duree_scopie_acquisition: state.dureeAcquisition || null,
        zkf_operation: operation.id,
      };
      let res;
      if (irradiation) {
        res = await api.updateIrradiation({
          ...irradiation,
          ...newIrradiation,
        });
      } else {
        res = await api.createIrradiation(newIrradiation);
      }
      setIrradiation(res);
      snackbar.show("Données d'irradiation enregistrées avec succès", "success");
    } catch (e) {
      console.error(e);
      snackbar.show("Erreur lors de l'enregistrement des donnees d'irradiation", "danger");
    }
  };

  return (
    <Stack gap={2}>
      <Typography level="title-md" startDecorator={<Radar />}>
        Données d'irradiation
      </Typography>
      <Stack direction="row" gap={2}>
        <FormControl>
          <FormLabel>Appareil de scopie</FormLabel>
          <MaterielIrradiationSelect
            value={state.materielIrradiation}
            onSelect={(value) => {
              updateState({
                materielIrradiation: value?.id || "",
                pdsUnite: value?.pds_unite || "",
                airKermaUnite: value?.air_kerma_unite || "",
              });
            }}
            width={"300px"}
          />
        </FormControl>
      </Stack>

      <Stack gap={2}>
        <Stack direction="row" gap={2}>
          <FormControl sx={{ width: "20%" }}>
            <FormLabel>Pds total</FormLabel>
            <Input
              type="number"
              value={state.pdsTotal || ""}
              onChange={(e) => updateState({ pdsTotal: Number(e.target.value) || null })}
              tabIndex={1}
            />
          </FormControl>
          <FormControl sx={{ width: "20%" }}>
            <FormLabel>Pds Unite</FormLabel>
            <Input
              value={state.pdsUnite}
              onChange={(e) => updateState({ pdsUnite: e.target.value || "" })}
              tabIndex={2}
            />
          </FormControl>
          <FormControl sx={{ width: "20%" }}>
            <FormLabel>Air kerma total</FormLabel>
            <Input
              type="number"
              value={state.airKermaTotal || ""}
              onChange={(e) => updateState({ airKermaTotal: Number(e.target.value) || null })}
              tabIndex={3}
            />
          </FormControl>
          <FormControl sx={{ width: "20%" }}>
            <FormLabel>Air kerma unite</FormLabel>
            <Input
              value={state.airKermaUnite}
              onChange={(e) => updateState({ airKermaUnite: e.target.value || "" })}
              tabIndex={4}
            />
          </FormControl>
        </Stack>

        <Stack direction="row" gap={2}>
          <FormControl sx={{ width: "20%" }}>
            <FormLabel>Duree scopie total</FormLabel>
            <DurationInput
              type="time"
              step="1"
              value={state.dureeScopieTotal}
              onChange={(e) => updateState({ dureeScopieTotal: e.target.value || "" })}
              tabIndex={5}
            />
          </FormControl>
          <FormControl sx={{ width: "20%" }}>
            <FormLabel>Volume iode</FormLabel>
            <Input
              type="number"
              value={state.volumeIode || ""}
              onChange={(e) => updateState({ volumeIode: Number(e.target.value) || null })}
              tabIndex={6}
            />
          </FormControl>
          <FormControl sx={{ width: "20%" }}>
            <FormLabel>Produit contraste</FormLabel>
            <ContrasteSelect
              value={state.pdtContraste}
              onSelect={(contraste) => updateState({ pdtContraste: contraste?.id || "" })}
            />
          </FormControl>
        </Stack>

        <Stack gap={2}>
          <Typography level="title-sm">Scopie</Typography>
          <Stack direction="row" gap={2}>
            <FormControl sx={{ width: "20%" }}>
              <FormLabel>Pds scopie</FormLabel>
              <Input
                type="number"
                value={state.pdsScopie || ""}
                onChange={(e) => updateState({ pdsScopie: Number(e.target.value) || null })}
              />
            </FormControl>
            <FormControl sx={{ width: "20%" }}>
              <FormLabel>Air kerma scopie</FormLabel>
              <Input
                type="number"
                value={state.airKermaScopie || ""}
                onChange={(e) => updateState({ airKermaScopie: Number(e.target.value) || null })}
              />
            </FormControl>
            <FormControl sx={{ width: "20%" }}>
              <FormLabel>Duree scopie</FormLabel>
              <DurationInput
                type="time"
                step="1"
                value={state.dureeScopie}
                onChange={(e) => updateState({ dureeScopie: e.target.value || "" })}
              />
            </FormControl>
          </Stack>
        </Stack>

        <Stack gap={2}>
          <Typography level="title-sm">Acquisition</Typography>
          <Stack direction="row" gap={2}>
            <FormControl sx={{ width: "20%" }}>
              <FormLabel>Pds acquisition</FormLabel>
              <Input
                type="number"
                value={state.pdsAcquisition || ""}
                onChange={(e) => updateState({ pdsAcquisition: Number(e.target.value) || null })}
              />
            </FormControl>
            <FormControl sx={{ width: "20%" }}>
              <FormLabel>Air kerma acquisition</FormLabel>
              <Input
                type="number"
                value={state.airKermaAcquisition || ""}
                onChange={(e) =>
                  updateState({ airKermaAcquisition: Number(e.target.value) || null })
                }
              />
            </FormControl>
            <FormControl sx={{ width: "20%" }}>
              <FormLabel>Duree acquisition</FormLabel>
              <DurationInput
                type="time"
                step="1"
                value={state.dureeAcquisition}
                onChange={(e) => updateState({ dureeAcquisition: e.target.value || "" })}
              />
            </FormControl>
          </Stack>
        </Stack>
      </Stack>
      <Stack direction="row" justifyContent="flex-end">
        <Button onClick={handleSave} disabled={!hasModification} startDecorator={<Save />}>
          Enregistrer les données d'irradiation
        </Button>
      </Stack>
    </Stack>
  );
}
