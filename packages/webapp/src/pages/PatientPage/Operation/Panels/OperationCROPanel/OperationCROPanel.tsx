import React from "react";

import { AutoAwesome } from "@mui/icons-material";
import Add from "@mui/icons-material/Add";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";
import Edit from "@mui/icons-material/Edit";
import Save from "@mui/icons-material/Save";
import Search from "@mui/icons-material/Search";
import { FormControl, FormLabel } from "@mui/joy";
import Button from "@mui/joy/Button";
import Divider from "@mui/joy/Divider";
import Input from "@mui/joy/Input";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import MultiRootEditor from "../../../../../components/Editor/MultiRootEditor";
import DoctorSelect from "../../../../../components/Forms/DoctorSelect";
import LastConsultationModalTextInsert from "../../../../../components/Forms/LastConsultationModalTextInsert";
import PrintButtons from "../../../../../components/PrintManager/PrintButtons";
import { useApi } from "../../../../../contexts/ApiContext";
import { CROType, PrintData } from "../../../../../models/custom";
import { isPatient, isUser } from "../../../../../models/type_guards";
import { Operation } from "../../../../../models/types";
import CroTypeModal from "../../../../Preferences/panels/DocumentsTypes/CroTypeModal";
import OperationEditDrawer from "../../OperationEditDrawer";
import OperationCROPanelScopie from "./OperationCROPanelScopie";
import OperationViewSheet from "./OperationViewSheet";

interface OperationCROPanelProps {
  operation: Operation;
  onUpdate: (updatedOperation: Partial<Operation>) => void;
}

export default function OperationCROPanel({ operation, onUpdate }: OperationCROPanelProps) {
  // Context
  const api = useApi();

  // States
  const [openEditDrawer, setOpenEditDrawer] = React.useState<boolean>(false);
  const [content, setContent] = React.useState<string>(operation.cro || "");
  const [indicationOperatoire, setIndicationOperatoire] = React.useState<string | null>(
    operation.indication_operatoire || ""
  );
  const [croTypes, setCroTypes] = React.useState<CROType[]>([]);
  const [search, setSearch] = React.useState<string>("");
  const [displayedCroList, setDisplayedCroList] = React.useState<CROType[]>([]);
  const [openLastConsultModal, setOpenLastConsultModal] = React.useState<boolean>(false);
  const [openCroType, setOpenCroType] = React.useState<boolean>(false);
  const [selectedCroType, setSelectedCroType] = React.useState<Partial<CROType>>();
  const [redacteur, setRedacteur] = React.useState<number | null>(
    isUser(operation.zkf_redacteur) ? operation.zkf_redacteur.id : operation.zkf_redacteur || null
  );

  // Fetch CRO types when component mounts
  React.useEffect(() => {
    api.getCROTypes().then(setCroTypes);
  }, [api]);

  React.useEffect(() => {
    setDisplayedCroList(
      croTypes.filter((cro) => cro.cro_type_titre?.toLowerCase().includes(search.toLowerCase()))
    );
  }, [croTypes, search]);

  const hasModification = React.useMemo(
    () =>
      operation.cro !== content ||
      operation.indication_operatoire !== indicationOperatoire ||
      redacteur !== operation.zkf_redacteur,
    [content, indicationOperatoire, operation, redacteur]
  );

  const insertCROType = async (cro_id: string) => {
    await handleSave();
    const cro = croTypes.find((cro) => cro.id === cro_id);
    const cro_content = await api?.getCroContentWithTemplate(operation.id, cro_id);
    if (cro && cro_content) {
      setContent((prevState) => (prevState ? prevState + "<br />" : "") + cro_content.cro_type);
      setIndicationOperatoire(
        (prevState) =>
          (prevState ? prevState + "<br />" : "") + cro_content.indication_operation_type
      );
    }
  };

  const handleSave = async () => {
    const newOp: Operation = {
      ...operation,
      cro: content,
      indication_operatoire: indicationOperatoire || "",
      zkf_redacteur: redacteur,
    };
    onUpdate(newOp);
  };

  const renderCroType = (cro: CROType) => (
    <Stack key={cro.id} direction={"row"} flex={1} gap={1} alignItems="center">
      <Edit
        cursor={"pointer"}
        onClick={() => {
          setSelectedCroType(cro);
          setOpenCroType(true);
        }}
      />
      <Stack
        flex={1}
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        gap={1}
        onClick={() => insertCROType(cro.id)}
        sx={{
          p: 0.5,
          borderRadius: "8px",
          "&:hover": {
            cursor: "pointer",
            backgroundColor: (theme) => theme.palette.background.level1,
          },
        }}
      >
        <Typography level="body-sm">{cro.cro_type_titre}</Typography>
        <ArrowRightAltIcon />
      </Stack>
    </Stack>
  );

  const beforePrint = async (): Promise<PrintData[]> => {
    if (
      operation.indication_operatoire !== indicationOperatoire ||
      operation.cro !== content ||
      redacteur !== operation.zkf_redacteur
    ) {
      await handleSave();
    }
    return [
      {
        type_document: "cro",
        id: operation.id,
        model_app_datas: operation.model_app_datas,
      },
    ];
  };

  return (
    <>
      {openEditDrawer && (
        <OperationEditDrawer
          open={openEditDrawer}
          onClose={() => setOpenEditDrawer(false)}
          operation={operation}
          onUpdate={onUpdate}
          onDelete={() => {}}
        />
      )}
      <Stack gap={2} sx={{ position: "relative", pb: 6 }}>
        <Stack direction="row" gap={1} justifyContent="space-between" alignItems="center">
          <Typography level="h4">Compte rendu opératoire</Typography>
          <Stack direction="row" gap={1} alignItems="center">
            <Button
              variant="plain"
              startDecorator={<Edit />}
              onClick={() => setOpenEditDrawer(true)}
            >
              Modifier les détails
            </Button>
            <Tooltip
              title={
                hasModification
                  ? "Impossible d'imprimer : vous devez enregistrer les modifications"
                  : "Imprimer le compte rendu opératoire"
              }
              placement="top"
              arrow
            >
              <span>
                <PrintButtons
                  files={[
                    {
                      type_document: "cro",
                      id: operation.id,
                      model_app_datas: operation.model_app_datas,
                    },
                  ]}
                  beforePrint={beforePrint}
                  withText
                  disabled={hasModification}
                />
              </span>
            </Tooltip>
            <Tooltip
              title={
                hasModification
                  ? "Enregistrer les modifications"
                  : "Aucune modification à enregistrer"
              }
              placement="top"
              arrow
            >
              <span>
                <Button
                  variant="solid"
                  color="primary"
                  onClick={handleSave}
                  startDecorator={<Save />}
                  disabled={!hasModification}
                >
                  Enregistrer
                </Button>
              </span>
            </Tooltip>
          </Stack>
        </Stack>

        <OperationViewSheet operation={operation} />

        <Sheet variant="outlined" sx={{ flex: 1, p: 2, borderRadius: "sm" }}>
          <Stack direction="row" gap={4}>
            <Stack direction="row" gap={2} sx={{ width: "300px" }}>
              <Stack direction="column" gap={2}>
                <Input
                  size="sm"
                  type="text"
                  placeholder="Rechercher un CRO type"
                  onChange={(e) => setSearch(e.target.value)}
                  startDecorator={<Search />}
                />
                <Stack direction="column" gap={1} overflow={"scroll"} maxHeight={"600px"}>
                  <Typography level="title-sm" color="primary" startDecorator={<AutoAwesome />}>
                    Suggestions
                  </Typography>
                  <Stack direction="column" gap={0.5}>
                    {displayedCroList.filter((cro) => cro.suggested).map(renderCroType)}
                  </Stack>
                  <Divider />
                  <Typography level="title-sm" color="primary">
                    Tous les CRO types
                  </Typography>
                  <Stack direction="column" gap={0.5}>
                    {displayedCroList.filter((cro) => !cro.suggested).map(renderCroType)}
                  </Stack>
                </Stack>
                <Button
                  startDecorator={<Add />}
                  variant="soft"
                  onClick={() => {
                    setSelectedCroType({
                      cro_type_indication_operatoire: indicationOperatoire || "",
                      cro_type: content,
                    });
                    setOpenCroType(true);
                  }}
                  sx={{ width: "100%" }}
                >
                  Créer un CRO type
                </Button>
              </Stack>
            </Stack>
            <Stack flex={1} gap={2}>
              <Stack gap={2}>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Typography level="title-md">Édition du contenu</Typography>
                  <Button
                    variant="soft"
                    color="neutral"
                    size="sm"
                    startDecorator={<Add />}
                    onClick={() => setOpenLastConsultModal(true)}
                  >
                    Dernière consultation
                  </Button>
                </Stack>
                <MultiRootEditor
                  indicationData={indicationOperatoire || ""}
                  croData={content || ""}
                  onIndicationChange={setIndicationOperatoire}
                  onCroChange={setContent}
                  indicationHeight="150px"
                  croHeight="450px"
                />
                <Stack direction="row" justifyContent="flex-end" alignItems="flex-end" gap={2}>
                  <FormControl>
                    <FormLabel>Rédacteur</FormLabel>
                    <DoctorSelect
                      value={redacteur}
                      onSelect={(value) => {
                        if (value) {
                          setRedacteur(value as number);
                        }
                      }}
                      style={{ width: "250px" }}
                    />
                  </FormControl>
                  <Tooltip
                    title={
                      hasModification
                        ? "Enregistrer les modifications"
                        : "Aucune modification à enregistrer"
                    }
                    placement="top"
                    arrow
                  >
                    <Button
                      startDecorator={<Save />}
                      color="primary"
                      onClick={handleSave}
                      disabled={!hasModification}
                    >
                      Enregistrer
                    </Button>
                  </Tooltip>
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        </Sheet>

        <Sheet variant="outlined" sx={{ p: 2, borderRadius: "sm" }}>
          <Stack gap={2}>
            <OperationCROPanelScopie operation={operation} />
          </Stack>
        </Sheet>

        {openLastConsultModal && (
          <LastConsultationModalTextInsert
            open={openLastConsultModal}
            initialText={indicationOperatoire || ""}
            onValidate={(value) => {
              value && setIndicationOperatoire(value);
              setOpenLastConsultModal(false);
            }}
            onClose={() => setOpenLastConsultModal(false)}
            operationId={operation.id}
            patientId={
              isPatient(operation.zkf_patient) ? operation.zkf_patient.id : operation.zkf_patient
            }
          />
        )}
        {openCroType && (
          <CroTypeModal
            open={openCroType}
            onClose={(newCroType) => {
              newCroType &&
                setCroTypes((prevState) =>
                  selectedCroType?.id
                    ? prevState.map((c) => (c.id === newCroType.id ? newCroType : c))
                    : [...prevState, newCroType]
                );
              setSelectedCroType(undefined);
              setOpenCroType(false);
            }}
            croType={selectedCroType}
          />
        )}
      </Stack>
    </>
  );
}
