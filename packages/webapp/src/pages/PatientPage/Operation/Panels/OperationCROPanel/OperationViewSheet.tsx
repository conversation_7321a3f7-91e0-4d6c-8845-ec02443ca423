import dayjs from "dayjs";
import React from "react";

import {
  AccessTime,
  Comment,
  LocalHospital,
  Masks,
  MedicalInformation,
  MedicalServices,
  PeopleAlt,
  Timer,
  Warning,
} from "@mui/icons-material";
import { Chip, FormControl, FormLabel, Sheet, Stack, Typography } from "@mui/joy";

import { useApi } from "../../../../../contexts/ApiContext";
import { isIntervenant } from "../../../../../models/type_guards";
import { Intervenant, Operation } from "../../../../../models/types";

function IntervenantList({ intervenants }: { intervenants: Intervenant[] }) {
  return intervenants.length > 0 ? (
    <Stack direction="row" gap={1}>
      {intervenants.map((intervenant) => (
        <Chip key={intervenant.id} variant="soft" color="primary" size="sm">
          {intervenant.titre} {intervenant.nom} {intervenant.prenom}
        </Chip>
      ))}
    </Stack>
  ) : (
    <Typography level="body-xs">Non renseigné.</Typography>
  );
}

interface OperationHeaderViewProps {
  operation: Operation;
}

export default function OperationViewSheet({ operation }: OperationHeaderViewProps) {
  const api = useApi();

  const [deepOperation, setDeepOperation] = React.useState<Operation>(operation);

  React.useEffect(() => {
    const fetchDeepOperation = async () => {
      try {
        // getting deep operation to get all the data for operateur, anesthesiste, aides_operatoire
        const res = await api.getOperation(operation.id, 1);
        setDeepOperation(res);
      } catch (error) {
        console.error(error);
      }
    };
    fetchDeepOperation();
  }, [operation, api]);

  function getOperationDuration(operation: Operation): string {
    if (!operation.operation_start || !operation.operation_end) return "";
    const start = dayjs(operation.operation_start);
    const end = dayjs(operation.operation_end);
    // could be 25 minutes or 2 hours and 25 minutes
    const duration = end.diff(start, "minutes");
    if (duration < 60) {
      return `${duration} minutes`;
    } else {
      return `${Math.floor(duration / 60)}h${duration % 60 > 0 ? ` ${duration % 60}min` : ""}`;
    }
  }

  return (
    <Stack direction="row" gap={2}>
      <Stack gap={2} flex={1}>
        <Sheet variant="outlined" sx={{ p: 2, flex: 1 }}>
          <Stack gap={1}>
            <Typography level="title-md">Intervenants</Typography>
            <Stack direction="row" gap={2} alignItems="flex-start" sx={{ flex: 1 }}>
              <FormControl sx={{ flex: 1 }}>
                <FormLabel>
                  <Masks />
                  Opérateur
                </FormLabel>
                <IntervenantList
                  intervenants={
                    deepOperation.operateur_new && isIntervenant(deepOperation.operateur_new[0])
                      ? (deepOperation.operateur_new as unknown as Intervenant[])
                      : []
                  }
                />
              </FormControl>
              <FormControl sx={{ flex: 1 }}>
                <FormLabel>
                  <MedicalServices />
                  Anesthésiste
                </FormLabel>
                <IntervenantList
                  intervenants={
                    deepOperation.anesthesiste_new &&
                    isIntervenant(deepOperation.anesthesiste_new[0])
                      ? (deepOperation.anesthesiste_new as unknown as Intervenant[])
                      : []
                  }
                />
              </FormControl>
              <FormControl sx={{ flex: 1 }}>
                <FormLabel>
                  <PeopleAlt />
                  Aide Opératoire
                </FormLabel>
                <IntervenantList
                  intervenants={
                    deepOperation.aides_operatoire &&
                    isIntervenant(deepOperation.aides_operatoire[0])
                      ? (deepOperation.aides_operatoire as unknown as Intervenant[])
                      : []
                  }
                />
              </FormControl>
            </Stack>
          </Stack>
        </Sheet>

        <Sheet variant="outlined" sx={{ p: 2, flex: 1 }}>
          <Stack gap={1}>
            <Typography level="title-md">Informations</Typography>
            <Stack direction="row" gap={2} alignItems="flex-start">
              <Stack direction="row" gap={2} sx={{ flex: 1 }}>
                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>
                    <AccessTime />
                    Début
                  </FormLabel>
                  <Typography level="body-md">
                    {operation.operation_start
                      ? dayjs(operation.operation_start).format("HH:mm")
                      : ""}
                  </Typography>
                </FormControl>

                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>
                    <AccessTime />
                    Fin
                  </FormLabel>
                  <Typography level="body-md">
                    {operation.operation_end ? dayjs(operation.operation_end).format("HH:mm") : ""}
                  </Typography>
                </FormControl>

                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>
                    <Timer />
                    Durée
                  </FormLabel>
                  <Typography level="body-md">{getOperationDuration(operation)}</Typography>
                </FormControl>
              </Stack>
              <Stack direction="row" gap={2} sx={{ flex: 1 }}>
                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>
                    <MedicalInformation />
                    Mode d'anesthésie
                  </FormLabel>
                  {operation.anesthesie ? (
                    <Chip variant="soft" color="neutral">
                      {operation.anesthesie}
                    </Chip>
                  ) : (
                    <Typography level="body-xs">Non renseigné.</Typography>
                  )}
                </FormControl>

                <FormControl sx={{ flex: 1 }}>
                  <FormLabel>
                    <LocalHospital />
                    Mode d'entrée
                  </FormLabel>
                  {operation.urgence ? (
                    <Chip
                      variant="soft"
                      color={operation.urgence === "urgence" ? "warning" : "neutral"}
                      startDecorator={operation.urgence === "urgence" && <Warning />}
                    >
                      {operation.urgence === "urgence" ? "Urgence" : "Programmé"}
                    </Chip>
                  ) : (
                    <Typography level="body-xs">Non renseigné.</Typography>
                  )}
                </FormControl>
              </Stack>
            </Stack>
          </Stack>
        </Sheet>
      </Stack>
      <Sheet variant="outlined" sx={{ p: 2, width: "300px" }}>
        <Stack gap={1}>
          <Typography level="title-md" startDecorator={<Comment />}>
            Commentaires
          </Typography>
          {operation.commentaire_operation ? (
            <Typography level="body-sm">{operation.commentaire_operation}</Typography>
          ) : (
            <Typography level="body-sm" sx={{ opacity: 0.5 }}>
              Pas de commentaire.
            </Typography>
          )}
        </Stack>
      </Sheet>
    </Stack>
  );
}
