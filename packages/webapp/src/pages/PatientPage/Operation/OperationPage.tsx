import dayjs from "dayjs";
import React, { useCallback } from "react";
import { useParams } from "react-router-dom";
import { Link } from "react-router-dom";

import { ArrowBack, Masks, StickyNote2, Tag, Warning, WarningAmber } from "@mui/icons-material";
import EditIcon from "@mui/icons-material/Edit";
import HistoryEduIcon from "@mui/icons-material/HistoryEdu";
import { Sheet } from "@mui/joy";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import Stack from "@mui/joy/Stack";
import Tab from "@mui/joy/Tab";
import TabList from "@mui/joy/TabList";
import TabPanel from "@mui/joy/TabPanel";
import Tabs from "@mui/joy/Tabs";
import Typography from "@mui/joy/Typography";
import { CalendarIcon, ClockIcon } from "@mui/x-date-pickers";

import AuditLogModal from "../../../components/AuditLog/AuditLogModal";
import DeleteConfirmationModal from "../../../components/Modals/DeleteConfirmationModal";
import PrintButtons from "../../../components/PrintManager/PrintButtons";
import { useApi } from "../../../contexts/ApiContext";
import { useCurrentPatient } from "../../../contexts/CurrentPatientContext";
import { useSnackbar } from "../../../contexts/SnackbarContext";
// Removed unused imports from type_guards
import { AuditLog, Consultation, Operation, Patient } from "../../../models/types";
import { router } from "../../../router";
import { formatDate } from "../../../utils/utils";
import OperationEditDrawer from "./OperationEditDrawer";
import OperationCCAMPanel from "./Panels/OperationCCAMPanel/OperationCCAMPanel";
import OperationCROPanel from "./Panels/OperationCROPanel/OperationCROPanel";
import OperationDocumentsPanel from "./Panels/OperationDocumentsPanel";
import OperationPerOpPanel from "./Panels/OperationPerOpPanel";
import OperationPostOpPanel from "./Panels/OperationPostOpPanel";
import OperationPreOpPanel from "./Panels/OperationPreOpPanel";
import OperationQuotesPanel from "./Panels/OperationQuotesPanel";

export default function OperationPage() {
  const { operation_id } = useParams<{ operation_id: string }>();
  const api = useApi();
  const { show } = useSnackbar();
  const { setCurrentPatient } = useCurrentPatient();

  const [operation, setOperation] = React.useState<Operation | null>(null);
  const [patient, setPatient] = React.useState<Patient | null>(null);
  const [consultPreOp, setConsultPreOp] = React.useState<Consultation | null>(null);
  const [history, setHistory] = React.useState<AuditLog[]>([]);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [deleteModalOpen, setDeleteModalOpen] = React.useState<boolean>(false);
  const [openModal, setOpenModal] = React.useState(false);

  // État pour suivre l'onglet actif, valeur par défaut récupérée du localStorage ou 0
  const [activeTab, setActiveTab] = React.useState<number>(() => {
    const savedTab = localStorage.getItem(`operation_tab_${operation_id}`);
    return savedTab ? parseInt(savedTab, 10) : 0;
  });

  const fetch = useCallback(async () => {
    if (api && operation_id) {
      api.getOperation(operation_id).then((res) => {
        setOperation(res);
        api.getPatient(res.zkf_patient).then((res) => {
          setPatient(res);
          setCurrentPatient(res);
        });
        if (res.zkf_consultation_pre_op) {
          api.getConsultation(res.zkf_consultation_pre_op).then((res) => setConsultPreOp(res));
        }
      });
    }
  }, [api, operation_id, setCurrentPatient]);

  React.useEffect(() => {
    fetch();
  }, [fetch]);

  if (!operation || !patient) return null;

  const handleUpdate = (updatedOperation: Partial<Operation>) => {
    api
      .updateOperation({
        id: operation.id,
        ...updatedOperation,
      })
      .then(async () => {
        await fetch();
        setDrawerOpen(false);
        show("Modifications enregistrées", "success");
      })
      .catch(() => show("Erreur lors de la modification", "danger"));
  };

  const handleDelete = () => {
    api
      .updateOperation({
        id: operation.id,
        deleted: true,
      })
      .then(async () => {
        router.navigate(`/hospitalisation/${operation.zkf_hospitalisation}`);
      })
      .catch(() => show("Erreur lors de la suppression de l'opération", "danger"));
  };

  const handleHistory = () => {
    api
      .getAuditLog(operation.id, "operation.operation", null, 1)
      .then((res) => {
        setOpenModal(true); // This is what actually opens the modal
        setHistory(res.results);
      })
      .catch(() => show("Erreur lors de la récupération de l'historique", "danger"));
  };

  return (
    <>
      <AuditLogModal open={openModal} onClose={() => setOpenModal(false)} history={history} />

      <OperationEditDrawer
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        operation={operation}
        onUpdate={handleUpdate}
        onDelete={() => setDeleteModalOpen(true)}
      />

      <Stack gap={1}>
        <Stack direction="row" gap={1} justifyContent="space-between">
          <Link to={`/hospitalisation/${operation.zkf_hospitalisation}`}>
            <Button variant="plain" color="neutral" size="sm">
              <ArrowBack /> Retour à la hospitalisation
            </Button>
          </Link>
          <Stack direction="row" gap={1} textAlign="right" alignItems="center">
            {consultPreOp && (
              <Typography level="body-xs">
                {dayjs(consultPreOp.consultation_date).diff(
                  dayjs(operation?.operation_date),
                  "days"
                )}{" "}
                jours après la consultation pré-opératoire.
              </Typography>
            )}
            <Stack direction="row" gap={1} justifyContent="end">
              <Stack direction="row" gap={1} justifyContent="end">
                <Button
                  color="neutral"
                  startDecorator={<HistoryEduIcon fontSize="small" />}
                  variant="plain"
                  onClick={handleHistory}
                >
                  Logs
                </Button>
                <Button
                  variant="plain"
                  color="neutral"
                  onClick={() => setDrawerOpen(true)}
                  startDecorator={<EditIcon />}
                >
                  Modifier l'opération
                </Button>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
        <Stack
          sx={{
            p: 2,
            pt: 0,
            mx: -2,
          }}
          direction="row"
          alignItems="flex-end"
          gap={2}
        >
          <Stack flex={1} direction="column" gap={0.5}>
            <Stack direction="row" gap={0.5}>
              <Chip color="success" startDecorator={<Masks fontSize="small" />}>
                Opération
              </Chip>
              {operation?.complication && (
                <Chip color="danger" startDecorator={<Warning fontSize="small" />}>
                  Complication
                </Chip>
              )}
              {operation?.urgence === "urgence" && (
                <Chip color="warning" startDecorator={<WarningAmber fontSize="small" />}>
                  Urgence
                </Chip>
              )}
            </Stack>
            <Stack direction="row" alignItems="flex-start" flex={1} gap={2}>
              <Typography
                level="title-lg"
                color="success"
                flex={1}
                endDecorator={
                  operation?.consentement_pre_operatoire_recupere ? null : (
                    <Chip
                      color="warning"
                      size="sm"
                      startDecorator={<HistoryEduIcon fontSize="small" />}
                    >
                      Consentement pré-opératoire non récupéré
                    </Chip>
                  )
                }
              >
                {operation?.titre_operation}
              </Typography>
            </Stack>
            <Stack direction="row" gap={0.5}>
              {operation.tags?.map((tag) => (
                <Chip key={tag} variant="soft" color="success" size="sm" startDecorator={<Tag />}>
                  {tag}
                </Chip>
              ))}
            </Stack>
            <Stack direction="row" gap={2}>
              <Typography level="body-md" startDecorator={<CalendarIcon fontSize="small" />}>
                {formatDate(operation?.operation_date)}
              </Typography>
              <Typography level="body-md" startDecorator={<ClockIcon fontSize="small" />}>
                {dayjs(operation?.operation_start).format("HH:mm")} —{" "}
                {dayjs(operation?.operation_end).format("HH:mm")}
              </Typography>
            </Stack>
          </Stack>
          <Sheet variant="outlined" sx={{ p: 1 }}>
            <Stack direction="row" gap={2} alignItems="center" justifyContent="space-between">
              <Typography level="body-sm" startDecorator={<StickyNote2 />}>
                Convocation
              </Typography>
              <PrintButtons
                size="sm"
                files={[
                  {
                    id: operation.id,
                    model_app_datas: operation.model_app_datas,
                    type_document: "convocation_operation",
                  },
                ]}
              />
            </Stack>
          </Sheet>
        </Stack>

        <Tabs
          sx={{ mx: -2, minHeight: "80vh", background: "white" }}
          value={activeTab}
          onChange={(event, newValue) => {
            if (newValue !== null) {
              setActiveTab(newValue as number);
              // Sauvegarder l'onglet actif dans le localStorage
              localStorage.setItem(`operation_tab_${operation_id}`, String(newValue));
            }
          }}
        >
          <TabList
            sx={{
              // min width for tab is 150px
              "& > button": {
                minWidth: "120px",
              },
            }}
          >
            <Tab variant="plain" color="neutral" value={0}>
              Peri op
            </Tab>
            {/*<Tab variant="plain" color="neutral" value={1}>*/}
            {/*  Per op*/}
            {/*</Tab>*/}
            {/*<Tab variant="plain" color="neutral" value={2}>*/}
            {/*  DMI*/}
            {/*</Tab>*/}
            <Tab variant="plain" color="neutral" value={3}>
              CRO
            </Tab>
            <Tab variant="plain" color="neutral" value={4}>
              Codage CCAM
            </Tab>
            <Tab variant="plain" color="neutral" value={5}>
              Post op
            </Tab>
            <Tab variant="plain" color="neutral" value={6}>
              Comptabilité
            </Tab>
            <Tab variant="plain" color="neutral" value={7}>
              Documents
            </Tab>
          </TabList>
          <TabPanel value={0}>
            <OperationPreOpPanel operation={operation} />
          </TabPanel>
          <TabPanel sx={{ background: "white" }} value={1}>
            <OperationPerOpPanel operation={operation!} />
          </TabPanel>
          <TabPanel value={2}>DMI</TabPanel>
          <TabPanel value={3}>
            <OperationCROPanel operation={operation} onUpdate={handleUpdate} />
          </TabPanel>
          <TabPanel value={4}>
            <OperationCCAMPanel operation={operation} />
          </TabPanel>
          <TabPanel value={5}>
            <OperationPostOpPanel operation={operation} />
          </TabPanel>
          <TabPanel value={6}>
            <OperationQuotesPanel operation={operation} />
          </TabPanel>
          <TabPanel value={7}>
            <OperationDocumentsPanel operation={operation} />
          </TabPanel>
        </Tabs>
        <DeleteConfirmationModal
          open={deleteModalOpen}
          onClose={() => setDeleteModalOpen(false)}
          onConfirm={handleDelete}
          title="Supprimer l'opération"
          message="Êtes-vous sûr de vouloir supprimer cette opération ?"
        />
      </Stack>
    </>
  );
}
