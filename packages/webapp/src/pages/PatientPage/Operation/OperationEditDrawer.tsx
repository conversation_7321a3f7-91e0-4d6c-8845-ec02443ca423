import dayjs from "dayjs";
import React from "react";

import SaveIcon from "@mui/icons-material/Save";
import WarningIcon from "@mui/icons-material/Warning";
import Autocomplete from "@mui/joy/Autocomplete";
import Button from "@mui/joy/Button";
import Checkbox from "@mui/joy/Checkbox";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import Input from "@mui/joy/Input";
import Stack from "@mui/joy/Stack";
import Textarea from "@mui/joy/Textarea";
import Typography from "@mui/joy/Typography";
import { CalendarIcon } from "@mui/x-date-pickers";

import CustomDrawer from "../../../components/CustomDrawer";
import ProtocoleSelect from "../../../components/Forms/ProtocoleSelect";
import TagAutocomplete from "../../../components/Forms/TagAutocomplete";
import TimeInput from "../../../components/Forms/TimeInput";
import { useApi } from "../../../contexts/ApiContext";
import { Intervenant, Operation } from "../../../models/types";

interface OperationEditDrawerProps {
  open: boolean;
  onClose: () => void;
  operation: Operation;
  onUpdate: (updatedOperation: Partial<Operation>) => void;
  onDelete: () => void;
}

export default function OperationEditDrawer({
  open,
  onClose,
  operation,
  onUpdate,
  onDelete,
}: OperationEditDrawerProps) {
  const [titre, setTitre] = React.useState<string>(operation.titre_operation || "");
  const [date, setDate] = React.useState<string>(operation.operation_date || "");
  const [debut, setDebut] = React.useState<string>(
    dayjs(operation?.operation_start).format("HH:mm") || ""
  );
  const [fin, setFin] = React.useState<string>(
    dayjs(operation?.operation_end).format("HH:mm") || ""
  );
  const [operateurs, setOperateurs] = React.useState<string[]>(operation.operateur_new || []);
  const [anesthesistes, setAnesthesistes] = React.useState<string[]>(
    operation.anesthesiste_new || []
  );
  const [aidesOperatoire, setAidesOperatoire] = React.useState<string[]>(
    operation.aides_operatoire || []
  );

  // États pour les intervenants
  const [intervenants, setIntervenants] = React.useState<{
    Operateur: Intervenant[];
    Anesthesiste: Intervenant[];
    "Aide Operatoire": Intervenant[];
  }>({ Operateur: [], Anesthesiste: [], "Aide Operatoire": [] });

  const [selectedOperateurs, setSelectedOperateurs] = React.useState<Intervenant[]>([]);
  const [selectedAnesthesistes, setSelectedAnesthesistes] = React.useState<Intervenant[]>([]);
  const [selectedAidesOperatoire, setSelectedAidesOperatoire] = React.useState<Intervenant[]>([]);

  // API
  const api = useApi();
  const [tags, setTags] = React.useState<string[]>(operation.tags || []);
  const [protocoles, setProtocoles] = React.useState<string[]>(operation.protocoles || []);
  const [commentaires, setCommentaires] = React.useState<string>(
    operation.commentaire_operation || ""
  );
  const [modeAnesthesie, setModeAnesthesie] = React.useState<string>(operation.anesthesie || "");
  const [anesthesieOptions, setAnesthesieOptions] = React.useState<Array<[string, number]>>([]);
  const [urgence, setUrgence] = React.useState<boolean>(operation.urgence === "urgence");

  // Charger les intervenants depuis l'API
  React.useEffect(() => {
    const loadIntervenants = async () => {
      try {
        console.log(`Fetching intervenants for operation ID: ${operation.id}`);
        const intervenantsResult = await api.getUserIntervenantsEvents("", "", operation.id);
        console.log("Intervenants loaded in drawer:", intervenantsResult);

        // Mise à jour des états avec les données reçues
        setIntervenants({
          Operateur: intervenantsResult.Operateur || [],
          Anesthesiste: intervenantsResult.Anesthesiste || [],
          "Aide Operatoire": intervenantsResult["Aide Operatoire"] || [],
        });
      } catch (error) {
        console.error("Error loading intervenants:", error);
      }
    };

    if (open) {
      loadIntervenants();
    }
  }, [api, operation.id, open]);

  // Mettre à jour les sélections basées sur les IDs lorsque les intervenants sont chargés
  React.useEffect(() => {
    if (intervenants.Operateur.length > 0 && operateurs.length > 0) {
      setSelectedOperateurs(intervenants.Operateur.filter((o) => operateurs.includes(o.id)));
    }

    if (intervenants.Anesthesiste.length > 0 && anesthesistes.length > 0) {
      setSelectedAnesthesistes(
        intervenants.Anesthesiste.filter((o) => anesthesistes.includes(o.id))
      );
    }

    if (intervenants["Aide Operatoire"].length > 0 && aidesOperatoire.length > 0) {
      setSelectedAidesOperatoire(
        intervenants["Aide Operatoire"].filter((o) => aidesOperatoire.includes(o.id))
      );
    }
  }, [intervenants, operateurs, anesthesistes, aidesOperatoire]);

  // Chargement des options d'anesthésie
  React.useEffect(() => {
    const loadAnesthesieOptions = async () => {
      try {
        const options = await api.getOperationFieldValues("anesthesie");
        if (Array.isArray(options)) {
          // Les options sont retournées sous forme de tuples [texte, nombre d'occurrences]
          const formattedOptions = options
            .map((option): [string, number] => {
              if (Array.isArray(option) && option.length === 2) {
                return [option[0], parseInt(option[1])];
              }
              // Si le format n'est pas celui attendu, on retourne une option avec 0 occurrences
              return [String(option), 0];
            })
            // Tri par nombre d'occurrences décroissant
            .sort((a, b) => b[1] - a[1]);

          setAnesthesieOptions(formattedOptions);
          console.log("Options d'anesthésie chargées (drawer):", formattedOptions);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des options d'anesthésie (drawer):", error);
      }
    };

    if (open) {
      loadAnesthesieOptions();
    }
  }, [api, open]);

  React.useEffect(() => {
    // Reset form fields when drawer opens or operation data changes
    setTitre(operation.titre_operation || "");
    setDate(operation.operation_date || "");
    setDebut(dayjs(operation?.operation_start).format("HH:mm") || "");
    setFin(dayjs(operation?.operation_end).format("HH:mm") || "");
    setOperateurs(operation.operateur_new || []);
    setAnesthesistes(operation.anesthesiste_new || []);
    setAidesOperatoire(operation.aides_operatoire || []);
    setTags(operation.tags || []);
    setProtocoles(operation.protocoles || []);
    setCommentaires(operation.commentaire_operation || "");
    setModeAnesthesie(operation.anesthesie || "");
    setUrgence(operation.urgence === "urgence");
  }, [
    open,
    operation.titre_operation,
    operation.operation_date,
    operation.operation_start,
    operation.operation_end,
    operation.operateur_new,
    operation.anesthesiste_new,
    operation.aides_operatoire,
    operation.tags,
    operation.protocoles,
    operation.commentaire_operation,
    operation.anesthesie,
    operation.urgence,
  ]);

  const handleUpdate = () => {
    const dateFinOpe = debut < fin ? date : dayjs(date).add(1, "day").format("YYYY-MM-DD");

    // Log pour débogage
    console.log("Enregistrement des modifications:", {
      titre: titre,
      date: date,
      debut: debut,
      fin: fin,
      operateurs: operateurs,
      anesthesistes: anesthesistes,
      aidesOperatoire: aidesOperatoire,
      urgence: urgence ? "urgence" : "programme",
    });

    onUpdate({
      titre_operation: titre,
      operation_date: date,
      operation_start: dayjs(`${date} ${debut}`).format("YYYY-MM-DDTHH:mm:ss.SSSZ"),
      operation_end: dayjs(`${dateFinOpe} ${fin}`).format("YYYY-MM-DDTHH:mm:ss.SSSZ"),
      operateur_new: operateurs,
      anesthesiste_new: anesthesistes,
      aides_operatoire: aidesOperatoire,
      tags,
      protocoles,
      commentaire_operation: commentaires,
      anesthesie: modeAnesthesie,
      urgence: urgence ? ("urgence" as const) : ("programme" as const),
    });

    // Fermer le drawer après mise à jour
    onClose();
  };

  const actions = (
    <Stack direction="row" gap={2} justifyContent="space-between">
      <Stack direction="row">
        <Button variant="plain" color="warning" onClick={onDelete} startDecorator={<WarningIcon />}>
          Supprimer l'opération
        </Button>
      </Stack>
      <Stack direction="row" gap={2}>
        <Button variant="plain" color="neutral" onClick={onClose}>
          Annuler
        </Button>
        <Button
          variant="solid"
          color="primary"
          onClick={handleUpdate}
          startDecorator={<SaveIcon />}
        >
          Enregistrer
        </Button>
      </Stack>
    </Stack>
  );

  return (
    <CustomDrawer open={open} onClose={onClose} title="Modifier l'opération" actions={actions}>
      <Stack spacing={2}>
        <FormControl>
          <FormLabel>Titre de l'opération</FormLabel>
          <Textarea
            value={titre}
            onChange={(e) => setTitre(e.target.value)}
            placeholder="Titre de l'opération"
            minRows={1}
            maxRows={3}
          />
        </FormControl>

        {/* Disposition en deux colonnes pour la date et les horaires */}
        <Stack direction="row" spacing={2} sx={{ width: "100%" }}>
          <Stack spacing={0.5} sx={{ flex: 0.8 }}>
            <FormLabel>Date de l'opération</FormLabel>
            <Input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              startDecorator={<CalendarIcon />}
              size="sm"
              sx={{ height: "36px" }}
              fullWidth
            />

            <FormControl>
              <Typography
                level="title-sm"
                startDecorator={
                  <Checkbox
                    checked={urgence}
                    onChange={(e) => {
                      setUrgence(e.target.checked);
                    }}
                  />
                }
              >
                Urgence
              </Typography>
            </FormControl>
          </Stack>

          <Stack spacing={0.5} sx={{ flex: 1.2 }}>
            <Typography sx={{ width: "100%", textAlign: "center" }} level="body-sm">
              Horaires
            </Typography>
            <Stack direction="row" spacing={1}>
              <FormControl sx={{ flex: 1 }}>
                <TimeInput value={debut} onChange={(value) => setDebut(value)} sx={{ flex: 1 }} />
              </FormControl>
              <FormControl sx={{ flex: 1 }}>
                <TimeInput
                  value={fin}
                  onChange={(value) => setFin(value)}
                  sx={{ flex: 1 }}
                  showIncrementButtons
                />
              </FormControl>
            </Stack>
          </Stack>
        </Stack>

        <FormControl>
          <FormLabel>Opérateurs</FormLabel>
          <Autocomplete
            multiple
            size="sm"
            value={selectedOperateurs}
            onChange={(e, newValues) => {
              setSelectedOperateurs(newValues);
              setOperateurs(newValues.map((op) => op.id));
            }}
            options={intervenants.Operateur || []}
            getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            filterSelectedOptions
            clearOnBlur={false}
            autoHighlight
            openOnFocus
            noOptionsText="Aucun opérateur disponible"
            loadingText="Chargement..."
          />
        </FormControl>

        <FormControl>
          <FormLabel>Anesthésistes</FormLabel>
          <Autocomplete
            multiple
            size="sm"
            value={selectedAnesthesistes}
            onChange={(e, newValues) => {
              setSelectedAnesthesistes(newValues);
              setAnesthesistes(newValues.map((an) => an.id));
            }}
            options={intervenants.Anesthesiste || []}
            getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            filterSelectedOptions
            clearOnBlur={false}
            autoHighlight
            openOnFocus
            noOptionsText="Aucun anesthésiste disponible"
            loadingText="Chargement..."
          />
        </FormControl>

        <FormControl>
          <FormLabel>Aides Opératoires</FormLabel>
          <Autocomplete
            multiple
            size="sm"
            value={selectedAidesOperatoire}
            onChange={(e, newValues) => {
              setSelectedAidesOperatoire(newValues);
              setAidesOperatoire(newValues.map((aide) => aide.id));
            }}
            options={intervenants["Aide Operatoire"] || []}
            getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            filterSelectedOptions
            clearOnBlur={false}
            autoHighlight
            openOnFocus
            noOptionsText="Aucun aide opératoire disponible"
            loadingText="Chargement..."
          />
        </FormControl>

        <FormControl>
          <FormLabel>Mode d'anesthésie</FormLabel>
          <Autocomplete
            freeSolo
            clearOnBlur={false}
            autoHighlight
            openOnFocus
            inputValue={modeAnesthesie}
            onInputChange={(event, newValue) => setModeAnesthesie(newValue || "")}
            options={
              anesthesieOptions
                .slice(0, 5) // Limiter aux 5 premières options (les plus fréquentes)
                .map((option) => option[0]) // Extraire le texte du tuple
            }
            renderOption={(props, option) => {
              // Trouver l'option correspondante pour afficher le nombre d'occurrences
              const fullOption = anesthesieOptions.find(([text]) => text === option);
              const occurrences = fullOption ? fullOption[1] : 0;

              return (
                <li {...props}>
                  <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{ width: "100%" }}
                  >
                    <Typography level="body-md">{option}</Typography>
                    <Typography
                      level="body-xs"
                      sx={{
                        color: "neutral.500",
                        bgcolor: "neutral.100",
                        px: 1,
                        py: 0.5,
                        borderRadius: "4px",
                        fontWeight: "600",
                      }}
                    >
                      {occurrences}
                    </Typography>
                  </Stack>
                </li>
              );
            }}
            filterOptions={(options, state) => {
              // Filtre personnalisé pour prendre en compte le texte saisi
              const inputValue = state.inputValue.toLowerCase();
              if (inputValue.length === 0) {
                return options; // Afficher toutes les options si aucun texte saisi
              }
              return options.filter((option) => option.toLowerCase().includes(inputValue));
            }}
            size="sm"
            placeholder="Mode d'anesthésie"
            slotProps={{
              input: {
                sx: { fontSize: "0.9rem" },
              },
              listbox: {
                sx: {
                  maxHeight: "200px",
                  boxShadow: "md",
                },
              },
            }}
            sx={{
              width: "100%",
              "& .MuiAutocomplete-endDecorator": { top: "unset" },
            }}
          />
        </FormControl>

        <FormControl>
          <FormLabel>Tags</FormLabel>
          <TagAutocomplete value={tags} onChange={setTags} />
        </FormControl>

        <FormControl>
          <FormLabel>Protocoles</FormLabel>
          <ProtocoleSelect value={protocoles} onChange={setProtocoles} />
        </FormControl>

        <FormControl>
          <FormLabel>Commentaires</FormLabel>
          <Textarea
            value={commentaires}
            onChange={(e) => setCommentaires(e.target.value)}
            minRows={2}
            maxRows={5}
          />
        </FormControl>
      </Stack>
    </CustomDrawer>
  );
}
