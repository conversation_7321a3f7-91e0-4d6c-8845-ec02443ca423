import React from "react";

import Add from "@mui/icons-material/Add";
import People from "@mui/icons-material/People";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import CircularProgress from "@mui/joy/CircularProgress";
import Skeleton from "@mui/joy/Skeleton";
import Stack from "@mui/joy/Stack";
import Typography from "@mui/joy/Typography";

import ConsultationCard from "../../components/ConsultationCard";
import { useApi } from "../../contexts/ApiContext";
import { Consultation } from "../../models/types";
import CreateSlotModal from "../Calendar/CreateSlotDrawer";

export default function ListOfConsultations({ patientId }: { patientId: string }) {
  const api = useApi();

  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [consultations, setConsultations] = React.useState<Consultation[]>([]);
  const [page, setPage] = React.useState(1);
  const [hasMore, setHasMore] = React.useState(false);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);

  const fetchConsultations = React.useCallback(async () => {
    try {
      setIsLoadingMore(true);
      const query = await api.getLastConsultations(patientId, undefined, undefined, page);
      const newConsultations = query.results;
      setHasMore(query.next !== null);
      // if page is more than 1, add the consultations to the existing consultations
      if (page > 1) {
        setConsultations((prevConsultations) => [...prevConsultations, ...newConsultations]);
      } else {
        setConsultations(newConsultations);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoadingMore(false);
      setLoading(false);
    }
  }, [api, patientId, page]);

  React.useEffect(() => {
    fetchConsultations();
  }, [fetchConsultations, page]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleOnSave = () => {
    handleCloseModal();
  };

  const handleLoadMore = () => {
    setPage(page + 1);
  };

  consultations.sort(
    (a, b) => new Date(b.consultation_date!).getTime() - new Date(a.consultation_date!).getTime()
  );

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography level="title-lg" startDecorator={<People />} color="primary">
          Consultations
        </Typography>
        <Button
          data-cy="consultation-create-button"
          variant="plain"
          startDecorator={<Add />}
          color="primary"
          size="sm"
          onClick={handleOpenModal}
        >
          Créer
        </Button>
      </Stack>
      {loading ? (
        <Stack sx={{ mt: 2 }} direction="column" gap={3}>
          {Array.from({ length: 6 }).map((_, index) => (
            <Box key={index} sx={{ width: "100%", height: "92px", position: "relative" }}>
              <Skeleton key={index} width="100%" height="92px" />
            </Box>
          ))}
        </Stack>
      ) : (
        <Stack sx={{ mt: 2 }} direction="column" gap={3}>
          {consultations && consultations.length > 0 ? (
            consultations.map((consult: Consultation) => (
              <ConsultationCard key={consult.id} consult={consult} />
            ))
          ) : (
            <Stack sx={{ height: "105px" }} alignItems="center" justifyContent="center">
              <Typography level="body-md" color="neutral">
                Aucune consultation
              </Typography>
            </Stack>
          )}
          {hasMore && (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 1 }}>
              <Button
                variant="outlined"
                color="primary"
                size="sm"
                onClick={handleLoadMore}
                data-cy="load-more-consultations"
                disabled={isLoadingMore}
                sx={{ width: 200 }}
              >
                {isLoadingMore ? <CircularProgress size="sm" /> : "Charger plus"}
              </Button>
            </Box>
          )}
        </Stack>
      )}
      {isModalOpen && (
        <CreateSlotModal
          onSave={handleOnSave}
          open={isModalOpen}
          onClose={handleCloseModal}
          forcedType="consultation"
          forcedCurrentPatient
        />
      )}
    </Box>
  );
}
