import React from "react";
import { useParams } from "react-router-dom";

import Edit from "@mui/icons-material/Edit";
import People from "@mui/icons-material/People";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Stack from "@mui/joy/Stack";
import Tab from "@mui/joy/Tab";
import TabList from "@mui/joy/TabList";
import TabPanel from "@mui/joy/TabPanel";
import Tabs from "@mui/joy/Tabs";

import PatientContactListModal from "../../components/Contact/PatientContactListModal";
import LoadingPage from "../../components/LoadingPage";
import { useApi } from "../../contexts/ApiContext";
import { useCurrentPatient } from "../../contexts/CurrentPatientContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { Hospitalisation, Patient } from "../../models/types";
import { router } from "../../router";
import DocumentsPage from "../Documents/DocumentsPage";
import MedicalHistory from "./History/MedicalHistory";
import ListOfConsultations from "./ListOfConsultations";
import ListOfHospitalisations from "./ListOfHospitalisations";
import PatientHeader from "./PatientHeader";

// Debug Icons

export default function PatientPage() {
  const { id } = useParams();
  const api = useApi();
  const { setCurrentPatient } = useCurrentPatient();
  const snackbar = useSnackbar();

  const [p, setPatient] = React.useState<Patient>();
  const [hospitalisations, setHospitalisations] = React.useState<Hospitalisation[]>([]);
  const [openContacts, setOpenContacts] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState<number>(() => {
    // Restaurer l'onglet actif depuis le localStorage
    const savedTab = localStorage.getItem(`patient_page_activeTab_${id}`);
    return savedTab ? parseInt(savedTab, 10) : 0;
  });
  const [currentHospitalisations, setCurrentHospitalisations] = React.useState<Hospitalisation[]>(
    []
  );

  React.useEffect(() => {
    const fetchCurrentHospitalisation = async () => {
      try {
        if (!id) return;
        const res = await api.getCurrentPatientHospitalisation(id);
        setCurrentHospitalisations(res.results);
      } catch (e) {
        console.error(e);
        snackbar.show(
          "Erreur lors de la récupération des hospitalisations actives du patient",
          "danger"
        );
      }
    };
    fetchCurrentHospitalisation();
  }, [api, id, snackbar]);

  React.useEffect(() => {
    const fetch = async () => {
      if (id && api) {
        try {
          const res = await api.getPatient(id, 1);
          setPatient(res);
          setCurrentPatient(res);
        } catch (e) {
          snackbar.show("Erreur lors de la récupération du patient", "danger");
          console.error(e);
        }
      }
    };
    fetch();
  }, [id, api, setCurrentPatient, snackbar]);

  const fetchHospitalisations = React.useCallback(async () => {
    if (!id) return;
    try {
      const res = await api.listHospitalisations(id);
      setHospitalisations(res.results);
    } catch (e) {
      console.error(e);
    }
  }, [api, id]);

  React.useEffect(() => {
    fetchHospitalisations();
  }, [api, id, fetchHospitalisations]);

  return p ? (
    <Box>
      <Stack
        direction="row"
        sx={{
          justifyContent: "space-between",
          alignItems: "flex-start",
        }}
      >
        <PatientHeader patient={p} />
        <Stack direction="row" spacing={1} alignItems="center">
          <Button
            onClick={() => router.navigate(`/patient/${p.id}/edit`)}
            startDecorator={<Edit />}
            variant="plain"
            color="neutral"
          >
            Modifier
          </Button>
          <Button
            onClick={() => setOpenContacts(true)}
            data-cy="contacts-button"
            startDecorator={<People />}
          >
            Contacts
          </Button>
        </Stack>
      </Stack>
      <Tabs
        aria-label="Informations patient"
        value={activeTab}
        onChange={(event, newValue) => {
          setActiveTab(newValue as number);
          // Sauvegarder l'onglet actif dans le localStorage
          localStorage.setItem(`patient_page_activeTab_${id}`, String(newValue));
        }}
        sx={{ mx: -2, mt: 2 }}
      >
        <TabList>
          <Tab sx={{ px: 4 }}>Général</Tab>
          <Tab sx={{ px: 4 }}>Documents</Tab>
        </TabList>
        <TabPanel value={0} sx={{ p: 0 }}>
          <Stack sx={{ minHeight: "70vh" }} direction="row">
            <Box sx={{ flex: 1, p: 2 }}>
              <MedicalHistory patient={p} onCompteRenduClick={() => setActiveTab(1)} />
            </Box>
            <Box
              sx={{
                flex: 1,
                p: 2,
                borderLeft: (theme) => `1px solid ${theme.palette.divider}`,
              }}
            >
              {id && <ListOfConsultations patientId={id} />}
            </Box>
            <Box
              sx={{
                flex: 1,
                p: 2,
                borderLeft: (theme) => `1px solid ${theme.palette.divider}`,
              }}
            >
              <ListOfHospitalisations
                currentHospitalisations={currentHospitalisations}
                hospitalisations={hospitalisations || []}
                refresh={fetchHospitalisations}
              />
            </Box>
          </Stack>
        </TabPanel>
        <TabPanel value={1} sx={{ p: 2 }}>
          <DocumentsPage />
        </TabPanel>
      </Tabs>

      {openContacts && (
        <PatientContactListModal
          patient={p}
          open={openContacts}
          onClose={() => setOpenContacts(false)}
        />
      )}
    </Box>
  ) : (
    <LoadingPage />
  );
}
