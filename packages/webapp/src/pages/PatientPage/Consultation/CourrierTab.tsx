import React from "react";

import ArrowBack from "@mui/icons-material/ArrowBack";
import Edit from "@mui/icons-material/Edit";
import Save from "@mui/icons-material/Save";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Drawer from "@mui/joy/Drawer";
import ModalClose from "@mui/joy/ModalClose";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import Editor from "../../../components/Editor/Editor";
import PrintButtons from "../../../components/PrintManager/PrintButtons";
import AudioTranscriptionList from "../../../components/SpeechToText/AudioTranscriptionList";
import { useApi } from "../../../contexts/ApiContext";
import { useSnackbar } from "../../../contexts/SnackbarContext";
import {
  Consultation,
  ConsultationType,
  PatchedConsultation,
  StatutCourrierConsultationEnum,
} from "../../../models/types";
import SpeechToTextPage from "../../SpeechToText/SpeechToTextDrawer";

interface CourrierTabProps {
  consultation: Consultation;
  onSave?: (consultation?: Consultation) => void;
  withHeader?: boolean;
  isEditMode?: boolean;
  onCourrierChange?: (courrier: string, basPage: string) => void;
  dataTimestamp?: number;
}

export default function CourrierTab({
  consultation,
  onSave,
  withHeader = true,
  isEditMode = false,
  onCourrierChange,
  dataTimestamp,
}: CourrierTabProps) {
  const api = useApi();
  const snackbar = useSnackbar();

  const [edit, setEdit] = React.useState(isEditMode);
  const [courrier, setCourrier] = React.useState<string>(consultation.courrier_consultation || "");
  const [basPage, setBasPage] = React.useState<string>(
    consultation.courrier_consultation_bas_page || ""
  );
  const [editorRef, setEditorRef] = React.useState<any>(null);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [consultations, setConsultations] = React.useState<ConsultationType[]>([]);
  const [showDictationDrawer, setShowDictationDrawer] = React.useState<boolean>(false);
  const [audioListKey, setAudioListKey] = React.useState<number>(0);

  React.useEffect(() => {
    api.getConsultationType().then(setConsultations);
  }, [api]);

  React.useEffect(() => {
    setCourrier(consultation.courrier_consultation || "");
    setBasPage(consultation.courrier_consultation_bas_page || "");
  }, [consultation]);

  const insertTextToEditor = React.useCallback(
    async (tempplateId: string) => {
      setLoading(true);
      try {
        const result = await api.getCourrierConsultationByTemplate(consultation.id, tempplateId);

        if (!editorRef) return;

        const insertHtml = (html: string) => {
          editorRef.model.change(() => {
            const selection = editorRef.model.document.selection;
            // Ensure the HTML is a string before passing to toView
            const viewFragment = editorRef.data.processor.toView(html);
            const modelFragment = editorRef.data.toModel(viewFragment);
            editorRef.model.insertContent(modelFragment, selection);
            editorRef.focus();
          });
        };

        if (typeof result === "string") {
          insertHtml(result);
        } else if (typeof result === "object" && result !== null) {
          const res = result as { corps?: string; bas_de_page?: string; should_replace_all?: boolean };
          if (res.corps) {
            if (res.should_replace_all) {
              // Si should_replace_all est true, remplacer tout le contenu
              editorRef.model.change(() => {
                const root = editorRef.model.document.getRoot();
                const range = editorRef.model.createRangeIn(root);
                editorRef.model.deleteContent(editorRef.model.createSelection(range));
                
                const viewFragment = editorRef.data.processor.toView(res.corps);
                const modelFragment = editorRef.data.toModel(viewFragment);
                editorRef.model.insertContent(modelFragment);
                editorRef.focus();
              });
            } else {
              // Sinon, insérer à la position actuelle
              insertHtml(res.corps);
            }
          }
          if (res.bas_de_page) {
            const updatedBasPage = (basPage + "\n" + res.bas_de_page).trim();
            setBasPage(updatedBasPage);
            onCourrierChange?.(courrier, updatedBasPage);
          }
        }
      } finally {
        setLoading(false);
      }
    },
    [api, consultation.id, editorRef, basPage, courrier, onCourrierChange]
  );

  const handleInsertTranscription = React.useCallback(
    (text: string) => {
      if (editorRef && text) {
        editorRef.model.change(() => {
          const selection = editorRef.model.document.selection;
          // Ensure text is a string
          const safeText = typeof text === "string" ? text : String(text);
          const viewFragment = editorRef.data.processor.toView(safeText);
          const modelFragment = editorRef.data.toModel(viewFragment);
          editorRef.model.insertContent(modelFragment, selection);
          editorRef.focus();
        });
      }
    },
    [editorRef]
  );

  const templates = React.useMemo(
    () => (
      <Sheet
        variant="outlined"
        sx={{
          height: "100%",
          overflow: "auto",
          p: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Typography level="title-md" p={1} sx={{ flexShrink: 0 }}>
          Liste des champs
        </Typography>
        <Box sx={{ overflow: "auto", flexGrow: 1 }}>
          {consultations.map((consultationType) => (
            <Typography
              key={consultationType.id}
              level={"body-xs"}
              sx={(theme) => ({
                "&:hover": {
                  backgroundColor: theme.palette.background.level2,
                  cursor: "pointer",
                },
                borderRadius: "sm",
                p: 0.5,
              })}
              onClick={() => insertTextToEditor(consultationType.id)}
              startDecorator={<ArrowBack />}
            >
              {consultationType.titre}
            </Typography>
          ))}
        </Box>
      </Sheet>
    ),
    [consultations, insertTextToEditor]
  );

  const handleEdit = React.useCallback(() => {
    setCourrier(consultation.courrier_consultation || "");
    setBasPage(consultation.courrier_consultation_bas_page || "");
    setEdit(true);
  }, [consultation]);

  const handleStatus = React.useCallback(
    (newValue: StatutCourrierConsultationEnum) => {
      api
        .updateConsultation({
          id: consultation.id,
          statut_courrier_consultation: newValue,
        })
        .then((updatedConsultation) => {
          onSave && onSave(updatedConsultation);
        })
        .catch(() =>
          snackbar.show("Erreur lors de la modification du statut du courrier", "danger")
        );
    },
    [api, consultation.id, onSave, snackbar]
  );

  const handleSave = React.useCallback(async () => {
    setLoading(true);
    try {
      // Mettre à jour le courrier de la consultation
      const patchedConsultation: PatchedConsultation = {
        courrier_consultation: courrier,
        courrier_consultation_bas_page: basPage,
      };
      await api.updateConsultation({ id: consultation.id, ...patchedConsultation });
      if (onSave) {
        onSave();
      }
      snackbar.show("Courrier sauvegardé avec succès", "success");
      setEdit(false); // Exit edit mode after successful save
    } catch (error) {
      console.error("Erreur lors de la sauvegarde du courrier", error);
      snackbar.show("Erreur lors de la sauvegarde du courrier", "danger");
    } finally {
      setLoading(false);
    }
  }, [api, basPage, consultation.id, courrier, onSave, snackbar]);

  const handleOpenDictationDrawer = React.useCallback(() => {
    if (consultation && consultation.id && consultation.zkf_patient) {
      setShowDictationDrawer(true);
    } else {
      snackbar.show("Information de consultation ou patient manquante pour la dictée.", "warning");
    }
  }, [consultation, snackbar]);

  const handleDictationDrawerClose = React.useCallback(() => {
    setShowDictationDrawer(false);
  }, []);

  const handleAudioSaved = React.useCallback(() => {
    setShowDictationDrawer(false);
    setAudioListKey((prevKey) => prevKey + 1);
    snackbar.show("Audio sauvegardé et en cours de transcription.", "success");
  }, [snackbar]);

  return (
    <Stack gap={2}>
      {withHeader && (
        <Stack direction="row" justifyContent="space-between">
          <Stack direction="row" width="100%" gap={2} justifyContent="space-between">
            <Stack direction="row" gap={1} alignItems="center">
              <Typography level="h4">Courrier</Typography>
              <Tooltip
                arrow
                placement="top"
                title={
                  edit
                    ? "Le statut du courrier n'est pas modifiable lorsque le courrier est en cours de rédaction"
                    : undefined
                }
              >
                <span>
                  <Select
                    value={consultation.statut_courrier_consultation}
                    onChange={(e, newValue) => handleStatus(newValue || "non_statue")}
                    disabled={edit}
                  >
                    <Option value={"non_statue"}>Non statué</Option>
                    <Option value={"non_fait"}>Non fait</Option>
                    <Option value={"transcrit"}>Transcrit</Option>
                    <Option value={"attente_validation"}>Attente validation</Option>
                    <Option value={"valide"}>Validé</Option>
                    <Option value={"envoye"}>Envoyé</Option>
                  </Select>
                </span>
              </Tooltip>
            </Stack>
            <Stack direction="row" gap={1} alignItems="center">
              {!edit && (
                <>
                  <Button
                    variant="plain"
                    color="neutral"
                    onClick={handleEdit}
                    startDecorator={<Edit />}
                  >
                    Modifier
                  </Button>
                  <PrintButtons
                    files={[
                      {
                        id: consultation.id,
                        model_app_datas: consultation.model_app_datas,
                        type_document: "courrier_consultation",
                      },
                    ]}
                    withText
                  />
                </>
              )}

              {edit && (
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={handleSave}
                  startDecorator={<Save />}
                >
                  Enregistrer
                </Button>
              )}
              {edit && (
                <Button variant="outlined" color="neutral" onClick={() => setEdit(false)}>
                  Annuler
                </Button>
              )}
            </Stack>
          </Stack>
        </Stack>
      )}
      <Stack gap={0.5}>
        <Typography level="body-sm">Contenu</Typography>
        {!edit && (
          <div
            style={{
              fontSize: "12px",
              height: "300px",
              border: "1px solid #ccc",
              borderRadius: "8px",
              padding: "8px",
              overflow: "scroll",
            }}
            dangerouslySetInnerHTML={{
              __html: consultation.courrier_consultation || "",
            }}
          />
        )}
        {edit && (
          <Stack direction={"row"} gap={2}>
            <div style={{ height: "300px", flex: 1 }}>
              <Editor
                data={courrier}
                onChange={(value) => {
                  setCourrier(value);
                  onCourrierChange && onCourrierChange(value, basPage);
                }}
                onReady={setEditorRef}
                disabled={loading}
              />
            </div>
            <Stack direction="column" gap={1} sx={{ width: 250, height: "300px" }}>
              <Box sx={{ flexBasis: "50%", overflow: "hidden", display: "flex" }}>{templates}</Box>
              <Box sx={{ flexBasis: "50%", overflow: "hidden", display: "flex" }}>
                <AudioTranscriptionList
                  consultationId={consultation.id}
                  patientId={consultation.zkf_patient}
                  onTranscriptionSelect={handleInsertTranscription}
                  onOpenDictationDrawer={handleOpenDictationDrawer}
                  isDictateButtonDisabled={!consultation?.zkf_patient}
                  refreshKey={dataTimestamp} // Pass the timestamp to trigger re-fetch
                  key={audioListKey}
                />
              </Box>
            </Stack>
          </Stack>
        )}
      </Stack>
      <Stack gap={0.5}>
        <Typography level="body-sm">Bas de page</Typography>
        {!edit && (
          <div
            style={{
              fontSize: "12px",
              height: "200px",
              border: "1px solid #ccc",
              borderRadius: "8px",
              padding: "8px",
            }}
            dangerouslySetInnerHTML={{
              __html: consultation.courrier_consultation_bas_page || "",
            }}
          />
        )}
        {edit && (
          <div style={{ height: "200px" }}>
            <Editor
              data={basPage}
              onChange={(value) => {
                setBasPage(value);
                onCourrierChange && onCourrierChange(courrier, value);
              }}
            />
          </div>
        )}
      </Stack>
      <Drawer
        anchor="right"
        open={showDictationDrawer}
        onClose={handleDictationDrawerClose}
        sx={{
          "& .MuiDrawer-paper": {
            width: "clamp(360px, 25vw, 600px)",
            boxSizing: "border-box",
          },
        }}
      >
        <ModalClose />
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            height: "100%",
            overflow: "auto",
            p: 0,
          }}
        >
          {consultation?.zkf_patient && consultation?.id && (
            <SpeechToTextPage
              initialPatientId={consultation.zkf_patient}
              initialConsultationId={consultation.id}
              onClose={handleDictationDrawerClose}
              onAudioSaved={handleAudioSaved}
            />
          )}
        </Box>
      </Drawer>
    </Stack>
  );
}
