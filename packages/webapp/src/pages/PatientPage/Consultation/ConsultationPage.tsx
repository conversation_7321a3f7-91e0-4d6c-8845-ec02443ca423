import moment from "moment";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";

import { Edit, List, OpenInNew, PeopleAlt, StickyNote2, Tag } from "@mui/icons-material";
import FileIcon from "@mui/icons-material/FilePresent";
import HistoryEduIcon from "@mui/icons-material/HistoryEdu";
import { Sheet, TabPanel } from "@mui/joy";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Chip from "@mui/joy/Chip";
import Stack from "@mui/joy/Stack";
import Tab from "@mui/joy/Tab";
import TabList from "@mui/joy/TabList";
import Tabs from "@mui/joy/Tabs";
import Tooltip from "@mui/joy/Tooltip";
import Typography from "@mui/joy/Typography";

import AuditLogModal from "../../../components/AuditLog/AuditLogModal";
import DeleteConfirmationModal from "../../../components/Modals/DeleteConfirmationModal";
import OrdonnanceListAndEdit from "../../../components/Ordonnances/OrdonnanceListAndEdit";
import PrintButtons from "../../../components/PrintManager/PrintButtons";
import { useApi } from "../../../contexts/ApiContext";
import { useCurrentPatient } from "../../../contexts/CurrentPatientContext";
import { useSnackbar } from "../../../contexts/SnackbarContext";
import { isPatient } from "../../../models/type_guards";
import {
  AuditLog,
  Consultation,
  Operation,
  PatchedConsultation,
  Patient,
  User,
  UserSites,
  UserSitesLieux,
} from "../../../models/types";
import { router } from "../../../router";
import { displayEtatConsultation, formatDate, formatHour } from "../../../utils/utils";
import DocumentsModal from "../DocumentsModal";
import ListOfEvents from "../ListOfEvents";
import ComplicationsTab from "./ComplicationsTab";
import ConsultationEditDrawer from "./ConsultationEditDrawer";
import ConsultationEtatButtons from "./ConsultationEtatButtons";
import CourrierTab from "./CourrierTab";
import ResumeTab from "./ResumeTab";

export default function ConsultationPage() {
  const api = useApi();
  const snackbar = useSnackbar();
  const { setCurrentPatient } = useCurrentPatient();
  const { show } = useSnackbar();
  const { id } = useParams();
  const navigate = useNavigate();

  const [consultation, setConsultation] = React.useState<Consultation | null>(null);
  const [openDocuments, setOpenDocuments] = React.useState(false);
  const [sortedOperations, setSortedOperations] = React.useState<Operation[]>([]);
  // Récupérer l'onglet actif sauvegardé ou utiliser 0 par défaut
  const [activeTab, setActiveTab] = React.useState<number>(() => {
    const savedTab = localStorage.getItem(`consultation_${id}_activeTab`);
    return savedTab ? parseInt(savedTab, 10) : 0;
  });
  const [open, setOpen] = React.useState<boolean>(false);
  const [selectedDocumentId, setSelectedDocumentId] = React.useState<string>("");
  const [openEditDrawer, setOpenEditDrawer] = React.useState(false);
  const [history, setHistory] = React.useState<AuditLog[]>([]);
  const [openModal, setOpenModal] = React.useState(false);

  const fetch = React.useCallback(async () => {
    if (api && id) {
      try {
        const res = await api.getConsultation(id, 1);
        setConsultation(res);
        if (!res) {
          snackbar.show("Consultation non trouvée", "danger");
          navigate("/");
          return;
        }
        const patientId = isPatient(res.zkf_patient)
          ? (res.zkf_patient as unknown as Patient).id
          : res.zkf_patient;
        const patient = await api.getPatient(patientId);
        setCurrentPatient(patient);
      } catch (error) {
        snackbar.show("Erreur lors de la récupération de la consultation", "danger");
      }
    }
  }, [api, id, setCurrentPatient, snackbar, navigate]);

  React.useEffect(() => {
    fetch();
  }, [fetch]);

  React.useEffect(() => {
    setSortedOperations(
      (consultation?.operation_liee as unknown as Operation[])?.sort((a, b) =>
        moment(a.operation_date).valueOf() < moment(b.operation_date).valueOf() ? -1 : 1
      )
    );
  }, [consultation]);

  const handleCloseDocuments = React.useCallback(() => setOpenDocuments(false), []);

  const handleOpenDocuments = React.useCallback((id: string) => {
    setOpenDocuments(true);
    setSelectedDocumentId(id);
  }, []);

  if (!consultation) return null;

  const handleSaveSettings = async (updatedConsultation: PatchedConsultation) => {
    try {
      await api.updateConsultation(updatedConsultation);
      await fetch();
      setOpenEditDrawer(false);
      snackbar.show("Consultation modifiée avec succès", "success");
    } catch (error) {
      snackbar.show("Erreur lors de la modification de la consultation", "danger");
    }
  };

  const handleDelete = async () => {
    try {
      await api.deleteConsultation(consultation.id);
      router.navigate(
        `/patient/${isPatient(consultation.zkf_patient) ? (consultation.zkf_patient as unknown as Patient).id : consultation.zkf_patient}`
      );
    } catch (error) {
      snackbar.show("Erreur lors de la suppression de la consultation", "danger");
    }
  };

  const handleHistory = () => {
    api
      .getAuditLog(consultation.id, "consultation.consultation", null, 1)
      .then((res) => {
        setHistory(res.results);
        setOpenModal(true);
      })
      .catch(() => show("Erreur lors de la récupération de l'historique", "danger"));
  };

  return (
    <>
      <AuditLogModal open={openModal} onClose={() => setOpenModal(false)} history={history} />

      <Box>
        <Stack direction="row" gap={4} justifyContent="space-between">
          <Box flex={1}>
            <div>
              <Chip size="sm" color="primary" startDecorator={<PeopleAlt fontSize="small" />}>
                CONSULTATION
              </Chip>
              <Stack direction="row" gap={1} alignItems={"center"}>
                {!consultation.motif_consultation ? (
                  <Typography level="h3" sx={{ color: "text.secondary" }}>
                    Consultation sans motif
                  </Typography>
                ) : (
                  <Typography level="h3">{consultation.motif_consultation}</Typography>
                )}
                {consultation.mode_consultation === "urgent" && (
                  <Chip variant="outlined" color="danger">
                    Urgence
                  </Chip>
                )}
                {consultation.premiere_consultation && (
                  <Chip variant="outlined" color="warning">
                    Première consultation
                  </Chip>
                )}
              </Stack>
            </div>
            {!!consultation.tags && (
              <Stack sx={{ mt: 0.5 }} direction="row" gap={0.5}>
                {consultation.tags?.map((tag) => (
                  <Chip key={tag} variant="outlined" color="neutral" startDecorator={<Tag />}>
                    {tag}
                  </Chip>
                ))}
              </Stack>
            )}
            <Stack sx={{ mt: 1 }} gap={0.25}>
              <Stack direction={"row"} gap={2}>
                {consultation.consultation_date && (
                  <Stack direction="row" alignItems="center" gap={0.5}>
                    <Typography level="body-xs" textTransform="uppercase">
                      DATE
                    </Typography>
                    <Typography level="body-md">
                      {formatDate(consultation.consultation_date)}
                    </Typography>
                  </Stack>
                )}
                {consultation.consultation_debut_prevu && (
                  <Stack direction="row" alignItems="center" gap={0.5}>
                    <Typography level="body-xs" textTransform="uppercase">
                      DÉBUT PRÉVU
                    </Typography>
                    <Typography level="body-md">
                      {formatHour(consultation.consultation_debut_prevu)}
                    </Typography>
                  </Stack>
                )}
              </Stack>
              <Stack direction={"row"} gap={2}>
                {consultation.consultation_debut_reel && (
                  <Stack direction="row" alignItems="center" gap={0.5}>
                    <Typography level="body-xs" textTransform="uppercase">
                      DÉBUT RÉEL
                    </Typography>
                    <Typography level="body-md">
                      {formatHour(consultation.consultation_debut_reel)}
                    </Typography>
                  </Stack>
                )}
                {consultation.consultation_fin_reel && (
                  <Stack direction="row" alignItems="center" gap={0.5}>
                    <Typography level="body-xs" textTransform="uppercase">
                      FIN RÉEL
                    </Typography>
                    <Typography level="body-md">
                      {formatHour(consultation.consultation_fin_reel)}
                    </Typography>
                  </Stack>
                )}
              </Stack>
              {consultation.zkf_site && (
                <Stack direction="row" alignItems="center" gap={0.5}>
                  <Typography level="body-xs" textTransform="uppercase">
                    SITE
                  </Typography>
                  <Typography level="body-md">
                    {(consultation.zkf_site as unknown as UserSites).nom_site}
                    &nbsp;—&nbsp;
                    {consultation.zkf_lieu &&
                      (consultation.zkf_lieu as unknown as UserSitesLieux).nom_lieu}
                    {consultation.zkf_redacteur &&
                      " - Dr." + (consultation.zkf_redacteur as unknown as User).last_name}
                  </Typography>
                </Stack>
              )}
              <Stack direction={"row"} gap={2}>
                <Stack direction="row" alignItems="center" gap={0.5}>
                  <Typography level="body-xs" textTransform="uppercase">
                    STATUT
                  </Typography>
                  <Typography level="body-md">
                    {displayEtatConsultation(consultation.etat_consultation)}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
          </Box>
          <Stack gap={1} alignItems="flex-end">
            <Stack direction="row" gap={1} alignItems="center">
              <Button
                color="neutral"
                startDecorator={<HistoryEduIcon fontSize="small" />}
                variant="plain"
                onClick={handleHistory}
              >
                Logs
              </Button>
              <Button
                onClick={() => setOpenEditDrawer(true)}
                variant="plain"
                color="neutral"
                startDecorator={<Edit />}
              >
                Modifier
              </Button>
              <Button onClick={() => setOpenDocuments(true)} startDecorator={<FileIcon />}>
                Documents
              </Button>
              <Sheet variant="outlined" sx={{ p: 1 }}>
                <Stack direction="row" gap={2} alignItems="center" justifyContent="space-between">
                  <Typography level="body-sm" startDecorator={<StickyNote2 />}>
                    Convocation
                  </Typography>
                  <PrintButtons
                    size="sm"
                    files={[
                      {
                        id: consultation.id,
                        model_app_datas: consultation.model_app_datas,
                        type_document: "convocation_consultation",
                      },
                    ]}
                  />
                </Stack>
              </Sheet>
            </Stack>
            <ConsultationEtatButtons consultation={consultation} refresh={fetch} />
          </Stack>
        </Stack>
        <Stack mt={4} direction={"row"} gap={2}>
          <ListOfEvents patient={consultation.zkf_patient as unknown as Patient} width={"200px"} />
          <Tabs
            sx={{
              flex: 1,
              borderLeft: "1px solid",
              borderTop: "1px solid",
              borderColor: (theme) => theme.palette.divider,
              borderTopLeftRadius: "8px",
            }}
            value={activeTab}
            onChange={(e, newValue) => {
              setActiveTab(newValue as number);
              // Sauvegarder l'onglet actif pour cette consultation
              localStorage.setItem(`consultation_${id}_activeTab`, String(newValue));
            }}
          >
            <TabList>
              <Tab sx={{ borderTopLeftRadius: "8px" }} value={0}>
                Résumé
              </Tab>
              <Tab value={1}>Courrier</Tab>
              <Tab value={2}>Ordonnances</Tab>
              {sortedOperations?.map((ope, i) => (
                <Tooltip
                  key={i}
                  title={`${formatDate(ope.operation_date)} - ${ope.titre_operation}`}
                >
                  <Tab
                    sx={{
                      maxWidth: "200px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      display: "inline-block",
                    }}
                    value={i + 3}
                  >
                    {ope.titre_operation}
                  </Tab>
                </Tooltip>
              ))}
            </TabList>
            <TabPanel value={0}>
              <ResumeTab
                consultation={consultation}
                onSave={fetch}
                onDocumentClick={handleOpenDocuments}
              />
            </TabPanel>
            <TabPanel value={1}>
              <CourrierTab consultation={consultation} onSave={fetch} />
            </TabPanel>
            <TabPanel value={2}>
              <Stack spacing={2}>
                <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    startDecorator={<List />}
                    endDecorator={<OpenInNew />}
                    onClick={() => {
                      // Avant de naviguer, sauvegarder l'état actuel comme "revenir à l'onglet ordonnances"
                      localStorage.setItem(`consultation_${id}_returnTo`, "2");

                      const patientId = isPatient(consultation.zkf_patient)
                        ? (consultation.zkf_patient as unknown as Patient).id
                        : consultation.zkf_patient;
                      navigate(`/ordonnances/${patientId}`);
                    }}
                    sx={{ mb: 2 }}
                  >
                    Voir toutes les ordonnances
                  </Button>
                </Box>
                <OrdonnanceListAndEdit
                  consultationId={consultation.id}
                  zkf_patient={consultation.zkf_patient}
                />
              </Stack>
            </TabPanel>
            {sortedOperations?.map((ope, i) => (
              <TabPanel value={i + 3} key={i}>
                <ComplicationsTab operation={ope as unknown as Operation} />
              </TabPanel>
            ))}
          </Tabs>
        </Stack>
        {openDocuments && (
          <DocumentsModal
            open={openDocuments}
            onClose={handleCloseDocuments}
            patient={consultation.zkf_patient as unknown as Patient}
            initialSelectedDocumentId={selectedDocumentId}
          />
        )}
        <DeleteConfirmationModal
          open={open}
          onClose={() => setOpen(false)}
          onConfirm={handleDelete}
          title="Supprimer la consultation"
          message="Êtes-vous sûr de vouloir supprimer cette consultation ?"
        />
        <ConsultationEditDrawer
          open={openEditDrawer}
          onClose={() => setOpenEditDrawer(false)}
          onSave={handleSaveSettings}
          consultation={consultation}
        />
      </Box>
    </>
  );
}
