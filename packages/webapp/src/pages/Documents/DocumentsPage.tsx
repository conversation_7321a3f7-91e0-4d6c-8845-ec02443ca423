import dayjs from "dayjs";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";

import CloseIcon from "@mui/icons-material/Close";
import ErrorOutlineRoundedIcon from "@mui/icons-material/ErrorOutlineRounded";
import UploadIcon from "@mui/icons-material/Upload";
import WarningRoundedIcon from "@mui/icons-material/WarningRounded";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import CircularProgress from "@mui/joy/CircularProgress";
import IconButton from "@mui/joy/IconButton";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Table from "@mui/joy/Table";
import Typography from "@mui/joy/Typography";

import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { CompteRendu, Contact, Patient, Specialite } from "../../models/types";

export default function DocumentsPage() {
  // Get patient id from route params.
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const api = useApi();
  const snackbar = useSnackbar();

  // Vérifier si nous sommes sur l'onglet Documents de PatientPage
  // Si l'URL ne contient pas "/documents", nous sommes probablement dans l'onglet Documents de PatientPage
  const isInPatientPageTab = !window.location.pathname.includes("/documents");

  const [patient, setPatient] = React.useState<Patient | null>(null);
  const [compteRendus, setCompteRendus] = React.useState<CompteRendu[]>([]);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [hasMoreDocuments, setHasMoreDocuments] = React.useState<boolean>(true);
  const [currentPage, setCurrentPage] = React.useState<number>(1);
  const [totalDocuments, setTotalDocuments] = React.useState<number>(0);
  const [patientsMap, setPatientsMap] = React.useState<Record<string, Patient>>({});
  const [contactsMap, setContactsMap] = React.useState<Record<string, Contact>>({});
  const [specialitiesMap, setSpecialitiesMap] = React.useState<Record<string, Specialite>>({});

  // Files currently uploading
  const [uploadingFiles, setUploadingFiles] = React.useState<{ id: string; file: File }[]>([]);
  // Files that failed to process
  const [erroredUploads, setErroredUploads] = React.useState<any[]>([]);

  // Variable pour suivre si nous venons d'une page patient
  const previousPatientRef = React.useRef<string | null>(null);

  // Effet pour charger les informations du patient lorsque l'ID change dans l'URL
  React.useEffect(() => {
    // Détecter la navigation d'une page patient vers la page principale
    if (!id && previousPatientRef.current) {
      // Nous venons d'une page patient et allons vers la page principale
      // Force un rechargement complet de la page pour réinitialiser tous les états
      window.location.reload();
      return;
    }

    // Mettre à jour la référence pour la prochaine navigation
    previousPatientRef.current = id || null;

    if (id) {
      api
        .getPatient(id)
        .then((p: Patient) => {
          setPatient(p);
        })
        .catch((error) => {
          console.error("Error loading patient:", error);
          snackbar.show("Erreur lors du chargement du patient", "danger");
        });
    } else {
      // Si nous sommes sur la page principale des documents, réinitialiser le patient
      setPatient(null);
    }
  }, [id, api, snackbar]);

  // Fetch documents for the selected patient or non-validated documents with pagination.
  const fetchDocuments = React.useCallback(
    async (page: number = 1, append: boolean = false, patientId?: string) => {
      // Éviter les appels si déjà en cours de chargement
      if (isLoading) {
        return;
      }

      try {
        setIsLoading(true);
        let response;

        try {
          // Utiliser l'ID du patient passé en paramètre s'il est fourni explicitement
          if (patientId !== undefined) {
            // Si un ID de patient est fourni (même vide), utiliser cette valeur exacte
            if (patientId) {
              // Récupérer les documents d'un patient spécifique
              response = await api.listCompteRendu(patientId, undefined, page, true, "date_examen");
            } else {
              // Si patientId est une chaîne vide, récupérer les documents non validés
              response = await api.listCompteRendu("", "not_validated", page, true, "created_on");
            }
          } else {
            // Comportement par défaut si patientId n'est pas fourni
            const effectivePatientId = patient ? patient.id : id;

            if (effectivePatientId) {
              // Récupérer les documents d'un patient spécifique
              response = await api.listCompteRendu(
                effectivePatientId,
                undefined,
                page,
                true,
                "date_examen"
              );
            } else {
              // Récupérer les documents non validés
              response = await api.listCompteRendu("", "not_validated", page, true, "created_on");
            }
          }
        } catch (error) {
          console.error("Error fetching documents:", error);
          snackbar.show("Erreur lors du chargement des documents", "warning");
          setIsLoading(false);
          return;
        }

        // Assurons-nous que response est bien un objet paginé
        const paginatedResponse = response as {
          results: CompteRendu[];
          count: number;
          next: string | null;
          previous: string | null;
        };
        const { results, count, next } = paginatedResponse;

        // Update state with the new documents
        if (append) {
          // Ajouter les nouveaux documents à la liste existante en évitant les duplications
          setCompteRendus((prev) => {
            // Créer un Map des documents existants par ID pour une recherche rapide
            const existingDocsMap = new Map(prev.map((doc) => [doc.id, doc]));

            // Filtrer les nouveaux documents pour ne garder que ceux qui n'existent pas déjà
            const newDocs = results.filter((doc) => !existingDocsMap.has(doc.id));

            // Pour le bouton "Afficher plus", ajouter les nouveaux documents à la fin
            return [...prev, ...newDocs];
          });
        } else {
          // Remplacer complètement la liste avec les nouveaux documents
          setCompteRendus(results);
        }

        setTotalDocuments(count);
        setHasMoreDocuments(next !== null);
        setCurrentPage(page);

        if (results && results.length > 0) {
          // Optimisation : ne traiter que les nouveaux documents, jamais les documents déjà traités
          // Extraire uniquement les IDs de patients et contacts des nouveaux documents
          const patientIds = new Set<string>();
          const contactIds = new Set<string>();

          results.forEach((doc: any) => {
            if (doc.zkf_patient && !patientsMap[doc.zkf_patient]) {
              patientIds.add(doc.zkf_patient);
            }
            if (doc.zkf_contact && !contactsMap[doc.zkf_contact]) {
              contactIds.add(doc.zkf_contact);
            }
          });

          // Ne récupérer que les nouvelles données nécessaires
          if (patientIds.size > 0 || contactIds.size > 0) {
            // Fetch only new patients
            const newPatientsData: Record<string, Patient> = { ...patientsMap };
            for (const patientId of Array.from(patientIds)) {
              try {
                const patientData = await api.getPatient(patientId);
                newPatientsData[patientId] = patientData;
              } catch (error) {
                console.error(`Failed to fetch patient with ID ${patientId}:`, error);
              }
            }
            setPatientsMap(newPatientsData);

            // Fetch only new contacts
            const newContactsData: Record<string, Contact> = { ...contactsMap };
            for (const contactId of Array.from(contactIds)) {
              try {
                const contactData = await api.getContact(contactId);
                newContactsData[contactId] = contactData;
              } catch (error) {
                console.error(`Failed to fetch contact with ID ${contactId}:`, error);
              }
            }
            setContactsMap(newContactsData);
          }

          // Suppression de la mise à jour redondante de la liste des documents
          // Cette partie est déjà gérée plus haut dans la fonction
        }

        // Fetch specialities only once
        if (Object.keys(specialitiesMap).length === 0) {
          try {
            const specialities = await api.listUserSpecialites();
            const specialitiesData: Record<string, Specialite> = {};
            specialities.forEach((speciality) => {
              specialitiesData[speciality.id] = speciality;
            });
            setSpecialitiesMap(specialitiesData);
          } catch (error) {
            console.error("Failed to fetch specialities:", error);
          }
        }
      } catch (e) {
        console.error("Error processing documents data:", e);
        // Différencier le message d'erreur pour aider au débogage
        snackbar.show("Erreur lors du traitement des données des documents", "danger");
      } finally {
        setIsLoading(false);
      }
    },
    [
      api,
      snackbar,
      patientsMap,
      contactsMap,
      specialitiesMap,
      setContactsMap,
      setPatientsMap,
      setSpecialitiesMap,
      id,
      patient,
      isLoading,
    ]
  ); // Includes all necessary dependencies while avoiding unnecessary re-creations

  // Effet pour gérer le chargement des documents lorsque le patient change ou l'URL change
  React.useEffect(() => {
    // Éviter les appels si déjà en cours de chargement
    if (isLoading) return;

    // Réinitialiser l'état de pagination
    setCompteRendus([]);
    setCurrentPage(1);
    setHasMoreDocuments(true);
    setTotalDocuments(0);

    // Cas 1: Nous avons un patient chargé
    if (patient) {
      fetchDocuments(1, false, patient.id);
      return;
    }

    // Cas 2: Nous avons un ID dans l'URL mais pas de patient chargé
    if (id) {
      fetchDocuments(1, false, id);
      return;
    }

    // Cas 3: Pas de patient, pas d'ID - charger les documents non validés
    fetchDocuments(1, false, "");

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patient?.id, id]);

  // When new documents are uploaded, add them to the list.
  const addCrToFilesList = React.useCallback(async (newCr: CompteRendu[]) => {
    // Update total count
    setTotalDocuments((prev: number) => prev + newCr.length);
    // Placer les nouveaux documents au début de la liste pour qu'ils apparaissent en haut
    setCompteRendus((prev: CompteRendu[]) => {
      // Éviter les doublons
      const existingIds = new Set(prev.map((doc) => doc.id));
      const uniqueNewDocs = newCr.filter((doc) => !existingIds.has(doc.id));
      return [...uniqueNewDocs, ...prev];
    });
  }, []);

  // Navigate to the document details page when a document is clicked.
  const handleDocumentClick = (document: CompteRendu | null) => {
    if (document) {
      navigate(`/document/${document.id}`);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files);
    if (!droppedFiles.length) return;

    // For each dropped file, generate a unique id and immediately add it to uploadingFiles.
    const newUploadingFiles = droppedFiles.map((file, index) => ({
      id: `upload-${Date.now()}-${index}`,
      file,
    }));
    setUploadingFiles((prev) => [...prev, ...newUploadingFiles]);

    const crErrors = [];
    const crCreated = [];

    // Process each file upload sequentially.
    for (const uploadItem of newUploadingFiles) {
      const file = uploadItem.file;
      const metaData = {
        nom: "",
        type_compte_rendu: "",
        zkf_contact: "",
        zkf_patient: patient ? patient.id : "",
        specialite: [] as string[],
        date_examen: "",
        date_entree: "",
        date_sortie: "",
        file,
        notification: false,
      };

      try {
        // Create the document record via the API.
        const c = await api.createCompteRendu(metaData, patient ? patient.id : null);
        crCreated.push(c);
        // Remove the file from the uploading list on success.
        setUploadingFiles((prev) => prev.filter((item) => item.id !== uploadItem.id));
      } catch (e) {
        crErrors.push(metaData);
        // Remove it from the uploading list on error.
        setUploadingFiles((prev) => prev.filter((item) => item.id !== uploadItem.id));
        // Save errored uploads so they appear in the table with an error icon.
        setErroredUploads((prev) => [...prev, metaData]);
      }
    }

    if (crCreated.length) {
      snackbar.show("Upload réussi", "success");
      addCrToFilesList(crCreated);
    }
    if (crErrors.length) {
      snackbar.show("Upload partiellement échoué", "danger");
    }
  };

  // Helper function to get patient display name
  const getPatientDisplayName = (patientId: string, document?: CompteRendu) => {
    // Vérifier d'abord si le patient existe dans notre cache
    const patientData = patientsMap[patientId];
    if (patientData) {
      return `${patientData.nom} ${patientData.prenom}`;
    }

    // Si le document a des informations sur le patient (nom, prénom), les utiliser
    if (document && document.patient_nom) {
      return document.patient_nom;
    }

    // Fallback si aucune information n'est disponible
    return `Patient (${patientId})`;
  };

  // Helper function to get contact display name
  const getContactDisplayName = (contactId: string, document?: CompteRendu) => {
    // Vérifier d'abord si le contact existe dans notre cache
    const contactData = contactsMap[contactId];
    if (contactData) {
      return `${contactData.nom} ${contactData.prenom}`;
    }

    // Si le document a un attribut contact_not_found ou auteur_nom
    if (document) {
      // Vérifier si le document a explicitement un flag contact_not_found
      if (document.contact_not_found) {
        if (document.auteur_nom) {
          return `Non trouvé dans la base : ${document.auteur_nom}`;
        }
      }
      // Si le document a un auteur_nom mais que le contact n'est pas dans notre cache
      else if (document.auteur_nom) {
        return document.auteur_nom;
      }
    }

    // Fallback si aucune information n'est disponible
    return `Contact non trouvé (${contactId})`;
  };

  const getSpecialityDisplayName = (specialityId: string) => {
    const specialityData = specialitiesMap[specialityId];
    if (specialityData) {
      return specialityData.spe;
    }
    return `Spécialité non trouvée (${specialityId})`;
  };

  // Build a combined list of table rows.
  const tableRows = [
    ...uploadingFiles.map((item) => ({
      key: item.id,
      name: "",
      status: "uploading",
      patient: patient ? patient.id : "",
      patientDisplay: patient ? `${patient.nom} ${patient.prenom}` : "",
      date: "",
      type: "",
      contact: "",
      contactDisplay: "",
      specialityDisplay: "",
      should_be_contacted: "",
      notification: "",
      document: null,
    })),
    ...erroredUploads.map((meta, idx) => ({
      key: `error-${idx}`,
      name: meta.nom,
      status: "error",
      patient: meta.zkf_patient,
      patientDisplay: meta.zkf_patient ? getPatientDisplayName(meta.zkf_patient) : "",
      date: "",
      type: meta.type_compte_rendu,
      contact: meta.zkf_contact,
      contactDisplay: meta.zkf_contact ? getContactDisplayName(meta.zkf_contact) : "",
      specialityDisplay: meta.specialite ? getSpecialityDisplayName(meta.specialite) : "",
      should_be_contacted: "",
      notification: meta.notification,
      document: null,
    })),
    ...compteRendus.map((cr) => ({
      key: cr.id,
      name: cr.nom,
      // If the document's status is false, we mark it as "warning"
      status: cr.status === false ? "warning" : "ok",
      patient: cr.zkf_patient,
      patientDisplay: cr.zkf_patient ? getPatientDisplayName(cr.zkf_patient, cr) : "",
      date: cr.date_examen ? cr.date_examen : cr.date_sortie ? cr.date_sortie : null,
      type: cr.type_compte_rendu,
      contact: cr.zkf_contact,
      contactDisplay: cr.zkf_contact
        ? getContactDisplayName(cr.zkf_contact, cr)
        : cr.auteur_nom
          ? cr.auteur_nom
          : "",
      specialityDisplay:
        cr.specialites && cr.specialites.length > 0 ? cr.specialites[0].spe || "" : "",
      should_be_contacted: cr.should_be_contacted,
      document: cr,
    })),
  ];

  // Function to load more documents when the button is clicked
  const handleLoadMore = React.useCallback(() => {
    if (!hasMoreDocuments || isLoading) {
      return;
    }

    // Charger la page suivante et ajouter les résultats aux documents existants
    const nextPage = currentPage + 1;

    // Utiliser l'ID du patient actuel ou l'ID de l'URL
    const effectivePatientId = patient ? patient.id : id;

    // Si nous sommes sur la page principale des documents sans patient sélectionné
    if (!effectivePatientId && !id) {
      // Passer une chaîne vide explicitement pour charger les documents non validés
      fetchDocuments(nextPage, true, "");
    } else {
      // Appeler fetchDocuments avec append=true pour ajouter les nouveaux documents
      fetchDocuments(nextPage, true, effectivePatientId);
    }
  }, [hasMoreDocuments, isLoading, currentPage, fetchDocuments, patient, id]);

  // Add handleDragOver function to prevent default behavior and allow drop
  const handleDragOver = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  return (
    <Stack spacing={2} sx={{ minHeight: "100vh" }} onDragOver={handleDragOver} onDrop={handleDrop}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mt: 2,
        }}
      >
        {patient && !isInPatientPageTab && (
          <IconButton onClick={() => navigate(-1)} variant="outlined" color="neutral" size="sm">
            <CloseIcon />
          </IconButton>
        )}
      </Box>
      {
        // if no documents, show a sheet with a message saying to upload documents or drop them on the page
        compteRendus.length === 0 && !isLoading && (
          <Sheet sx={{ mt: 2, height: "500px" }} variant="outlined">
            <Stack spacing={2} alignItems="center" justifyContent="center" height="100%">
              <UploadIcon sx={{ fontSize: "100px" }} color="primary" />
              <Typography level="body-lg" color="neutral">
                Glissez et déposez vos documents ou cliquez sur le bouton ci-dessous pour les
                téléverser.
              </Typography>
            </Stack>
          </Sheet>
        )
      }

      {/* Display the combined list of documents in a table with status icons in the first column */}
      {tableRows.length > 0 && (
        <Sheet sx={{ mt: 2, borderRadius: "8px" }} variant="outlined">
          <Table aria-label="Documents Table">
            <thead>
              <tr>
                <th style={{ width: "5%", textAlign: "center" }}></th>
                {/*<th style={{ textAlign: "center" }}>Description/th>*/}
                {!patient && <th style={{ textAlign: "center" }}>Patient</th>}
                <th style={{ textAlign: "center" }}>Date</th>
                <th style={{ textAlign: "center" }}>Type</th>
                <th style={{ textAlign: "center" }}>Auteur</th>
                <th style={{ textAlign: "center" }}>Spécialité</th>
                <th style={{ textAlign: "center" }}>Patient à convoquer</th>
                {/*<th style={{ width: "10%", textAlign: "center" }}></th>*/}
              </tr>
            </thead>
            <tbody>
              {tableRows.map((row) => (
                <tr
                  key={row.key}
                  style={{
                    cursor: row.document ? "pointer" : "default",
                    backgroundColor: "initial",
                  }}
                  onMouseEnter={(e) => {
                    if (!(e.target as HTMLElement).closest("button")) {
                      e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.04)";
                    }
                  }}
                  onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "")}
                  onClick={(e) => {
                    if ((e.target as HTMLElement).closest("button, a")) {
                      return;
                    }
                    handleDocumentClick(row.document);
                  }}
                >
                  <td style={{ textAlign: "center" }}>
                    {row.status === "uploading" && <CircularProgress size="sm" />}
                    {row.status === "warning" && <WarningRoundedIcon color="warning" />}
                    {row.status === "error" && <ErrorOutlineRoundedIcon color="error" />}
                  </td>
                  {/*<td style={{ textAlign: "center" }}>{row.name}</td>*/}
                  {!patient && <td style={{ textAlign: "center" }}>{row.patientDisplay}</td>}
                  <td style={{ textAlign: "center" }}>
                    {row.date ? dayjs(row.date).format("DD/MM/YYYY") : ""}
                  </td>
                  <td style={{ textAlign: "center" }}>{row.type}</td>
                  <td style={{ textAlign: "center" }}>{row.contactDisplay}</td>
                  <td style={{ textAlign: "center" }}>{row.specialityDisplay}</td>
                  <td style={{ textAlign: "center" }}>
                    {row.should_be_contacted === true
                      ? "Oui"
                      : row.should_be_contacted === false
                        ? "Non"
                        : "Non déterminé"}
                  </td>
                  {/*<td style={{ textAlign: "center" }}>
                    {row.document && (
                      <Stack direction="row" spacing={1} justifyContent="center">
                        {/* Bouton de validation - n'afficher que pour les documents non validés */}
                  {/*row.status === "warning" && (
                          <IconButton
                            size="sm"
                            variant="outlined"
                            color="success"
                            onClick={(e) => handleValidateDocument(e, row.document)}
                            title="Valider le document"
                            sx={{
                              borderRadius: '8px',
                              '&:hover': {
                                backgroundColor: 'success.softHoverBg',
                              }
                            }}
                          >
                            <CheckCircle />
                          </IconButton>
                        )*/}

                  {/* Bouton de suppression */}
                  {/*<IconButton
                          size="sm"
                          variant="outlined"
                          color="danger"
                          onClick={(e) => handleDeleteDocument(e, row.document)}
                          title="Supprimer le document"
                          sx={{
                            borderRadius: '8px',
                            '&:hover': {
                              backgroundColor: 'danger.softHoverBg',
                            }
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>}
                      </Stack>
                    )}
                  </td>*/}
                </tr>
              ))}
            </tbody>
          </Table>
        </Sheet>
      )}

      {/* Informations sur les documents et bouton pour en afficher plus */}
      {compteRendus.length > 0 && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            py: 2,
            mt: 2,
          }}
        >
          {/* Afficher le nombre de documents chargés */}
          <Typography level="body-sm">
            {compteRendus.length} document{compteRendus.length > 1 ? "s" : ""} affiché
            {compteRendus.length > 1 ? "s" : ""} sur {totalDocuments} au total
          </Typography>

          {/* Bouton ou message en fonction de l'état */}
          {isLoading && currentPage === 1 ? (
            <Stack direction="row" spacing={1} alignItems="center" sx={{ my: 1 }}>
              <CircularProgress size="sm" />
              <Typography level="body-md">Chargement des documents...</Typography>
            </Stack>
          ) : hasMoreDocuments ? (
            <Button
              variant="outlined"
              onClick={handleLoadMore}
              disabled={isLoading}
              size="lg"
              sx={{
                minWidth: "250px",
                mt: 1,
                "&:hover": {
                  backgroundColor: "primary.softHoverBg",
                },
              }}
              startDecorator={isLoading && <CircularProgress size="sm" />}
            >
              {isLoading ? "Chargement..." : "Afficher plus de documents"}
            </Button>
          ) : (
            <Typography level="body-sm" sx={{ color: "text.secondary" }}>
              Tous les documents ont été chargés
            </Typography>
          )}
        </Box>
      )}
    </Stack>
  );
}
