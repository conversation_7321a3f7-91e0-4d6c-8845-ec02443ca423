import React, { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import { Edit, Save, Warning } from "@mui/icons-material";
import Add from "@mui/icons-material/Add";
import ArrowBack from "@mui/icons-material/ArrowBack";
import CloseIcon from "@mui/icons-material/Close";
import DownloadIcon from "@mui/icons-material/Download";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import PrintIcon from "@mui/icons-material/Print";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Divider from "@mui/joy/Divider";
import FormControl from "@mui/joy/FormControl";
import FormHelperText from "@mui/joy/FormHelperText";
import FormLabel from "@mui/joy/FormLabel";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Stack from "@mui/joy/Stack";
import Switch from "@mui/joy/Switch";
import Typography from "@mui/joy/Typography";

import ContactModalForm from "../../components/Contact/ContactForm";
import PreviewFile from "../../components/File/PreviewFile";
import ContactSelector from "../../components/Forms/ContactSelector";
import { CreatePatient } from "../../components/Forms/CreatePatient";
import PatientSelector from "../../components/Forms/PatientSelector";
import SpecialiteMultiSelect from "../../components/Forms/SpecialiteMultiSelect";
import ModalComponent from "../../components/Modals/Modal";
import PatientCard from "../../components/PatientCard";
// Import PDFPreviewAndPrint to get correct typing for the usePDFPreview hook
import PDFPreviewAndPrint from "../../components/PDF/PDFPreview";
import { useApi } from "../../contexts/ApiContext";
import { useSnackbar } from "../../contexts/SnackbarContext";
import { CompteRendu, Contact, Patient, Specialite } from "../../models/types";

// Composant pour afficher le PDF dans la modal avec URL d'objet correctement gérée
interface PDFPreviewInModalProps {
  file: File;
  pdfRef: React.RefObject<any>;
}

const PDFPreviewInModal: React.FC<PDFPreviewInModalProps> = ({ file, pdfRef }) => {
  const [fileUrl, setFileUrl] = useState<string | null>(null);

  // Création de l'URL au montage du composant
  useEffect(() => {
    const url = URL.createObjectURL(file);
    setFileUrl(url);

    // Nettoyer l'URL quand le composant est démonté
    return () => {
      URL.revokeObjectURL(url);
    };
  }, [file]);

  if (!fileUrl) return null;

  return <PDFPreviewAndPrint ref={pdfRef} fileUrl={fileUrl} withPrint={true} theme="light" />;
};

export default function DocumentDetailPage() {
  const { documentId } = useParams<{ documentId: string }>();
  const navigate = useNavigate();
  const api = useApi();
  const snackbar = useSnackbar();

  const [documentData, setDocumentData] = React.useState<CompteRendu | null>(null);
  const [file, setFile] = React.useState<File | null>(null);
  const [patient, setPatient] = React.useState<Patient | null>(null);
  const [openCreatePatientModal, setOpenCreatePatientModal] = React.useState(false);
  const [metadata, setMetadata] = React.useState({
    nom: "",
    type_compte_rendu: "",
    zkf_contact: "",
    zkf_patient: "",
    specialite: [] as string[],
    date_examen: "",
    date_entree: "",
    date_sortie: "",
    should_be_contacted: false,
  });
  const [contact, setContact] = React.useState<Contact | null>(null);
  const [isEditing, setIsEditing] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [specialitiesMap, setSpecialitiesMap] = React.useState<Map<string, Specialite>>(new Map());
  const [isContactInfoModalOpen, setIsContactInfoModalOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isPrintModalOpen, setIsPrintModalOpen] = React.useState(false);
  const pdfRef = React.useRef(null);

  const [validationErrors, setValidationErrors] = React.useState<Record<string, string>>({});

  const handleSave = React.useCallback(() => {
    const requiredDate =
      metadata.type_compte_rendu === "HOSPITALISATION"
        ? metadata.date_entree
        : metadata.date_examen;

    setValidationErrors({});
    const errors: Record<string, string> = {};

    if (!metadata.zkf_patient) {
      errors.patient = "Le patient est requis.";
    }
    if (!requiredDate) {
      errors.date = "La date est requise.";
    }
    if (!metadata.nom) {
      errors.nom = "Le nom du document est requis.";
    }

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    if (!documentData) {
      return;
    }

    const updatedDocument = {
      ...documentData,
      ...metadata,
      status: true,
      type_compte_rendu: metadata.type_compte_rendu as
        | "CONSULTATION"
        | "HOSPITALISATION"
        | "EXAMEN_COMPLEMENTAIRE"
        | "ORDONNANCE_TRAITEMENT",
    };

    api
      .updateCompteRendu(updatedDocument)
      .then((res) => {
        setDocumentData(res);
        snackbar.show("Modifications enregistrées", "success");
        setIsEditing(false);
      })
      .catch((error) => {
        snackbar.show("Erreur lors de l'enregistrement", "danger");
      });
  }, [metadata, documentData, api, snackbar, setIsEditing, setDocumentData]);

  const handleEdit = React.useCallback(() => {
    setIsEditing(true);
  }, [setIsEditing]);

  const handleCancelEdit = React.useCallback(() => {
    setIsEditing(false);
    if (documentData) {
      setMetadata({
        nom: documentData.nom || "",
        type_compte_rendu: documentData.type_compte_rendu || "CONSULTATION",
        zkf_contact: documentData.zkf_contact || "",
        zkf_patient: documentData.zkf_patient || "",
        specialite: documentData.specialite || [],
        date_examen: documentData.date_examen || "",
        date_entree: documentData.date_entree || "",
        date_sortie: documentData.date_sortie || "",
        should_be_contacted: documentData.should_be_contacted || false,
      });
      setPatient(null);
      setContact(null);
    }
    setValidationErrors({});
  }, [setIsEditing, documentData, setMetadata, setPatient, setContact]);

  const handleDelete = React.useCallback(() => {
    if (!documentData || isDeleting) return;

    if (
      !window.confirm(
        "Êtes-vous sûr de vouloir supprimer ce document ? Cette action est irréversible."
      )
    ) {
      return;
    }

    setIsDeleting(true);
    snackbar.show("Suppression en cours...", "primary");

    api
      .deleteCompteRendu(documentData.id)
      .then(() => {
        snackbar.show("Document supprimé", "success");
        navigate(-1);
      })
      .catch(() => {
        snackbar.show("Erreur lors de la suppression", "danger");
        setIsDeleting(false);
      });
  }, [documentData, isDeleting, setIsDeleting, snackbar, api, navigate]);

  // Référence pour éviter les rechargements inutiles
  const documentLoadedRef = useRef<Record<string, boolean>>({});
  const [documentLoadError, setDocumentLoadError] = useState<boolean>(false);

  React.useEffect(() => {
    if (!documentId) return;

    // Si ce document a déjà été chargé, ne pas le recharger
    if (documentLoadedRef.current[documentId] === true) {
      return;
    }

    let isMounted = true;
    setIsLoading(true);
    setDocumentData(null);
    setDocumentLoadError(false);

    api
      .getCompteRendu(documentId)
      .then((data: CompteRendu) => {
        if (!isMounted) return;

        // Marquer ce document comme chargé
        documentLoadedRef.current[documentId] = true;

        setDocumentData(data);
        setIsEditing(!data.status);

        setMetadata({
          nom: data.nom || "",
          type_compte_rendu: data.type_compte_rendu || "CONSULTATION",
          zkf_contact: data.zkf_contact || "",
          zkf_patient: data.zkf_patient || "",
          specialite: data.specialite || [],
          date_examen: data.date_examen || "",
          date_entree: data.date_entree || "",
          date_sortie: data.date_sortie || "",
          should_be_contacted: data.should_be_contacted || false,
        });
      })
      .catch((error) => {
        if (!isMounted) return;

        // Marquer ce document comme ayant échoué
        setDocumentLoadError(true);

        console.error("Error loading document data:", error);
        snackbar.show("Erreur lors du chargement des données du document", "danger");
        setIsEditing(false);
      })
      .finally(() => {
        if (isMounted) {
          setIsLoading(false);
        }
      });

    return () => {
      isMounted = false;
    };
  }, [documentId, api, snackbar]);

  // Load file directly
  const fileLoadedRef = useRef<Record<string, boolean>>({});
  const [fileLoadError, setFileLoadError] = useState<boolean>(false);

  React.useEffect(() => {
    // Réinitialiser l'erreur de chargement lorsque l'URL du fichier change
    if (documentData?.examen_file) {
      setFileLoadError(false);
    }
  }, [documentData?.examen_file]);

  React.useEffect(() => {
    // Vérifier si l'URL du fichier existe et n'a pas encore été chargée
    if (!documentData?.examen_file || isDeleting || fileLoadError) {
      return;
    }

    // Si nous avons déjà chargé ce fichier (succès ou échec), ne pas le recharger
    if (fileLoadedRef.current[documentData.examen_file] === true) {
      return;
    }

    // Marquer l'URL comme étant en cours de chargement
    fileLoadedRef.current[documentData.examen_file] = true;

    setIsLoading(true);

    api
      .getMedia(documentData.examen_file)
      .then((blob: Blob | undefined) => {
        if (!blob) {
          setFileLoadError(true);
          setIsLoading(false);
          snackbar.show("Le fichier n'existe pas", "warning");
          return;
        }

        try {
          // Create new file object
          const newFile = new File([blob], documentData?.nom || "document", {
            type: blob.type,
          });

          setFile(newFile);
        } catch (error) {
          console.error("Error creating file:", error);
          snackbar.show("Erreur lors de la création du fichier", "danger");
          setFileLoadError(true);
        } finally {
          setIsLoading(false);
        }
      })
      .catch((error) => {
        // Afficher une seule notification d'erreur
        console.error("Error loading document file:", error);

        // Marquer cette URL comme ayant échoué pour éviter de réessayer
        setFileLoadError(true);

        if (error.message === "Not found") {
          snackbar.show("Le fichier n'existe pas sur le serveur", "warning");
        } else {
          snackbar.show("Erreur lors du chargement du fichier document", "danger");
        }

        setIsLoading(false);
      });

    // Nettoyage au démontage
    return () => {
      // Nettoyage si nécessaire
    };
  }, [documentData?.examen_file, documentData?.nom, isDeleting, fileLoadError, api, snackbar]);

  // Stockage de référence pour les patients déjà chargés
  const patientsCache = useRef<Record<string, Patient>>({});

  React.useEffect(() => {
    const fetchPatient = async () => {
      if (!metadata.zkf_patient) {
        setPatient(null);
        return;
      }

      // Si le patient est déjà dans le cache, l'utiliser
      if (patientsCache.current[metadata.zkf_patient]) {
        setPatient(patientsCache.current[metadata.zkf_patient]);
        return;
      }

      // Si c'est un nouveau patient ou qu'il a changé
      if (!patient || patient.id !== metadata.zkf_patient) {
        try {
          const p = await api.getPatient(metadata.zkf_patient);
          // Stocker dans le cache
          patientsCache.current[metadata.zkf_patient] = p;
          setPatient(p);
        } catch (error) {
          console.error("Error fetching patient:", error);
          setPatient(null);
        }
      }
    };

    fetchPatient();
  }, [metadata.zkf_patient, api, patient]);

  // Stockage de référence pour les contacts déjà chargés
  const contactsCache = useRef<Record<string, Contact>>({});

  React.useEffect(() => {
    const fetchContact = async () => {
      if (!metadata.zkf_contact) {
        setContact(null);
        return;
      }

      // Si le contact est déjà dans le cache, l'utiliser
      if (contactsCache.current[metadata.zkf_contact]) {
        setContact(contactsCache.current[metadata.zkf_contact]);
        return;
      }

      // Si c'est un nouveau contact ou qu'il a changé
      if (!contact || contact.id !== metadata.zkf_contact) {
        try {
          const c = await api.getContact(metadata.zkf_contact);
          // Stocker dans le cache
          contactsCache.current[metadata.zkf_contact] = c;
          setContact(c);
        } catch (error) {
          console.error("Error fetching contact:", error);
          setContact(null);
        }
      }
    };

    fetchContact();
  }, [metadata.zkf_contact, api, contact]);

  // Pour éviter de recharger les spécialités à chaque rendu
  const specialitiesLoadedRef = useRef<boolean>(false);

  React.useEffect(() => {
    // Si les spécialités sont déjà chargées, ne pas les recharger
    if (specialitiesMap.size > 0 || specialitiesLoadedRef.current) {
      return;
    }

    // Marquer comme déjà chargé pour éviter les appels multiples
    specialitiesLoadedRef.current = true;

    let isMounted = true;
    api
      .listUserSpecialites()
      .then((data: Specialite[]) => {
        if (isMounted) {
          const map = new Map<string, Specialite>();
          data.forEach((spec) => map.set(spec.id, spec));
          setSpecialitiesMap(map);
        }
      })
      .catch((error) => {
        console.error("Error loading specialties:", error);
        if (isMounted) {
          snackbar.show("Erreur lors du chargement des spécialités", "danger");
        }
      });
    return () => {
      isMounted = false;
    };
  }, [api, snackbar, specialitiesMap]);

  if (isLoading && !documentData) {
    return (
      <Typography sx={{ p: 4, textAlign: "center" }}>
        Chargement des détails du document...
      </Typography>
    );
  }

  if (!documentData && documentLoadError) {
    return (
      <Typography color="danger" sx={{ p: 4, textAlign: "center" }}>
        Impossible de charger les détails du document.
      </Typography>
    );
  }

  if (!documentData) {
    return (
      <Typography color="danger" sx={{ p: 4, textAlign: "center" }}>
        Aucun document trouvé.
      </Typography>
    );
  }

  return (
    <Stack direction="row" sx={{ m: -2 }}>
      <Box
        sx={{
          width: "450px",
          flexShrink: 0,
          height: "100vh",
          borderRight: "1px solid",
          borderColor: "divider",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Header Section */}
        <Stack direction="row" spacing={2} justifyContent="space-between" sx={{ p: 2 }}>
          <Button
            size="sm"
            variant="plain"
            color="neutral"
            onClick={() => navigate(-1)}
            startDecorator={<ArrowBack sx={{ fontSize: "16px" }} />}
          >
            Revenir à la page précédente
          </Button>
          <Typography level="h4" component="h1">
            Document
          </Typography>
        </Stack>

        <Divider />

        {/* Scrollable Form Section */}
        <Box sx={{ flexGrow: 1, overflow: "auto", p: 2, maxHeight: "calc(100vh - 186px)" }}>
          {!documentData && !isDeleting && (
            <Typography level="body-md" sx={{ my: 2 }}>
              Chargement des métadonnées...
            </Typography>
          )}
          {!documentData && !isLoading && !isDeleting && (
            <Typography color="danger">Impossible de charger les données.</Typography>
          )}

          {documentData && (
            <>
              {isEditing ? (
                <Stack direction="column" spacing={2}>
                  <FormControl required error={!!validationErrors.patient}>
                    <FormLabel>Patient</FormLabel>
                    {patient ? (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Box sx={{ flexGrow: 1 }}>
                          <PatientCard patient={patient} />
                        </Box>
                        <IconButton
                          variant="soft"
                          color="danger"
                          size="sm"
                          onClick={() => {
                            setPatient(null);
                            setMetadata({ ...metadata, zkf_patient: "" });
                          }}
                          aria-label="Retirer le patient lié"
                        >
                          <CloseIcon />
                        </IconButton>
                      </Stack>
                    ) : (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <PatientSelector
                          key={metadata.zkf_patient}
                          value={metadata.zkf_patient}
                          onSelect={(newValue: Patient | null) => {
                            setPatient(newValue);
                            setMetadata({
                              ...metadata,
                              zkf_patient: newValue?.id || "",
                            });
                          }}
                        />

                        <IconButton
                          variant="outlined"
                          color="primary"
                          onClick={() => setOpenCreatePatientModal(true)}
                          aria-label="Ajouter un nouveau patient"
                        >
                          <Add />
                        </IconButton>
                      </Stack>
                    )}
                    {validationErrors.patient && (
                      <FormHelperText>{validationErrors.patient}</FormHelperText>
                    )}
                  </FormControl>

                  {openCreatePatientModal && (
                    <Sheet
                      variant="outlined"
                      sx={{
                        p: 2,
                        my: 2,
                        position: "relative",
                        borderRadius: "sm",
                      }}
                    >
                      <IconButton
                        aria-label="Fermer la création de patient"
                        onClick={() => setOpenCreatePatientModal(false)}
                        sx={{ position: "absolute", top: 8, right: 8 }}
                      >
                        <CloseIcon />
                      </IconButton>
                      <Typography level="title-lg" component="h3" sx={{ mb: 2 }}>
                        Créer un nouveau patient
                      </Typography>
                      <CreatePatient
                        disableRedirect={true}
                        onSuccess={(newPatient: Patient) => {
                          setOpenCreatePatientModal(false);
                          snackbar.show(
                            `Patient '${newPatient.prenom} ${newPatient.nom}' créé avec succès.`,
                            "success"
                          );
                          setMetadata({
                            ...metadata,
                            zkf_patient: newPatient.id,
                          });
                        }}
                      />
                    </Sheet>
                  )}

                  <FormControl required error={!!validationErrors.nom}>
                    <FormLabel>Description</FormLabel>
                    <Input
                      value={metadata.nom}
                      onChange={(e) => setMetadata({ ...metadata, nom: e.target.value })}
                    />
                    {validationErrors.nom && (
                      <FormHelperText>{validationErrors.nom}</FormHelperText>
                    )}
                  </FormControl>

                  <FormControl>
                    <FormLabel>Auteur</FormLabel>
                    {contact ? (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography>
                            {contact.prenom} {contact.nom}
                          </Typography>
                        </Box>
                        <IconButton
                          variant="soft"
                          color="danger"
                          size="sm"
                          onClick={() => {
                            setContact(null);
                            setMetadata({ ...metadata, zkf_contact: "" });
                          }}
                          aria-label="Retirer le contact lié"
                        >
                          <CloseIcon />
                        </IconButton>
                      </Stack>
                    ) : (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <ContactSelector
                          key={metadata.zkf_contact}
                          value={metadata.zkf_contact}
                          onSelect={(newValue: Contact | null) => {
                            setContact(newValue);
                            setMetadata({
                              ...metadata,
                              zkf_contact: newValue?.id || "",
                            });
                          }}
                          patient={patient || undefined}
                        />

                        {/* <IconButton
                          variant="outlined"
                          color="primary"
                          onClick={() => setOpenCreatePatientModal(true)}
                          aria-label="Ajouter un nouveau contact"
                        >
                          <Add />
                        </IconButton> */}
                      </Stack>
                    )}
                  </FormControl>

                  <FormControl>
                    <FormLabel>Type de document</FormLabel>
                    <Select
                      value={metadata.type_compte_rendu}
                      onChange={(_, newValue) =>
                        setMetadata({
                          ...metadata,
                          type_compte_rendu: newValue || "",
                        })
                      }
                    >
                      <Option value="CONSULTATION">Consultation</Option>
                      <Option value="HOSPITALISATION">Hospitalisation</Option>
                      <Option value="EXAMEN_COMPLEMENTAIRE">Examen complémentaire</Option>
                      <Option value="ORDONNANCE_TRAITEMENT">Ordonnance / Traitement</Option>
                      <Option value="BIOLOGIE">Biologie</Option>
                    </Select>
                  </FormControl>

                  <FormControl>
                    <FormLabel>Spécialité</FormLabel>
                    <SpecialiteMultiSelect
                      value={metadata.specialite}
                      onSelect={(newValue: Array<{ id: string }> | null) =>
                        setMetadata({
                          ...metadata,
                          specialite: newValue?.map((s) => s.id) || [],
                        })
                      }
                    />
                  </FormControl>

                  <FormControl orientation="horizontal" sx={{ justifyContent: "space-between" }}>
                    <FormLabel>Doit être contacté ?</FormLabel>
                    <Switch
                      checked={metadata.should_be_contacted || false}
                      onChange={(event) =>
                        setMetadata({
                          ...metadata,
                          should_be_contacted: event.target.checked,
                        })
                      }
                      endDecorator={metadata.should_be_contacted ? "Oui" : "Non"}
                    />
                  </FormControl>

                  {metadata.type_compte_rendu === "HOSPITALISATION" ? (
                    <Stack direction="row" spacing={2}>
                      <FormControl required error={!!validationErrors.date} sx={{ flexGrow: 1 }}>
                        <FormLabel>Date d'entrée</FormLabel>
                        <Input
                          type="date"
                          name="date_entree"
                          value={metadata.date_entree || ""}
                          onChange={(e) =>
                            setMetadata({
                              ...metadata,
                              date_entree: e.target.value,
                            })
                          }
                          slotProps={{
                            input: { max: metadata.date_sortie || undefined },
                          }}
                        />
                        {validationErrors.date && (
                          <FormHelperText>{validationErrors.date}</FormHelperText>
                        )}
                      </FormControl>
                      <FormControl sx={{ flexGrow: 1 }}>
                        <FormLabel>Date de sortie</FormLabel>
                        <Input
                          type="date"
                          name="date_sortie"
                          value={metadata.date_sortie || ""}
                          onChange={(e) =>
                            setMetadata({
                              ...metadata,
                              date_sortie: e.target.value,
                            })
                          }
                          slotProps={{
                            input: { min: metadata.date_entree || undefined },
                          }}
                        />
                      </FormControl>
                    </Stack>
                  ) : (
                    <FormControl required error={!!validationErrors.date} sx={{ flexGrow: 1 }}>
                      <FormLabel>Date de l'examen</FormLabel>
                      <Input
                        type="date"
                        name="date_examen"
                        value={metadata.date_examen || ""}
                        onChange={(e) =>
                          setMetadata({
                            ...metadata,
                            date_examen: e.target.value,
                          })
                        }
                        slotProps={{
                          input: { max: new Date().toISOString().split("T")[0] },
                        }}
                      />
                      {validationErrors.date && (
                        <FormHelperText>{validationErrors.date}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                </Stack>
              ) : (
                <Sheet variant="outlined" sx={{ p: 2, borderRadius: "sm" }}>
                  <Stack spacing={1.5}>
                    <Box>
                      <Typography level="body-sm" fontWeight="lg">
                        Patient
                      </Typography>
                      {patient ? (
                        <PatientCard patient={patient} />
                      ) : (
                        <Typography>Non associé</Typography>
                      )}
                    </Box>
                    <Box>
                      <Typography level="body-sm" fontWeight="lg">
                        Description
                      </Typography>
                      <Typography>{metadata.nom || "-"}</Typography>
                    </Box>
                    <Box>
                      <Typography level="body-sm" fontWeight="lg">
                        Auteur
                      </Typography>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography>
                          {contact
                            ? `${contact.prenom} ${contact.nom}`
                            : documentData?.auteur_nom || "Non trouvé"}{" "}
                          {documentData?.contact_not_found &&
                            !contact &&
                            ` (Non trouvé dans la base)`}
                        </Typography>
                        {contact && (
                          <IconButton
                            size="sm"
                            variant="plain"
                            color="neutral"
                            onClick={() => setIsContactInfoModalOpen(true)}
                            aria-label="Afficher les détails du contact"
                          >
                            <InfoOutlinedIcon />
                          </IconButton>
                        )}
                      </Stack>
                    </Box>
                    <Box>
                      <Typography level="body-sm" fontWeight="lg">
                        Spécialité
                      </Typography>
                      <Typography>
                        {metadata.specialite
                          .map((id) => specialitiesMap.get(id)?.spe)
                          .filter(Boolean)
                          .join(", ") || "-"}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography level="body-sm" fontWeight="lg">
                        Doit être contacté ?
                      </Typography>
                      <Typography>{documentData?.should_be_contacted ? "Oui" : "Non"}</Typography>
                    </Box>
                    {metadata.type_compte_rendu === "HOSPITALISATION" ? (
                      <Stack direction="row" spacing={4}>
                        <Box>
                          <Typography level="body-sm" fontWeight="lg">
                            Date d'entrée
                          </Typography>
                          <Typography>
                            {metadata.date_entree
                              ? new Date(metadata.date_entree + "T00:00:00").toLocaleDateString()
                              : "-"}
                          </Typography>
                        </Box>
                        <Box>
                          <Typography level="body-sm" fontWeight="lg">
                            Date de sortie
                          </Typography>
                          <Typography>
                            {metadata.date_sortie
                              ? new Date(metadata.date_sortie + "T00:00:00").toLocaleDateString()
                              : "-"}
                          </Typography>
                        </Box>
                      </Stack>
                    ) : (
                      <Box>
                        <Typography level="body-sm" fontWeight="lg">
                          Date de l'examen
                        </Typography>
                        <Typography>
                          {metadata.date_examen
                            ? new Date(metadata.date_examen + "T00:00:00").toLocaleDateString()
                            : "-"}
                        </Typography>
                      </Box>
                    )}
                  </Stack>
                </Sheet>
              )}
            </>
          )}
        </Box>

        <Divider />

        {/* Buttons Section */}
        <Stack direction="row" spacing={2} sx={{ p: 2 }}>
          {isEditing ? (
            <Stack direction="row" spacing={1} flexGrow={1} justifyContent="space-between">
              <Button
                color="danger"
                variant="plain"
                startDecorator={<Warning />}
                onClick={handleDelete}
                disabled={isDeleting}
                sx={{ ml: "auto" }}
              >
                Supprimer
              </Button>
              <Stack direction="row" spacing={1} justifyContent="flex-end">
                <Button
                  variant="plain"
                  color="neutral"
                  onClick={handleCancelEdit}
                  disabled={isDeleting || isLoading}
                >
                  Annuler
                </Button>
                <Button
                  color="primary"
                  onClick={handleSave}
                  disabled={isDeleting || isLoading}
                  startDecorator={<Save />}
                >
                  Enregistrer
                </Button>
              </Stack>
            </Stack>
          ) : (
            <Stack direction="row" spacing={1} flexGrow={1} justifyContent="space-between">
              <Button
                color="danger"
                variant="plain"
                startDecorator={<Warning />}
                onClick={handleDelete}
                disabled={isDeleting}
                sx={{ ml: "auto" }}
              >
                Supprimer
              </Button>
              <Button
                color="primary"
                onClick={handleEdit}
                disabled={isDeleting}
                startDecorator={<Edit />}
              >
                Éditer
              </Button>
            </Stack>
          )}
        </Stack>
      </Box>

      <Box
        sx={{
          flexGrow: 1,
          height: "100vh",
          overflow: "hidden",
          p: 2,
          display: "flex",
          flexDirection: "column",
        }}
      >
        {/* Buttons for Print and Download */}
        {file && (
          <Stack direction="row" spacing={2} justifyContent="flex-end" sx={{ mb: 2 }}>
            <Button
              variant="soft"
              color="primary"
              startDecorator={<PrintIcon />}
              onClick={() => {
                // Ouvrir la modal d'impression
                setIsPrintModalOpen(true);
              }}
            >
              Imprimer
            </Button>
            <Button
              variant="soft"
              color="primary"
              startDecorator={<DownloadIcon />}
              onClick={() => {
                // Crée un lien de téléchargement et le clique automatiquement
                if (file) {
                  const link = document.createElement("a");
                  const objectUrl = URL.createObjectURL(file);
                  link.href = objectUrl;
                  link.download = file.name || documentData?.nom || "document";
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  // Nettoyer l'URL pour éviter les fuites de mémoire - délai plus long pour s'assurer que le téléchargement a commencé
                  setTimeout(() => URL.revokeObjectURL(objectUrl), 1000);
                }
              }}
            >
              Télécharger
            </Button>
          </Stack>
        )}

        {/* Document Preview */}
        <Box sx={{ flex: 1, overflow: "auto" }}>
          {isLoading ? (
            <Typography sx={{ p: 4, textAlign: "center" }}>Chargement du document...</Typography>
          ) : file ? (
            <PreviewFile file={file} height="100%" />
          ) : fileLoadError ? (
            <Typography sx={{ p: 4, textAlign: "center", color: "warning.main" }}>
              Le fichier demandé n'existe pas ou est inaccessible
            </Typography>
          ) : (
            <Typography sx={{ p: 4, textAlign: "center" }}>Aucun document à afficher</Typography>
          )}
        </Box>
      </Box>

      {/* Contact Info Modal */}
      {contact && (
        <ContactModalForm
          open={isContactInfoModalOpen}
          onClose={() => setIsContactInfoModalOpen(false)}
          contact={contact}
          patient={undefined}
          onSave={(updatedContact) => {
            setIsContactInfoModalOpen(false);
            // Consider calling fetchDocumentData() again if necessary
          }}
        />
      )}

      {/* Print Modal */}
      {file && file.type === "application/pdf" && (
        <ModalComponent
          open={isPrintModalOpen}
          onClose={() => {
            // Nettoyer les ressources avant de fermer
            setIsPrintModalOpen(false);
          }}
          title="Aperçu avant impression"
          style={{
            width: "90%",
            maxWidth: "1200px",
            height: "90vh",
            maxHeight: "900px",
          }}
        >
          <Stack
            sx={{
              height: "100%",
              width: "100%",
            }}
          >
            <Box
              sx={{
                flex: 1,
                height: "calc(100% - 70px)",
                width: "100%",
                "& .rpv-core__viewer": {
                  // Style the viewer container
                  border: "1px solid rgba(0, 0, 0, 0.1)",
                  borderRadius: "4px",
                  height: "100% !important",
                },
                "& .rpv-core__page-layer": {
                  // Centered pages with improved scaling
                  display: "flex",
                  justifyContent: "center",
                  padding: "0.5rem",
                },
                "& .rpv-core__page": {
                  // Ensure page fits well within the container
                  boxShadow: "0 0 10px rgba(0, 0, 0, 0.15)",
                  margin: "0.5rem 0",
                  borderRadius: "4px",
                },
              }}
            >
              {file ? (
                <PDFPreviewInModal file={file} pdfRef={pdfRef} />
              ) : (
                <Typography sx={{ p: 4, textAlign: "center", color: "white" }}>
                  Aucun document à imprimer
                </Typography>
              )}
            </Box>
          </Stack>
        </ModalComponent>
      )}
    </Stack>
  );
}
