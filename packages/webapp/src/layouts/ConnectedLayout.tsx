// Import helper components for INS status
import React from "react";

import CheckCircleOutline from "@mui/icons-material/CheckCircleOutline";
import Close from "@mui/icons-material/Close";
import ErrorOutline from "@mui/icons-material/ErrorOutline";
import HelpOutline from "@mui/icons-material/HelpOutline";
import HourglassEmpty from "@mui/icons-material/HourglassEmpty";
import WrongLocation from "@mui/icons-material/WrongLocation";
import Box from "@mui/joy/Box";
import Stack from "@mui/joy/Stack";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";

import CurrentPatientBanner from "../components/CurrentPatientBanner/CurrentPatientBanner";
import LoadingPage from "../components/LoadingPage";
import NavigationTracker from "../components/Navigation/NavigationTracker";
import Sidebar from "../components/Sidebar/Sidebar";
import { useApi } from "../contexts/ApiContext";
import { useCurrentPatient } from "../contexts/CurrentPatientContext";
import { useSnackbar } from "../contexts/SnackbarContext";
import { useUser } from "../contexts/UserContext";

const insStatusMap: {
  [key: string]: { text: string; icon: React.ReactElement; color: any };
} = {
  PENDING: {
    text: "Recherche en attente",
    icon: <HourglassEmpty sx={{ fontSize: "16px" }} />,
    color: "neutral",
  },
  IN_PROGRESS: {
    text: "Recherche en cours",
    icon: <HourglassEmpty sx={{ fontSize: "16px" }} />,
    color: "primary",
  },
  SUCCESS: {
    text: "Succès",
    icon: <CheckCircleOutline sx={{ fontSize: "16px" }} />,
    color: "success",
  },
  NOT_ELIGIBLE_INFO: {
    text: "Non éligible",
    icon: <Close sx={{ fontSize: "16px" }} />,
    color: "warning",
  },
  NOT_ELIGIBLE_ENV: {
    text: "Non éligible",
    icon: <WrongLocation sx={{ fontSize: "16px" }} />,
    color: "warning",
  },
  API_ERROR: {
    text: "Non Trouvé",
    icon: <ErrorOutline sx={{ fontSize: "16px" }} />,
    color: "danger",
  },
  DEFAULT: {
    text: "Statut inconnu",
    icon: <HelpOutline sx={{ fontSize: "16px" }} />,
    color: "neutral",
  },
};

export default function ConnectedLayout({ children }: { children: React.ReactNode }) {
  const { loading } = useUser();
  const { currentPatient, setCurrentPatient } = useCurrentPatient();

  const api = useApi();
  const snackbar = useSnackbar();

  const [isResetting, setIsResetting] = React.useState(false);
  const [isVerifying, setIsVerifying] = React.useState(false);
  const pollingIntervalRef = React.useRef<NodeJS.Timeout | null>(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  React.useEffect(() => {
    const clearPollingInterval = () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };

    if (
      currentPatient &&
      currentPatient.id &&
      (currentPatient.ins_lookup_status === "PENDING" ||
        currentPatient.ins_lookup_status === "IN_PROGRESS")
    ) {
      clearPollingInterval();

      pollingIntervalRef.current = setInterval(async () => {
        if (!currentPatient || !currentPatient.id || !api) return;
        try {
          console.log(
            `Polling for patient ${currentPatient.id}, current INS status: ${currentPatient.ins_lookup_status}`
          );
          const updatedPatient = await api.getPatient(currentPatient.id, 1);
          if (updatedPatient) {
            setCurrentPatient(updatedPatient);
            if (
              updatedPatient.ins_lookup_status !== "PENDING" &&
              updatedPatient.ins_lookup_status !== "IN_PROGRESS"
            ) {
              clearPollingInterval();
            }
          }
        } catch (error) {
          console.error("Error polling patient status:", error);
        }
      }, 5000);
    } else {
      clearPollingInterval();
    }

    return () => {
      clearPollingInterval();
    };
  }, [currentPatient, api, setCurrentPatient]);

  const handleResetIns = async () => {
    if (!currentPatient?.id) return;
    setIsResetting(true);
    try {
      await api.resetPatientIns(currentPatient.id);
      snackbar.show("Statut INS réinitialisé", "success");
      const res = await api.getPatient(currentPatient.id, 1);
      if (res) {
        setCurrentPatient(res);
      }
    } catch (e) {
      console.error("Error resetting INS:", e);
      snackbar.show("Erreur lors de la réinitialisation du statut INS", "danger");
    } finally {
      setIsResetting(false);
    }
  };

  const handleVerifyIns = async () => {
    if (!currentPatient?.id) return;
    setIsVerifying(true);
    try {
      // Await the result of the API call
      const verificationResult = await api.verifyPatientIns(currentPatient.id);

      // Check the boolean result
      if (verificationResult === true) {
        snackbar.show(`Vérification INS réussie`, "success");
        // Only refresh patient data if verification was successful
        const res = await api.getPatient(currentPatient.id, 1);
        if (res) {
          setCurrentPatient(res);
        }
      } else {
        // Handle the case where the API returns false (verification failed)
        snackbar.show("La vérification INS a retourné un résultat négatif.", "danger");
        // Optionally refresh patient data here too, or handle differently
        const res = await api.getPatient(currentPatient.id, 1);
        if (res) {
          setCurrentPatient(res);
        }
      }
    } catch (e) {
      console.error("Error verifying INS:", e);
      snackbar.show("Erreur lors de la vérification INS", "danger");
    } finally {
      setIsVerifying(false);
    }
  };

  const insStatusInfo = currentPatient?.ins_lookup_status
    ? insStatusMap[currentPatient.ins_lookup_status] || insStatusMap.DEFAULT
    : insStatusMap.DEFAULT;

  let insTooltipTitle = `Statut INS: ${insStatusInfo.text}`;

  if (currentPatient?.ins_lookup_status === "SUCCESS" && currentPatient.ins) {
    insTooltipTitle = `INS: ${currentPatient.ins}`;
    if (currentPatient.ins_oid?.endsWith(".9")) {
      insTooltipTitle += " (Provisoire)";
    }
    if (!currentPatient.checked_identity || currentPatient.checked_identity === "NONE") {
      insTooltipTitle += "\nIdentité patient non vérifiée.";
    }
  } else if (
    currentPatient &&
    (currentPatient.ins_lookup_status === "API_ERROR" ||
      currentPatient.ins_lookup_status === "NOT_ELIGIBLE_INFO" ||
      currentPatient.ins_lookup_status === "NOT_ELIGIBLE_ENV")
  ) {
    if (currentPatient.ins_lookup_details) {
      insTooltipTitle = `${insStatusInfo.text}: ${currentPatient.ins_lookup_details}`;
    } else {
      insTooltipTitle = `${insStatusInfo.text}: Détails non disponibles`;
    }
  } else if (currentPatient?.ins_lookup_details) {
    // Fallback for any other status that has details unexpectedly
    insTooltipTitle = `${insStatusInfo.text} (Détails: ${currentPatient.ins_lookup_details})`;
  }

  if (loading) return <LoadingPage />;

  return (
    <Stack direction="row">
      {!isMobile && <Sidebar />}
      {/* Composant pour le suivi automatique de la navigation */}
      <NavigationTracker />
      <Box sx={{ position: "relative", flex: 1 }}>
        <Stack>
          {/* Conditionally render CurrentPatientBanner */}
          {!isMobile && (
            <CurrentPatientBanner
              patient={currentPatient}
              handleResetIns={handleResetIns}
              handleVerifyIns={handleVerifyIns}
              isResetting={isResetting}
              isVerifying={isVerifying}
              insStatusInfo={insStatusInfo}
              insTooltipTitle={insTooltipTitle}
            />
          )}
          <Box
            sx={{
              // If isMobile, banner is hidden, so content takes full height.
              // If not isMobile, banner (50px) is shown.
              minHeight: isMobile ? "100vh" : "calc(100vh - 50px)",
              overflow: "auto",
              p: 2,
              // If isMobile, sidebar is hidden, so no marginLeft needed.
              // If not isMobile, sidebar (96px) is shown.
              marginLeft: isMobile ? "0px" : "96px",
            }}
          >
            {children}
          </Box>
        </Stack>
      </Box>
    </Stack>
  );
}
