# Carte Vitale Feature Documentation

## Overview

The Carte Vitale feature allows users to read patient information directly from a French health insurance card (Carte Vitale) using a connected card reader. This feature streamlines patient registration by automatically extracting and parsing patient data from the card.

## Architecture

### Components

1. **CarteVitaleService** (`src/services/CarteVitaleService.ts`)
   - Handles communication with the local carte vitale reader service
   - Provides service availability checking
   - Includes mock functionality for development

2. **CarteVitaleModal** (`src/components/CarteVitale/CarteVitaleModal.tsx`)
   - Displays parsed carte vitale information
   - Allows user to select which patients to create
   - Shows backend errors for duplicate patients

3. **CarteVitaleParser** (`src/utils/CarteVitaleParser.ts`)
   - Parses XML data from the carte vitale service
   - Converts raw data to application-compatible format
   - Validates data integrity

### Data Flow

1. User clicks "Lire carte vitale" button
2. System checks if carte vitale service is available (always true in dev mode)
3. Service reads XML data from the card reader (or uses mock data in dev)
4. Parser converts XML to structured patient data
5. Modal displays information and allows user selection
6. Selected patients are created using existing backend API
7. Backend handles duplicate detection and returns appropriate errors

## Usage

### Prerequisites

- **Production**: Carte vitale reader hardware + local service on `localhost:8082`
- **Development**: No prerequisites - uses mock data

### User Interface

The "Lire carte vitale" button appears in the **PatientSearchAutocomplete** component:
- Patient search dropdowns throughout the app
- "No patients found" states when searching
- Empty search states before typing

### Button States

- **Enabled**: Service is available (always in dev mode)
- **Disabled**: Service is not available (production only)
- **Loading**: Currently reading carte vitale data

### Modal Features

- **Patient Selection**: Choose which persons from the carte vitale to create
- **Error Display**: Shows backend errors for duplicate patients
- **Batch Creation**: Create multiple patients at once
- **Error Handling**: Clear error messages for various failure scenarios

## Development

### Mock Mode

In development mode (`NODE_ENV === 'development'`), the system:
- Always reports the service as available (button enabled)
- Uses mock data instead of the real service
- This allows testing without physical hardware

### Service Endpoints

The carte vitale service should provide:
- `GET /status` - Check if service is running
- `GET /read` - Read carte vitale data (returns XML)

### XML Format Expected

```xml
<?xml version="1.0" encoding="UTF-8"?>
<carteVitale>
  <beneficiaire>
    <nom>MARTIN</nom>
    <prenom>Jean</prenom>
    <dateNaissance>1985-03-15</dateNaissance>
    <sexe>M</sexe>
    <numeroSecu>1850315123456</numeroSecu>
  </beneficiaire>
  <ayantsDroit>
    <ayantDroit>
      <nom>MARTIN</nom>
      <prenom>Marie</prenom>
      <dateNaissance>1987-07-22</dateNaissance>
      <sexe>F</sexe>
      <lien>CONJOINT</lien>
    </ayantDroit>
  </ayantsDroit>
</carteVitale>
```

## Backend Integration

The feature uses the existing patient creation API. The backend automatically:
- Detects duplicate patients during creation
- Returns 400 error with `PATIENT_ALREADY_EXISTS` and existing patient data
- No frontend patient matching logic needed

### Error Response Format

```json
{
  "error": "PATIENT_ALREADY_EXISTS",
  "patient": {
    "id": "7232af28-93b3-405e-9f71-248ab4c7842f",
    "nom": "Martin",
    "prenom": "Jean",
    "dob": "1985-03-15",
    ...
  }
}
```

## Error Handling

### Service Unavailable (Production Only)
- Button is disabled with tooltip explanation
- Background check runs on component mount

### Read Failures
- Timeout errors (5 second limit)
- Connection failures
- Invalid XML format
- Missing required data

### Duplicate Patients
- Backend returns specific error with existing patient details
- Modal displays error and allows user to see existing patient
- Modal stays open to allow user to deselect duplicates

## Security Considerations

- All carte vitale data processing happens in the frontend
- No sensitive data is sent to the backend until user confirmation
- Service communication is limited to localhost only
- XML parsing includes validation to prevent injection attacks

## Future Enhancements

1. **Update Existing**: Allow updating existing patient information
2. **Batch Operations**: Handle multiple carte vitale reads in sequence
3. **Audit Trail**: Log carte vitale usage for compliance
4. **Configuration**: Configurable service endpoint and timeout settings

## Troubleshooting

### Common Issues

1. **Button Disabled (Production)**
   - Check if carte vitale service is running
   - Verify service is accessible on localhost:8082

2. **Read Timeout**
   - Check if carte vitale is properly inserted
   - Verify reader hardware is functioning

3. **Duplicate Patient Errors**
   - Review existing patient information in modal
   - Deselect duplicate patients before creating

### Debug Mode

Enable debug logging by setting `localStorage.debug = 'carte-vitale'` in browser console.
