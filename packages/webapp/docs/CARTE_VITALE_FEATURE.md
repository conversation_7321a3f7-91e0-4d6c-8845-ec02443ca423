# Carte Vitale Feature Documentation

## Overview

The Carte Vitale feature allows users to read patient information directly from a French health insurance card (Carte Vitale) using a connected card reader. This feature streamlines patient registration by automatically extracting and parsing patient data from the card.

## Architecture

### Components

1. **CarteVitaleService** (`src/services/CarteVitaleService.ts`)
   - Handles communication with the local carte vitale reader service
   - Provides service availability checking
   - Includes mock functionality for development

2. **CarteVitaleModal** (`src/components/CarteVitale/CarteVitaleModal.tsx`)
   - Displays parsed carte vitale information
   - Allows user to select which patients to create
   - Shows potential matches with existing patients

3. **CarteVitaleParser** (`src/utils/CarteVitaleParser.ts`)
   - Parses XML data from the carte vitale service
   - Converts raw data to application-compatible format
   - Validates data integrity

4. **PatientMatcher** (`src/utils/PatientMatcher.ts`)
   - Finds potential matches between carte vitale data and existing patients
   - Calculates match scores based on name, birth date, and other criteria
   - Suggests actions (CREATE, UPDATE, CONFIRM)

### Data Flow

1. User clicks "Lire carte vitale" button
2. System checks if carte vitale service is available
3. Service reads XML data from the card reader
4. Parser converts XML to structured patient data
5. Matcher searches for similar existing patients
6. Modal displays information and allows user selection
7. Selected patients are created in the system

## Usage

### Prerequisites

- Carte vitale reader hardware connected to the computer
- Local carte vitale service running on `localhost:8082`
- Valid carte vitale inserted in the reader

### User Interface

The "Lire carte vitale" button appears next to the "Ajouter un patient" button in:
- Patient list page (`/patients`)
- Other patient creation contexts

### Button States

- **Enabled**: Service is available and ready
- **Disabled**: Service is not available or not responding
- **Loading**: Currently reading carte vitale data

### Modal Features

- **Patient Selection**: Choose which persons from the carte vitale to create
- **Match Detection**: Shows similar existing patients with match scores
- **Batch Creation**: Create multiple patients at once
- **Error Handling**: Clear error messages for various failure scenarios

## Development

### Mock Mode

In development mode (`NODE_ENV === 'development'`), the system uses mock data instead of the real service. This allows testing without physical hardware.

### Service Endpoints

The carte vitale service should provide:
- `GET /status` - Check if service is running
- `GET /read` - Read carte vitale data (returns XML)

### XML Format Expected

```xml
<?xml version="1.0" encoding="UTF-8"?>
<carteVitale>
  <beneficiaire>
    <nom>MARTIN</nom>
    <prenom>Jean</prenom>
    <dateNaissance>1985-03-15</dateNaissance>
    <sexe>M</sexe>
    <numeroSecu>1850315123456</numeroSecu>
  </beneficiaire>
  <ayantsDroit>
    <ayantDroit>
      <nom>MARTIN</nom>
      <prenom>Marie</prenom>
      <dateNaissance>1987-07-22</dateNaissance>
      <sexe>F</sexe>
      <lien>CONJOINT</lien>
    </ayantDroit>
  </ayantsDroit>
</carteVitale>
```

## Error Handling

### Service Unavailable
- Button is disabled with tooltip explanation
- Background check runs on component mount

### Read Failures
- Timeout errors (5 second limit)
- Connection failures
- Invalid XML format
- Missing required data

### User Feedback
- Success notifications for created patients
- Error messages with specific failure reasons
- Loading states during operations

## Security Considerations

- All carte vitale data processing happens in the frontend
- No sensitive data is sent to the backend until user confirmation
- Service communication is limited to localhost only
- XML parsing includes validation to prevent injection attacks

## Future Enhancements

1. **Enhanced Matching**: More sophisticated patient matching algorithms
2. **Update Existing**: Allow updating existing patient information
3. **Batch Operations**: Handle multiple carte vitale reads in sequence
4. **Audit Trail**: Log carte vitale usage for compliance
5. **Configuration**: Configurable service endpoint and timeout settings

## Troubleshooting

### Common Issues

1. **Button Disabled**
   - Check if carte vitale service is running
   - Verify service is accessible on localhost:8082
   - Ensure carte vitale reader is connected

2. **Read Timeout**
   - Check if carte vitale is properly inserted
   - Verify reader hardware is functioning
   - Try removing and reinserting the card

3. **Invalid Data**
   - Ensure carte vitale is not damaged
   - Check if card is supported by the reader
   - Verify XML format matches expected structure

### Debug Mode

Enable debug logging by setting `localStorage.debug = 'carte-vitale'` in browser console.
