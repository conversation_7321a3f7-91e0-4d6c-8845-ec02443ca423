import click
import subprocess

from packages.devtools.devops import devops


@click.group()
def cli():
    pass


@cli.command()
@click.argument("files", nargs=-1, type=click.Path(exists=True))
def check_types(files) -> None:
    """
    Checks all the Python types using mypy.
    """
    print("*** mypy ***")
    if files:
        python_files = [f for f in files if f.endswith(".py")]
        if python_files:
            subprocess.run(
                ["poetry", "run", "mypy"] + list(python_files),
                check=True,
            )
    else:
        subprocess.run(
            "poetry run mypy --config-file=mypy.ini",
            shell=True,
        )


@cli.command()
@click.argument("files", nargs=-1, type=click.Path(exists=True))
def check_lint(files) -> None:
    """
    Run flake8 to check for linting issues.
    """
    print("*** flake8 ***")
    if files:
        python_files = [f for f in files if f.endswith(".py")]
        if python_files:
            subprocess.run(
                ["poetry", "run", "flake8"] + list(python_files),
                check=True,
            )
    else:
        subprocess.run(
            "poetry run flake8 .",
            shell=True,
        )


@cli.command()
@click.argument("files", nargs=-1, type=click.Path(exists=True))
def format(files) -> None:
    """
    Format both Python and JavaScript code.
    """
    if files:
        python_files = [f for f in files if f.endswith(".py")]
        js_files = [f for f in files if f.endswith((".js", ".jsx", ".ts", ".tsx"))]

        if python_files:
            print("running black on specific files")
            subprocess.run(
                ["poetry", "run", "black"] + list(python_files),
                check=True,
            )
        if js_files:
            print("running prettier on specific files")
            subprocess.run(
                ["npx", "prettier", "--write"] + list(js_files),
                check=True,
            )
    else:
        print("running black")
        subprocess.run(
            """poetry run black .
            (cd packages/webapp && npx prettier . --write)
            """,
            shell=True,
        )


@cli.command()
def generate_ts_models() -> None:
    """
    Generate TypeScript models from Django models.
    """
    # run this python packages/backend/manage.py spectacular --color --file schema.yml
    # then this npx openapi-typescript schema.yml -o src/models/ts.d.ts
    print("Generating TypeScript models")
    subprocess.run(
        "poetry run python packages/backend/manage.py spectacular --file schema.yml",
        shell=True,
        check=True,
    )
    subprocess.run(
        "npx openapi-typescript schema.yml -o packages/webapp/src/models/ts.d.ts --default-non-nullable=false",
        shell=True,
        check=True,
    )


@cli.command()
def tests() -> None:
    """
    Runs all the tests.
    """
    # print("*** Pytest (backend) ***")
    # subprocess.run(
    #     "poetry run pytest",
    #     shell=True,
    #     check=False,
    # )
    print("*** Cypress (e2e - webapp) ***")
    subprocess.run(
        "(cd packages/webapp && npm run test)",
        shell=True,
        check=False,
    )


cli.add_command(devops)


if __name__ == "__main__":
    cli()
