# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# dotenv
.env

# virtualenv
.venv
venv/
ENV/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

#.pyc extensions
*.pyc

#databases
*.dbg
*.sqlite3
*.dbf

#pictures
*.jpg
*.jpeg

#pdf
*.pdf

#ccam files
babylone/codage/ccam/files


*.sqlite

#node_modules
node_modules

#static files
/babylone/static/admin
/babylone/static/*
*/babylone/static/*
!babylone/
!/babylone/static/css/

#fichiers pickles
*.pkl

#media
/babylone/media/*

babylone/assets/distri/bundle.js
babylone/assets/distri/bundle.js

#pyCharm
/babylone/.idea*

**/.DS_Store

*.idea/workspace.xml
.idea/workspace.xml
/.idea
*/.idea
/packages/backend/media
/packages/backend/static

test.ipynb

# Webapp test
packages/webapp/cypress/screenshots
/packages/backend/codage/ccam/files

#.docx files
*.docx

*.gguf

*.doc

# nlp test
packages/backend/impl/services/test/csv_db

backup.sql
**/.claude/settings.local.json
packages/webapp/src/pages/Preferences/.claude/settings.local.json
/packages/backend/codage/ccam/files
/packages/backend/media


meiflow_speech_to_text.json

# SSL certificates for local development
cert.pem
key.pem
meiflow_speech_to_text.json
/packages/.claude
