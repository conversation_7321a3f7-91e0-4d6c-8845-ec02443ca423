[tool.poetry]
name = "babylone"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
django = "^5.0.6"
celery = "^5.4.0"
django-celery-results = "^2.5.1"
django-celery-beat = "^2.6.0"
django-widget-tweaks = "^1.5.0"
django-taggit = "^5.0.1"
setuptools = "^69.5.1"
xhtml2pdf = "^0.2.15"
pypdf2 = "^3.0.1"
pandas = "^2.2.2"
scikit-learn = "^1.4.2"
beautifulsoup4 = "^4.12.3"
nltk = "^3.8.1"
xlsxwriter = "^3.2.0"
python-barcode = "^0.15.1"
simpledbf = "^0.2.6"
dbfread = "^2.0.7"
sqlalchemy = "^2.0.30"
djangorestframework = "^3.15.1"
django-cors-headers = "^4.3.1"
fastapi = "^0.111.0"
python-keycloak = "^4.2.2"
gunicorn = "^23.0.0"
overrides = "7.7.0"
scipy = "^1.15.0"
psycopg = "^3.2.3"
django-silk = "^5.3.2"
tqdm = "^4.67.1"
networkx = "^3.4.2"
psycopg2-binary = "^2.9.10"
jours-feries-france = "^0.7.0"
pytest = "^8.3.4"
python-docx = "^1.1.2"
google-generativeai = "^0.8.4"
python-dotenv = "^1.0.1"
python-magic = "^0.4.27"
docx2txt = "^0.8"
olefile = "^0.47"
oletools = "^0.60.2"
docx = "^0.2.4"
requests = "^2.32.3"
mistralai = "^1.6.0" # Updated Mistral AI library
google-cloud-speech = "^2.32.0"
ffmpeg-python = "^0.2.0"
redis = "^5.2.1"
django-extensions = "^4.1"
werkzeug = "^3.1.3"
pyopenssl = "^25.1.0"
django-auditlog = "^3.1.2"
openpyxl = "^3.1.5"

[tool.poetry.group.dev.dependencies]
drf-spectacular = "^0.27.2"
mypy = "^1.10.0"
black = "^24.4.2"
flake8 = "^7.0.0"
autoflake = "^2.3.1"
ipykernel = "^6.29.5"
pyright = "^1.1.398"
lefthook = "^1.11.13"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.autoflake]
imports = ["django"]
recursive = true

[tool.pyright]
include = ["."]
exclude = ["node_modules", "build", "dist", "venv", "*/migrations/*"]
pythonVersion = "3.12"
typeCheckingMode = "basic"
reportMissingImports = false
stubPath = "./packages/backend/typings"

# Django-specific issues that need to be suppressed
reportAttributeAccessIssue = false  # Needed for Django models accessing .objects, .DoesNotExist, etc. - these are dynamically added at runtime
reportGeneralTypeIssues = false     # Needed - errors occur in the use of generics and type compatibility
reportInvalidTypeArguments = false  # Needed - errors occur with type arguments for classes like Empty
reportOperatorIssue = false         # Needed - errors with Q objects and other Django QuerySet operators
reportArgumentType = false          # Needed - errors with Django model field arguments like default=False
reportOptionalMemberAccess = false  # Needed - errors with attribute access on potentially None values
reportCallIssue = false             # Needed - errors with calling objects that might not be callable
reportOptionalSubscript = false     # Needed - errors with subscripting potentially None values
reportIndexIssue = false            # Needed - errors with accessing items on objects without __getitem__ like cached_property
