name: Release

on:
  workflow_dispatch:
    inputs:
      version_type:
        description: "Version bump type"
        required: true
        default: "patch"
        type: choice
        options:
          - major
          - minor
          - patch

jobs:
  create-release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
    if: github.ref == 'refs/heads/master'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12"

      - name: Cache Poetry dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/pypoetry
            ~/.cache/pip
          key: ${{ runner.os }}-poetry-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            ${{ runner.os }}-poetry-

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "lts/*"
          cache: "npm"
          cache-dependency-path: packages/webapp/package-lock.json

      - name: Configure Git
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

      - name: Get current version from tags
        id: get_version
        run: |
          # Get the latest tag, or default to 0.0.0 if no tags exist
          LATEST_TAG=$(git tag -l --sort=-version:refname | head -n1)
          if [ -z "$LATEST_TAG" ]; then
            LATEST_TAG="v0.0.0"
          fi
          echo "latest_tag=$LATEST_TAG" >> $GITHUB_OUTPUT

          # Remove 'v' prefix for version calculations
          CURRENT_VERSION=${LATEST_TAG#v}
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT

      - name: Calculate new version
        id: calc_version
        run: |
          CURRENT_VERSION="${{ steps.get_version.outputs.current_version }}"
          VERSION_TYPE="${{ github.event.inputs.version_type }}"

          # Split version into parts
          IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
          MAJOR=${VERSION_PARTS[0]:-0}
          MINOR=${VERSION_PARTS[1]:-0}
          PATCH=${VERSION_PARTS[2]:-0}

          # Increment based on type
          case $VERSION_TYPE in
            major)
              MAJOR=$((MAJOR + 1))
              MINOR=0
              PATCH=0
              ;;
            minor)
              MINOR=$((MINOR + 1))
              PATCH=0
              ;;
            patch)
              PATCH=$((PATCH + 1))
              ;;
          esac

          NEW_VERSION="$MAJOR.$MINOR.$PATCH"
          NEW_TAG="v$NEW_VERSION"
          RELEASE_BRANCH="release/$NEW_TAG"

          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "new_tag=$NEW_TAG" >> $GITHUB_OUTPUT
          echo "release_branch=$RELEASE_BRANCH" >> $GITHUB_OUTPUT

      - name: Create release branch
        run: |
          RELEASE_BRANCH="${{ steps.calc_version.outputs.release_branch }}"
          git checkout -b "$RELEASE_BRANCH"

      - name: Create version.txt file
        run: |
          NEW_VERSION="${{ steps.calc_version.outputs.new_version }}"
          echo "$NEW_VERSION" > version.txt
          git add version.txt
          git commit -m "Release version $NEW_VERSION"

      - name: Create and push tag
        run: |
          NEW_TAG="${{ steps.calc_version.outputs.new_tag }}"
          git tag -a "$NEW_TAG" -m "Release $NEW_TAG"
          git push origin "$NEW_TAG"

      - name: Push release branch
        run: |
          RELEASE_BRANCH="${{ steps.calc_version.outputs.release_branch }}"
          git push origin "$RELEASE_BRANCH"

      # Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Login to GitHub Container Registry
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Build and push backend Docker image
      - name: Build and push backend Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ghcr.io/meiflow/meiflow-backend:${{ steps.calc_version.outputs.new_version }}
            ghcr.io/meiflow/meiflow-backend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Build and push webapp Docker image
      - name: Build and push webapp Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./packages/webapp
          push: true
          tags: |
            ghcr.io/meiflow/meiflow-webapp:${{ steps.calc_version.outputs.new_version }}
            ghcr.io/meiflow/meiflow-webapp:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Create GitHub Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh release create ${{ steps.calc_version.outputs.new_tag }} \
            --title "Release ${{ steps.calc_version.outputs.new_tag }}" \
            --notes "Release ${{ steps.calc_version.outputs.new_tag }}

          This release was created automatically via GitHub Actions.

          **Changes:**
          - Version bump (${{ github.event.inputs.version_type }})

          **Docker Images:**
          - \`ghcr.io/meiflow/meiflow-backend:${{ steps.calc_version.outputs.new_version }}\`
          - \`ghcr.io/meiflow/meiflow-webapp:${{ steps.calc_version.outputs.new_version }}\`

          These images are also tagged as \`latest\`."
