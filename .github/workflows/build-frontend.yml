name: Build Frontend

on:
  push:
    branches:
      - master
      - introduce-new-webapp
  pull_request:
    branches:
      - introduce-new-webapp

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'

    - name: Install dependencies
      run: npm ci
      working-directory: ./packages/webapp

    - name: Build frontend
      run: npm run build
      working-directory: ./packages/webapp

    # - name: Test frontend
    #   run: npm test
    #   working-directory: ./packages/webapp
