---
applyTo: "**/*.py,packages/backend/**"
---

# Django Backend (DRF) Specific Guidelines

These guidelines apply specifically to the Django backend, located in `packages/backend`.

## API Design

- **RESTful Principles:** Design the API following RESTful principles, often using Django REST Framework (DRF).
- **FHIR Compatibility:** Try to stay close to FHIR standards where applicable.
- **Versioning:** Use API versioning (e.g., `/api/v1/`).
- **Endpoints:** Organized into `packages/backend/api/[app_name]`.
- **Serializers:** Define data shapes in `[app_name]/serializers.py`.
- **HTTP Methods:** Use appropriate HTTP methods.
- **Authentication & Permissions:** Handled by Django auth and DRF permission classes.

## Database

- **Database:** PostgreSQL.
- **ORM:** Django ORM.
- **Migrations:** Via Django migrations in `[app_name]/migrations/`.
- **Repository Pattern:** Implemented in `packages/backend/impl/django/repositories/`.

## Services

- **Business Logic:** In `packages/backend/impl/services/`.

## Configuration

- **Settings:** In `packages/backend/babylone/settings/`.

## Testing

- **Framework:** pytest.
- **Tests:** Co-located with apps or in `tests` directories.
