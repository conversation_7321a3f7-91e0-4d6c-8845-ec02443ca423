---
applyTo: "**/*.py,packages/backend/**"
---

# General Python Guidelines

Follow PEP 8. Use `ruff` for formatting. Use type hints and docstrings.

## Django App Structure

- **models.py, views.py, serializers.py, urls.py, admin.py, forms.py, tests/**.
- **impl/services:** business logic.
- **impl/django/repositories:** repository pattern.
- **settings:** in `packages/backend/babylone/settings/`.

## Best Practices

- Use logging, not `print()`.
- Catch specific exceptions.
- Optimize queries with `select_related`/`prefetch_related`.
- Keep views lean; delegate logic to services.
- Write tests for models, views, services.
