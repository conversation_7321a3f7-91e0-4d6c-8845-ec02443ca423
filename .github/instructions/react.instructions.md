---
applyTo: "packages/webapp/**,**/*.tsx,**/*.css,**/*.ts"
---

# React Specific Guidelines

- Use functional components with hooks.
- Components in own dirs. PascalCase names.
- Define TS interfaces for props.

## Styling

- MUI Joy UI & Chakra UI.
- Sass (`.scss`) & CSS.

## Data & State

- API clients in `src/clients`.
- Auth via `@axa-fr/react-oidc`.
- State with React Context.

## Testing

- Cypress in `packages/webapp/cypress/`.
