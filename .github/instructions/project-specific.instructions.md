---
applyTo: "**"
---

# Project Overview

This project is a web application with a Python/Django backend and a React frontend.
It utilizes Docker for containerization and development consistency.

## Project Structure

- **packages/backend:** Django backend application.
- **packages/webapp:** React frontend application.
- **packages/devtools:** Development tools and utilities.
- **.github/workflows:** CI/CD pipelines.
- **Root Files:** `cli`, `Dockerfile`, `compose.yml`, `pyproject.toml`, `package.json`.

## Key Technologies & Practices

- **Backend:** Python, Django, DRF.
- **Frontend:** TypeScript, React.
- **Database:** PostgreSQL.
- **Containerization:** Docker & Docker Compose.
- **CI/CD:** GitHub Actions.
- **Auth:** Keycloak SSO.
- **Deps:** Poetry for backend, npm for frontend.
