---
applyTo: "**/*.ts,**/*.tsx"
---

# General TypeScript Guidelines

Follow Airbnb JS Style Guide. Use Prettier.

- `.tsx` for JSX, `.ts` otherwise.
- Prefer interfaces over `any`.
- Use absolute imports with `@/` if set.

## Structure

- `components/`, `pages/`, `models/`, `clients/`, `hooks/`, `contexts/`, `utils/`, `assets/`, `layouts/`.

## Best Practices

- Avoid class components.
- Use specific types, not `any`.
- Consistent import paths.
