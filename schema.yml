openapi: 3.0.3
info:
  title: ''
  version: 0.0.0
paths:
  /api/agenda/day-events-pdf/:
    get:
      operationId: api_agenda_day_events_pdf_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                content:
                  application/pdf:
                    schema:
                      type: string
                      format: binary
          description: ''
  /api/agenda/items/:
    get:
      operationId: api_agenda_items_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AllCalendarItems'
          description: ''
  /api/agenda/leave/:
    get:
      operationId: api_agenda_leave_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Absence'
          description: ''
    post:
      operationId: api_agenda_leave_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Absence'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Absence'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Absence'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Absence'
          description: ''
  /api/agenda/leave/{id}/:
    get:
      operationId: api_agenda_leave_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Absence'
          description: ''
    put:
      operationId: api_agenda_leave_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Absence'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Absence'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Absence'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Absence'
          description: ''
    patch:
      operationId: api_agenda_leave_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAbsence'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAbsence'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAbsence'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Absence'
          description: ''
    delete:
      operationId: api_agenda_leave_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/agenda/user-event/:
    get:
      operationId: api_agenda_user_event_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserEventList'
          description: ''
    post:
      operationId: api_agenda_user_event_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserEvent'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserEvent'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserEvent'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserEvent'
          description: ''
  /api/agenda/user-event-category/:
    get:
      operationId: api_agenda_user_event_category_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserEventCategory'
          description: ''
    post:
      operationId: api_agenda_user_event_category_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserEventCategory'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserEventCategory'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserEventCategory'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserEventCategory'
          description: ''
  /api/agenda/user-event/{id}/:
    get:
      operationId: api_agenda_user_event_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserEvent'
          description: ''
    put:
      operationId: api_agenda_user_event_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserEvent'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserEvent'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserEvent'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserEvent'
          description: ''
    patch:
      operationId: api_agenda_user_event_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserEvent'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserEvent'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserEvent'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserEvent'
          description: ''
    delete:
      operationId: api_agenda_user_event_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/agenda/vacation/:
    get:
      operationId: api_agenda_vacation_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Vacation'
          description: ''
    post:
      operationId: api_agenda_vacation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Vacation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Vacation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Vacation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Vacation'
          description: ''
  /api/agenda/vacation/{id}/:
    get:
      operationId: api_agenda_vacation_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Vacation'
          description: ''
    put:
      operationId: api_agenda_vacation_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Vacation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Vacation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Vacation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Vacation'
          description: ''
    patch:
      operationId: api_agenda_vacation_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedVacation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedVacation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedVacation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Vacation'
          description: ''
    delete:
      operationId: api_agenda_vacation_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/antecedent/{patient_id}/{specialite}:
    get:
      operationId: api_antecedent_retrieve
      parameters:
      - in: path
        name: patient_id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: api_antecedent_update
      parameters:
      - in: path
        name: patient_id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: api_antecedent_partial_update
      parameters:
      - in: path
        name: patient_id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: api_antecedent_destroy
      parameters:
      - in: path
        name: patient_id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/auditlog/:
    get:
      operationId: api_auditlog_list
      description: List all AuditLog entries.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAuditLogList'
          description: ''
  /api/auditlog/{id}:
    get:
      operationId: api_auditlog_retrieve
      description: Retrieve a single AuditLog entry.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLog'
          description: ''
  /api/ccam/acte:
    get:
      operationId: api_ccam_acte_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedActeList'
          description: ''
  /api/ccam/acte/{acte}:
    get:
      operationId: api_ccam_acte_retrieve
      description: |-
        Create a Dictionary of objects from multiple models/serializers.

        Mixin is expecting the view will have a querylist variable, which is
        a list/tuple of dicts containing, at mininum, a `queryset` key and a
        `serializer_class` key,  as below:

        queryList = [
            {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
            {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
            {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
            ...
        ]

        This mixin returns a dictionary of serialized data separated by object type, e.g.:

        {
            'MyModelA': [
                { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
                { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
                ...
            ],
            'MyModelB': [
                { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
                ...
            ]
            ...
        }
      parameters:
      - in: path
        name: acte
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/ccam/menu:
    get:
      operationId: api_ccam_menu_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/R_menu'
          description: ''
  /api/ccam/menu/{menu_id}:
    get:
      operationId: api_ccam_menu_retrieve
      parameters:
      - in: path
        name: menu_id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/R_menu'
          description: ''
  /api/ccam/modificateur:
    get:
      operationId: api_ccam_modificateur_retrieve
      description: |-
        Create a Dictionary of objects from multiple models/serializers.

        Mixin is expecting the view will have a querylist variable, which is
        a list/tuple of dicts containing, at mininum, a `queryset` key and a
        `serializer_class` key,  as below:

        queryList = [
            {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
            {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
            {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
            ...
        ]

        This mixin returns a dictionary of serialized data separated by object type, e.g.:

        {
            'MyModelA': [
                { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
                { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
                ...
            ],
            'MyModelB': [
                { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
                ...
            ]
            ...
        }
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/ccam/modificateur-list:
    get:
      operationId: api_ccam_modificateur_list_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedR_activite_modificateurList'
          description: ''
    post:
      operationId: api_ccam_modificateur_list_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/R_activite_modificateur'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/R_activite_modificateur'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/R_activite_modificateur'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/R_activite_modificateur'
          description: ''
  /api/ccam/r_activite:
    get:
      operationId: api_ccam_r_activite_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/R_activite'
          description: ''
    post:
      operationId: api_ccam_r_activite_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/R_activite'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/R_activite'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/R_activite'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/R_activite'
          description: ''
  /api/ccam/r_tb_23:
    get:
      operationId: api_ccam_r_tb_23_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedR_tb_23List'
          description: ''
    post:
      operationId: api_ccam_r_tb_23_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/R_tb_23'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/R_tb_23'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/R_tb_23'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/R_tb_23'
          description: ''
  /api/ccam/search:
    get:
      operationId: api_ccam_search_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Acte'
          description: ''
    post:
      operationId: api_ccam_search_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Acte'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Acte'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Acte'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Acte'
          description: ''
  /api/ccam/suggested-actes:
    get:
      operationId: api_ccam_suggested_actes_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Acte'
          description: ''
  /api/comptabilite/avoir/:
    get:
      operationId: api_comptabilite_avoir_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAvoirList'
          description: ''
    post:
      operationId: api_comptabilite_avoir_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Avoir'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Avoir'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Avoir'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Avoir'
          description: ''
  /api/comptabilite/avoir/{id}/:
    get:
      operationId: api_comptabilite_avoir_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Avoir'
          description: ''
    put:
      operationId: api_comptabilite_avoir_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Avoir'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Avoir'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Avoir'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Avoir'
          description: ''
    patch:
      operationId: api_comptabilite_avoir_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAvoir'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAvoir'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAvoir'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Avoir'
          description: ''
    delete:
      operationId: api_comptabilite_avoir_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/comptabilite/code:
    get:
      operationId: api_comptabilite_code_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OperationDevisCcam'
          description: ''
    post:
      operationId: api_comptabilite_code_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationDevisCcam'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OperationDevisCcam'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OperationDevisCcam'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationDevisCcam'
          description: ''
  /api/comptabilite/code/{id}/:
    get:
      operationId: api_comptabilite_code_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationDevisCcam'
          description: ''
    put:
      operationId: api_comptabilite_code_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationDevisCcam'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OperationDevisCcam'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OperationDevisCcam'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationDevisCcam'
          description: ''
    patch:
      operationId: api_comptabilite_code_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOperationDevisCcam'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOperationDevisCcam'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOperationDevisCcam'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationDevisCcam'
          description: ''
    delete:
      operationId: api_comptabilite_code_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/comptabilite/devis/:
    get:
      operationId: api_comptabilite_devis_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDevisList'
          description: ''
    post:
      operationId: api_comptabilite_devis_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Devis'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Devis'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Devis'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Devis'
          description: ''
  /api/comptabilite/devis/{id}/:
    get:
      operationId: api_comptabilite_devis_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Devis'
          description: ''
    put:
      operationId: api_comptabilite_devis_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Devis'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Devis'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Devis'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Devis'
          description: ''
    patch:
      operationId: api_comptabilite_devis_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDevis'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDevis'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDevis'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Devis'
          description: ''
    delete:
      operationId: api_comptabilite_devis_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/comptabilite/facture/:
    get:
      operationId: api_comptabilite_facture_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFactureList'
          description: ''
    post:
      operationId: api_comptabilite_facture_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Facture'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Facture'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Facture'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Facture'
          description: ''
  /api/comptabilite/facture/{id}/:
    get:
      operationId: api_comptabilite_facture_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Facture'
          description: ''
    put:
      operationId: api_comptabilite_facture_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Facture'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Facture'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Facture'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Facture'
          description: ''
    patch:
      operationId: api_comptabilite_facture_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedFacture'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedFacture'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedFacture'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Facture'
          description: ''
    delete:
      operationId: api_comptabilite_facture_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/comptabilite/groupe-facturation/:
    get:
      operationId: api_comptabilite_groupe_facturation_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedGroupeFacturationList'
          description: ''
    post:
      operationId: api_comptabilite_groupe_facturation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupeFacturation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupeFacturation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupeFacturation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupeFacturation'
          description: ''
  /api/comptabilite/groupe-facturation/{id}/:
    get:
      operationId: api_comptabilite_groupe_facturation_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupeFacturation'
          description: ''
    put:
      operationId: api_comptabilite_groupe_facturation_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupeFacturation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GroupeFacturation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GroupeFacturation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupeFacturation'
          description: ''
    patch:
      operationId: api_comptabilite_groupe_facturation_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedGroupeFacturation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedGroupeFacturation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedGroupeFacturation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupeFacturation'
          description: ''
    delete:
      operationId: api_comptabilite_groupe_facturation_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/comptabilite/relance-facture/:
    get:
      operationId: api_comptabilite_relance_facture_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedRelanceFactureList'
          description: ''
    post:
      operationId: api_comptabilite_relance_facture_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RelanceFacture'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RelanceFacture'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RelanceFacture'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RelanceFacture'
          description: ''
  /api/comptabilite/relance-facture/{id}/:
    get:
      operationId: api_comptabilite_relance_facture_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RelanceFacture'
          description: ''
    put:
      operationId: api_comptabilite_relance_facture_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RelanceFacture'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RelanceFacture'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RelanceFacture'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RelanceFacture'
          description: ''
    patch:
      operationId: api_comptabilite_relance_facture_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedRelanceFacture'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedRelanceFacture'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedRelanceFacture'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RelanceFacture'
          description: ''
    delete:
      operationId: api_comptabilite_relance_facture_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/compte-rendu/:
    get:
      operationId: api_compte_rendu_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCompteRenduList'
          description: ''
    post:
      operationId: api_compte_rendu_create
      tags:
      - api
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CompteRendu'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CompteRendu'
          application/json:
            schema:
              $ref: '#/components/schemas/CompteRendu'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompteRendu'
          description: ''
  /api/compte-rendu/{id}:
    get:
      operationId: api_compte_rendu_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompteRendu'
          description: ''
    put:
      operationId: api_compte_rendu_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CompteRendu'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CompteRendu'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompteRendu'
          description: ''
    patch:
      operationId: api_compte_rendu_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCompteRendu'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCompteRendu'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompteRendu'
          description: ''
    delete:
      operationId: api_compte_rendu_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/compte-rendu/duplicate:
    post:
      operationId: api_compte_rendu_duplicate_create
      description: Check for duplicate CompteRenduCorrespondant entries based on the
        provided zkf_patient, auteur and date_examen.
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/consultation/:
    get:
      operationId: api_consultation_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedConsultationList'
          description: ''
    post:
      operationId: api_consultation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Consultation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Consultation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Consultation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultation'
          description: ''
  /api/consultation/{id}:
    get:
      operationId: api_consultation_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultation'
          description: ''
    put:
      operationId: api_consultation_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Consultation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Consultation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Consultation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultation'
          description: ''
    patch:
      operationId: api_consultation_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedConsultation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedConsultation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedConsultation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultation'
          description: ''
    delete:
      operationId: api_consultation_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/consultation/consultation-type/:
    get:
      operationId: api_consultation_consultation_type_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConsultationType'
          description: ''
    post:
      operationId: api_consultation_consultation_type_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultationType'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsultationType'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsultationType'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultationType'
          description: ''
  /api/consultation/consultation-type/{id}:
    get:
      operationId: api_consultation_consultation_type_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultationType'
          description: ''
    put:
      operationId: api_consultation_consultation_type_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultationType'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsultationType'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsultationType'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultationType'
          description: ''
    patch:
      operationId: api_consultation_consultation_type_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedConsultationType'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedConsultationType'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedConsultationType'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultationType'
          description: ''
    delete:
      operationId: api_consultation_consultation_type_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/consultation/generate-consultation-type:
    get:
      operationId: api_consultation_generate_consultation_type_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/consultation/recent-consultations/:
    get:
      operationId: api_consultation_recent_consultations_list
      description: |-
        Vue pour lister les consultations récentes, tous patients confondus.
        Supporte le tri (par défaut par date de consultation décroissante) et la pagination.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedConsultationList'
          description: ''
  /api/consultation/suggested-consultations:
    get:
      operationId: api_consultation_suggested_consultations_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/contact/:
    get:
      operationId: api_contact_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Contact'
          description: ''
    post:
      operationId: api_contact_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Contact'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Contact'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Contact'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
          description: ''
  /api/contact/{id}:
    get:
      operationId: api_contact_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
          description: ''
    put:
      operationId: api_contact_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Contact'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Contact'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Contact'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
          description: ''
    patch:
      operationId: api_contact_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedContact'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedContact'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedContact'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
          description: ''
    delete:
      operationId: api_contact_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/contact/adresse:
    get:
      operationId: api_contact_adresse_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Adresse'
          description: ''
    post:
      operationId: api_contact_adresse_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Adresse'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Adresse'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Adresse'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Adresse'
          description: ''
  /api/contact/adresse/{id}:
    get:
      operationId: api_contact_adresse_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Adresse'
          description: ''
    put:
      operationId: api_contact_adresse_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Adresse'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Adresse'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Adresse'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Adresse'
          description: ''
    patch:
      operationId: api_contact_adresse_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAdresse'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAdresse'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAdresse'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Adresse'
          description: ''
    delete:
      operationId: api_contact_adresse_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/contact/correspondant:
    get:
      operationId: api_contact_correspondant_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Correspondant'
          description: ''
    post:
      operationId: api_contact_correspondant_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Correspondant'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Correspondant'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Correspondant'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Correspondant'
          description: ''
  /api/contact/correspondant-patient:
    get:
      operationId: api_contact_correspondant_patient_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CorrespondantPatient'
          description: ''
  /api/contact/correspondant/{id}:
    get:
      operationId: api_contact_correspondant_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Correspondant'
          description: ''
    put:
      operationId: api_contact_correspondant_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Correspondant'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Correspondant'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Correspondant'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Correspondant'
          description: ''
    patch:
      operationId: api_contact_correspondant_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCorrespondant'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCorrespondant'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCorrespondant'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Correspondant'
          description: ''
    delete:
      operationId: api_contact_correspondant_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/contact/email:
    get:
      operationId: api_contact_email_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Email'
          description: ''
    post:
      operationId: api_contact_email_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Email'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Email'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Email'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Email'
          description: ''
  /api/contact/email/{id}:
    get:
      operationId: api_contact_email_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Email'
          description: ''
    put:
      operationId: api_contact_email_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Email'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Email'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Email'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Email'
          description: ''
    patch:
      operationId: api_contact_email_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmail'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmail'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmail'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Email'
          description: ''
    delete:
      operationId: api_contact_email_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/contact/telephone:
    get:
      operationId: api_contact_telephone_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Telephone'
          description: ''
    post:
      operationId: api_contact_telephone_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Telephone'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Telephone'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Telephone'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telephone'
          description: ''
  /api/contact/telephone/{id}:
    get:
      operationId: api_contact_telephone_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telephone'
          description: ''
    put:
      operationId: api_contact_telephone_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Telephone'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Telephone'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Telephone'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telephone'
          description: ''
    patch:
      operationId: api_contact_telephone_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTelephone'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTelephone'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTelephone'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Telephone'
          description: ''
    delete:
      operationId: api_contact_telephone_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/contact/ville:
    get:
      operationId: api_contact_ville_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Ville'
          description: ''
    post:
      operationId: api_contact_ville_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Ville'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Ville'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Ville'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ville'
          description: ''
  /api/document-libre/:
    get:
      operationId: api_document_libre_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DocumentLibre'
          description: ''
    post:
      operationId: api_document_libre_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentLibre'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentLibre'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentLibre'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibre'
          description: ''
  /api/document-libre/{id}:
    get:
      operationId: api_document_libre_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibre'
          description: ''
    put:
      operationId: api_document_libre_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentLibre'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentLibre'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentLibre'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibre'
          description: ''
    patch:
      operationId: api_document_libre_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDocumentLibre'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDocumentLibre'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDocumentLibre'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibre'
          description: ''
    delete:
      operationId: api_document_libre_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/document-libre/template:
    get:
      operationId: api_document_libre_template_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DocumentLibreTemplate'
          description: ''
    post:
      operationId: api_document_libre_template_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentLibreTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentLibreTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentLibreTemplate'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibreTemplate'
          description: ''
  /api/document-libre/template/{id}:
    get:
      operationId: api_document_libre_template_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibreTemplate'
          description: ''
    put:
      operationId: api_document_libre_template_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentLibreTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentLibreTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentLibreTemplate'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibreTemplate'
          description: ''
    patch:
      operationId: api_document_libre_template_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDocumentLibreTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDocumentLibreTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDocumentLibreTemplate'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLibreTemplate'
          description: ''
    delete:
      operationId: api_document_libre_template_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/hospitalisation/:
    get:
      operationId: api_hospitalisation_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedHospitalisationList'
          description: ''
    post:
      operationId: api_hospitalisation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Hospitalisation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Hospitalisation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Hospitalisation'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Hospitalisation'
          description: ''
  /api/hospitalisation/{id}:
    get:
      operationId: api_hospitalisation_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Hospitalisation'
          description: ''
    put:
      operationId: api_hospitalisation_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Hospitalisation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Hospitalisation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Hospitalisation'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Hospitalisation'
          description: ''
    patch:
      operationId: api_hospitalisation_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedHospitalisation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedHospitalisation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedHospitalisation'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Hospitalisation'
          description: ''
    delete:
      operationId: api_hospitalisation_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      responses:
        '204':
          description: No response body
  /api/hospitalisation/{id}/generate-crh:
    get:
      operationId: api_hospitalisation_generate_crh_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/hospitalisation/{id}/suivi:
    get:
      operationId: api_hospitalisation_suivi_list
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Suivi'
          description: ''
    post:
      operationId: api_hospitalisation_suivi_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Suivi'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Suivi'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Suivi'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Suivi'
          description: ''
  /api/hospitalisation/{id}/suivi/{suivi_pk}:
    get:
      operationId: api_hospitalisation_suivi_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: suivi_pk
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Suivi'
          description: ''
    put:
      operationId: api_hospitalisation_suivi_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: suivi_pk
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Suivi'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Suivi'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Suivi'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Suivi'
          description: ''
    patch:
      operationId: api_hospitalisation_suivi_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: suivi_pk
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSuivi'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSuivi'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSuivi'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Suivi'
          description: ''
    delete:
      operationId: api_hospitalisation_suivi_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: suivi_pk
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/hospitalisation/{zkf_patient}/create:
    get:
      operationId: api_hospitalisation_create_retrieve
      description: |-
        Create a Dictionary of objects from multiple models/serializers.

        Mixin is expecting the view will have a querylist variable, which is
        a list/tuple of dicts containing, at mininum, a `queryset` key and a
        `serializer_class` key,  as below:

        queryList = [
            {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
            {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
            {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
            ...
        ]

        This mixin returns a dictionary of serialized data separated by object type, e.g.:

        {
            'MyModelA': [
                { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
                { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
                ...
            ],
            'MyModelB': [
                { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
                ...
            ]
            ...
        }
      parameters:
      - in: path
        name: zkf_patient
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      responses:
        '200':
          description: No response body
  /api/hospitalisation/consigne-sortie/:
    get:
      operationId: api_hospitalisation_consigne_sortie_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConsignesSortie'
          description: ''
    post:
      operationId: api_hospitalisation_consigne_sortie_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsignesSortie'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsignesSortie'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsignesSortie'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsignesSortie'
          description: ''
  /api/hospitalisation/consigne-sortie/{id}:
    get:
      operationId: api_hospitalisation_consigne_sortie_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsignesSortie'
          description: ''
    put:
      operationId: api_hospitalisation_consigne_sortie_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsignesSortie'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsignesSortie'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsignesSortie'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsignesSortie'
          description: ''
    patch:
      operationId: api_hospitalisation_consigne_sortie_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedConsignesSortie'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedConsignesSortie'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedConsignesSortie'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsignesSortie'
          description: ''
    delete:
      operationId: api_hospitalisation_consigne_sortie_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/hospitalisation/generate-consigne-sortie:
    get:
      operationId: api_hospitalisation_generate_consigne_sortie_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/hospitalisation/service:
    get:
      operationId: api_hospitalisation_service_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Hospitalisation'
          description: ''
  /api/intervenant/:
    get:
      operationId: api_intervenant_retrieve
      description: |-
        Create a Dictionary of objects from multiple models/serializers.

        Mixin is expecting the view will have a querylist variable, which is
        a list/tuple of dicts containing, at mininum, a `queryset` key and a
        `serializer_class` key,  as below:

        queryList = [
            {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
            {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
            {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
            ...
        ]

        This mixin returns a dictionary of serialized data separated by object type, e.g.:

        {
            'MyModelA': [
                { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
                { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
                ...
            ],
            'MyModelB': [
                { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
                ...
            ]
            ...
        }
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/media/{url}:
    get:
      operationId: api_media_retrieve
      description: Serve a file from the media directory
      parameters:
      - in: path
        name: url
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/notification/:
    get:
      operationId: api_notification_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedNotificationList'
          description: ''
    post:
      operationId: api_notification_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Notification'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Notification'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Notification'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
  /api/notification/{id}:
    get:
      operationId: api_notification_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
    put:
      operationId: api_notification_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Notification'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Notification'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Notification'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
    patch:
      operationId: api_notification_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedNotification'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedNotification'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedNotification'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
    delete:
      operationId: api_notification_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/notification/read:
    post:
      operationId: api_notification_read_create
      description: |-
        Delete all notifications read for the user
        #
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/operation/:
    get:
      operationId: api_operation_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Operation'
          description: ''
    post:
      operationId: api_operation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Operation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Operation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Operation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Operation'
          description: ''
  /api/operation-templates/:
    get:
      operationId: api_operation_templates_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/operation-templates/{specialite}:
    get:
      operationId: api_operation_templates_retrieve_2
      parameters:
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/operation-templates/{specialite}/{template_name}:
    get:
      operationId: api_operation_templates_retrieve_3
      parameters:
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      - in: path
        name: template_name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: api_operation_templates_create
      parameters:
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      - in: path
        name: template_name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '201':
          description: No response body
  /api/operation-templates/{specialite}/{template_name}/{id}:
    get:
      operationId: api_operation_templates_retrieve_4
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      - in: path
        name: template_name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: api_operation_templates_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      - in: path
        name: template_name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: api_operation_templates_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      - in: path
        name: template_name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: api_operation_templates_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: specialite
        schema:
          type: string
        required: true
      - in: path
        name: template_name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/operation/{operation_pk}/{cro_type_pk}/generate-cro:
    get:
      operationId: api_operation_generate_cro_retrieve
      parameters:
      - in: path
        name: cro_type_pk
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: operation_pk
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/operation/{operation_pk}/peri-op:
    get:
      operationId: api_operation_peri_op_retrieve
      description: |-
        Create a Dictionary of objects from multiple models/serializers.

        Mixin is expecting the view will have a querylist variable, which is
        a list/tuple of dicts containing, at mininum, a `queryset` key and a
        `serializer_class` key,  as below:

        queryList = [
            {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
            {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
            {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
            ...
        ]

        This mixin returns a dictionary of serialized data separated by object type, e.g.:

        {
            'MyModelA': [
                { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
                { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
                ...
            ],
            'MyModelB': [
                { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
                ...
            ]
            ...
        }
      parameters:
      - in: path
        name: operation_pk
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/operation/{id}:
    get:
      operationId: api_operation_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Operation'
          description: ''
    put:
      operationId: api_operation_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Operation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Operation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Operation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Operation'
          description: ''
    patch:
      operationId: api_operation_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOperation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOperation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOperation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Operation'
          description: ''
    delete:
      operationId: api_operation_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/operation/anesthesie-type:
    get:
      operationId: api_operation_anesthesie_type_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Operation'
          description: ''
  /api/operation/appareil-scopie:
    get:
      operationId: api_operation_appareil_scopie_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MatrielIrradiation'
          description: ''
    post:
      operationId: api_operation_appareil_scopie_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MatrielIrradiation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MatrielIrradiation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MatrielIrradiation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MatrielIrradiation'
          description: ''
  /api/operation/appareil-scopie/{id}:
    get:
      operationId: api_operation_appareil_scopie_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MatrielIrradiation'
          description: ''
    put:
      operationId: api_operation_appareil_scopie_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MatrielIrradiation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MatrielIrradiation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MatrielIrradiation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MatrielIrradiation'
          description: ''
    patch:
      operationId: api_operation_appareil_scopie_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedMatrielIrradiation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedMatrielIrradiation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedMatrielIrradiation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MatrielIrradiation'
          description: ''
    delete:
      operationId: api_operation_appareil_scopie_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/operation/complications-post-op:
    get:
      operationId: api_operation_complications_post_op_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/operation/complications-post-op/{id}:
    get:
      operationId: api_operation_complications_post_op_retrieve_2
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: api_operation_complications_post_op_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: api_operation_complications_post_op_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: api_operation_complications_post_op_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/operation/contrast:
    get:
      operationId: api_operation_contrast_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Contraste'
          description: ''
    post:
      operationId: api_operation_contrast_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Contraste'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Contraste'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Contraste'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contraste'
          description: ''
  /api/operation/contrast/{id}:
    get:
      operationId: api_operation_contrast_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contraste'
          description: ''
    put:
      operationId: api_operation_contrast_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Contraste'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Contraste'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Contraste'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contraste'
          description: ''
    patch:
      operationId: api_operation_contrast_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedContraste'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedContraste'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedContraste'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contraste'
          description: ''
    delete:
      operationId: api_operation_contrast_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/operation/cro-type:
    get:
      operationId: api_operation_cro_type_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CroType'
          description: ''
    post:
      operationId: api_operation_cro_type_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CroType'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CroType'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CroType'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CroType'
          description: ''
  /api/operation/cro-type/{id}:
    get:
      operationId: api_operation_cro_type_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CroType'
          description: ''
    put:
      operationId: api_operation_cro_type_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CroType'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CroType'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CroType'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CroType'
          description: ''
    patch:
      operationId: api_operation_cro_type_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCroType'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCroType'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCroType'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CroType'
          description: ''
    delete:
      operationId: api_operation_cro_type_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/operation/irradiation:
    get:
      operationId: api_operation_irradiation_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Irradiation'
          description: ''
    post:
      operationId: api_operation_irradiation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Irradiation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Irradiation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Irradiation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Irradiation'
          description: ''
  /api/operation/irradiation/{id}:
    get:
      operationId: api_operation_irradiation_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Irradiation'
          description: ''
    put:
      operationId: api_operation_irradiation_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Irradiation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Irradiation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Irradiation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Irradiation'
          description: ''
    patch:
      operationId: api_operation_irradiation_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedIrradiation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedIrradiation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedIrradiation'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Irradiation'
          description: ''
    delete:
      operationId: api_operation_irradiation_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/operation/list-fields:
    get:
      operationId: api_operation_list_fields_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/operation/operation-model-fields:
    get:
      operationId: api_operation_operation_model_fields_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/ordonnance/:
    get:
      operationId: api_ordonnance_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedOrdonnanceList'
          description: ''
    post:
      operationId: api_ordonnance_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Ordonnance'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Ordonnance'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Ordonnance'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ordonnance'
          description: ''
  /api/ordonnance/{id}:
    get:
      operationId: api_ordonnance_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ordonnance'
          description: ''
    put:
      operationId: api_ordonnance_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Ordonnance'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Ordonnance'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Ordonnance'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ordonnance'
          description: ''
    patch:
      operationId: api_ordonnance_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOrdonnance'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOrdonnance'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOrdonnance'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ordonnance'
          description: ''
    delete:
      operationId: api_ordonnance_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/ordonnance/categorie:
    get:
      operationId: api_ordonnance_categorie_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CategorieOrdonnance'
          description: ''
    post:
      operationId: api_ordonnance_categorie_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategorieOrdonnance'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CategorieOrdonnance'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CategorieOrdonnance'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorieOrdonnance'
          description: ''
  /api/ordonnance/template/:
    get:
      operationId: api_ordonnance_template_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrdonnanceTemplate'
          description: ''
    post:
      operationId: api_ordonnance_template_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrdonnanceTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OrdonnanceTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OrdonnanceTemplate'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrdonnanceTemplate'
          description: ''
  /api/ordonnance/template/{id}:
    get:
      operationId: api_ordonnance_template_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrdonnanceTemplate'
          description: ''
    put:
      operationId: api_ordonnance_template_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrdonnanceTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OrdonnanceTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OrdonnanceTemplate'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrdonnanceTemplate'
          description: ''
    patch:
      operationId: api_ordonnance_template_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOrdonnanceTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOrdonnanceTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOrdonnanceTemplate'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrdonnanceTemplate'
          description: ''
    delete:
      operationId: api_ordonnance_template_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/patient/:
    get:
      operationId: api_patient_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPatientList'
          description: ''
    post:
      operationId: api_patient_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Patient'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Patient'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Patient'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'
          description: ''
  /api/patient/{id}/:
    get:
      operationId: api_patient_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'
          description: ''
    put:
      operationId: api_patient_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Patient'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Patient'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Patient'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'
          description: ''
    patch:
      operationId: api_patient_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPatient'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPatient'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPatient'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'
          description: ''
    delete:
      operationId: api_patient_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/patient/{id}/consultations/:
    get:
      operationId: api_patient_consultations_list
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPatientList'
          description: ''
  /api/patient/{id}/events/:
    get:
      operationId: api_patient_events_list
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AllCalendarItems'
          description: ''
  /api/patient/{id}/reset_ins/:
    post:
      operationId: api_patient_reset_ins_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/patient/{id}/verify_ins/:
    post:
      operationId: api_patient_verify_ins_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/patient/coverage/:
    get:
      operationId: api_patient_coverage_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CouvertureMaladie'
          description: ''
    post:
      operationId: api_patient_coverage_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CouvertureMaladie'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CouvertureMaladie'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CouvertureMaladie'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CouvertureMaladie'
          description: ''
  /api/patient/model/fields/:
    get:
      operationId: api_patient_model_fields_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/patient/search/:
    get:
      operationId: api_patient_search_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPatientList'
          description: ''
    post:
      operationId: api_patient_search_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Patient'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Patient'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Patient'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Patient'
          description: ''
  /api/print-manager/:
    get:
      operationId: api_print_manager_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GestionnaireImpression'
          description: ''
    post:
      operationId: api_print_manager_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GestionnaireImpression'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GestionnaireImpression'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GestionnaireImpression'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GestionnaireImpression'
          description: ''
    delete:
      operationId: api_print_manager_destroy
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/print-manager/{id}:
    get:
      operationId: api_print_manager_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GestionnaireImpression'
          description: ''
    put:
      operationId: api_print_manager_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GestionnaireImpression'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GestionnaireImpression'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/GestionnaireImpression'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GestionnaireImpression'
          description: ''
    patch:
      operationId: api_print_manager_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedGestionnaireImpression'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedGestionnaireImpression'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedGestionnaireImpression'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GestionnaireImpression'
          description: ''
    delete:
      operationId: api_print_manager_destroy_2
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/print-manager/compte-rendus/consultation:
    get:
      operationId: api_print_manager_compte_rendus_consultation_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedExtendedConsultationList'
          description: ''
    post:
      operationId: api_print_manager_compte_rendus_consultation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExtendedConsultation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ExtendedConsultation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ExtendedConsultation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtendedConsultation'
          description: ''
  /api/print-manager/compte-rendus/hospitalisation:
    get:
      operationId: api_print_manager_compte_rendus_hospitalisation_list
      parameters:
      - name: limit
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: offset
        required: false
        in: query
        description: The initial index from which to return the results.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedExtendedHospitalisationList'
          description: ''
    post:
      operationId: api_print_manager_compte_rendus_hospitalisation_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExtendedHospitalisation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ExtendedHospitalisation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ExtendedHospitalisation'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExtendedHospitalisation'
          description: ''
  /api/print-manager/print:
    get:
      operationId: api_print_manager_print_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: api_print_manager_print_create
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/print-manager/print-selected:
    get:
      operationId: api_print_manager_print_selected_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
    post:
      operationId: api_print_manager_print_selected_create
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/protocole/:
    get:
      operationId: api_protocole_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Protocole'
          description: ''
    post:
      operationId: api_protocole_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Protocole'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Protocole'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Protocole'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Protocole'
          description: ''
  /api/protocole/{id}/:
    get:
      operationId: api_protocole_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Protocole'
          description: ''
    put:
      operationId: api_protocole_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Protocole'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Protocole'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Protocole'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Protocole'
          description: ''
    patch:
      operationId: api_protocole_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedProtocole'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedProtocole'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedProtocole'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Protocole'
          description: ''
    delete:
      operationId: api_protocole_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/protocole/answerformfield/:
    get:
      operationId: api_protocole_answerformfield_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAnswerFormFieldList'
          description: ''
    post:
      operationId: api_protocole_answerformfield_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnswerFormField'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AnswerFormField'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AnswerFormField'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnswerFormField'
          description: ''
  /api/protocole/answerformfield/{id}/:
    get:
      operationId: api_protocole_answerformfield_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnswerFormField'
          description: ''
    put:
      operationId: api_protocole_answerformfield_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnswerFormField'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AnswerFormField'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AnswerFormField'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnswerFormField'
          description: ''
    patch:
      operationId: api_protocole_answerformfield_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAnswerFormField'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAnswerFormField'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAnswerFormField'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnswerFormField'
          description: ''
    delete:
      operationId: api_protocole_answerformfield_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/protocole/form/:
    get:
      operationId: api_protocole_form_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFormList'
          description: ''
    post:
      operationId: api_protocole_form_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Form'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Form'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Form'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Form'
          description: ''
  /api/protocole/form/{id}/:
    get:
      operationId: api_protocole_form_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Form'
          description: ''
    put:
      operationId: api_protocole_form_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Form'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Form'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Form'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Form'
          description: ''
    patch:
      operationId: api_protocole_form_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedForm'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedForm'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedForm'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Form'
          description: ''
    delete:
      operationId: api_protocole_form_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/protocole/formfield/:
    get:
      operationId: api_protocole_formfield_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFormFieldList'
          description: ''
    post:
      operationId: api_protocole_formfield_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormField'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FormField'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FormField'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormField'
          description: ''
  /api/protocole/formfield/{id}/:
    get:
      operationId: api_protocole_formfield_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormField'
          description: ''
    put:
      operationId: api_protocole_formfield_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormField'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FormField'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FormField'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormField'
          description: ''
    patch:
      operationId: api_protocole_formfield_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedFormField'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedFormField'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedFormField'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormField'
          description: ''
    delete:
      operationId: api_protocole_formfield_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/protocole/formtemplate/:
    get:
      operationId: api_protocole_formtemplate_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFormTemplateList'
          description: ''
    post:
      operationId: api_protocole_formtemplate_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FormTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FormTemplate'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormTemplate'
          description: ''
  /api/protocole/formtemplates/{id}/:
    get:
      operationId: api_protocole_formtemplates_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormTemplate'
          description: ''
    put:
      operationId: api_protocole_formtemplates_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FormTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FormTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FormTemplate'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormTemplate'
          description: ''
    patch:
      operationId: api_protocole_formtemplates_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedFormTemplate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedFormTemplate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedFormTemplate'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormTemplate'
          description: ''
    delete:
      operationId: api_protocole_formtemplates_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/settings/model-fields:
    get:
      operationId: api_settings_model_fields_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/tag/:
    get:
      operationId: api_tag_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FullTag'
          description: ''
    post:
      operationId: api_tag_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FullTag'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FullTag'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FullTag'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FullTag'
          description: ''
  /api/token-auth/:
    post:
      operationId: api_token_auth_create
      tags:
      - api
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AuthToken'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AuthToken'
          application/json:
            schema:
              $ref: '#/components/schemas/AuthToken'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthToken'
          description: ''
  /api/user/:
    get:
      operationId: api_user_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: api_user_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/user/{id}/:
    get:
      operationId: api_user_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: api_user_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: api_user_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUser'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    delete:
      operationId: api_user_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/user/{user_id}/intervenant:
    get:
      operationId: api_user_intervenant_retrieve_2
      parameters:
      - in: path
        name: user_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Intervenant'
          description: ''
    put:
      operationId: api_user_intervenant_update_2
      parameters:
      - in: path
        name: user_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Intervenant'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Intervenant'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Intervenant'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Intervenant'
          description: ''
    patch:
      operationId: api_user_intervenant_partial_update_2
      parameters:
      - in: path
        name: user_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedIntervenant'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedIntervenant'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedIntervenant'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Intervenant'
          description: ''
    delete:
      operationId: api_user_intervenant_destroy_2
      parameters:
      - in: path
        name: user_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/user/entete-document:
    get:
      operationId: api_user_entete_document_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EnteteDocument'
          description: ''
    post:
      operationId: api_user_entete_document_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnteteDocument'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EnteteDocument'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EnteteDocument'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnteteDocument'
          description: ''
  /api/user/entete-document/{id}:
    get:
      operationId: api_user_entete_document_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnteteDocument'
          description: ''
    put:
      operationId: api_user_entete_document_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnteteDocument'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EnteteDocument'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EnteteDocument'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnteteDocument'
          description: ''
    patch:
      operationId: api_user_entete_document_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEnteteDocument'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEnteteDocument'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEnteteDocument'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnteteDocument'
          description: ''
    delete:
      operationId: api_user_entete_document_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/user/groups:
    get:
      operationId: api_user_groups_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Group'
          description: ''
  /api/user/intervenant:
    get:
      operationId: api_user_intervenant_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Intervenant'
          description: ''
  /api/user/intervenant/{id}:
    get:
      operationId: api_user_intervenant_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Intervenant'
          description: ''
    put:
      operationId: api_user_intervenant_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Intervenant'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Intervenant'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Intervenant'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Intervenant'
          description: ''
    patch:
      operationId: api_user_intervenant_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedIntervenant'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedIntervenant'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedIntervenant'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Intervenant'
          description: ''
    delete:
      operationId: api_user_intervenant_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/user/intervenants-event:
    get:
      operationId: api_user_intervenants_event_retrieve
      description: |-
        Create a Dictionary of objects from multiple models/serializers.

        Mixin is expecting the view will have a querylist variable, which is
        a list/tuple of dicts containing, at mininum, a `queryset` key and a
        `serializer_class` key,  as below:

        queryList = [
            {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
            {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
            {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
            ...
        ]

        This mixin returns a dictionary of serialized data separated by object type, e.g.:

        {
            'MyModelA': [
                { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
                { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
                ...
            ],
            'MyModelB': [
                { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
                ...
            ]
            ...
        }
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/user/linked-users:
    get:
      operationId: api_user_linked_users_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
          description: ''
  /api/user/me:
    get:
      operationId: api_user_me_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/user/notification-choices/:
    get:
      operationId: api_user_notification_choices_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/user/preferences-user/{id}:
    get:
      operationId: api_user_preferences_user_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreferenceUserSerialyzer'
          description: ''
    put:
      operationId: api_user_preferences_user_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreferenceUserSerialyzer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PreferenceUserSerialyzer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PreferenceUserSerialyzer'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreferenceUserSerialyzer'
          description: ''
    patch:
      operationId: api_user_preferences_user_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPreferenceUserSerialyzer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPreferenceUserSerialyzer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPreferenceUserSerialyzer'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreferenceUserSerialyzer'
          description: ''
  /api/user/profile-user/{id}:
    get:
      operationId: api_user_profile_user_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileUserSerialyzer'
          description: ''
    put:
      operationId: api_user_profile_user_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProfileUserSerialyzer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProfileUserSerialyzer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProfileUserSerialyzer'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileUserSerialyzer'
          description: ''
    patch:
      operationId: api_user_profile_user_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedProfileUserSerialyzer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedProfileUserSerialyzer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedProfileUserSerialyzer'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileUserSerialyzer'
          description: ''
  /api/user/profile/{id}:
    get:
      operationId: api_user_profile_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileUserSerialyzer'
          description: ''
    put:
      operationId: api_user_profile_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProfileUserSerialyzer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProfileUserSerialyzer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProfileUserSerialyzer'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileUserSerialyzer'
          description: ''
    patch:
      operationId: api_user_profile_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedProfileUserSerialyzer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedProfileUserSerialyzer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedProfileUserSerialyzer'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileUserSerialyzer'
          description: ''
  /api/user/service:
    get:
      operationId: api_user_service_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Service'
          description: ''
  /api/user/service/{id}:
    get:
      operationId: api_user_service_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Service'
          description: ''
    put:
      operationId: api_user_service_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Service'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Service'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Service'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Service'
          description: ''
    patch:
      operationId: api_user_service_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedService'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedService'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedService'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Service'
          description: ''
    delete:
      operationId: api_user_service_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/user/site:
    get:
      operationId: api_user_site_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserSites'
          description: ''
  /api/user/site/{site_id}/lieu:
    get:
      operationId: api_user_site_lieu_list
      parameters:
      - in: path
        name: site_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserSitesLieux'
          description: ''
  /api/user/specialites:
    get:
      operationId: api_user_specialites_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Specialite'
          description: ''
  /api/user/specialites/{id}:
    get:
      operationId: api_user_specialites_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Specialite'
          description: ''
    put:
      operationId: api_user_specialites_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Specialite'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Specialite'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Specialite'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Specialite'
          description: ''
    patch:
      operationId: api_user_specialites_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSpecialite'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSpecialite'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSpecialite'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Specialite'
          description: ''
    delete:
      operationId: api_user_specialites_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/user/users-lies-all:
    get:
      operationId: api_user_users_lies_all_retrieve
      description: |-
        Create a Dictionary of objects from multiple models/serializers.

        Mixin is expecting the view will have a querylist variable, which is
        a list/tuple of dicts containing, at mininum, a `queryset` key and a
        `serializer_class` key,  as below:

        queryList = [
            {'queryset': MyModalA.objects.all(), 'serializer_class': MyModelASerializer ),
            {'queryset': MyModalB.objects.all(), 'serializer_class': MyModelBSerializer ),
            {'queryset': MyModalC.objects.all(), 'serializer_class': MyModelCSerializer ),
            ...
        ]

        This mixin returns a dictionary of serialized data separated by object type, e.g.:

        {
            'MyModelA': [
                { 'id': 1, 'type': 'myModelA', 'title': 'some_object' },
                { 'id': 8, 'type': 'myModelA', 'title': 'anotherother_object' },
                ...
            ],
            'MyModelB': [
                { 'id': 4, 'type': 'myModelB', 'title': 'some_other_object' },
                ...
            ]
            ...
        }
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /audio-recording/:
    get:
      operationId: audio_recording_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - audio-recording
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAudioRecordingList'
          description: ''
    post:
      operationId: audio_recording_create
      tags:
      - audio-recording
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AudioRecording'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AudioRecording'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioRecording'
          description: ''
  /audio-recording/{id}/:
    get:
      operationId: audio_recording_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this audio recording.
        required: true
      tags:
      - audio-recording
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioRecording'
          description: ''
    put:
      operationId: audio_recording_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this audio recording.
        required: true
      tags:
      - audio-recording
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AudioRecording'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AudioRecording'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioRecording'
          description: ''
    patch:
      operationId: audio_recording_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this audio recording.
        required: true
      tags:
      - audio-recording
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAudioRecording'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAudioRecording'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioRecording'
          description: ''
    delete:
      operationId: audio_recording_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this audio recording.
        required: true
      tags:
      - audio-recording
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /audio-recording/{id}/correct/:
    post:
      operationId: audio_recording_correct_create
      description: Corrects the medical transcription using Mistral LLM and saves
        the result.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this audio recording.
        required: true
      tags:
      - audio-recording
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AudioRecording'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AudioRecording'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioRecording'
          description: ''
  /audio-recording/update-audio/{id}:
    put:
      operationId: audio_recording_update_audio_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - audio-recording
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AudioRecording'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AudioRecording'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioRecording'
          description: ''
  /audio-recording/upload-audio/{patient_pk}:
    post:
      operationId: audio_recording_upload_audio_create
      parameters:
      - in: path
        name: patient_pk
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - audio-recording
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AudioRecording'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AudioRecording'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AudioRecording'
          description: ''
components:
  schemas:
    Absence:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_heure_start:
          type: string
          format: date-time
        date_heure_end:
          type: string
          format: date-time
        motif:
          type: string
          maxLength: 500
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
      required:
      - created_on
      - date_heure_end
      - date_heure_start
      - id
      - modified_on
      - motif
      - zkf_user
    Acte:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        pu_base:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        cod_acte:
          type: string
          nullable: true
          maxLength: 10
        dt_modif:
          type: string
          format: date
          nullable: true
        menu_cod:
          type: string
          nullable: true
          maxLength: 10
        fraidp_cod:
          type: string
          nullable: true
          maxLength: 10
        rembou_cod:
          type: string
          nullable: true
          maxLength: 10
        type_cod:
          type: string
          nullable: true
          maxLength: 10
        cod_struct:
          type: string
          nullable: true
          maxLength: 10
        nom_court:
          type: string
          nullable: true
          maxLength: 200
        nom_long:
          type: string
          nullable: true
          maxLength: 300
        nom_long0:
          type: string
          nullable: true
          maxLength: 300
        nom_long1:
          type: string
          nullable: true
          maxLength: 300
        nom_long2:
          type: string
          nullable: true
          maxLength: 300
        nom_long3:
          type: string
          nullable: true
          maxLength: 300
        nom_long4:
          type: string
          nullable: true
          maxLength: 300
        nom_long5:
          type: string
          nullable: true
          maxLength: 300
        nom_long6:
          type: string
          nullable: true
          maxLength: 300
        nom_long7:
          type: string
          nullable: true
          maxLength: 300
        nom_long8:
          type: string
          nullable: true
          maxLength: 300
        nom_long9:
          type: string
          nullable: true
          maxLength: 300
        nom_longa:
          type: string
          nullable: true
          maxLength: 300
        nom_longb:
          type: string
          nullable: true
          maxLength: 300
        nom_longc:
          type: string
          nullable: true
          maxLength: 300
        nom_longd:
          type: string
          nullable: true
          maxLength: 300
        nom_longe:
          type: string
          nullable: true
          maxLength: 300
        sexe:
          type: string
          nullable: true
          maxLength: 20
        dt_creatio:
          type: string
          format: date
          nullable: true
        dt_fin:
          type: string
          format: date
          nullable: true
        entente:
          type: string
          nullable: true
          maxLength: 20
        dt_effet:
          type: string
          format: date
          nullable: true
        dt_arrete:
          type: string
          format: date
          nullable: true
        dt_jo:
          type: string
          format: date
          nullable: true
        mfic_place:
          type: string
          nullable: true
          maxLength: 20
        precedent:
          type: string
          nullable: true
          maxLength: 20
        suivant:
          type: string
          nullable: true
          maxLength: 20
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - id
      - modified_on
      - pu_base
    ActionEnum:
      enum:
      - 0
      - 1
      - 2
      - 3
      type: integer
      description: |-
        * `0` - create
        * `1` - update
        * `2` - delete
        * `3` - access
    Adresse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          nullable: true
          maxLength: 254
        structure:
          type: string
          nullable: true
          maxLength: 256
        numero_rue:
          type: string
          nullable: true
          maxLength: 12
        rue:
          type: string
          nullable: true
          maxLength: 254
        complement_adresse:
          type: string
          nullable: true
          maxLength: 254
        code_postal:
          type: integer
          maximum: **********
          minimum: 0
        ville:
          type: string
          maxLength: 254
        editable:
          type: boolean
        preferred:
          type: boolean
          description: Mode de communication préféré ?
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
      required:
      - code_postal
      - created_on
      - id
      - modified_on
      - ville
    AffichageServiceEnum:
      enum:
      - SPE
      - USE
      type: string
      description: |-
        * `SPE` - Specialite
        * `USE` - User
    AffichageServiceStatusEnum:
      enum:
      - STA
      - DYN
      type: string
      description: |-
        * `STA` - Static
        * `DYN` - Dynamique
    AllCalendarItems:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        corps:
          type: string
        start:
          type: integer
        end:
          type: integer
        type:
          type: string
        zkf_user:
          type: string
        zkf_patient:
          type: object
          additionalProperties:
            type: string
        text_color:
          type: string
        background_color:
          type: string
        resource_id:
          type: string
        operations:
          type: array
          items: {}
        event_data:
          type: object
          additionalProperties:
            type: string
        all_day:
          type: boolean
          default: false
      required:
      - corps
      - end
      - id
      - start
      - title
      - type
      - zkf_user
    AnswerFormField:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        response:
          type: string
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
        zkf_form_field:
          type: string
          format: uuid
        form:
          type: string
          format: uuid
      required:
      - created_on
      - form
      - id
      - modified_on
      - response
      - zkf_form_field
      - zkf_protocole
    AudioRecording:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: Date of creation.
        auteur:
          type: integer
          nullable: true
        auteur_id:
          type: integer
          writeOnly: true
        document:
          type: string
          format: uuid
          nullable: true
        consultation:
          allOf:
          - $ref: '#/components/schemas/Consultation'
          readOnly: true
        consultation_id:
          type: string
          format: uuid
          writeOnly: true
        hospitalisation:
          type: string
          format: uuid
          nullable: true
        patient:
          allOf:
          - $ref: '#/components/schemas/Patient'
          readOnly: true
        patient_id:
          type: string
          format: uuid
          writeOnly: true
        file:
          type: string
          format: uri
        title:
          type: string
          nullable: true
          description: Title of the audio recording.
          maxLength: 200
        transcription:
          type: string
          nullable: true
          description: Transcription of the audio recording.
        transcription_corrected:
          type: string
          nullable: true
          description: LLM-corrected medical transcription.
        status:
          allOf:
          - $ref: '#/components/schemas/StatusEnum'
          description: |-
            Status of the audio recording validation and lifecycle.

            * `unverified` - UNVERIFIED
            * `pending_validation` - PENDING_VALIDATION
            * `validated` - VALIDATED
            * `pending_deletion_validated` - PENDING_DELETION_VALIDATED
            * `pending_deletion_inactive` - PENDING_DELETION_INACTIVE
            * `pending_deletion_7day_warning_sent` - PENDING_DELETION_7DAY_WARNING_SENT
            * `deleted` - DELETED
      required:
      - consultation
      - created_at
      - file
      - id
      - patient
    AuditLog:
      type: object
      description: Serializer for the AuditLog model.
      properties:
        id:
          type: integer
          readOnly: true
        actor:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        object:
          type: string
          readOnly: true
        object_pk:
          type: string
          readOnly: true
          maxLength: 255
        object_id:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          readOnly: true
          nullable: true
          title: Id de l’objet
        object_repr:
          type: string
          readOnly: true
          title: Object representation
        serialized_data:
          readOnly: true
          nullable: true
        action:
          allOf:
          - $ref: '#/components/schemas/ActionEnum'
          readOnly: true
          minimum: 0
          maximum: 32767
        changes_text:
          type: string
          readOnly: true
          title: Message de modification
        changes:
          readOnly: true
          nullable: true
          title: Message de modification
        cid:
          type: string
          readOnly: true
          nullable: true
          title: Correlation ID
          maxLength: 255
        remote_addr:
          type: string
          readOnly: true
          nullable: true
          title: Remote address
        remote_port:
          type: integer
          maximum: **********
          minimum: 0
          readOnly: true
          nullable: true
        timestamp:
          type: string
          format: date-time
          readOnly: true
        additional_data:
          readOnly: true
          nullable: true
        actor_email:
          type: string
          readOnly: true
          nullable: true
          maxLength: 254
        content_type:
          type: integer
          readOnly: true
          title: Type de contenu
      required:
      - action
      - actor
      - actor_email
      - additional_data
      - changes
      - changes_text
      - cid
      - content_type
      - id
      - object
      - object_id
      - object_pk
      - object_repr
      - remote_addr
      - remote_port
      - serialized_data
      - timestamp
    AuthToken:
      type: object
      properties:
        username:
          type: string
          writeOnly: true
          title: Nom de l'utilisateur
        password:
          type: string
          writeOnly: true
          title: Mot de passe
        token:
          type: string
          readOnly: true
          title: Jeton
      required:
      - password
      - token
      - username
    Avoir:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_operation:
          type: string
          format: date
          nullable: true
        date_avoir:
          type: string
          format: date
          nullable: true
        numero_avoir:
          type: string
          nullable: true
          maxLength: 255
        montant:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        note:
          type: string
          nullable: true
        avoir_valide:
          type: boolean
        date_avoir_validation:
          type: string
          format: date-time
          nullable: true
        avoir_acquite:
          type: boolean
        date_avoir_acquite:
          type: string
          format: date
          nullable: true
        mode_reglement:
          type: string
          nullable: true
          maxLength: 255
        numero_cheque:
          type: string
          nullable: true
          maxLength: 255
        numero_banque_cheque:
          type: string
          nullable: true
          maxLength: 255
        iban:
          type: string
          nullable: true
          maxLength: 255
        date_depot_cheque:
          type: string
          format: date
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_createur:
          type: integer
          nullable: true
        zkf_acte_realisateur:
          type: integer
          nullable: true
          description: Personne ayant réalisé l'acte
        zkf_groupe_facturation:
          type: string
          format: uuid
        zkf_facture:
          type: string
          format: uuid
          nullable: true
        avoir_validation_user:
          type: integer
          nullable: true
        zkf_acteur_lie:
          type: array
          items:
            type: integer
          description: Personnes liées à l'acte (chirurgien, etc...)
      required:
      - created_on
      - id
      - modified_on
      - zkf_groupe_facturation
      - zkf_patient
    BlankEnum:
      enum:
      - ''
    CategorieEnum:
      enum:
      - PROFESSIONNEL
      - PORTABLE
      - DOMICILE
      - AUTRE
      - FAX
      type: string
      description: |-
        * `PROFESSIONNEL` - Professionnel
        * `PORTABLE` - Portable
        * `DOMICILE` - Domicile
        * `AUTRE` - Autre
        * `FAX` - Fax
    CategorieOrdonnance:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        categorie:
          type: string
          maxLength: 256
        information_patient_ordonnance:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - categorie
      - created_on
      - id
      - information_patient_ordonnance
      - modified_on
    CheckedIdentityEnum:
      enum:
      - NONE
      - FR_ID
      - EU_ID
      - PASSPORT
      - SEJOUR
      - FRANCE_IDENTITE
      - LAPOSTE
      - VITALE_APP
      - ATTESTATION
      type: string
      description: |-
        * `NONE` - Identité non vérifiée
        * `FR_ID` - Carte Nationale d'Identité Française
        * `EU_ID` - Carte d'Identité (UE/EEE/CH/UK/Microétats)
        * `PASSPORT` - Passeport
        * `SEJOUR` - Titre de Séjour
        * `FRANCE_IDENTITE` - France Identité
        * `LAPOSTE` - Identité Numérique La Poste
        * `VITALE_APP` - Application Carte Vitale
        * `ATTESTATION` - Attestation du professionnel
    CompteRendu:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        patient_nom:
          type: string
          readOnly: true
        auteur_nom:
          type: string
          readOnly: true
        contact_not_found:
          type: string
          readOnly: true
        should_be_contacted:
          type: boolean
        specialites:
          type: array
          items:
            $ref: '#/components/schemas/Specialite'
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom:
          type: string
          nullable: true
          description: Nom descriptif du document
          maxLength: 256
        type_compte_rendu:
          $ref: '#/components/schemas/TypeCompteRenduEnum'
        date_examen:
          type: string
          format: date
          nullable: true
        date_entree:
          type: string
          format: date
          nullable: true
        date_sortie:
          type: string
          format: date
          nullable: true
        examen_file:
          type: string
          format: uri
        status:
          type: boolean
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_consultation:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        auteur:
          type: string
          format: uuid
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
        type_examen:
          type: array
          items:
            type: string
            format: uuid
      required:
      - auteur_nom
      - contact_not_found
      - created_on
      - examen_file
      - id
      - modified_on
      - patient_nom
      - specialites
    ConsignesSortie:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_consigne:
          type: string
          nullable: true
          maxLength: 200
        texte_suivi:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
          readOnly: true
        zkf_environnement:
          type: string
          format: uuid
          readOnly: true
        zkf_service:
          type: string
          format: uuid
          nullable: true
        zkf_specialite:
          type: string
          format: uuid
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
          nullable: true
      required:
      - created_on
      - id
      - modified_on
      - suggested
      - texte_suivi
      - zkf_environnement
      - zkf_user
    Consultation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        consultation_date:
          type: string
          format: date
          nullable: true
        consultation_arrive:
          type: string
          format: time
          nullable: true
        consultation_debut_prevu:
          type: string
          format: time
          nullable: true
        consultation_fin_prevu:
          type: string
          format: time
          nullable: true
        consultation_debut_reel:
          type: string
          format: time
          nullable: true
        consultation_fin_reel:
          type: string
          format: time
          nullable: true
        premiere_consultation:
          type: boolean
        mode_consultation:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeConsultationEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        motif_consultation:
          type: string
          nullable: true
        reminder_consultation:
          type: string
          nullable: true
          maxLength: 256
        observation_consultation:
          type: string
          nullable: true
        statut_courrier_consultation:
          $ref: '#/components/schemas/StatutCourrierConsultationEnum'
        courrier_consultation:
          type: string
        courrier_consultation_bas_page:
          type: string
        etat_consultation:
          $ref: '#/components/schemas/EtatConsultationEnum'
        depassement_honoraire:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        mode_reglement:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeReglementEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        total_prix_consultation:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_site:
          type: string
          format: uuid
        zkf_lieu:
          type: string
          format: uuid
          nullable: true
        zkf_redacteur:
          type: integer
          nullable: true
        zkf_statut_consultation:
          type: string
          format: uuid
          nullable: true
        operation_liee:
          type: array
          items:
            type: string
            format: uuid
        consultations_type:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - model_app_datas
      - modified_on
      - zkf_patient
      - zkf_site
    ConsultationType:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          maxLength: 256
        corps:
          type: string
          nullable: true
        bas_de_page:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          readOnly: true
        zkf_user:
          type: integer
          readOnly: true
          nullable: true
        users:
          type: array
          items:
            type: integer
        sites:
          type: array
          items:
            type: string
            format: uuid
        lieus:
          type: array
          items:
            type: string
            format: uuid
        services:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - modified_on
      - titre
      - zkf_environnement
      - zkf_user
    Contact:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom:
          type: string
          nullable: true
          maxLength: 254
        prenom:
          type: string
          nullable: true
          maxLength: 254
        rpps:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
        sexe:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/SexeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        structure:
          type: string
          nullable: true
          maxLength: 254
        titre:
          $ref: '#/components/schemas/ContactTitreEnum'
        en_activite:
          type: boolean
        comment:
          type: string
          nullable: true
        created_from_annuaire_sante:
          type: boolean
        annuaire_sante_verification:
          type: string
          format: date-time
          nullable: true
        annuaire_sante_last_attempt:
          type: string
          format: date-time
          nullable: true
        id_annuaire_sante:
          type: string
          nullable: true
          maxLength: 254
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contactcategorie:
          type: string
          format: uuid
          readOnly: true
        zkf_environnement_contact:
          type: string
          format: uuid
          readOnly: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - modified_on
      - zkf_contactcategorie
      - zkf_environnement_contact
    ContactTitreEnum:
      enum:
      - Docteur
      - Professeur
      - Madame
      - Monsieur
      type: string
      description: |-
        * `Docteur` - Docteur
        * `Professeur` - Professeur
        * `Madame` - Madame
        * `Monsieur` - Monsieur
    Contraste:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        denomination:
          type: string
          maxLength: 60
        composition:
          type: string
          nullable: true
          maxLength: 254
        teneur_iode:
          type: string
          nullable: true
          maxLength: 254
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        service:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - denomination
      - id
      - modified_on
      - service
    Correspondant:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        specialite_string:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          $ref: '#/components/schemas/CorrespondantTitreEnum'
        en_activite:
          type: boolean
        created_by:
          type: integer
          readOnly: true
          nullable: true
        modified_by:
          type: integer
          readOnly: true
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_by
      - created_on
      - id
      - modified_by
      - modified_on
      - specialite_string
    CorrespondantPatient:
      type: object
      properties:
        contact:
          type: string
          readOnly: true
        send_report:
          type: boolean
          description: Envoyer les rapports/courriers à ce contact
        referral_source:
          type: boolean
          description: Contact adresseur
      required:
      - contact
    CorrespondantTitreEnum:
      enum:
      - Docteur
      - Professeur
      type: string
      description: |-
        * `Docteur` - Docteur
        * `Professeur` - Professeur
    CouvertureMaladie:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom:
          type: string
          maxLength: 254
        tiers_payant:
          type: boolean
        couverture:
          type: integer
          maximum: **********
          minimum: -**********
        ordo_bizone:
          type: boolean
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - couverture
      - created_on
      - id
      - modified_on
      - nom
      - ordo_bizone
      - tiers_payant
    CroType:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        cro_type_titre_descriptif:
          type: string
          nullable: true
          description: Titre court pour selection dans la liste
          maxLength: 200
        cro_type_titre:
          type: string
          nullable: true
          description: Titre du CRO type à inserer dans une operation
          maxLength: 200
        cro_type_indication_operatoire:
          type: string
          nullable: true
          description: Indication opératoire du CRO type à inserer dans une operation
        cro_type:
          type: string
          nullable: true
        nom_modele_specialite:
          type: string
          nullable: true
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
      required:
      - created_on
      - id
      - modified_on
      - suggested
      - zkf_user
    Devis:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_devis:
          type: string
          format: date
        depassement_honoraire:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_createur:
          type: integer
          nullable: true
        zkf_devisfavoris:
          type: string
          format: uuid
          nullable: true
      required:
      - created_on
      - id
      - model_app_datas
      - modified_on
      - zkf_patient
    DocumentLibre:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_document:
          type: string
          maxLength: 200
        date_document:
          type: string
          format: date
          readOnly: true
        corps_document:
          type: string
          nullable: true
        document_libre_file:
          type: string
          format: uri
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_consultation:
          type: string
          format: uuid
          nullable: true
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_document_template:
          type: string
          format: uuid
          nullable: true
        redacteur_document:
          type: integer
          nullable: true
      required:
      - created_on
      - date_document
      - id
      - model_app_datas
      - modified_on
      - titre_document
      - zkf_patient
    DocumentLibreTemplate:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_document:
          type: string
          maxLength: 200
        description_document:
          type: string
          nullable: true
        corps_document:
          type: string
          nullable: true
        document_type_file:
          type: string
          format: uri
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        specialite:
          type: array
          items:
            type: string
            format: uuid
        service:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - modified_on
      - suggested
      - titre_document
    Email:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        email:
          type: string
          format: email
          description: Entrer l'email
          maxLength: 254
        email_mssante_boolean:
          type: boolean
          description: Email messagerie sécurisée ?
        editable:
          type: boolean
        categorie:
          type: string
          nullable: true
          maxLength: 254
        preferred:
          type: boolean
          description: Mode de communication préféré ?
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
      required:
      - created_on
      - email
      - id
      - modified_on
    EnteteDocument:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          maxLength: 256
        texte:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
      required:
      - created_on
      - id
      - modified_on
      - texte
      - titre
      - zkf_environnement
    EtatConsultationEnum:
      enum:
      - a_venir
      - arrive
      - debutee
      - finie
      - non_venu
      - annule
      - excuse
      type: string
      description: |-
        * `a_venir` - A venir
        * `arrive` - Arrive
        * `debutee` - Debutee
        * `finie` - Finie
        * `non_venu` - Non venu
        * `annule` - annulée
        * `excuse` - Excusée
    EtatEnum:
      enum:
      - VIVANT
      - DCD
      type: string
      description: |-
        * `VIVANT` - Vivant
        * `DCD` - Décédé
    ExtendedConsultation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        model_app_datas:
          type: string
          readOnly: true
        zkf_patient:
          $ref: '#/components/schemas/Patient'
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        consultation_date:
          type: string
          format: date
          nullable: true
        consultation_arrive:
          type: string
          format: time
          nullable: true
        consultation_debut_prevu:
          type: string
          format: time
          nullable: true
        consultation_fin_prevu:
          type: string
          format: time
          nullable: true
        consultation_debut_reel:
          type: string
          format: time
          nullable: true
        consultation_fin_reel:
          type: string
          format: time
          nullable: true
        premiere_consultation:
          type: boolean
        mode_consultation:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeConsultationEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        motif_consultation:
          type: string
          nullable: true
        reminder_consultation:
          type: string
          nullable: true
          maxLength: 256
        observation_consultation:
          type: string
          nullable: true
        statut_courrier_consultation:
          $ref: '#/components/schemas/StatutCourrierConsultationEnum'
        courrier_consultation:
          type: string
        courrier_consultation_bas_page:
          type: string
        etat_consultation:
          $ref: '#/components/schemas/EtatConsultationEnum'
        depassement_honoraire:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        mode_reglement:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeReglementEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        total_prix_consultation:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_site:
          type: string
          format: uuid
        zkf_lieu:
          type: string
          format: uuid
          nullable: true
        zkf_redacteur:
          type: integer
          nullable: true
        zkf_statut_consultation:
          type: string
          format: uuid
          nullable: true
        operation_liee:
          type: array
          items:
            type: string
            format: uuid
        consultations_type:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - model_app_datas
      - modified_on
      - zkf_patient
      - zkf_site
    ExtendedHospitalisation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        operations:
          type: string
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        zkf_patient:
          $ref: '#/components/schemas/Patient'
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        entree_date:
          type: string
          format: date
          nullable: true
        entree_heure:
          type: string
          format: time
          nullable: true
        sortie_date:
          type: string
          format: date
          nullable: true
        hospitalisation_ambulatoire:
          type: boolean
        a_programmer:
          type: boolean
        annulation_hospitalisation:
          type: boolean
        commentaire_annulation_hospitalisation:
          type: string
        motif_hospitalisation:
          type: string
        histoire_maladie:
          type: string
        examen_clinique:
          type: string
        traitement_sortie:
          type: string
          nullable: true
        crh:
          type: string
        date_redaction_crh:
          type: string
          format: date
          nullable: true
        statut_crh:
          $ref: '#/components/schemas/StatutCrhEnum'
        complication_post_op:
          type: boolean
          nullable: true
        detail_complication_post_op:
          type: string
        mode_entree:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeEntreeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        origine_transfert:
          type: string
          nullable: true
          maxLength: 256
        transfusion:
          type: boolean
        test_portage_bmr:
          type: boolean
        examens_biologie_radiologie:
          type: boolean
        regles_hygieno_dietetiques:
          type: boolean
        remise_document_patient:
          type: boolean
        destination_retour_domicile:
          type: boolean
        destination_retour_detail:
          type: string
          nullable: true
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_consigne_sortie:
          type: string
          format: uuid
          nullable: true
        service_hospitalisation:
          type: string
          format: uuid
        specialite_referente:
          type: string
          format: uuid
        zkf_redacteur:
          type: integer
          nullable: true
        operation_liee:
          type: array
          items:
            type: string
            format: uuid
        consignes_sortie:
          type: array
          items:
            type: string
            format: uuid
        medecin_referent:
          type: array
          items:
            type: integer
          description: Choisir un ou plusieurs médecin(s) référent(s)
      required:
      - created_on
      - id
      - medecin_referent
      - model_app_datas
      - modified_on
      - operations
      - service_hospitalisation
      - specialite_referente
      - zkf_patient
    Facture:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_operation:
          type: string
          format: date
          nullable: true
        date_facture:
          type: string
          format: date
          nullable: true
        numero_facture:
          type: string
          nullable: true
          maxLength: 255
        montant:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        note:
          type: string
          nullable: true
        facture_validee:
          type: boolean
        date_facture_validation:
          type: string
          format: date-time
          nullable: true
        facture_acquitee:
          type: boolean
        date_facture_acquitee:
          type: string
          format: date
          nullable: true
        mode_reglement:
          type: string
          nullable: true
          maxLength: 255
        numero_cheque:
          type: string
          nullable: true
          maxLength: 255
        numero_banque_cheque:
          type: string
          nullable: true
          maxLength: 255
        iban:
          type: string
          nullable: true
          maxLength: 255
        date_depot_cheque:
          type: string
          format: date
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_createur:
          type: integer
          nullable: true
        zkf_acte_realisateur:
          type: integer
          nullable: true
          description: Personne ayant réalisé l'acte
        zkf_groupe_facturation:
          type: string
          format: uuid
        facture_validation_user:
          type: integer
          nullable: true
        zkf_acteur_lie:
          type: array
          items:
            type: string
            format: uuid
          description: Personnes liées à l'acte (chirurgien, etc...)
      required:
      - created_on
      - id
      - modified_on
      - zkf_groupe_facturation
      - zkf_patient
    FieldTypeEnum:
      enum:
      - text
      - number
      - date
      - select
      - boolean
      type: string
      description: |-
        * `text` - Text
        * `number` - Number
        * `date` - Date
        * `select` - Select
        * `boolean` - Boolean
    Form:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
        zkf_form_template:
          type: string
          format: uuid
        responses:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - modified_on
      - zkf_form_template
      - zkf_protocole
    FormField:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        zkf_protocole:
          $ref: '#/components/schemas/Protocole'
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        label:
          type: string
          maxLength: 256
        field_type:
          $ref: '#/components/schemas/FieldTypeEnum'
        timing_operation:
          type: string
          nullable: true
          title: Timing par rapport à l'operation
          description: pre, per, post ou suivi
          maxLength: 256
        description:
          type: string
          maxLength: 256
        default:
          type: string
          nullable: true
          maxLength: 256
        options:
          type: string
          nullable: true
          description: Options séparées par des virgules
          maxLength: 256
        min:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        max:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - description
      - field_type
      - id
      - label
      - modified_on
      - zkf_protocole
    FormTemplate:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        name:
          type: string
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
        zkf_specialite:
          type: string
          format: uuid
        formfields:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - formfields
      - id
      - modified_on
      - name
      - zkf_protocole
      - zkf_specialite
    FullTag:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          maxLength: 100
        slug:
          type: string
          pattern: ^[-\w]+$
          maxLength: 100
        specialite:
          type: string
          format: uuid
        specialites:
          type: array
          items:
            type: string
            format: uuid
        user:
          type: array
          items:
            type: integer
      required:
      - id
      - name
      - slug
      - specialite
    GestionnaireImpression:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        patient:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        string_of_html:
          type: string
          nullable: true
        zkf_id:
          type: string
          format: uuid
          nullable: true
        titre_document:
          type: string
          nullable: true
          maxLength: 256
        url_document:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        type_document:
          type: string
          nullable: true
          maxLength: 256
        object_id:
          type: string
          format: uuid
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          nullable: true
        zkf_user:
          type: integer
        zkf_patient:
          type: string
          format: uuid
        content_type:
          type: integer
          nullable: true
      required:
      - created_on
      - id
      - modified_on
      - patient
      - zkf_patient
      - zkf_user
    Group:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          title: Nom
          maxLength: 150
        permissions:
          type: array
          items:
            type: integer
      required:
      - id
      - name
    GroupeFacturation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_groupe:
          type: string
          maxLength: 256
        nom_societe:
          type: string
          maxLength: 255
        siren:
          type: string
          maxLength: 255
        siret:
          type: string
          maxLength: 255
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        utilisateurs_lies:
          type: array
          items:
            type: integer
      required:
      - created_on
      - id
      - modified_on
      - nom_groupe
      - nom_societe
      - siren
      - siret
      - zkf_environnement
    Hospitalisation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        operations:
          type: string
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        entree_date:
          type: string
          format: date
          nullable: true
        entree_heure:
          type: string
          format: time
          nullable: true
        sortie_date:
          type: string
          format: date
          nullable: true
        hospitalisation_ambulatoire:
          type: boolean
        a_programmer:
          type: boolean
        annulation_hospitalisation:
          type: boolean
        commentaire_annulation_hospitalisation:
          type: string
        motif_hospitalisation:
          type: string
        histoire_maladie:
          type: string
        examen_clinique:
          type: string
        traitement_sortie:
          type: string
          nullable: true
        crh:
          type: string
        date_redaction_crh:
          type: string
          format: date
          nullable: true
        statut_crh:
          $ref: '#/components/schemas/StatutCrhEnum'
        complication_post_op:
          type: boolean
          nullable: true
        detail_complication_post_op:
          type: string
        mode_entree:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeEntreeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        origine_transfert:
          type: string
          nullable: true
          maxLength: 256
        transfusion:
          type: boolean
        test_portage_bmr:
          type: boolean
        examens_biologie_radiologie:
          type: boolean
        regles_hygieno_dietetiques:
          type: boolean
        remise_document_patient:
          type: boolean
        destination_retour_domicile:
          type: boolean
        destination_retour_detail:
          type: string
          nullable: true
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_consigne_sortie:
          type: string
          format: uuid
          nullable: true
        service_hospitalisation:
          type: string
          format: uuid
        specialite_referente:
          type: string
          format: uuid
        zkf_redacteur:
          type: integer
          nullable: true
        operation_liee:
          type: array
          items:
            type: string
            format: uuid
        consignes_sortie:
          type: array
          items:
            type: string
            format: uuid
        medecin_referent:
          type: array
          items:
            type: integer
          description: Choisir un ou plusieurs médecin(s) référent(s)
      required:
      - created_on
      - id
      - medecin_referent
      - model_app_datas
      - modified_on
      - operations
      - service_hospitalisation
      - specialite_referente
      - zkf_patient
    Intervenant:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        default:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          nullable: true
          maxLength: 256
        nom:
          type: string
          nullable: true
          maxLength: 256
        prenom:
          type: string
          nullable: true
          maxLength: 256
        is_active:
          type: boolean
        date_debut:
          type: string
          format: date
          nullable: true
        date_fin:
          type: string
          format: date
          nullable: true
        remplacant:
          type: boolean
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        zkf_user:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        specialite_principale:
          type: string
          format: uuid
          nullable: true
        specialite_secondaire:
          type: array
          items:
            type: string
            format: uuid
        service:
          type: array
          items:
            type: string
            format: uuid
        site:
          type: array
          items:
            type: string
            format: uuid
        groupe:
          type: array
          items:
            type: integer
      required:
      - created_on
      - default
      - id
      - modified_on
      - zkf_environnement
    Irradiation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        pds_total:
          type: number
          format: double
          nullable: true
        pds_unite:
          type: string
          nullable: true
          maxLength: 30
        air_kerma_total:
          type: number
          format: double
          nullable: true
        air_kerma_unite:
          type: string
          nullable: true
          maxLength: 30
        duree_scopie_total:
          type: string
          format: time
          nullable: true
        volume_iode:
          type: number
          format: double
          nullable: true
        pds_scopie:
          type: number
          format: double
          nullable: true
        air_kerma_scopie:
          type: number
          format: double
          nullable: true
        duree_scopie_scopie:
          type: string
          format: time
          nullable: true
        pds_acquisition:
          type: number
          format: double
          nullable: true
        air_kerma_acquisition:
          type: number
          format: double
          nullable: true
        duree_scopie_acquisition:
          type: string
          format: time
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        produit_contraste:
          type: string
          format: uuid
          nullable: true
      required:
      - created_on
      - id
      - modified_on
    LevelEnum:
      enum:
      - SUCCESS
      - INFO
      - WARNING
      - ERROR
      type: string
      description: |-
        * `SUCCESS` - success
        * `INFO` - info
        * `WARNING` - warning
        * `ERROR` - error
    MatrielIrradiation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        site:
          type: string
          format: uuid
        specialite:
          type: string
          format: uuid
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_appareil_radio:
          type: string
          maxLength: 256
        marque_appareil_radio:
          type: string
          maxLength: 256
        date_premiere_utilisation:
          type: string
          format: date
          nullable: true
        en_utilisation:
          type: boolean
        pds_unite:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/PdsUniteEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        air_kerma_unite:
          type: string
          nullable: true
          maxLength: 30
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        zkf_site:
          type: array
          items:
            type: string
            format: uuid
        zkf_specialite:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - en_utilisation
      - id
      - marque_appareil_radio
      - modified_on
      - nom_appareil_radio
    ModeConsultationEnum:
      enum:
      - programme
      - urgent
      type: string
      description: |-
        * `programme` - Programmée
        * `urgent` - Urgence
    ModeEntreeEnum:
      enum:
      - programme
      - urgence
      - transfert
      type: string
      description: |-
        * `programme` - Programmé
        * `urgence` - Urgence
        * `transfert` - Transfert
    ModeReglementEnum:
      enum:
      - cheque
      - especes
      - gratuit
      - cb
      - tiers_payant
      - CMU
      type: string
      description: |-
        * `cheque` - Cheque
        * `especes` - Espèces
        * `gratuit` - Gratuit
        * `cb` - Carte banquaire
        * `tiers_payant` - Tiers Payant
        * `CMU` - cmu
    Nested:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        password:
          type: string
          title: Mot de passe
          maxLength: 128
        last_login:
          type: string
          format: date-time
          nullable: true
          title: Dernière connexion
        is_superuser:
          type: boolean
          title: Statut super-utilisateur
          description: Précise que l’utilisateur possède toutes les permissions sans
            les assigner explicitement.
        username:
          type: string
          title: Nom d’utilisateur
          description: Requis. 150 caractères maximum. Uniquement des lettres, nombres
            et les caractères « @ », « . », « + », « - » et « _ ».
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        email:
          type: string
          format: email
          title: Adresse électronique
          maxLength: 254
        is_staff:
          type: boolean
          title: Statut équipe
          description: Précise si l’utilisateur peut se connecter à ce site d'administration.
        is_active:
          type: boolean
          title: Actif
          description: Précise si l’utilisateur doit être considéré comme actif. Décochez
            ceci plutôt que de supprimer le compte.
        date_joined:
          type: string
          format: date-time
          title: Date d’inscription
        groups:
          type: array
          items:
            type: integer
            title: Groupes
          title: Groupes
          description: Les groupes dont fait partie cet utilisateur. Celui-ci obtient
            tous les droits de tous les groupes auxquels il appartient.
        user_permissions:
          type: array
          items:
            type: integer
            title: Permissions de l’utilisateur
          title: Permissions de l’utilisateur
          description: Permissions spécifiques à cet utilisateur.
      required:
      - id
      - password
      - username
    NfTypeEnum:
      enum:
      - cr_consultation
      - cr_operation
      - cr_hospitalisation
      - user_added_to_staff
      - cr_correspondant
      - patient_added_in_protocol
      - user_added_in_protocol
      - audio_deletion_scheduled_validated
      - audio_deletion_scheduled_inactive
      - audio_deleted
      - audio_deletion_cancelled
      type: string
      description: |-
        * `cr_consultation` - Compte rendu de consultation
        * `cr_operation` - Compte rendu d'opération
        * `cr_hospitalisation` - Compte rendu d'hospitalisation
        * `user_added_to_staff` - Utilisateur ajouté à un staff
        * `cr_correspondant` - Compte rendu de correspondant ajouté
        * `patient_added_in_protocol` - Patient ajouté dans un protocole
        * `user_added_in_protocol` - Utilisateur ajouté dans un protocole
        * `audio_deletion_scheduled_validated` - Suppression audio (consult. validée) programmée
        * `audio_deletion_scheduled_inactive` - Suppression audio (consult. inactive) programmée
        * `audio_deleted` - Audio supprimé
        * `audio_deletion_cancelled` - Suppression audio annulée
    Notification:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        recipients:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        level:
          $ref: '#/components/schemas/LevelEnum'
        flagged:
          type: boolean
        actor_object_id:
          type: string
          format: uuid
          nullable: true
          title: ID of the actor object
        actor_text:
          type: string
          nullable: true
          title: Anonymous text for actor
          maxLength: 50
        actor_url_text:
          type: string
          nullable: true
          title: Anonymous URL for actor
          maxLength: 200
        verb:
          type: string
          title: Verb of the action
          maxLength: 50
        title:
          type: string
          title: Title of the notification
          maxLength: 255
        description:
          type: string
          nullable: true
          title: Description of the notification
          maxLength: 255
        nf_type:
          allOf:
          - $ref: '#/components/schemas/NfTypeEnum'
          title: Type of notification
        target_object_id:
          type: string
          format: uuid
          nullable: true
          title: ID of the target object
        target_text:
          type: string
          nullable: true
          title: Anonymous text for target
          maxLength: 50
        target_url_text:
          type: string
          nullable: true
          title: Anonymous URL for target
          maxLength: 200
        obj_object_id:
          type: string
          format: uuid
          nullable: true
          title: ID of the target object
        obj_text:
          type: string
          nullable: true
          title: Anonymous text for action object
          maxLength: 100
        obj_url_text:
          type: string
          nullable: true
          title: Anonymous URL for action object
          maxLength: 200
        extra:
          nullable: true
          title: JSONField to store addtional data
        doc_link:
          type: string
          nullable: true
        read:
          type: boolean
          title: Read status
        route_view_object:
          type: string
          nullable: true
          title: Route to view object
          maxLength: 256
        created_by:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        modified_by:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        deleted_by:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        zkf_recipient:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        actor_content_type:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        target_content_type:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        obj_content_type:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
      required:
      - actor_content_type
      - created_by
      - created_on
      - deleted_by
      - id
      - modified_by
      - modified_on
      - obj_content_type
      - recipients
      - target_content_type
      - title
      - verb
      - zkf_recipient
    NullEnum:
      enum:
      - null
    Operation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        model_app_datas:
          type: string
          readOnly: true
        protocoles:
          type: array
          items:
            type: string
            format: uuid
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        operation_date:
          type: string
          format: date
          nullable: true
        operation_start:
          type: string
          format: date-time
          nullable: true
        operation_end:
          type: string
          format: date-time
          nullable: true
        a_programmer:
          type: boolean
        annulation_operation:
          type: boolean
        commentaire_programmation_operation:
          type: string
        consentement_pre_operatoire_recupere:
          type: boolean
        urgence:
          oneOf:
          - $ref: '#/components/schemas/UrgenceEnum'
          - $ref: '#/components/schemas/BlankEnum'
        commentaire_operation:
          type: string
        titre_operation:
          type: string
          maxLength: 256
        indication_operatoire:
          type: string
        anesthesie:
          type: string
          maxLength: 50
        cro:
          type: string
        complication:
          type: boolean
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_cro_type:
          type: string
          format: uuid
          nullable: true
        zkf_site:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_consultation_pre_op:
          type: string
          format: uuid
          nullable: true
        anesthesiste_old:
          type: integer
          nullable: true
        zkf_redacteur:
          type: integer
          nullable: true
        materiel_irradiation:
          type: string
          format: uuid
          nullable: true
        cro_types:
          type: array
          items:
            type: string
            format: uuid
        operateurs_old:
          type: array
          items:
            type: integer
        aide_operatoire_old:
          type: array
          items:
            type: integer
        operateur_new:
          type: array
          items:
            type: string
            format: uuid
        anesthesiste_new:
          type: array
          items:
            type: string
            format: uuid
        aides_operatoire:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - model_app_datas
      - modified_on
      - titre_operation
      - zkf_patient
    OperationDevisCcam:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        acte_ccam:
          type: string
          maxLength: 15
        acte_nom_long:
          type: string
          nullable: true
        acte_principal_secondaire_supplementaire:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        acte_activite:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_phase:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_grille:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_modificateur_urgence:
          type: string
          nullable: true
          maxLength: 2
        acte_modificateur_chir:
          type: string
          nullable: true
          maxLength: 12
        acte_modificateur:
          type: string
          nullable: true
          maxLength: 12
        acte_association:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_supplementaire:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        prix_base:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        prix_modificateurs:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        depassement_honoraire_acte:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_devis:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_facture:
          type: string
          format: uuid
          nullable: true
        zkf_devis_favoris:
          type: string
          format: uuid
          nullable: true
        zkf_acte:
          type: string
          format: uuid
          nullable: true
      required:
      - acte_ccam
      - created_on
      - id
      - modified_on
    Ordonnance:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_ordonnance:
          type: string
          nullable: true
          maxLength: 256
        date_ordonnance:
          type: string
          format: date-time
        corps_ordonnance:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_ordo_template:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_consultation:
          type: string
          format: uuid
          nullable: true
        redacteur:
          type: integer
          nullable: true
        ordonnance_template_lie:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - date_ordonnance
      - id
      - model_app_datas
      - modified_on
      - zkf_patient
    OrdonnanceTemplate:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_ordonnance:
          type: string
          maxLength: 200
        description_ordonnance:
          type: string
          nullable: true
        corps_ordonnance:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
        categorie_ordonnance:
          type: string
          format: uuid
          nullable: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - modified_on
      - specialite
      - suggested
      - titre_ordonnance
      - zkf_user
    PaginatedActeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Acte'
    PaginatedAnswerFormFieldList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/AnswerFormField'
    PaginatedAudioRecordingList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/AudioRecording'
    PaginatedAuditLogList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/AuditLog'
    PaginatedAvoirList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Avoir'
    PaginatedCompteRenduList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/CompteRendu'
    PaginatedConsultationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Consultation'
    PaginatedDevisList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Devis'
    PaginatedExtendedConsultationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/ExtendedConsultation'
    PaginatedExtendedHospitalisationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=400&limit=100
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?offset=200&limit=100
        results:
          type: array
          items:
            $ref: '#/components/schemas/ExtendedHospitalisation'
    PaginatedFactureList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Facture'
    PaginatedFormFieldList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/FormField'
    PaginatedFormList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Form'
    PaginatedFormTemplateList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/FormTemplate'
    PaginatedGroupeFacturationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/GroupeFacturation'
    PaginatedHospitalisationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Hospitalisation'
    PaginatedNotificationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Notification'
    PaginatedOrdonnanceList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Ordonnance'
    PaginatedPatientList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Patient'
    PaginatedR_activite_modificateurList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/R_activite_modificateur'
    PaginatedR_tb_23List:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/R_tb_23'
    PaginatedRelanceFactureList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/RelanceFacture'
    PaginatedUserEventList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserEvent'
    PaginatedUserList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    PatchedAbsence:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_heure_start:
          type: string
          format: date-time
        date_heure_end:
          type: string
          format: date-time
        motif:
          type: string
          maxLength: 500
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
    PatchedAdresse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          nullable: true
          maxLength: 254
        structure:
          type: string
          nullable: true
          maxLength: 256
        numero_rue:
          type: string
          nullable: true
          maxLength: 12
        rue:
          type: string
          nullable: true
          maxLength: 254
        complement_adresse:
          type: string
          nullable: true
          maxLength: 254
        code_postal:
          type: integer
          maximum: **********
          minimum: 0
        ville:
          type: string
          maxLength: 254
        editable:
          type: boolean
        preferred:
          type: boolean
          description: Mode de communication préféré ?
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
    PatchedAnswerFormField:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        response:
          type: string
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
        zkf_form_field:
          type: string
          format: uuid
        form:
          type: string
          format: uuid
    PatchedAudioRecording:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
          description: Date of creation.
        auteur:
          type: integer
          nullable: true
        auteur_id:
          type: integer
          writeOnly: true
        document:
          type: string
          format: uuid
          nullable: true
        consultation:
          allOf:
          - $ref: '#/components/schemas/Consultation'
          readOnly: true
        consultation_id:
          type: string
          format: uuid
          writeOnly: true
        hospitalisation:
          type: string
          format: uuid
          nullable: true
        patient:
          allOf:
          - $ref: '#/components/schemas/Patient'
          readOnly: true
        patient_id:
          type: string
          format: uuid
          writeOnly: true
        file:
          type: string
          format: uri
        title:
          type: string
          nullable: true
          description: Title of the audio recording.
          maxLength: 200
        transcription:
          type: string
          nullable: true
          description: Transcription of the audio recording.
        transcription_corrected:
          type: string
          nullable: true
          description: LLM-corrected medical transcription.
        status:
          allOf:
          - $ref: '#/components/schemas/StatusEnum'
          description: |-
            Status of the audio recording validation and lifecycle.

            * `unverified` - UNVERIFIED
            * `pending_validation` - PENDING_VALIDATION
            * `validated` - VALIDATED
            * `pending_deletion_validated` - PENDING_DELETION_VALIDATED
            * `pending_deletion_inactive` - PENDING_DELETION_INACTIVE
            * `pending_deletion_7day_warning_sent` - PENDING_DELETION_7DAY_WARNING_SENT
            * `deleted` - DELETED
    PatchedAvoir:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_operation:
          type: string
          format: date
          nullable: true
        date_avoir:
          type: string
          format: date
          nullable: true
        numero_avoir:
          type: string
          nullable: true
          maxLength: 255
        montant:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        note:
          type: string
          nullable: true
        avoir_valide:
          type: boolean
        date_avoir_validation:
          type: string
          format: date-time
          nullable: true
        avoir_acquite:
          type: boolean
        date_avoir_acquite:
          type: string
          format: date
          nullable: true
        mode_reglement:
          type: string
          nullable: true
          maxLength: 255
        numero_cheque:
          type: string
          nullable: true
          maxLength: 255
        numero_banque_cheque:
          type: string
          nullable: true
          maxLength: 255
        iban:
          type: string
          nullable: true
          maxLength: 255
        date_depot_cheque:
          type: string
          format: date
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_createur:
          type: integer
          nullable: true
        zkf_acte_realisateur:
          type: integer
          nullable: true
          description: Personne ayant réalisé l'acte
        zkf_groupe_facturation:
          type: string
          format: uuid
        zkf_facture:
          type: string
          format: uuid
          nullable: true
        avoir_validation_user:
          type: integer
          nullable: true
        zkf_acteur_lie:
          type: array
          items:
            type: integer
          description: Personnes liées à l'acte (chirurgien, etc...)
    PatchedCompteRendu:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        patient_nom:
          type: string
          readOnly: true
        auteur_nom:
          type: string
          readOnly: true
        contact_not_found:
          type: string
          readOnly: true
        should_be_contacted:
          type: boolean
        specialites:
          type: array
          items:
            $ref: '#/components/schemas/Specialite'
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom:
          type: string
          nullable: true
          description: Nom descriptif du document
          maxLength: 256
        type_compte_rendu:
          $ref: '#/components/schemas/TypeCompteRenduEnum'
        date_examen:
          type: string
          format: date
          nullable: true
        date_entree:
          type: string
          format: date
          nullable: true
        date_sortie:
          type: string
          format: date
          nullable: true
        examen_file:
          type: string
          format: uri
        status:
          type: boolean
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_consultation:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        auteur:
          type: string
          format: uuid
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
        type_examen:
          type: array
          items:
            type: string
            format: uuid
    PatchedConsignesSortie:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_consigne:
          type: string
          nullable: true
          maxLength: 200
        texte_suivi:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
          readOnly: true
        zkf_environnement:
          type: string
          format: uuid
          readOnly: true
        zkf_service:
          type: string
          format: uuid
          nullable: true
        zkf_specialite:
          type: string
          format: uuid
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
          nullable: true
    PatchedConsultation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        consultation_date:
          type: string
          format: date
          nullable: true
        consultation_arrive:
          type: string
          format: time
          nullable: true
        consultation_debut_prevu:
          type: string
          format: time
          nullable: true
        consultation_fin_prevu:
          type: string
          format: time
          nullable: true
        consultation_debut_reel:
          type: string
          format: time
          nullable: true
        consultation_fin_reel:
          type: string
          format: time
          nullable: true
        premiere_consultation:
          type: boolean
        mode_consultation:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeConsultationEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        motif_consultation:
          type: string
          nullable: true
        reminder_consultation:
          type: string
          nullable: true
          maxLength: 256
        observation_consultation:
          type: string
          nullable: true
        statut_courrier_consultation:
          $ref: '#/components/schemas/StatutCourrierConsultationEnum'
        courrier_consultation:
          type: string
        courrier_consultation_bas_page:
          type: string
        etat_consultation:
          $ref: '#/components/schemas/EtatConsultationEnum'
        depassement_honoraire:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        mode_reglement:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeReglementEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        total_prix_consultation:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_site:
          type: string
          format: uuid
        zkf_lieu:
          type: string
          format: uuid
          nullable: true
        zkf_redacteur:
          type: integer
          nullable: true
        zkf_statut_consultation:
          type: string
          format: uuid
          nullable: true
        operation_liee:
          type: array
          items:
            type: string
            format: uuid
        consultations_type:
          type: array
          items:
            type: string
            format: uuid
    PatchedConsultationType:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          maxLength: 256
        corps:
          type: string
          nullable: true
        bas_de_page:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          readOnly: true
        zkf_user:
          type: integer
          readOnly: true
          nullable: true
        users:
          type: array
          items:
            type: integer
        sites:
          type: array
          items:
            type: string
            format: uuid
        lieus:
          type: array
          items:
            type: string
            format: uuid
        services:
          type: array
          items:
            type: string
            format: uuid
    PatchedContact:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom:
          type: string
          nullable: true
          maxLength: 254
        prenom:
          type: string
          nullable: true
          maxLength: 254
        rpps:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
          nullable: true
        sexe:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/SexeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        structure:
          type: string
          nullable: true
          maxLength: 254
        titre:
          $ref: '#/components/schemas/ContactTitreEnum'
        en_activite:
          type: boolean
        comment:
          type: string
          nullable: true
        created_from_annuaire_sante:
          type: boolean
        annuaire_sante_verification:
          type: string
          format: date-time
          nullable: true
        annuaire_sante_last_attempt:
          type: string
          format: date-time
          nullable: true
        id_annuaire_sante:
          type: string
          nullable: true
          maxLength: 254
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contactcategorie:
          type: string
          format: uuid
          readOnly: true
        zkf_environnement_contact:
          type: string
          format: uuid
          readOnly: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
    PatchedContraste:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        denomination:
          type: string
          maxLength: 60
        composition:
          type: string
          nullable: true
          maxLength: 254
        teneur_iode:
          type: string
          nullable: true
          maxLength: 254
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        service:
          type: array
          items:
            type: string
            format: uuid
    PatchedCorrespondant:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        specialite_string:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          $ref: '#/components/schemas/CorrespondantTitreEnum'
        en_activite:
          type: boolean
        created_by:
          type: integer
          readOnly: true
          nullable: true
        modified_by:
          type: integer
          readOnly: true
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
    PatchedCroType:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        cro_type_titre_descriptif:
          type: string
          nullable: true
          description: Titre court pour selection dans la liste
          maxLength: 200
        cro_type_titre:
          type: string
          nullable: true
          description: Titre du CRO type à inserer dans une operation
          maxLength: 200
        cro_type_indication_operatoire:
          type: string
          nullable: true
          description: Indication opératoire du CRO type à inserer dans une operation
        cro_type:
          type: string
          nullable: true
        nom_modele_specialite:
          type: string
          nullable: true
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
    PatchedDevis:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_devis:
          type: string
          format: date
        depassement_honoraire:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_createur:
          type: integer
          nullable: true
        zkf_devisfavoris:
          type: string
          format: uuid
          nullable: true
    PatchedDocumentLibre:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_document:
          type: string
          maxLength: 200
        date_document:
          type: string
          format: date
          readOnly: true
        corps_document:
          type: string
          nullable: true
        document_libre_file:
          type: string
          format: uri
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_consultation:
          type: string
          format: uuid
          nullable: true
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_document_template:
          type: string
          format: uuid
          nullable: true
        redacteur_document:
          type: integer
          nullable: true
    PatchedDocumentLibreTemplate:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_document:
          type: string
          maxLength: 200
        description_document:
          type: string
          nullable: true
        corps_document:
          type: string
          nullable: true
        document_type_file:
          type: string
          format: uri
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        specialite:
          type: array
          items:
            type: string
            format: uuid
        service:
          type: array
          items:
            type: string
            format: uuid
    PatchedEmail:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        email:
          type: string
          format: email
          description: Entrer l'email
          maxLength: 254
        email_mssante_boolean:
          type: boolean
          description: Email messagerie sécurisée ?
        editable:
          type: boolean
        categorie:
          type: string
          nullable: true
          maxLength: 254
        preferred:
          type: boolean
          description: Mode de communication préféré ?
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
    PatchedEnteteDocument:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          maxLength: 256
        texte:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
    PatchedFacture:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_operation:
          type: string
          format: date
          nullable: true
        date_facture:
          type: string
          format: date
          nullable: true
        numero_facture:
          type: string
          nullable: true
          maxLength: 255
        montant:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        note:
          type: string
          nullable: true
        facture_validee:
          type: boolean
        date_facture_validation:
          type: string
          format: date-time
          nullable: true
        facture_acquitee:
          type: boolean
        date_facture_acquitee:
          type: string
          format: date
          nullable: true
        mode_reglement:
          type: string
          nullable: true
          maxLength: 255
        numero_cheque:
          type: string
          nullable: true
          maxLength: 255
        numero_banque_cheque:
          type: string
          nullable: true
          maxLength: 255
        iban:
          type: string
          nullable: true
          maxLength: 255
        date_depot_cheque:
          type: string
          format: date
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_createur:
          type: integer
          nullable: true
        zkf_acte_realisateur:
          type: integer
          nullable: true
          description: Personne ayant réalisé l'acte
        zkf_groupe_facturation:
          type: string
          format: uuid
        facture_validation_user:
          type: integer
          nullable: true
        zkf_acteur_lie:
          type: array
          items:
            type: string
            format: uuid
          description: Personnes liées à l'acte (chirurgien, etc...)
    PatchedForm:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
        zkf_form_template:
          type: string
          format: uuid
        responses:
          type: array
          items:
            type: string
            format: uuid
    PatchedFormField:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        zkf_protocole:
          $ref: '#/components/schemas/Protocole'
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        label:
          type: string
          maxLength: 256
        field_type:
          $ref: '#/components/schemas/FieldTypeEnum'
        timing_operation:
          type: string
          nullable: true
          title: Timing par rapport à l'operation
          description: pre, per, post ou suivi
          maxLength: 256
        description:
          type: string
          maxLength: 256
        default:
          type: string
          nullable: true
          maxLength: 256
        options:
          type: string
          nullable: true
          description: Options séparées par des virgules
          maxLength: 256
        min:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        max:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
    PatchedFormTemplate:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        name:
          type: string
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_protocole:
          type: string
          format: uuid
        zkf_specialite:
          type: string
          format: uuid
        formfields:
          type: array
          items:
            type: string
            format: uuid
    PatchedGestionnaireImpression:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        patient:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        string_of_html:
          type: string
          nullable: true
        zkf_id:
          type: string
          format: uuid
          nullable: true
        titre_document:
          type: string
          nullable: true
          maxLength: 256
        url_document:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        type_document:
          type: string
          nullable: true
          maxLength: 256
        object_id:
          type: string
          format: uuid
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          nullable: true
        zkf_user:
          type: integer
        zkf_patient:
          type: string
          format: uuid
        content_type:
          type: integer
          nullable: true
    PatchedGroupeFacturation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_groupe:
          type: string
          maxLength: 256
        nom_societe:
          type: string
          maxLength: 255
        siren:
          type: string
          maxLength: 255
        siret:
          type: string
          maxLength: 255
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        utilisateurs_lies:
          type: array
          items:
            type: integer
    PatchedHospitalisation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        operations:
          type: string
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        entree_date:
          type: string
          format: date
          nullable: true
        entree_heure:
          type: string
          format: time
          nullable: true
        sortie_date:
          type: string
          format: date
          nullable: true
        hospitalisation_ambulatoire:
          type: boolean
        a_programmer:
          type: boolean
        annulation_hospitalisation:
          type: boolean
        commentaire_annulation_hospitalisation:
          type: string
        motif_hospitalisation:
          type: string
        histoire_maladie:
          type: string
        examen_clinique:
          type: string
        traitement_sortie:
          type: string
          nullable: true
        crh:
          type: string
        date_redaction_crh:
          type: string
          format: date
          nullable: true
        statut_crh:
          $ref: '#/components/schemas/StatutCrhEnum'
        complication_post_op:
          type: boolean
          nullable: true
        detail_complication_post_op:
          type: string
        mode_entree:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ModeEntreeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        origine_transfert:
          type: string
          nullable: true
          maxLength: 256
        transfusion:
          type: boolean
        test_portage_bmr:
          type: boolean
        examens_biologie_radiologie:
          type: boolean
        regles_hygieno_dietetiques:
          type: boolean
        remise_document_patient:
          type: boolean
        destination_retour_domicile:
          type: boolean
        destination_retour_detail:
          type: string
          nullable: true
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_consigne_sortie:
          type: string
          format: uuid
          nullable: true
        service_hospitalisation:
          type: string
          format: uuid
        specialite_referente:
          type: string
          format: uuid
        zkf_redacteur:
          type: integer
          nullable: true
        operation_liee:
          type: array
          items:
            type: string
            format: uuid
        consignes_sortie:
          type: array
          items:
            type: string
            format: uuid
        medecin_referent:
          type: array
          items:
            type: integer
          description: Choisir un ou plusieurs médecin(s) référent(s)
    PatchedIntervenant:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        default:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre:
          type: string
          nullable: true
          maxLength: 256
        nom:
          type: string
          nullable: true
          maxLength: 256
        prenom:
          type: string
          nullable: true
          maxLength: 256
        is_active:
          type: boolean
        date_debut:
          type: string
          format: date
          nullable: true
        date_fin:
          type: string
          format: date
          nullable: true
        remplacant:
          type: boolean
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        zkf_user:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        specialite_principale:
          type: string
          format: uuid
          nullable: true
        specialite_secondaire:
          type: array
          items:
            type: string
            format: uuid
        service:
          type: array
          items:
            type: string
            format: uuid
        site:
          type: array
          items:
            type: string
            format: uuid
        groupe:
          type: array
          items:
            type: integer
    PatchedIrradiation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        pds_total:
          type: number
          format: double
          nullable: true
        pds_unite:
          type: string
          nullable: true
          maxLength: 30
        air_kerma_total:
          type: number
          format: double
          nullable: true
        air_kerma_unite:
          type: string
          nullable: true
          maxLength: 30
        duree_scopie_total:
          type: string
          format: time
          nullable: true
        volume_iode:
          type: number
          format: double
          nullable: true
        pds_scopie:
          type: number
          format: double
          nullable: true
        air_kerma_scopie:
          type: number
          format: double
          nullable: true
        duree_scopie_scopie:
          type: string
          format: time
          nullable: true
        pds_acquisition:
          type: number
          format: double
          nullable: true
        air_kerma_acquisition:
          type: number
          format: double
          nullable: true
        duree_scopie_acquisition:
          type: string
          format: time
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        produit_contraste:
          type: string
          format: uuid
          nullable: true
    PatchedMatrielIrradiation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        site:
          type: string
          format: uuid
        specialite:
          type: string
          format: uuid
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_appareil_radio:
          type: string
          maxLength: 256
        marque_appareil_radio:
          type: string
          maxLength: 256
        date_premiere_utilisation:
          type: string
          format: date
          nullable: true
        en_utilisation:
          type: boolean
        pds_unite:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/PdsUniteEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        air_kerma_unite:
          type: string
          nullable: true
          maxLength: 30
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        zkf_site:
          type: array
          items:
            type: string
            format: uuid
        zkf_specialite:
          type: array
          items:
            type: string
            format: uuid
    PatchedNotification:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        recipients:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        level:
          $ref: '#/components/schemas/LevelEnum'
        flagged:
          type: boolean
        actor_object_id:
          type: string
          format: uuid
          nullable: true
          title: ID of the actor object
        actor_text:
          type: string
          nullable: true
          title: Anonymous text for actor
          maxLength: 50
        actor_url_text:
          type: string
          nullable: true
          title: Anonymous URL for actor
          maxLength: 200
        verb:
          type: string
          title: Verb of the action
          maxLength: 50
        title:
          type: string
          title: Title of the notification
          maxLength: 255
        description:
          type: string
          nullable: true
          title: Description of the notification
          maxLength: 255
        nf_type:
          allOf:
          - $ref: '#/components/schemas/NfTypeEnum'
          title: Type of notification
        target_object_id:
          type: string
          format: uuid
          nullable: true
          title: ID of the target object
        target_text:
          type: string
          nullable: true
          title: Anonymous text for target
          maxLength: 50
        target_url_text:
          type: string
          nullable: true
          title: Anonymous URL for target
          maxLength: 200
        obj_object_id:
          type: string
          format: uuid
          nullable: true
          title: ID of the target object
        obj_text:
          type: string
          nullable: true
          title: Anonymous text for action object
          maxLength: 100
        obj_url_text:
          type: string
          nullable: true
          title: Anonymous URL for action object
          maxLength: 200
        extra:
          nullable: true
          title: JSONField to store addtional data
        doc_link:
          type: string
          nullable: true
        read:
          type: boolean
          title: Read status
        route_view_object:
          type: string
          nullable: true
          title: Route to view object
          maxLength: 256
        created_by:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        modified_by:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        deleted_by:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        zkf_recipient:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        actor_content_type:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        target_content_type:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
        obj_content_type:
          allOf:
          - $ref: '#/components/schemas/Nested'
          readOnly: true
    PatchedOperation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        model_app_datas:
          type: string
          readOnly: true
        protocoles:
          type: array
          items:
            type: string
            format: uuid
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        operation_date:
          type: string
          format: date
          nullable: true
        operation_start:
          type: string
          format: date-time
          nullable: true
        operation_end:
          type: string
          format: date-time
          nullable: true
        a_programmer:
          type: boolean
        annulation_operation:
          type: boolean
        commentaire_programmation_operation:
          type: string
        consentement_pre_operatoire_recupere:
          type: boolean
        urgence:
          oneOf:
          - $ref: '#/components/schemas/UrgenceEnum'
          - $ref: '#/components/schemas/BlankEnum'
        commentaire_operation:
          type: string
        titre_operation:
          type: string
          maxLength: 256
        indication_operatoire:
          type: string
        anesthesie:
          type: string
          maxLength: 50
        cro:
          type: string
        complication:
          type: boolean
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_cro_type:
          type: string
          format: uuid
          nullable: true
        zkf_site:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_consultation_pre_op:
          type: string
          format: uuid
          nullable: true
        anesthesiste_old:
          type: integer
          nullable: true
        zkf_redacteur:
          type: integer
          nullable: true
        materiel_irradiation:
          type: string
          format: uuid
          nullable: true
        cro_types:
          type: array
          items:
            type: string
            format: uuid
        operateurs_old:
          type: array
          items:
            type: integer
        aide_operatoire_old:
          type: array
          items:
            type: integer
        operateur_new:
          type: array
          items:
            type: string
            format: uuid
        anesthesiste_new:
          type: array
          items:
            type: string
            format: uuid
        aides_operatoire:
          type: array
          items:
            type: string
            format: uuid
    PatchedOperationDevisCcam:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        acte_ccam:
          type: string
          maxLength: 15
        acte_nom_long:
          type: string
          nullable: true
        acte_principal_secondaire_supplementaire:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        acte_activite:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_phase:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_grille:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_modificateur_urgence:
          type: string
          nullable: true
          maxLength: 2
        acte_modificateur_chir:
          type: string
          nullable: true
          maxLength: 12
        acte_modificateur:
          type: string
          nullable: true
          maxLength: 12
        acte_association:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        acte_supplementaire:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        prix_base:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        prix_modificateurs:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        depassement_honoraire_acte:
          type: string
          format: decimal
          pattern: ^-?\d{0,5}(?:\.\d{0,2})?$
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_devis:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_facture:
          type: string
          format: uuid
          nullable: true
        zkf_devis_favoris:
          type: string
          format: uuid
          nullable: true
        zkf_acte:
          type: string
          format: uuid
          nullable: true
    PatchedOrdonnance:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        model_app_datas:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_ordonnance:
          type: string
          nullable: true
          maxLength: 256
        date_ordonnance:
          type: string
          format: date-time
        corps_ordonnance:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_ordo_template:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_hospitalisation:
          type: string
          format: uuid
          nullable: true
        zkf_operation:
          type: string
          format: uuid
          nullable: true
        zkf_consultation:
          type: string
          format: uuid
          nullable: true
        redacteur:
          type: integer
          nullable: true
        ordonnance_template_lie:
          type: array
          items:
            type: string
            format: uuid
    PatchedOrdonnanceTemplate:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        suggested:
          type: string
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        titre_ordonnance:
          type: string
          maxLength: 200
        description_ordonnance:
          type: string
          nullable: true
        corps_ordonnance:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
        categorie_ordonnance:
          type: string
          format: uuid
          nullable: true
        specialite:
          type: array
          items:
            type: string
            format: uuid
    PatchedPatient:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        reset_ins_confirmation:
          type: boolean
          writeOnly: true
          default: false
        model_app_datas:
          type: string
          readOnly: true
        correspondants:
          type: array
          items:
            $ref: '#/components/schemas/CorrespondantPatient'
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom:
          type: string
          maxLength: 50
        nom_naissance:
          type: string
          maxLength: 50
        prenom:
          type: string
          maxLength: 150
        sexe:
          $ref: '#/components/schemas/SexeEnum'
        dob:
          type: string
          format: date
        etat:
          $ref: '#/components/schemas/EtatEnum'
        date_etat:
          type: string
          format: date
        mode_information_dernier_etat:
          type: string
          nullable: true
          maxLength: 50
        cause_deces:
          type: string
          nullable: true
        numero_securite_sociale:
          type: string
          nullable: true
          maxLength: 60
        ins:
          type: string
          readOnly: true
          nullable: true
          description: Identifiant INS du patient (qualifié ou provisoire)
        ins_oid:
          type: string
          readOnly: true
          nullable: true
          description: OID du système INS qui a fourni l'identifiant
        birth_place_code:
          type: string
          nullable: true
          description: Code INSEE de lieu de naissance
          maxLength: 5
        ins_checked:
          type: boolean
          readOnly: true
          description: Indique si une tentative de recherche INS a été effectuée (succès
            ou échec).
        ins_lookup_status:
          type: string
          readOnly: true
          description: Statut de la dernière tentative de recherche INS automatique.
        ins_lookup_details:
          type: string
          readOnly: true
          nullable: true
          description: Détails ou message d'erreur de la dernière recherche INS.
        checked_identity:
          allOf:
          - $ref: '#/components/schemas/CheckedIdentityEnum'
          description: |-
            Document utilisé pour vérifier l'identité du patient.

            * `NONE` - Identité non vérifiée
            * `FR_ID` - Carte Nationale d'Identité Française
            * `EU_ID` - Carte d'Identité (UE/EEE/CH/UK/Microétats)
            * `PASSPORT` - Passeport
            * `SEJOUR` - Titre de Séjour
            * `FRANCE_IDENTITE` - France Identité
            * `LAPOSTE` - Identité Numérique La Poste
            * `VITALE_APP` - Application Carte Vitale
            * `ATTESTATION` - Attestation du professionnel
        opposition_data_exploitation:
          type: boolean
        note_opposition_data_exploitation:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        couverture_maladie:
          type: string
          format: uuid
          nullable: true
        zkf_assurance_sante:
          type: string
          format: uuid
          nullable: true
        praticien_referent:
          type: array
          items:
            type: integer
    PatchedPreferenceUserSerialyzer:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        current_praticiens_lies_selectionnes:
          type: array
          items:
            $ref: '#/components/schemas/User'
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        affichage_service:
          $ref: '#/components/schemas/AffichageServiceEnum'
        consultation_duree:
          type: string
          description: Durée moyenne d'une consultation (HH:MM:SS)
        affichage_service_status:
          $ref: '#/components/schemas/AffichageServiceStatusEnum'
        report_hebdo:
          type: boolean
        impression_recto_verso:
          type: boolean
        preference_notification: {}
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        user:
          type: integer
        ordonnance_default:
          type: string
          format: uuid
          nullable: true
    PatchedProfileUserSerialyzer:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        secretaire:
          type: string
          maxLength: 200
        role:
          oneOf:
          - $ref: '#/components/schemas/RoleEnum'
          - $ref: '#/components/schemas/BlankEnum'
        titre:
          oneOf:
          - $ref: '#/components/schemas/ProfileUserSerialyzerTitreEnum'
          - $ref: '#/components/schemas/BlankEnum'
        modificateurs_default:
          type: string
          nullable: true
          description: 'Modificateurs par défaut pour les actes CCAM: lettres séparées
            par une virgule. Ex: J,K '
          maxLength: 200
        rpps:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          nullable: true
        annuaire_sante_verification:
          type: string
          format: date-time
          nullable: true
        annuaire_sante_last_attempt:
          type: string
          format: date-time
          nullable: true
        entete_ordonnance:
          type: string
        last_seen:
          type: string
          format: date-time
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        user:
          type: integer
        zkf_environnement_user:
          type: string
          format: uuid
          nullable: true
        specialite_active:
          type: string
          format: uuid
          nullable: true
        entete:
          type: string
          format: uuid
          nullable: true
        conventionnement:
          type: string
          format: uuid
          nullable: true
        activite_ccam:
          type: string
          format: uuid
          nullable: true
        praticiens_lies:
          type: array
          items:
            type: integer
        zkf_environnement_lie:
          type: array
          items:
            type: string
            format: uuid
        secretariat:
          type: array
          items:
            type: string
            format: uuid
        specialite_profile:
          type: array
          items:
            type: string
            format: uuid
        service:
          type: array
          items:
            type: string
            format: uuid
        site:
          type: array
          items:
            type: string
            format: uuid
        groupe_facturation:
          type: array
          items:
            type: string
            format: uuid
    PatchedProtocole:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        libelle:
          type: string
          maxLength: 256
        description:
          type: string
          maxLength: 256
        frequence_rappel:
          type: string
          nullable: true
          description: en mois, séparés par un point virgule
          maxLength: 256
        temporalite_rappel:
          type: string
          description: jour, semaine ou mois
          maxLength: 256
        default_fields:
          type: string
          nullable: true
        protocole_summary:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement_protocole:
          type: string
          format: uuid
        protocole_users:
          type: array
          items:
            type: integer
    PatchedRelanceFacture:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_relance:
          type: string
          format: date
          nullable: true
        note_relance:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_facture:
          type: string
          format: uuid
    PatchedService:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_service:
          type: string
          maxLength: 200
        template_document:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          readOnly: true
        site_service:
          type: string
          format: uuid
        specialite_service:
          type: array
          items:
            type: string
            format: uuid
    PatchedSpecialite:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        spe:
          type: string
          maxLength: 200
        spe_medecin:
          type: string
          maxLength: 200
        type_specialite:
          $ref: '#/components/schemas/TypeSpecialiteEnum'
        data_app:
          type: string
          maxLength: 200
        omop_code:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
    PatchedSuivi:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        texte_suivi:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_hospitalisation:
          type: string
          format: uuid
    PatchedTelephone:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        numero:
          type: string
          maxLength: 20
        categorie:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/CategorieEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        editable:
          type: boolean
        preferred:
          type: boolean
          description: Mode de communication préféré ?
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
    PatchedUser:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        profile_id:
          type: string
          readOnly: true
        preferences_id:
          type: string
          readOnly: true
        default:
          type: string
          readOnly: true
        annuaire_sante_verification:
          type: string
          readOnly: true
        profile:
          allOf:
          - $ref: '#/components/schemas/ProfileUserSerialyzer'
          readOnly: true
        specialite_principale:
          type: string
          readOnly: true
        intervenant:
          type: string
          readOnly: true
        last_login:
          type: string
          format: date-time
          nullable: true
          title: Dernière connexion
        is_superuser:
          type: boolean
          title: Statut super-utilisateur
          description: Précise que l’utilisateur possède toutes les permissions sans
            les assigner explicitement.
        username:
          type: string
          title: Nom d’utilisateur
          description: Requis. 150 caractères maximum. Uniquement des lettres, nombres
            et les caractères « @ », « . », « + », « - » et « _ ».
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        email:
          type: string
          format: email
          title: Adresse électronique
          maxLength: 254
        is_staff:
          type: boolean
          title: Statut équipe
          description: Précise si l’utilisateur peut se connecter à ce site d'administration.
        is_active:
          type: boolean
          title: Actif
          description: Précise si l’utilisateur doit être considéré comme actif. Décochez
            ceci plutôt que de supprimer le compte.
        date_joined:
          type: string
          format: date-time
          title: Date d’inscription
        groups:
          type: array
          items:
            type: integer
            title: Groupes
          title: Groupes
          description: Les groupes dont fait partie cet utilisateur. Celui-ci obtient
            tous les droits de tous les groupes auxquels il appartient.
        user_permissions:
          type: array
          items:
            type: integer
            title: Permissions de l’utilisateur
          title: Permissions de l’utilisateur
          description: Permissions spécifiques à cet utilisateur.
    PatchedUserEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        notify_users:
          type: boolean
        notify_other_users:
          type: string
          nullable: true
          maxLength: 256
        event_title:
          type: string
          maxLength: 256
        event_note:
          type: string
          nullable: true
        event_start:
          type: string
          format: date-time
        event_end:
          type: string
          format: date-time
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        event_category:
          type: string
          format: uuid
          nullable: true
        users:
          type: array
          items:
            type: integer
    PatchedVacation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        type_vacation:
          $ref: '#/components/schemas/TypeVacationEnum'
        jour_semaine:
          type: integer
          maximum: 32767
          minimum: 0
          description: Lundi:1 Dimanche:7
        debut_vacation:
          type: string
          format: time
        fin_vacation:
          type: string
          format: time
        repetition_semaine:
          $ref: '#/components/schemas/RepetitionSemaineEnum'
        debut_periode:
          type: string
          format: date
        fin_periode:
          type: string
          format: date
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
        site:
          type: string
          format: uuid
    Patient:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        tags:
          type: array
          items:
            type: string
        reset_ins_confirmation:
          type: boolean
          writeOnly: true
          default: false
        model_app_datas:
          type: string
          readOnly: true
        correspondants:
          type: array
          items:
            $ref: '#/components/schemas/CorrespondantPatient'
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom:
          type: string
          maxLength: 50
        nom_naissance:
          type: string
          maxLength: 50
        prenom:
          type: string
          maxLength: 150
        sexe:
          $ref: '#/components/schemas/SexeEnum'
        dob:
          type: string
          format: date
        etat:
          $ref: '#/components/schemas/EtatEnum'
        date_etat:
          type: string
          format: date
        mode_information_dernier_etat:
          type: string
          nullable: true
          maxLength: 50
        cause_deces:
          type: string
          nullable: true
        numero_securite_sociale:
          type: string
          nullable: true
          maxLength: 60
        ins:
          type: string
          readOnly: true
          nullable: true
          description: Identifiant INS du patient (qualifié ou provisoire)
        ins_oid:
          type: string
          readOnly: true
          nullable: true
          description: OID du système INS qui a fourni l'identifiant
        birth_place_code:
          type: string
          nullable: true
          description: Code INSEE de lieu de naissance
          maxLength: 5
        ins_checked:
          type: boolean
          readOnly: true
          description: Indique si une tentative de recherche INS a été effectuée (succès
            ou échec).
        ins_lookup_status:
          type: string
          readOnly: true
          description: Statut de la dernière tentative de recherche INS automatique.
        ins_lookup_details:
          type: string
          readOnly: true
          nullable: true
          description: Détails ou message d'erreur de la dernière recherche INS.
        checked_identity:
          allOf:
          - $ref: '#/components/schemas/CheckedIdentityEnum'
          description: |-
            Document utilisé pour vérifier l'identité du patient.

            * `NONE` - Identité non vérifiée
            * `FR_ID` - Carte Nationale d'Identité Française
            * `EU_ID` - Carte d'Identité (UE/EEE/CH/UK/Microétats)
            * `PASSPORT` - Passeport
            * `SEJOUR` - Titre de Séjour
            * `FRANCE_IDENTITE` - France Identité
            * `LAPOSTE` - Identité Numérique La Poste
            * `VITALE_APP` - Application Carte Vitale
            * `ATTESTATION` - Attestation du professionnel
        opposition_data_exploitation:
          type: boolean
        note_opposition_data_exploitation:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        couverture_maladie:
          type: string
          format: uuid
          nullable: true
        zkf_assurance_sante:
          type: string
          format: uuid
          nullable: true
        praticien_referent:
          type: array
          items:
            type: integer
      required:
      - correspondants
      - created_on
      - dob
      - id
      - ins
      - ins_checked
      - ins_lookup_details
      - ins_lookup_status
      - ins_oid
      - model_app_datas
      - modified_on
      - nom
      - nom_naissance
      - prenom
      - sexe
    PdsUniteEnum:
      enum:
      - Gycm2
      - mGycm2
      type: string
      description: |-
        * `Gycm2` - Gycm2
        * `mGycm2` - mGycm2
    PreferenceUserSerialyzer:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        current_praticiens_lies_selectionnes:
          type: array
          items:
            $ref: '#/components/schemas/User'
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        affichage_service:
          $ref: '#/components/schemas/AffichageServiceEnum'
        consultation_duree:
          type: string
          description: Durée moyenne d'une consultation (HH:MM:SS)
        affichage_service_status:
          $ref: '#/components/schemas/AffichageServiceStatusEnum'
        report_hebdo:
          type: boolean
        impression_recto_verso:
          type: boolean
        preference_notification: {}
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        user:
          type: integer
        ordonnance_default:
          type: string
          format: uuid
          nullable: true
      required:
      - created_on
      - current_praticiens_lies_selectionnes
      - id
      - modified_on
      - user
    ProfileUserSerialyzer:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        secretaire:
          type: string
          maxLength: 200
        role:
          oneOf:
          - $ref: '#/components/schemas/RoleEnum'
          - $ref: '#/components/schemas/BlankEnum'
        titre:
          oneOf:
          - $ref: '#/components/schemas/ProfileUserSerialyzerTitreEnum'
          - $ref: '#/components/schemas/BlankEnum'
        modificateurs_default:
          type: string
          nullable: true
          description: 'Modificateurs par défaut pour les actes CCAM: lettres séparées
            par une virgule. Ex: J,K '
          maxLength: 200
        rpps:
          type: integer
          maximum: 9223372036854775807
          minimum: -9223372036854775808
          format: int64
          nullable: true
        annuaire_sante_verification:
          type: string
          format: date-time
          nullable: true
        annuaire_sante_last_attempt:
          type: string
          format: date-time
          nullable: true
        entete_ordonnance:
          type: string
        last_seen:
          type: string
          format: date-time
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        user:
          type: integer
        zkf_environnement_user:
          type: string
          format: uuid
          nullable: true
        specialite_active:
          type: string
          format: uuid
          nullable: true
        entete:
          type: string
          format: uuid
          nullable: true
        conventionnement:
          type: string
          format: uuid
          nullable: true
        activite_ccam:
          type: string
          format: uuid
          nullable: true
        praticiens_lies:
          type: array
          items:
            type: integer
        zkf_environnement_lie:
          type: array
          items:
            type: string
            format: uuid
        secretariat:
          type: array
          items:
            type: string
            format: uuid
        specialite_profile:
          type: array
          items:
            type: string
            format: uuid
        service:
          type: array
          items:
            type: string
            format: uuid
        site:
          type: array
          items:
            type: string
            format: uuid
        groupe_facturation:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - modified_on
      - service
      - site
      - specialite_profile
      - user
    ProfileUserSerialyzerTitreEnum:
      enum:
      - Mr
      - Dr
      - Mme
      type: string
      description: |-
        * `Mr` - Monsieur
        * `Dr` - Docteur
        * `Mme` - Madame
    Protocole:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        libelle:
          type: string
          maxLength: 256
        description:
          type: string
          maxLength: 256
        frequence_rappel:
          type: string
          nullable: true
          description: en mois, séparés par un point virgule
          maxLength: 256
        temporalite_rappel:
          type: string
          description: jour, semaine ou mois
          maxLength: 256
        default_fields:
          type: string
          nullable: true
        protocole_summary:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement_protocole:
          type: string
          format: uuid
        protocole_users:
          type: array
          items:
            type: integer
      required:
      - created_on
      - description
      - id
      - libelle
      - modified_on
      - protocole_users
      - temporalite_rappel
      - zkf_environnement_protocole
    R_activite:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        cod_activ:
          type: string
          nullable: true
          maxLength: 5
        libelle:
          type: string
          nullable: true
          maxLength: 256
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - id
      - modified_on
    R_activite_modificateur:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        aa_code:
          type: string
          nullable: true
          maxLength: 10
        aadt_modif:
          type: string
          format: date
          nullable: true
        modifi_cod:
          type: string
          nullable: true
          maxLength: 2
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - id
      - modified_on
    R_menu:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        cod_menu:
          type: string
          nullable: true
          maxLength: 7
        rang:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        libelle:
          type: string
          nullable: true
          maxLength: 256
        cod_pere:
          type: integer
          maximum: 32767
          minimum: 0
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - id
      - modified_on
    R_tb_23:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        cod_grille:
          type: string
          nullable: true
          maxLength: 5
        libelle:
          type: string
          nullable: true
          maxLength: 100
        definition:
          type: string
          nullable: true
          maxLength: 150
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - id
      - modified_on
    RelanceFacture:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        date_relance:
          type: string
          format: date
          nullable: true
        note_relance:
          type: string
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_facture:
          type: string
          format: uuid
      required:
      - created_on
      - id
      - modified_on
      - zkf_facture
    RepetitionSemaineEnum:
      enum:
      - PAIRE
      - IMPAIRE
      - TOUTES
      type: string
      description: |-
        * `PAIRE` - Paire
        * `IMPAIRE` - Impaire
        * `TOUTES` - Toutes
    RoleEnum:
      enum:
      - SEC
      - MED
      - INF
      type: string
      description: |-
        * `SEC` - Secretaire
        * `MED` - Medecin
        * `INF` - Infirmier
    Service:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_service:
          type: string
          maxLength: 200
        template_document:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
          readOnly: true
        site_service:
          type: string
          format: uuid
        specialite_service:
          type: array
          items:
            type: string
            format: uuid
      required:
      - created_on
      - id
      - modified_on
      - nom_service
      - site_service
      - specialite_service
      - zkf_environnement
    SexeEnum:
      enum:
      - MALE
      - FEMALE
      type: string
      description: |-
        * `MALE` - Homme
        * `FEMALE` - Femme
    Specialite:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        spe:
          type: string
          maxLength: 200
        spe_medecin:
          type: string
          maxLength: 200
        type_specialite:
          $ref: '#/components/schemas/TypeSpecialiteEnum'
        data_app:
          type: string
          maxLength: 200
        omop_code:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - data_app
      - id
      - modified_on
      - spe
      - spe_medecin
      - type_specialite
    StatusEnum:
      enum:
      - unverified
      - pending_validation
      - validated
      - pending_deletion_validated
      - pending_deletion_inactive
      - pending_deletion_7day_warning_sent
      - deleted
      type: string
      description: |-
        * `unverified` - UNVERIFIED
        * `pending_validation` - PENDING_VALIDATION
        * `validated` - VALIDATED
        * `pending_deletion_validated` - PENDING_DELETION_VALIDATED
        * `pending_deletion_inactive` - PENDING_DELETION_INACTIVE
        * `pending_deletion_7day_warning_sent` - PENDING_DELETION_7DAY_WARNING_SENT
        * `deleted` - DELETED
    StatutCourrierConsultationEnum:
      enum:
      - envoye
      - valide
      - attente_validation
      - transcrit
      - non_fait
      - non_statue
      type: string
      description: |-
        * `envoye` - Envoyé
        * `valide` - Validé
        * `attente_validation` - En attente de validation
        * `transcrit` - Transcrit (audio)
        * `non_fait` - Non fait
        * `non_statue` - Non statué
    StatutCrhEnum:
      enum:
      - envoye
      - valide
      - attente_validation
      - non_fait
      - non_statue
      type: string
      description: |-
        * `envoye` - Envoyé
        * `valide` - Validé
        * `attente_validation` - En attente de validation
        * `non_fait` - Non fait
        * `non_statue` - Non statué
    Suivi:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        texte_suivi:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_patient:
          type: string
          format: uuid
        zkf_hospitalisation:
          type: string
          format: uuid
      required:
      - created_on
      - id
      - modified_on
      - texte_suivi
      - zkf_hospitalisation
      - zkf_patient
    Telephone:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        numero:
          type: string
          maxLength: 20
        categorie:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/CategorieEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        editable:
          type: boolean
        preferred:
          type: boolean
          description: Mode de communication préféré ?
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_contact:
          type: string
          format: uuid
          nullable: true
        zkf_patient:
          type: string
          format: uuid
          nullable: true
      required:
      - created_on
      - id
      - modified_on
      - numero
    TypeCompteRenduEnum:
      enum:
      - CONSULTATION
      - HOSPITALISATION
      - EXAMEN_COMPLEMENTAIRE
      - ORDONNANCE_TRAITEMENT
      - BIOLOGIE
      - AUTREZ
      type: string
      description: |-
        * `CONSULTATION` - Consultation
        * `HOSPITALISATION` - Hospitalisation
        * `EXAMEN_COMPLEMENTAIRE` - Examen Complementaire
        * `ORDONNANCE_TRAITEMENT` - Ordonnance Traitement
        * `BIOLOGIE` - Biologie
        * `AUTREZ` - Autre
    TypeSpecialiteEnum:
      enum:
      - CHIRURGICAL
      - MEDICAL
      type: string
      description: |-
        * `CHIRURGICAL` - Chirurgical
        * `MEDICAL` - Medical
    TypeVacationEnum:
      enum:
      - CONSULTATION
      - OPERATION
      type: string
      description: |-
        * `CONSULTATION` - Consultation
        * `OPERATION` - Operation
    UrgenceEnum:
      enum:
      - urgence
      - programme
      type: string
      description: |-
        * `urgence` - Urgence
        * `programme` - Programmé
    User:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        profile_id:
          type: string
          readOnly: true
        preferences_id:
          type: string
          readOnly: true
        default:
          type: string
          readOnly: true
        annuaire_sante_verification:
          type: string
          readOnly: true
        profile:
          allOf:
          - $ref: '#/components/schemas/ProfileUserSerialyzer'
          readOnly: true
        specialite_principale:
          type: string
          readOnly: true
        intervenant:
          type: string
          readOnly: true
        last_login:
          type: string
          format: date-time
          nullable: true
          title: Dernière connexion
        is_superuser:
          type: boolean
          title: Statut super-utilisateur
          description: Précise que l’utilisateur possède toutes les permissions sans
            les assigner explicitement.
        username:
          type: string
          title: Nom d’utilisateur
          description: Requis. 150 caractères maximum. Uniquement des lettres, nombres
            et les caractères « @ », « . », « + », « - » et « _ ».
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        email:
          type: string
          format: email
          title: Adresse électronique
          maxLength: 254
        is_staff:
          type: boolean
          title: Statut équipe
          description: Précise si l’utilisateur peut se connecter à ce site d'administration.
        is_active:
          type: boolean
          title: Actif
          description: Précise si l’utilisateur doit être considéré comme actif. Décochez
            ceci plutôt que de supprimer le compte.
        date_joined:
          type: string
          format: date-time
          title: Date d’inscription
        groups:
          type: array
          items:
            type: integer
            title: Groupes
          title: Groupes
          description: Les groupes dont fait partie cet utilisateur. Celui-ci obtient
            tous les droits de tous les groupes auxquels il appartient.
        user_permissions:
          type: array
          items:
            type: integer
            title: Permissions de l’utilisateur
          title: Permissions de l’utilisateur
          description: Permissions spécifiques à cet utilisateur.
      required:
      - annuaire_sante_verification
      - default
      - id
      - intervenant
      - preferences_id
      - profile
      - profile_id
      - specialite_principale
      - username
    UserEvent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        notify_users:
          type: boolean
        notify_other_users:
          type: string
          nullable: true
          maxLength: 256
        event_title:
          type: string
          maxLength: 256
        event_note:
          type: string
          nullable: true
        event_start:
          type: string
          format: date-time
        event_end:
          type: string
          format: date-time
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        event_category:
          type: string
          format: uuid
          nullable: true
        users:
          type: array
          items:
            type: integer
      required:
      - created_on
      - event_end
      - event_start
      - id
      - modified_on
    UserEventCategory:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        name:
          type: string
          maxLength: 256
        description:
          type: string
        color:
          type: string
          maxLength: 15
        duration:
          type: string
          nullable: true
        allow_overlap:
          type: boolean
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - created_on
      - description
      - id
      - modified_on
    UserSites:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_site:
          type: string
          maxLength: 200
        adresse_site:
          type: string
          maxLength: 256
        zip_code_site:
          type: integer
          maximum: **********
          minimum: -**********
        ville_site:
          type: string
          maxLength: 200
        telephone_site:
          type: string
          nullable: true
          maxLength: 30
        adeli_site:
          type: string
          nullable: true
          maxLength: 30
        operation_schedule_color:
          type: string
          maxLength: 8
        consultation_schedule_color:
          type: string
          maxLength: 8
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement_site:
          type: string
          format: uuid
      required:
      - adresse_site
      - created_on
      - id
      - modified_on
      - nom_site
      - ville_site
      - zip_code_site
      - zkf_environnement_site
    UserSitesLieux:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_lieu:
          type: string
          maxLength: 200
        acces_lieu:
          type: string
        description_lieu:
          type: string
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_environnement:
          type: string
          format: uuid
        zkf_site:
          type: string
          format: uuid
      required:
      - created_on
      - id
      - modified_on
      - nom_lieu
      - zkf_environnement
      - zkf_site
    Vacation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        type_vacation:
          $ref: '#/components/schemas/TypeVacationEnum'
        jour_semaine:
          type: integer
          maximum: 32767
          minimum: 0
          description: Lundi:1 Dimanche:7
        debut_vacation:
          type: string
          format: time
        fin_vacation:
          type: string
          format: time
        repetition_semaine:
          $ref: '#/components/schemas/RepetitionSemaineEnum'
        debut_periode:
          type: string
          format: date
        fin_periode:
          type: string
          format: date
          nullable: true
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
        zkf_user:
          type: integer
        site:
          type: string
          format: uuid
      required:
      - created_on
      - debut_periode
      - debut_vacation
      - fin_vacation
      - id
      - jour_semaine
      - modified_on
      - site
      - zkf_user
    Ville:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        created_on:
          type: string
          format: date-time
          readOnly: true
        modified_on:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        deleted:
          type: boolean
        deleted_on:
          type: string
          format: date-time
          nullable: true
        nom_ville:
          type: string
          maxLength: 254
        code_postal:
          type: integer
          maximum: **********
          minimum: 0
        coordonnees_gps:
          type: string
          nullable: true
          maxLength: 40
        created_by:
          type: integer
          nullable: true
        modified_by:
          type: integer
          nullable: true
        deleted_by:
          type: integer
          nullable: true
      required:
      - code_postal
      - created_on
      - id
      - modified_on
      - nom_ville
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    tokenAuth:
      type: apiKey
      in: header
      name: Authorization
      description: Token-based authentication with required prefix "Token"
